{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a9b178bd196083b32fcfd866e6d8b324", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b2f4b25a8d90636f47ae32244238e77c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9880514fc91ab5e7a717bbfa3515910b5c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984e4d86d70890c21ed16cf87923a2cf2b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9880514fc91ab5e7a717bbfa3515910b5c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b7fb43e5cb40c6c38d1a3e45c7031a6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f609d637001a58abb4e10aaf306f1834", "guid": "bfdfe7dc352907fc980b868725387e98f50432fb543da2cb681feadb7db53c45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871eac12f3d4b860fd80689678b6e49e9", "guid": "bfdfe7dc352907fc980b868725387e9832fc697faa56797f5b8afb29bc276ffd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0a1097ba3d8ba3c70c4adaf0c770b45", "guid": "bfdfe7dc352907fc980b868725387e98fc20be4da2c440287bbf1cbedcf94fd6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa4db5cfc5356bd542c4fe74c5fe7788", "guid": "bfdfe7dc352907fc980b868725387e9813feee1561a7cd950e5c73a4daf26f14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98212d70fdf8ffd12291e6625cccfc2039", "guid": "bfdfe7dc352907fc980b868725387e9815d54c329cf2f0b4bd5b36b521240522", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eace881c029f8184be6d720e98ccd4c1", "guid": "bfdfe7dc352907fc980b868725387e98bdfbfd044d2ec3ab88047adcbef24500", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a4df61834adf6402628c7c02516153a", "guid": "bfdfe7dc352907fc980b868725387e9810610d177662453151cdc4b63a7d85e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abc6fa7f4a398d4da29c9e3545116c0d", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d8997fa21cf4e715d2df2ced469893b", "guid": "bfdfe7dc352907fc980b868725387e987ad27b82047bac70ac3bcebe4685132e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e2d57af6a627617dc81b190f831cf00", "guid": "bfdfe7dc352907fc980b868725387e98bd0c5a36bba2ed80ad99f5425ed2dea2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861554aa844f39af6ed862ee58ad10fd3", "guid": "bfdfe7dc352907fc980b868725387e98b52e497e9af1d2da3bb49d00caf084b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983eca1caff53fbc9247bd3f3bd90c8db6", "guid": "bfdfe7dc352907fc980b868725387e9820284b959847fac503baa71b6165fd42", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ec4a8282f9f314a4146e5c4c6e193ccf", "guid": "bfdfe7dc352907fc980b868725387e98d1fe2b05cf8888e79702cb844a1a04be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980481e4d53b31fc851133fabd4043d9a5", "guid": "bfdfe7dc352907fc980b868725387e980d8eacccc49d84483f216968ca7ba0bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884a24c97476bbb97e75fd275591dc205", "guid": "bfdfe7dc352907fc980b868725387e987cc96cda263366425e0b2e833ef263ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ccc20ff9544cc50aa3217c25bb65253", "guid": "bfdfe7dc352907fc980b868725387e9868b72e7a6235f1c92f364b80486692c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842b26c229b8e41d0f5bfc2502e673914", "guid": "bfdfe7dc352907fc980b868725387e987a46065074241f2ab00863937b2975af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1a3279c974691f6f9913aea58c9c134", "guid": "bfdfe7dc352907fc980b868725387e98e3054d8768ec0d1ac05682b8e956d31d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989df7085b09904aaecb5afd22cd2b0fba", "guid": "bfdfe7dc352907fc980b868725387e98c245691c4fde8c2526c0a55da880353d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ea9bc790ad372119a1d0c17c13795eb", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829927953d93aedaec1591a74c7de340f", "guid": "bfdfe7dc352907fc980b868725387e98f892d0f6f1575ca88d966310c64a1c4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e0cd7c86db934bf64493c9309d4a380", "guid": "bfdfe7dc352907fc980b868725387e98a1baa3d39093094b02632391ea7de55f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987edb9567f67e4ea3eb30811372dac00d", "guid": "bfdfe7dc352907fc980b868725387e9837e5cbaf52d88d601f03525db1fc0734"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c76c242737552e27b78a64000c3ed69", "guid": "bfdfe7dc352907fc980b868725387e989e732f409a6b27f50dd1d5b98e3923e8"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}