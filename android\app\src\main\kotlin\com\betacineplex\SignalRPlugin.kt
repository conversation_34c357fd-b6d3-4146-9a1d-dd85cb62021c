package com.betacineplex

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.google.gson.JsonObject
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import microsoft.aspnet.signalr.client.ConnectionState
import microsoft.aspnet.signalr.client.Platform
import microsoft.aspnet.signalr.client.http.android.AndroidPlatformComponent
import microsoft.aspnet.signalr.client.hubs.HubConnection
import microsoft.aspnet.signalr.client.hubs.HubProxy
import microsoft.aspnet.signalr.client.transport.ServerSentEventsTransport
import java.util.Timer
import java.util.TimerTask

class SignalRPlugin : FlutterPlugin, MethodChannel.MethodCallHandler, EventChannel.StreamHandler {
    private lateinit var methodChannel: MethodChannel
    private lateinit var connectionStateChannel: EventChannel
    private lateinit var dataChannel: EventChannel

    private var connectionStateEventSink: EventChannel.EventSink? = null
    private var dataEventSink: EventChannel.EventSink? = null

    private var mHubConnection: HubConnection? = null
    private var mHubProxy: HubProxy? = null
    private val handler = Handler(Looper.getMainLooper())
    private var timer: Timer? = null

    private val TAG = "SignalRPlugin"
    private val hubName = "chooseSeatHub"
    private val defaultUrl = "https://betacinemas.vn/signalr/hubs"

    override fun onAttachedToEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        methodChannel = MethodChannel(binding.binaryMessenger, "com.betacineplex/signalr")
        methodChannel.setMethodCallHandler(this)

        connectionStateChannel = EventChannel(binding.binaryMessenger, "com.betacineplex/signalr/connection_state")
        connectionStateChannel.setStreamHandler(this)

        dataChannel = EventChannel(binding.binaryMessenger, "com.betacineplex/signalr/data")
        dataChannel.setStreamHandler(object : EventChannel.StreamHandler {
            override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                dataEventSink = events
            }

            override fun onCancel(arguments: Any?) {
                dataEventSink = null
            }
        })
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "initialize" -> {
                result.success(true)
            }
            "start", "connect" -> {
                val url = call.argument<String>("url") ?: defaultUrl
                val hubName = call.argument<String>("hubName") ?: this.hubName
                startSignalR(url, result)
            }
            "stop", "disconnect" -> {
                stopSignalR(result)
            }
            "sendSeat", "invoke" -> {
                if (call.method == "sendSeat") {
                    val showId = call.argument<String>("showId") ?: ""
                    val seatIndex = call.argument<Int>("seatIndex") ?: 0
                    val status = call.argument<Int>("status") ?: 0
                    sendSeat(showId, seatIndex, status, result)
                } else {
                    val method = call.argument<String>("method") ?: ""
                    val arguments = call.argument<List<Any>>("arguments") ?: listOf()

                    if (method == "sendMessage" && arguments.size >= 4) {
                        val showId = arguments[1].toString()
                        val seatIndex = arguments[2].toString().toIntOrNull() ?: 0
                        val status = arguments[3].toString().toIntOrNull() ?: 0
                        sendSeat(showId, seatIndex, status, result)
                    } else {
                        result.error("INVALID_ARGUMENTS", "Invalid arguments for invoke method", null)
                    }
                }
            }
            "on" -> {
                val method = call.argument<String>("method") ?: ""
                if (method.isNotEmpty()) {
                    try {
                        mHubProxy?.on(method, { args ->
                            Log.d(TAG, "Received $method with ${args} arguments")
                        }, Any::class.java)
                        result.success(true)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error registering for method $method: ${e.message}")
                        result.error("ON_ERROR", "Failed to register for method", e.message)
                    }
                } else {
                    result.error("INVALID_METHOD", "Invalid method name", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun startSignalR(url: String, result: MethodChannel.Result) {
        try {
            // Load Android platform component
            Platform.loadPlatformComponent(AndroidPlatformComponent())

            // Create hub connection
            mHubConnection = HubConnection(url, null, true) { message, _ ->
                Log.d(TAG, message)
            }

            // Create hub proxy
            mHubProxy = mHubConnection?.createHubProxy(hubName)

            // Set up transport
            val clientTransport = ServerSentEventsTransport(mHubConnection?.logger)

            // Start connection
            mHubConnection?.start(clientTransport)

            // Set up event handlers
            setupEventHandlers()

            // Start keep-alive timer
            startKeepAliveTimer()

            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error starting SignalR: ${e.message}")
            result.error("START_ERROR", "Failed to start SignalR connection", e.message)
        }
    }

    private fun setupEventHandlers() {
        // Handle received data
        mHubConnection?.received { jsonElement ->
            if (jsonElement.isJsonObject) {
                val jsonObject = jsonElement.asJsonObject
                if (jsonObject.has("A")) {
                    try {
                        val ja = jsonObject.getAsJsonArray("A")
                        val data = HashMap<String, Any>()
                        data["connectionId"] = ja[0].asString
                        data["showId"] = ja[1].asString
                        data["seatIndex"] = ja[2].asInt
                        data["seatStatus"] = ja[3].asInt

                        handler.post {
                            dataEventSink?.success(data)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing data: ${e.message}")
                    }
                }
            }
        }

        // Handle connection state changes
        mHubConnection?.connected {
            handler.post {
                val data = HashMap<String, Any>()
                data["state"] = 2 // Connected
                data["connectionId"] = mHubConnection?.connectionId ?: ""
                connectionStateEventSink?.success(data)
            }

            // Subscribe to broadcast message
            mHubProxy?.on("broadcastMessage", { args ->
                // Handle broadcast message
                Log.d(TAG, "Received broadcast message with ${args} arguments")
            }, Any::class.java)
        }

        mHubConnection?.reconnecting {
            handler.post {
                val data = HashMap<String, Any>()
                data["state"] = 3 // Reconnecting
                data["connectionId"] = mHubConnection?.connectionId ?: ""
                connectionStateEventSink?.success(data)
            }
        }

        mHubConnection?.closed {
            handler.post {
                val data = HashMap<String, Any>()
                data["state"] = 0 // Disconnected
                data["connectionId"] = mHubConnection?.connectionId ?: ""
                connectionStateEventSink?.success(data)
            }
        }

        mHubConnection?.error { error ->
            handler.post {
                val data = HashMap<String, Any>()
                data["state"] = 4 // Error
                data["connectionId"] = mHubConnection?.connectionId ?: ""
                data["error"] = error?.message ?: "Unknown error"
                connectionStateEventSink?.success(data)
            }
        }
    }

    private fun startKeepAliveTimer() {
        timer?.cancel()
        timer = Timer()
        timer?.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                if (mHubConnection?.state == ConnectionState.Connected) {
                    // Send a keep-alive message
                    try {
                        mHubProxy?.invoke("ping")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error sending keep-alive: ${e.message}")
                    }
                }
            }
        }, 30000, 30000) // Every 30 seconds
    }

    private fun stopSignalR(result: MethodChannel.Result) {
        try {
            timer?.cancel()
            timer = null

            mHubConnection?.stop()
            mHubConnection = null
            mHubProxy = null

            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping SignalR: ${e.message}")
            result.error("STOP_ERROR", "Failed to stop SignalR connection", e.message)
        }
    }

    private fun sendSeat(showId: String, seatIndex: Int, status: Int, result: MethodChannel.Result) {
        try {
            if (mHubConnection?.state != ConnectionState.Connected) {
                result.error("NOT_CONNECTED", "SignalR is not connected", null)
                return
            }

            val args = arrayOf(
                mHubConnection?.connectionId ?: "",
                showId,
                seatIndex.toString(),
                status.toString()
            )

            mHubProxy?.invoke("sendMessage", *args)
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error sending seat: ${e.message}")
            result.error("SEND_ERROR", "Failed to send seat data", e.message)
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        methodChannel.setMethodCallHandler(null)
        connectionStateChannel.setStreamHandler(null)
        dataChannel.setStreamHandler(null)

        stopSignalR(object : MethodChannel.Result {
            override fun success(result: Any?) {}
            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {}
            override fun notImplemented() {}
        })
    }

    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        connectionStateEventSink = events
    }

    override fun onCancel(arguments: Any?) {
        connectionStateEventSink = null
    }
}
