package com.betacineplex

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.google.gson.JsonObject
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import microsoft.aspnet.signalr.client.ConnectionState
import microsoft.aspnet.signalr.client.Platform
import microsoft.aspnet.signalr.client.http.android.AndroidPlatformComponent
import microsoft.aspnet.signalr.client.hubs.HubConnection
import microsoft.aspnet.signalr.client.hubs.HubProxy
import microsoft.aspnet.signalr.client.transport.ServerSentEventsTransport
import java.util.Timer
import java.util.TimerTask

class SignalRPlugin : FlutterPlugin, MethodChannel.MethodCallHandler, EventChannel.StreamHandler {
    private lateinit var methodChannel: MethodChannel
    private lateinit var connectionStateChannel: EventChannel
    private lateinit var dataChannel: EventChannel

    private var connectionStateEventSink: EventChannel.EventSink? = null
    private var dataEventSink: EventChannel.EventSink? = null

    private var mHubConnection: HubConnection? = null
    private var mHubProxy: HubProxy? = null
    private val handler = Handler(Looper.getMainLooper())
    private var timer: Timer? = null

    private val TAG = "SignalRPlugin"
    private val hubName = "chooseSeatHub"
    private val defaultUrl = "https://www.betacinemas.vn"

    override fun onAttachedToEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        methodChannel = MethodChannel(binding.binaryMessenger, "com.betacineplex/signalr")
        methodChannel.setMethodCallHandler(this)

        connectionStateChannel = EventChannel(binding.binaryMessenger, "com.betacineplex/signalr/connection_state")
        connectionStateChannel.setStreamHandler(this)

        dataChannel = EventChannel(binding.binaryMessenger, "com.betacineplex/signalr/data")
        dataChannel.setStreamHandler(object : EventChannel.StreamHandler {
            override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                dataEventSink = events
            }

            override fun onCancel(arguments: Any?) {
                dataEventSink = null
            }
        })
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "initialize" -> {
                result.success(true)
            }
            "start", "connect" -> {
                val url = call.argument<String>("url") ?: defaultUrl
                val hubName = call.argument<String>("hubName") ?: this.hubName
                val token = call.argument<String>("token")
                startSignalR(url, hubName, token, result)
            }
            "stop", "disconnect" -> {
                stopSignalR(result)
            }
            "reconnect" -> {
                reconnect()
                result.success(true)
            }
            "sendSeat", "invoke" -> {
                if (call.method == "sendSeat") {
                    val showId = call.argument<String>("showId") ?: ""
                    val seatIndex = call.argument<Int>("seatIndex") ?: 0
                    val status = call.argument<Int>("status") ?: 0
                    sendSeat(showId, seatIndex, status, result)
                } else {
                    val method = call.argument<String>("method") ?: ""
                    val arguments = call.argument<List<Any>>("arguments") ?: listOf()

                    when (method) {
                        "sendMessage" -> {
                            if (arguments.size >= 4) {
                                val showId = arguments[1].toString()
                                val seatIndex = arguments[2].toString().toIntOrNull() ?: 0
                                val status = arguments[3].toString().toIntOrNull() ?: 0
                                sendSeat(showId, seatIndex, status, result)
                            } else {
                                result.error("INVALID_ARGUMENTS", "sendMessage requires 4 arguments", null)
                            }
                        }
                        "JoinGroup", "joinGroup" -> {
                            if (arguments.isNotEmpty()) {
                                val groupName = arguments[0].toString()
                                joinGroup(groupName, result)
                            } else {
                                result.error("INVALID_ARGUMENTS", "JoinGroup requires group name", null)
                            }
                        }
                        "LeaveGroup", "leaveGroup" -> {
                            if (arguments.isNotEmpty()) {
                                val groupName = arguments[0].toString()
                                leaveGroup(groupName, result)
                            } else {
                                result.error("INVALID_ARGUMENTS", "LeaveGroup requires group name", null)
                            }
                        }
                        else -> {
                            // Generic invoke for other methods
                            invokeMethod(method, arguments, result)
                        }
                    }
                }
            }
            "on" -> {
                val method = call.argument<String>("method") ?: ""
                if (method.isNotEmpty()) {
                    try {
                        mHubProxy?.on(method, { args ->
                            Log.d(TAG, "Received $method with ${args} arguments")
                        }, Any::class.java)
                        result.success(true)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error registering for method $method: ${e.message}")
                        result.error("ON_ERROR", "Failed to register for method", e.message)
                    }
                } else {
                    result.error("INVALID_METHOD", "Invalid method name", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun startSignalR(url: String, hubName: String, token: String?, result: MethodChannel.Result) {
        try {
            Log.d(TAG, "Starting SignalR connection to $url")

            // Load Android platform component
            Platform.loadPlatformComponent(AndroidPlatformComponent())
            Log.d(TAG, "Loaded Android platform component")

            // Process URL similar to Android repo
            val processedUrl = if (url.endsWith("/signalr/hubs")) {
                url
            } else {
                val baseUrl = if (url.endsWith("/")) url else "$url/"
                "${baseUrl}signalr/hubs"
            }

            Log.d(TAG, "Processed URL: $processedUrl")

            // Create hub connection with enhanced logging and optional token
            val queryString = if (token != null && token.isNotEmpty()) {
                "Bearer=$token"
            } else {
                null
            }

            mHubConnection = HubConnection(processedUrl, queryString, true) { message, logLevel ->
                when (logLevel) {
                    microsoft.aspnet.signalr.client.LogLevel.Information -> Log.i(TAG, message)
                    microsoft.aspnet.signalr.client.LogLevel.Verbose -> Log.v(TAG, message)
                    microsoft.aspnet.signalr.client.LogLevel.Debug -> Log.d(TAG, message)
                    microsoft.aspnet.signalr.client.LogLevel.Warning -> Log.w(TAG, message)
                    microsoft.aspnet.signalr.client.LogLevel.Error -> Log.e(TAG, message)
                    microsoft.aspnet.signalr.client.LogLevel.Critical -> Log.e(TAG, "CRITICAL: $message")
                }
            }
            Log.d(TAG, "Created hub connection")

            // Create hub proxy
            mHubProxy = mHubConnection?.createHubProxy(hubName)
            Log.d(TAG, "Created hub proxy for $hubName")

            // Set up event handlers before starting connection
            setupEventHandlers()
            Log.d(TAG, "Set up event handlers")

            // Start connection with auto transport negotiation
            Log.d(TAG, "Starting connection with auto transport negotiation")
            val signalRFuture = mHubConnection?.start()

            // Handle connection result asynchronously
            signalRFuture?.done {
                Log.d(TAG, "SignalR connection done successfully")
                handler.post {
                    val connectionId = mHubConnection?.connectionId
                    Log.d(TAG, "Connection successful with ID: $connectionId")

                    // Start keep-alive timer
                    startKeepAliveTimer()

                    // Return success with connection info
                    val resultMap = HashMap<String, Any?>()
                    resultMap["success"] = true
                    resultMap["connectionId"] = connectionId
                    result.success(resultMap)
                }
            }?.onError { error ->
                Log.e(TAG, "SignalR connection error: ${error.message}")
                handler.post {
                    result.error("CONNECTION_FAILED", "Failed to connect to SignalR hub", error.message)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error starting SignalR: ${e.message}")
            e.printStackTrace()
            result.error("START_ERROR", "Failed to start SignalR connection: ${e.message}", e.stackTraceToString())
        }
    }

    private fun setupEventHandlers() {
        // Handle received data
        mHubConnection?.received { jsonElement ->
            if (jsonElement.isJsonObject) {
                val jsonObject = jsonElement.asJsonObject
                if (jsonObject.has("A")) {
                    try {
                        val ja = jsonObject.getAsJsonArray("A")
                        val data = HashMap<String, Any>()
                        data["connectionId"] = ja[0].asString
                        data["showId"] = ja[1].asString
                        data["seatIndex"] = ja[2].asInt
                        data["seatStatus"] = ja[3].asInt

                        handler.post {
                            dataEventSink?.success(data)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing data: ${e.message}")
                    }
                }
            }
        }

        // Handle connection state changes
        mHubConnection?.connected {
            Log.d(TAG, "SignalR connected with ID: ${mHubConnection?.connectionId}")
            handler.post {
                val data = HashMap<String, Any>()
                data["state"] = 2 // Connected
                data["connectionId"] = mHubConnection?.connectionId ?: ""
                connectionStateEventSink?.success(data)
            }

            // Subscribe to broadcast message
            mHubProxy?.on("broadcastMessage", { args ->
                Log.d(TAG, "Received broadcast message with ${args.size} arguments")

                // Parse and forward the broadcast message
                if (args.size >= 4) {
                    try {
                        val data = HashMap<String, Any>()
                        data["connectionId"] = args[0].toString()
                        data["showId"] = args[1].toString()
                        data["seatIndex"] = when (val seatIndex = args[2]) {
                            is Int -> seatIndex
                            is String -> seatIndex.toIntOrNull() ?: 0
                            else -> 0
                        }
                        data["seatStatus"] = when (val seatStatus = args[3]) {
                            is Int -> seatStatus
                            is String -> seatStatus.toIntOrNull() ?: 0
                            else -> 0
                        }

                        handler.post {
                            dataEventSink?.success(data)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing broadcast message: ${e.message}")
                    }
                }
            }, Any::class.java)
        }

        mHubConnection?.reconnecting {
            handler.post {
                val data = HashMap<String, Any>()
                data["state"] = 3 // Reconnecting
                data["connectionId"] = mHubConnection?.connectionId ?: ""
                connectionStateEventSink?.success(data)
            }
        }

        mHubConnection?.closed {
            Log.d(TAG, "SignalR connection closed")
            handler.post {
                val data = HashMap<String, Any>()
                data["state"] = 0 // Disconnected
                data["connectionId"] = ""
                connectionStateEventSink?.success(data)
            }

            // Auto-reconnect after a delay if not manually disconnected
            if (mHubConnection != null) {
                Log.d(TAG, "Attempting auto-reconnect in 5 seconds")
                Handler(Looper.getMainLooper()).postDelayed({
                    if (mHubConnection != null) {
                        reconnect()
                    }
                }, 5000)
            }
        }

        mHubConnection?.error { error ->
            handler.post {
                val data = HashMap<String, Any>()
                data["state"] = 4 // Error
                data["connectionId"] = mHubConnection?.connectionId ?: ""
                data["error"] = error?.message ?: "Unknown error"
                connectionStateEventSink?.success(data)
            }
        }
    }

    private fun startKeepAliveTimer() {
        timer?.cancel()
        timer = Timer()
        timer?.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                if (mHubConnection?.state == ConnectionState.Connected) {
                    // Send a keep-alive message
                    try {
                        mHubProxy?.invoke("ping")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error sending keep-alive: ${e.message}")
                    }
                }
            }
        }, 30000, 30000) // Every 30 seconds
    }

    private fun reconnect() {
        try {
            Log.d(TAG, "Attempting to reconnect SignalR")
            mHubConnection?.stop()
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping connection for reconnect: ${e.message}")
        }

        // Wait a second before reconnecting
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                Log.d(TAG, "Starting reconnection")
                mHubConnection?.start()
            } catch (e: Exception) {
                Log.e(TAG, "Error reconnecting: ${e.message}")
            }
        }, 1000)
    }

    private fun stopSignalR(result: MethodChannel.Result) {
        try {
            Log.d(TAG, "Stopping SignalR connection")

            timer?.cancel()
            timer = null

            mHubConnection?.stop()
            mHubConnection = null
            mHubProxy = null

            // Notify connection state change
            handler.post {
                val data = HashMap<String, Any>()
                data["state"] = 0 // Disconnected
                data["connectionId"] = ""
                connectionStateEventSink?.success(data)
            }

            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping SignalR: ${e.message}")
            result.error("STOP_ERROR", "Failed to stop SignalR connection", e.message)
        }
    }

    private fun joinGroup(groupName: String, result: MethodChannel.Result) {
        try {
            if (mHubConnection?.state != ConnectionState.Connected) {
                result.error("NOT_CONNECTED", "SignalR is not connected", null)
                return
            }

            Log.d(TAG, "Joining group: $groupName")
            mHubProxy?.invoke("JoinGroup", groupName)
            Log.d(TAG, "Successfully joined group: $groupName")
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error joining group $groupName: ${e.message}")
            result.error("JOIN_GROUP_ERROR", "Failed to join group", e.message)
        }
    }

    private fun leaveGroup(groupName: String, result: MethodChannel.Result) {
        try {
            if (mHubConnection?.state != ConnectionState.Connected) {
                result.error("NOT_CONNECTED", "SignalR is not connected", null)
                return
            }

            Log.d(TAG, "Leaving group: $groupName")
            mHubProxy?.invoke("LeaveGroup", groupName)
            Log.d(TAG, "Successfully left group: $groupName")
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error leaving group $groupName: ${e.message}")
            result.error("LEAVE_GROUP_ERROR", "Failed to leave group", e.message)
        }
    }

    private fun invokeMethod(method: String, arguments: List<Any>, result: MethodChannel.Result) {
        try {
            if (mHubConnection?.state != ConnectionState.Connected) {
                result.error("NOT_CONNECTED", "SignalR is not connected", null)
                return
            }

            Log.d(TAG, "Invoking method: $method with ${arguments.size} arguments")

            when (arguments.size) {
                0 -> mHubProxy?.invoke(method)
                1 -> mHubProxy?.invoke(method, arguments[0])
                2 -> mHubProxy?.invoke(method, arguments[0], arguments[1])
                3 -> mHubProxy?.invoke(method, arguments[0], arguments[1], arguments[2])
                4 -> mHubProxy?.invoke(method, arguments[0], arguments[1], arguments[2], arguments[3])
                else -> {
                    Log.e(TAG, "Unsupported number of arguments: ${arguments.size}")
                    result.error("TOO_MANY_ARGUMENTS", "Too many arguments for method invoke", null)
                    return
                }
            }

            Log.d(TAG, "Successfully invoked method: $method")
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error invoking method $method: ${e.message}")
            result.error("INVOKE_ERROR", "Failed to invoke method", e.message)
        }
    }

    private fun sendSeat(showId: String, seatIndex: Int, status: Int, result: MethodChannel.Result) {
        try {
            if (mHubConnection?.state != ConnectionState.Connected) {
                result.error("NOT_CONNECTED", "SignalR is not connected", null)
                return
            }

            val args = arrayOf(
                mHubConnection?.connectionId ?: "",
                showId,
                seatIndex.toString(),
                status.toString()
            )

            mHubProxy?.invoke("sendMessage", *args)
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error sending seat: ${e.message}")
            result.error("SEND_ERROR", "Failed to send seat data", e.message)
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        methodChannel.setMethodCallHandler(null)
        connectionStateChannel.setStreamHandler(null)
        dataChannel.setStreamHandler(null)

        stopSignalR(object : MethodChannel.Result {
            override fun success(result: Any?) {}
            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {}
            override fun notImplemented() {}
        })
    }

    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        connectionStateEventSink = events
    }

    override fun onCancel(arguments: Any?) {
        connectionStateEventSink = null
    }
}
