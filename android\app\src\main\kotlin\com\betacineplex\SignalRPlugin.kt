package com.betacineplex

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import microsoft.aspnet.signalr.client.ConnectionState
import microsoft.aspnet.signalr.client.Platform
import microsoft.aspnet.signalr.client.http.android.AndroidPlatformComponent
import microsoft.aspnet.signalr.client.hubs.HubConnection
import microsoft.aspnet.signalr.client.hubs.HubProxy
import microsoft.aspnet.signalr.client.transport.ServerSentEventsTransport
import microsoft.aspnet.signalr.client.transport.LongPollingTransport
import java.util.Timer
import java.util.TimerTask

// State management exactly like Android repo
enum class StateSignalR {
    disconnected,
    connecting,
    connected,
    reconnecting,
    error
}

class SignalRPlugin : FlutterPlugin, MethodChannel.MethodCallHandler, EventChannel.StreamHandler {
    private lateinit var methodChannel: MethodChannel
    private lateinit var connectionStateChannel: EventChannel
    private lateinit var dataChannel: EventChannel

    private var connectionStateEventSink: EventChannel.EventSink? = null
    private var dataEventSink: EventChannel.EventSink? = null

    private var mHubConnection: HubConnection? = null
    private var mHubProxy: HubProxy? = null
    private val handler = Handler(Looper.getMainLooper())
    private var timer: Timer? = null

    // State tracking exactly like Android repo
    private var currentState: StateSignalR = StateSignalR.disconnected
    private val listenerConnections = mutableListOf<(StateSignalR) -> Unit>()
    private val listenerData = mutableListOf<(Map<String, Any>) -> Unit>()

    private val TAG = "SignalRPlugin"
    private val hubName = "chooseSeatHub"
    private val defaultUrl = "https://www.betacinemas.vn"

    override fun onAttachedToEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        methodChannel = MethodChannel(binding.binaryMessenger, "com.betacineplex/signalr")
        methodChannel.setMethodCallHandler(this)

        connectionStateChannel = EventChannel(binding.binaryMessenger, "com.betacineplex/signalr/connection_state")
        connectionStateChannel.setStreamHandler(this)

        dataChannel = EventChannel(binding.binaryMessenger, "com.betacineplex/signalr/data")
        dataChannel.setStreamHandler(object : EventChannel.StreamHandler {
            override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                dataEventSink = events
            }

            override fun onCancel(arguments: Any?) {
                dataEventSink = null
            }
        })
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "initialize" -> {
                result.success(true)
            }
            "start", "connect" -> {
                val url = call.argument<String>("url") ?: defaultUrl
                val hubName = call.argument<String>("hubName") ?: this.hubName
                val token = call.argument<String>("token")
                startSignalR(url, hubName, token, result)
            }
            "stop", "disconnect" -> {
                stopSignalR(result)
            }
            "reconnect" -> {
                reconnect()
                result.success(true)
            }
            "sendSeat", "invoke" -> {
                if (call.method == "sendSeat") {
                    val showId = call.argument<String>("showId") ?: ""
                    val seatIndex = call.argument<Int>("seatIndex") ?: 0
                    val status = call.argument<Int>("status") ?: 0
                    sendSeat(showId, seatIndex, status, result)
                } else {
                    val method = call.argument<String>("method") ?: ""
                    val arguments = call.argument<List<Any>>("arguments") ?: listOf()

                    when (method) {
                        "sendMessage" -> {
                            if (arguments.size >= 4) {
                                val showId = arguments[1].toString()
                                val seatIndex = arguments[2].toString().toIntOrNull() ?: 0
                                val status = arguments[3].toString().toIntOrNull() ?: 0
                                sendSeat(showId, seatIndex, status, result)
                            } else {
                                result.error("INVALID_ARGUMENTS", "sendMessage requires 4 arguments", null)
                            }
                        }
                        "JoinGroup", "joinGroup" -> {
                            if (arguments.isNotEmpty()) {
                                val groupName = arguments[0].toString()
                                joinGroup(groupName, result)
                            } else {
                                result.error("INVALID_ARGUMENTS", "JoinGroup requires group name", null)
                            }
                        }
                        "LeaveGroup", "leaveGroup" -> {
                            if (arguments.isNotEmpty()) {
                                val groupName = arguments[0].toString()
                                leaveGroup(groupName, result)
                            } else {
                                result.error("INVALID_ARGUMENTS", "LeaveGroup requires group name", null)
                            }
                        }
                        else -> {
                            // Generic invoke for other methods
                            invokeMethod(method, arguments, result)
                        }
                    }
                }
            }
            "on" -> {
                val method = call.argument<String>("method") ?: ""
                if (method.isNotEmpty()) {
                    try {
                        mHubProxy?.on(method, { args ->
                            Log.d(TAG, "Received $method with ${args} arguments")
                        }, Any::class.java)
                        result.success(true)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error registering for method $method: ${e.message}")
                        result.error("ON_ERROR", "Failed to register for method", e.message)
                    }
                } else {
                    result.error("INVALID_METHOD", "Invalid method name", null)
                }
            }
            "getState" -> {
                val stateValue = when (currentState) {
                    StateSignalR.disconnected -> 0
                    StateSignalR.connecting -> 1
                    StateSignalR.connected -> 2
                    StateSignalR.reconnecting -> 3
                    StateSignalR.error -> 4
                }
                result.success(stateValue)
            }
            "getConnectionId" -> {
                result.success(connectionId())
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    // State notification exactly like Android repo
    private fun notifyStateChange(newState: StateSignalR) {
        if (currentState != newState) {
            currentState = newState
            Log.d(TAG, "State changed to: $newState")

            // Notify Flutter listeners
            handler.post {
                val data = HashMap<String, Any>()
                data["state"] = when (newState) {
                    StateSignalR.disconnected -> 0
                    StateSignalR.connecting -> 1
                    StateSignalR.connected -> 2
                    StateSignalR.reconnecting -> 3
                    StateSignalR.error -> 4
                }
                data["connectionId"] = mHubConnection?.connectionId ?: ""
                connectionStateEventSink?.success(data)
            }

            // Notify internal listeners
            listenerConnections.forEach { listener ->
                try {
                    listener(newState)
                } catch (e: Exception) {
                    Log.e(TAG, "Error notifying state listener: ${e.message}")
                }
            }
        }
    }

    private fun startSignalR(url: String, hubName: String, token: String?, result: MethodChannel.Result) {
        var resultSent = false

        fun sendResult(action: () -> Unit) {
            if (!resultSent) {
                resultSent = true
                try {
                    action()
                } catch (e: Exception) {
                    Log.e(TAG, "Error sending result: ${e.message}")
                }
            }
        }

        try {
            Log.d(TAG, "Starting SignalR connection to $url")

            // Notify connecting state
            notifyStateChange(StateSignalR.connecting)

            // Load Android platform component - exactly like Android repo
            Platform.loadPlatformComponent(AndroidPlatformComponent())

            // Process URL exactly like Android repo
            val serverUrl = if (url.endsWith("/signalr/hubs")) {
                url
            } else {
                val baseUrl = if (url.endsWith("/")) url else "$url/"
                "${baseUrl}signalr/hubs"
            }

            Log.d(TAG, "Server URL: $serverUrl")

            // Create hub connection exactly like Android repo
            mHubConnection = HubConnection(serverUrl, null, true) { message, _ ->
                Log.d(TAG, message)
            }

            // Create hub proxy
            mHubProxy = mHubConnection?.createHubProxy(hubName)

            // Set up transport exactly like Android repo
            val clientTransport = ServerSentEventsTransport(mHubConnection?.logger)

            // Start connection with specific transport
            val signalRFuture = mHubConnection?.start(clientTransport)

            // Set up event handlers after starting connection
            setupEventHandlers()

            // Keep-alive will be started in connected event handler

            // Add timeout to prevent slow connection issues
            val timeoutHandler = Handler(Looper.getMainLooper())
            val timeoutRunnable = Runnable {
                Log.w(TAG, "SignalR connection timeout after 30 seconds")
                sendResult { result.error("CONNECTION_TIMEOUT", "SignalR connection timeout", null) }
            }
            timeoutHandler.postDelayed(timeoutRunnable, 30000) // 30 seconds timeout

            // Handle connection result
            try {
                signalRFuture?.done {
                    Log.d(TAG, "SignalR connection done successfully")
                    timeoutHandler.removeCallbacks(timeoutRunnable) // Cancel timeout
                    handler.post {
                        val connectionId = mHubConnection?.connectionId
                        Log.d(TAG, "Connection successful with ID: $connectionId")

                        // Return success with connection info
                        val resultMap = HashMap<String, Any?>()
                        resultMap["success"] = true
                        resultMap["connectionId"] = connectionId
                        sendResult { result.success(resultMap) }
                    }
                }?.onError { error ->
                    Log.e(TAG, "SignalR connection error: ${error.message}")
                    timeoutHandler.removeCallbacks(timeoutRunnable) // Cancel timeout
                    handler.post {
                        sendResult { result.error("CONNECTION_FAILED", "Failed to connect to SignalR hub", error.message) }
                    }
                }?.onCancelled {
                    Log.w(TAG, "SignalR connection cancelled")
                    timeoutHandler.removeCallbacks(timeoutRunnable) // Cancel timeout
                    handler.post {
                        sendResult { result.error("CONNECTION_CANCELLED", "SignalR connection was cancelled", null) }
                    }
                }
            } catch (e: InterruptedException) {
                Log.e(TAG, "SignalR connection interrupted: ${e.message}")
                timeoutHandler.removeCallbacks(timeoutRunnable) // Cancel timeout
                sendResult { result.error("CONNECTION_INTERRUPTED", "SignalR connection was interrupted", e.message) }
            } catch (e: Exception) {
                Log.e(TAG, "SignalR connection exception: ${e.message}")
                timeoutHandler.removeCallbacks(timeoutRunnable) // Cancel timeout
                sendResult { result.error("CONNECTION_EXCEPTION", "SignalR connection failed with exception", e.message) }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error starting SignalR: ${e.message}")
            e.printStackTrace()
            sendResult { result.error("START_ERROR", "Failed to start SignalR connection: ${e.message}", e.stackTraceToString()) }
        }
    }

    private fun setupEventHandlers() {
        // Handle received data exactly like Android repo
        mHubConnection?.received { jsonElement ->
            if (jsonElement.isJsonObject) {
                val jsonObject = jsonElement.asJsonObject
                if (jsonObject.has("A")) {
                    try {
                        val ja = jsonObject.getAsJsonArray("A")
                        // Create SeatSignalrResponse equivalent data
                        val data = HashMap<String, Any>()
                        data["connectionId"] = ja[0].asString
                        data["showId"] = ja[1].asString
                        data["seatIndex"] = ja[2].asInt
                        data["seatStatus"] = ja[3].asInt

                        handler.post {
                            dataEventSink?.success(data)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing received data: ${e.message}")
                    }
                }
            }
        }

        // Handle connection state changes exactly like Android repo
        mHubConnection?.connected {
            Log.d(TAG, "SignalR connected with ID: ${mHubConnection?.connectionId}")

            // Start keep-alive runnable exactly like Android repo
            handler.post(keepAliveRunnable)

            // Subscribe to broadcastMessage exactly like Android repo
            subscribe("broadcastMessage")

            // Notify connection state using state management
            notifyStateChange(StateSignalR.connected)
        }

        mHubConnection?.reconnecting {
            Log.d(TAG, "SignalR reconnecting")
            notifyStateChange(StateSignalR.reconnecting)
        }

        mHubConnection?.closed {
            Log.d(TAG, "SignalR connection closed")
            notifyStateChange(StateSignalR.disconnected)
        }

        mHubConnection?.error { error ->
            Log.e(TAG, "SignalR connection error: ${error?.message}")
            notifyStateChange(StateSignalR.error)
        }

        mHubConnection?.stateChanged { oldState, newState ->
            Log.d(TAG, "SignalR state changed from $oldState to $newState")
            if (newState == ConnectionState.Disconnected) {
                // Could implement auto-reconnect here like Android repo
                // mHubConnection?.start()
            }
        }
    }

    // Keep-alive runnable exactly like Android repo
    private val keepAliveRunnable = object : Runnable {
        override fun run() {
            if (mHubConnection?.state == ConnectionState.Disconnected) {
                reconnect()
            }
            handler.postDelayed(this, 5 * 1000) // 5 seconds like Android repo
        }
    }

    // Subscribe method exactly like Android repo
    fun subscribe(event: String) {
        try {
            Log.d(TAG, "Subscribing to event: $event")
            mHubProxy?.on(event,
                { arg1: Any?, arg2: Any?, arg3: Any?, arg4: Any? ->
                    Log.d(TAG, "Received $event with 4 arguments")

                    try {
                        val data = HashMap<String, Any>()
                        data["connectionId"] = arg1?.toString() ?: ""
                        data["showId"] = arg2?.toString() ?: ""
                        data["seatIndex"] = when (arg3) {
                            is Int -> arg3
                            is String -> arg3.toIntOrNull() ?: 0
                            else -> 0
                        }
                        data["seatStatus"] = when (arg4) {
                            is Int -> arg4
                            is String -> arg4.toIntOrNull() ?: 0
                            else -> 0
                        }

                        handler.post {
                            dataEventSink?.success(data)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing $event: ${e.message}")
                    }
                },
                Any::class.java, Any::class.java, Any::class.java, Any::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Error subscribing to $event: ${e.message}")
        }
    }

    // Unsubscribe method exactly like Android repo
    fun unSubscribe(event: String) {
        try {
            Log.d(TAG, "Unsubscribing from event: $event")
            mHubProxy?.removeSubscription(event)
        } catch (e: Exception) {
            Log.e(TAG, "Error unsubscribing from $event: ${e.message}")
        }
    }



    private fun reconnect() {
        try {
            Log.d(TAG, "Attempting to reconnect SignalR")
            notifyStateChange(StateSignalR.reconnecting)

            // Stop current connection
            try {
                mHubConnection?.stop()
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping connection for reconnect: ${e.message}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in reconnect preparation: ${e.message}")
            notifyStateChange(StateSignalR.error)
        }

        // Wait a second before reconnecting - exactly like Android repo
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                Log.d(TAG, "Starting reconnection")
                val signalRFuture = mHubConnection?.start()

                // Handle reconnection result
                signalRFuture?.done {
                    Log.d(TAG, "Reconnection successful")
                    notifyStateChange(StateSignalR.connected)
                }?.onError { error ->
                    Log.e(TAG, "Reconnection failed: ${error.message}")
                    notifyStateChange(StateSignalR.error)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error reconnecting: ${e.message}")
                notifyStateChange(StateSignalR.error)
            }
        }, 1000)
    }

    // Stop method exactly like Android repo
    fun stop() {
        try {
            Log.d(TAG, "Stopping SignalR connection")

            // Remove keep-alive runnable exactly like Android repo
            handler.removeCallbacks(keepAliveRunnable)

            // Stop connection
            mHubConnection?.stop()

            // Notify disconnected state
            notifyStateChange(StateSignalR.disconnected)
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping SignalR: ${e.message}")
            notifyStateChange(StateSignalR.error)
        }
    }

    private fun stopSignalR(result: MethodChannel.Result) {
        try {
            Log.d(TAG, "Stopping SignalR connection")

            // Stop using the public method
            stop()

            // Clear references
            mHubConnection = null
            mHubProxy = null

            // Notify connection state change
            handler.post {
                val data = HashMap<String, Any>()
                data["state"] = 0 // Disconnected
                data["connectionId"] = ""
                connectionStateEventSink?.success(data)
            }

            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping SignalR: ${e.message}")
            result.error("STOP_ERROR", "Failed to stop SignalR connection", e.message)
        }
    }

    // Connection ID method exactly like Android repo
    fun connectionId(): String {
        return mHubConnection?.connectionId ?: ""
    }

    private fun joinGroup(groupName: String, result: MethodChannel.Result) {
        try {
            if (mHubConnection?.state != ConnectionState.Connected) {
                result.error("NOT_CONNECTED", "SignalR is not connected", null)
                return
            }

            Log.d(TAG, "Joining group: $groupName")
            mHubProxy?.invoke("JoinGroup", groupName)
            Log.d(TAG, "Successfully joined group: $groupName")
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error joining group $groupName: ${e.message}")
            result.error("JOIN_GROUP_ERROR", "Failed to join group", e.message)
        }
    }

    private fun leaveGroup(groupName: String, result: MethodChannel.Result) {
        try {
            if (mHubConnection?.state != ConnectionState.Connected) {
                result.error("NOT_CONNECTED", "SignalR is not connected", null)
                return
            }

            Log.d(TAG, "Leaving group: $groupName")
            mHubProxy?.invoke("LeaveGroup", groupName)
            Log.d(TAG, "Successfully left group: $groupName")
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error leaving group $groupName: ${e.message}")
            result.error("LEAVE_GROUP_ERROR", "Failed to leave group", e.message)
        }
    }

    // Invoke method exactly like Android repo
    fun invoke(event: String, message: List<Any>) {
        try {
            if (mHubConnection?.state != ConnectionState.Connected) {
                Log.w(TAG, "Cannot invoke $event: SignalR is not connected")
                return
            }

            Log.d(TAG, "Invoking $event with ${message.size} arguments")

            // Invoke exactly like Android repo with 4 arguments
            if (message.size >= 4) {
                mHubProxy?.invoke(event, message[0], message[1], message[2], message[3])
            } else {
                Log.w(TAG, "Invoke requires 4 arguments, got ${message.size}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error invoking $event: ${e.message}")
        }
    }

    private fun invokeMethod(method: String, arguments: List<Any>, result: MethodChannel.Result) {
        try {
            if (mHubConnection?.state != ConnectionState.Connected) {
                result.error("NOT_CONNECTED", "SignalR is not connected", null)
                return
            }

            Log.d(TAG, "Invoking method: $method with ${arguments.size} arguments")

            when (arguments.size) {
                0 -> mHubProxy?.invoke(method)
                1 -> mHubProxy?.invoke(method, arguments[0])
                2 -> mHubProxy?.invoke(method, arguments[0], arguments[1])
                3 -> mHubProxy?.invoke(method, arguments[0], arguments[1], arguments[2])
                4 -> mHubProxy?.invoke(method, arguments[0], arguments[1], arguments[2], arguments[3])
                else -> {
                    Log.e(TAG, "Unsupported number of arguments: ${arguments.size}")
                    result.error("TOO_MANY_ARGUMENTS", "Too many arguments for method invoke", null)
                    return
                }
            }

            Log.d(TAG, "Successfully invoked method: $method")
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error invoking method $method: ${e.message}")
            result.error("INVOKE_ERROR", "Failed to invoke method", e.message)
        }
    }

    private fun sendSeat(showId: String, seatIndex: Int, status: Int, result: MethodChannel.Result) {
        try {
            if (mHubConnection?.state != ConnectionState.Connected) {
                result.error("NOT_CONNECTED", "SignalR is not connected", null)
                return
            }

            val args = arrayOf(
                mHubConnection?.connectionId ?: "",
                showId,
                seatIndex.toString(),
                status.toString()
            )

            mHubProxy?.invoke("sendMessage", *args)
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error sending seat: ${e.message}")
            result.error("SEND_ERROR", "Failed to send seat data", e.message)
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        methodChannel.setMethodCallHandler(null)
        connectionStateChannel.setStreamHandler(null)
        dataChannel.setStreamHandler(null)

        stopSignalR(object : MethodChannel.Result {
            override fun success(result: Any?) {}
            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {}
            override fun notImplemented() {}
        })
    }

    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        connectionStateEventSink = events
    }

    override fun onCancel(arguments: Any?) {
        connectionStateEventSink = null
    }
}
