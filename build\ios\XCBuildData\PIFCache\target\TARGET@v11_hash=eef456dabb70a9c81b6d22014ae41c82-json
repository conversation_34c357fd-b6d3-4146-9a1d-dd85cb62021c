{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bb09079de2b5b70c759d6d700d6ca1bc", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Alamofire/Alamofire-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Alamofire/Alamofire-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Alamofire/Alamofire.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Alamofire", "PRODUCT_NAME": "Alamofire", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.1", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a22c801e3a3841bb573aa94cbc52ea8d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9848f1817dadd6fc71ce6e6029b4ff8c8c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Alamofire/Alamofire-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Alamofire/Alamofire-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Alamofire/Alamofire.modulemap", "PRODUCT_MODULE_NAME": "Alamofire", "PRODUCT_NAME": "Alamofire", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.1", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98402c5b1c723eac0a3c85bb45961bb5cc", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9848f1817dadd6fc71ce6e6029b4ff8c8c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Alamofire/Alamofire-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Alamofire/Alamofire-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Alamofire/Alamofire.modulemap", "PRODUCT_MODULE_NAME": "Alamofire", "PRODUCT_NAME": "Alamofire", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.1", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984209c9547a621ebd1822bb0d1afc0d27", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9236f160e15274c03b90c64d57dc1a7", "guid": "bfdfe7dc352907fc980b868725387e9817e52e8235a1ea2b3c9c9dec509ec0f5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e1a1bbd5ccd80b6d6955dca7cbbe43e1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a2a937a4bc65084443fb2d821ae4219d", "guid": "bfdfe7dc352907fc980b868725387e98fc058b88dd8859429569cd15d2466658"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804a0484ccca55a4604b5e99354b480b9", "guid": "bfdfe7dc352907fc980b868725387e98666338227c0f240e1592d27de2a8fea8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a5825e707ad520263f7d9ee6b11e452", "guid": "bfdfe7dc352907fc980b868725387e98fbfde5091ca281a5daf024d5a41c377e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989986d2524c07e0a38c855a6f3ec3ddc9", "guid": "bfdfe7dc352907fc980b868725387e9859be49f76bbab87ba5ef3c03f6b24878"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c717fe3902fca891ad2328f020ea7419", "guid": "bfdfe7dc352907fc980b868725387e982a4ab9328205ebd9fbaea86d00db8d85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f3256f43d783b3201ad39914252f01d", "guid": "bfdfe7dc352907fc980b868725387e989c208d3dcfe79e9ec64874cb4175ff06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e962f67c006db5e9215fb0f37e29538", "guid": "bfdfe7dc352907fc980b868725387e98f825b1a6889646de7ae8f8d0cd93f4e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f1d03a0b3dcf9750027bfedb663867c", "guid": "bfdfe7dc352907fc980b868725387e980c5fde4561ab782c5543f69aafd95957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d1abd5fff73ac27128d6bbc1125d2c9", "guid": "bfdfe7dc352907fc980b868725387e989f2175fb8cc13eb259dab2ec5d781317"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806215193a283c8319fdb6e7cc13617fa", "guid": "bfdfe7dc352907fc980b868725387e987e796182c1a6e8717d5478b255547832"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827a434b4c4e4812eed60d67970c312ec", "guid": "bfdfe7dc352907fc980b868725387e983cf8bfc579523d2d90791b9b9c6b34cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f48572c026ffbbe882fa0c9d614e4aaf", "guid": "bfdfe7dc352907fc980b868725387e98b98aa880a1fedf75162f29a6aacf643c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868d7c91ca46516e3b9a56368c7b283ce", "guid": "bfdfe7dc352907fc980b868725387e988df96ba8d73f95362a81c92f4343f3fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee59ab7b6b666c94d63a973813631e86", "guid": "bfdfe7dc352907fc980b868725387e9831b964fae3f30dcf52ca30f4e348412f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e1175ad770a135d8e2d3d81e0311659", "guid": "bfdfe7dc352907fc980b868725387e98445f5741e7d872fc9b76cedc37985f2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e7325bd4f46a4d2026e68c1d01bd6ec", "guid": "bfdfe7dc352907fc980b868725387e98d16d9892f71c2d166a57a487b73ed3c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ab8dab83ac37029f4bd6a83bb235d00", "guid": "bfdfe7dc352907fc980b868725387e981eef05adc4b82a63e043f0e1c4b9ebcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98428f6f3387f4981cd8a268b75f7bb758", "guid": "bfdfe7dc352907fc980b868725387e98198f6fdc779d7d947a3749c705bfbd76"}], "guid": "bfdfe7dc352907fc980b868725387e988dfa578f8980968463f447056c33e769", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e983e0dc1a0c8780c4e42ec24c0f90e0e20"}], "guid": "bfdfe7dc352907fc980b868725387e9875e54020d6199955bfff6b1b8cdc47ec", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e982398eb3870775fc1b13c54448711417f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98ba3679b4c4428e6a6d83c5308d3af99b", "name": "Alamofire", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9878a7912f930d33f69a5abbeb079e5b03", "name": "Alamofire.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}