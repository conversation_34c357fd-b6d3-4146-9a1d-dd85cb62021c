package vn.zenity.betacineplex.helper.extension

import androidx.annotation.IdRes
import androidx.appcompat.app.AppCompatActivity
import android.view.View

/**
 * Created by tranduc on 1/8/18.
 */


fun <T : View?> AppCompatActivity.bindBy(@IdRes idRes: Int): Lazy<T>? {
    return unsafeLazy { findViewById<T>(idRes)}
}

fun <T : View?> View.bindBy(@IdRes idRes: Int): Lazy<T>? {
    @Suppress("UNCHECKED_CAST")
    return unsafeLazy { findViewById<T>(idRes)}
}

fun <T : View> AppCompatActivity.bind(@IdRes idRes: Int): T? {
    return findViewById(idRes)
}

fun <T : View> View.bind(@IdRes idRes: Int): T? {
    return findViewById(idRes)
}

private fun <T> unsafeLazy(initializer: () -> T) = lazy(LazyThreadSafetyMode.NONE, initializer)