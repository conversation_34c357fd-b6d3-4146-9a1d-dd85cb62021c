{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986f890d08ba2ca7072b8a23b14f09a0cb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f1f8693f7c908f5b0affe1f241e6c220", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a7f9784253a0c04373b2c743a9c2fcfa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b731d65cc4406511fff066edd613cb3e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a7f9784253a0c04373b2c743a9c2fcfa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986d26b1adc32d733906901b1009d9fe42", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f40236f7e7f8f877f73b64d3d0140ecc", "guid": "bfdfe7dc352907fc980b868725387e9896f12159b024320a005ded0cea3d9d83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b5d2e0d56a0a2dcf44eadd433f63912", "guid": "bfdfe7dc352907fc980b868725387e98e43631fad6506776abad51f12862ffbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7735dc445246d5b8ee3c52a973eb306", "guid": "bfdfe7dc352907fc980b868725387e98db3278b756a4d9b02999eaefa831e29c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980654a7d3a5f99c53888de9ee27e456fc", "guid": "bfdfe7dc352907fc980b868725387e98f0850e9be1a16e613b75f72d64f521a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca5f9da59783e71f75fa66d2fb406593", "guid": "bfdfe7dc352907fc980b868725387e9877cabb98d23ecc35a485dc8c2e280484", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b960a65829fb23ab40a71b68a72a6ccc", "guid": "bfdfe7dc352907fc980b868725387e98b8e17c2a5f891e9cc1572908a7618af1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98676b8a63a26c89b0557e01164a898e37", "guid": "bfdfe7dc352907fc980b868725387e984703b7dcab508f7cc1f2804ca0b9c908", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f58ac89db2db1239e5ef56c268612919", "guid": "bfdfe7dc352907fc980b868725387e98a199371f1a346b671b89f22c82e24a60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2775f3bdab12785431074be274f086a", "guid": "bfdfe7dc352907fc980b868725387e9879d6fee1fc0de55f71dd3027fbe6aaa6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1a5116cd8254f75327970ba18a4a880", "guid": "bfdfe7dc352907fc980b868725387e988f9b07eb7c2a1bc2cf83402ab942ef34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98329a877ec1a6763c33965acd4046e8c8", "guid": "bfdfe7dc352907fc980b868725387e9830f0f928678b3a15df75654129fd8571", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdd548a6936116870fd6edc26b77e4e6", "guid": "bfdfe7dc352907fc980b868725387e98a1b7234155e530a31b94c2ae62000859", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd9b31ba947906e7e787379ead6be0fa", "guid": "bfdfe7dc352907fc980b868725387e98a4c52247a6a8341335e241fb41c4ca05", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a960ae4e319c2723d591e23745fadacf", "guid": "bfdfe7dc352907fc980b868725387e987b9000704bfee22762f2a73f4e7cae26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844aeef7391b89da5b8d3155c37d2eee9", "guid": "bfdfe7dc352907fc980b868725387e98d003cfaca71f92ff4b9f0c3d8ae7ef7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b64943c39ed58d04b1de0bff09eb4c97", "guid": "bfdfe7dc352907fc980b868725387e98103be823e3e1a715ec8145593f0c3210", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9891b6c87882cff834a6d4722792d27722", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e58f04235eedbb0a1644a0d08c3da7b9", "guid": "bfdfe7dc352907fc980b868725387e986d33fbe1b8b953dfd72e90e4328c206f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca580cfe5adca7b6db44dcbb7db8c865", "guid": "bfdfe7dc352907fc980b868725387e987cfede425176636679afe620b6e65872"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834ec30921f980761b5d6d93a2b13bd14", "guid": "bfdfe7dc352907fc980b868725387e986cdb4c8c5d03d6dec6bbcb7369576213"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a507eb62b583e56a78f34dac09c04df", "guid": "bfdfe7dc352907fc980b868725387e986c4fba26ab9aec315429c21a90ae439f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981440f557d3a1e83dd75a31b6e0ee1025", "guid": "bfdfe7dc352907fc980b868725387e983e0a9363aed82112dc21c827b16dcdeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a04482332da55759bc07c55de7f85d9", "guid": "bfdfe7dc352907fc980b868725387e98a582498800b10e42dff8c6853f58914e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ca100e241e3a273782e44b58c5e5ca2", "guid": "bfdfe7dc352907fc980b868725387e9876e2fcde8e9bf49d940acd65e1dee028"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efcc80e6d690ed892bd999caae094412", "guid": "bfdfe7dc352907fc980b868725387e98cc14715b5f033db4d0789e8693e70d05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982682e8b3c3e63a282cd6e55076ea07cc", "guid": "bfdfe7dc352907fc980b868725387e9868a2a35993f2fcf5087d40396f426b3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de3eca62a00a80349c5425bbd2f8b192", "guid": "bfdfe7dc352907fc980b868725387e987567d6a6245f15e108298d6be9a43613"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821e7963762e0de89b63668e87bc1eaef", "guid": "bfdfe7dc352907fc980b868725387e98f1a0992b778d57663aaa15add653d22f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3c25242239cd9e2a7b11bc0f3fb3a2b", "guid": "bfdfe7dc352907fc980b868725387e98375343f53ecceeeb9bbcd2035b3354d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed359fd0dc5bde6fa3e9620c23ae0865", "guid": "bfdfe7dc352907fc980b868725387e98c22ce526dc185ca6a2dfc1c3953f397c"}], "guid": "bfdfe7dc352907fc980b868725387e98448c8389dfc199dac83239b7c4d4a87d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98f30217f2638041bc05fbf64d6676681c"}], "guid": "bfdfe7dc352907fc980b868725387e9837666ca8f131a9306dd950b0c4ffecf0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ceec10663e715758ba9294abee964c51", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}