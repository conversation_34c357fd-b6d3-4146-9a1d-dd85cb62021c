

class MCauHinhSanPham {
  final String? productId;
  final String? productName;
  final String? gasTankId;
  final String? gasTankCode;
  final String? gasTankName;
  final int? gasTankPrice;
  final int? gasTankWeight;
  final String? residualGasId;
  final String? residualGasCode;
  final String? residualGasName;
  final int? residualGasPrice;
  final int? residualGasWeight;



  const MCauHinhSanPham( {
    this.productId,
    this.productName,
    this.gasTankId,
    this.gasTankCode,
    this.gasTankName,
    this.gasTankPrice,
    this.gasTankWeight,
    this.residualGasId,
    this.residualGasCode,
    this.residualGasName,
    this.residualGasPrice,
    this.residualGasWeight,

  });

  factory MCauHinhSanPham.fromJson(dynamic json) =>
      MCauHinhSanPham(
        productId: json['productId'] as String?,
        productName: json['productName'] as String?,
        gasTankName: json['gasTankName'] as String?,
        gasTankPrice: json['gasTankPrice'] as int?,
        gasTankWeight: json['gasTankWeight'] as int?,
        gasTankId: json['gasTankId'] as String?,
        gasTankCode: json['gasTankCode'] as String?,
        residualGasId: json['residualGasId'] as String?,
        residualGasCode: json['residualGasCode'] as String?,
        residualGasName: json['residualGasName'] as String?,
        residualGasPrice: json['residualGasPrice'] as int?,
        residualGasWeight: json['residualGasWeight'] as int?,

      );

  Map<String, dynamic> toJson() =>
      {
        'productId': productId,
        'productName': productName,
        'gasTankName': gasTankName,
        'gasTankPrice': gasTankPrice,
        'gasTankWeight': gasTankWeight,
        'gasTankId': gasTankId,
        'gasTankCode': gasTankCode,
        'residualGasId': residualGasId,
        'residualGasCode': residualGasCode,
        'residualGasName': residualGasName,
        'residualGasPrice': residualGasPrice,
        'residualGasWeight': residualGasWeight,
      };

  MCauHinhSanPham copyWith({
     String? productId,
     String? productName,
     String? gasTankId,
     String? gasTankCode,
     String? gasTankName,
     int? gasTankPrice,
    int? gasTankWeight,
     String? residualGasId,
     String? residualGasCode,
     String? residualGasName,
     int? residualGasPrice,
    int? residualGasWeight,
  }) {
    return MCauHinhSanPham(
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      gasTankId: gasTankId ?? this.gasTankId,
      gasTankCode: gasTankCode ?? this.gasTankCode,
      gasTankName: gasTankName ?? this.gasTankName,
      gasTankPrice: gasTankPrice ?? this.gasTankPrice,
      gasTankWeight: gasTankWeight ?? this.gasTankWeight,
      residualGasId: residualGasId ?? this.residualGasId,
      residualGasCode: residualGasCode ?? this.residualGasCode,
      residualGasName: residualGasName ?? this.residualGasName,
      residualGasPrice: residualGasPrice ?? this.residualGasPrice,
      residualGasWeight: residualGasWeight ?? this.residualGasWeight,


    );
  }
}
