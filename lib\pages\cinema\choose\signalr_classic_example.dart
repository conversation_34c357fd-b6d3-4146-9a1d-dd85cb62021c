import 'package:flutter/material.dart';
import 'package:flutter_app/services/signalr_classic/signalr_classic_service.dart';

class SignalRClassicExample extends StatefulWidget {
  final String showId;

  const SignalRClassicExample({Key? key, required this.showId}) : super(key: key);

  @override
  State<SignalRClassicExample> createState() => _SignalRClassicExampleState();
}

class _SignalRClassicExampleState extends State<SignalRClassicExample> {
  final SignalRClassicService _signalRService = SignalRClassicService();
  bool _isConnected = false;
  String _connectionId = '';
  List<SeatSignalrResponse> _seatUpdates = [];

  @override
  void initState() {
    super.initState();
    _initSignalR();
  }

  Future<void> _initSignalR() async {
    // Initialize the SignalR service
    await _signalRService.initialize();

    // Add connection state listener
    _signalRService.addConnectionListener((isConnected) {
      setState(() {
        _isConnected = isConnected;
        _connectionId = _signalRService.connectionId;
      });
    });

    // Add data listener
    _signalRService.addDataListener((data) {
      setState(() {
        _seatUpdates.add(data);
        // Keep only the last 10 updates
        if (_seatUpdates.length > 10) {
          _seatUpdates.removeAt(0);
        }
      });
    });

    // Connect to the SignalR hub
    await _signalRService.connect(
      'https://www.betacinemas.vn',
      hubName: 'chooseSeatHub',
    );
  }

  Future<void> _sendSeatUpdate(int seatIndex, int status) async {
    if (_isConnected) {
      await _signalRService.sendSeat(widget.showId, seatIndex, status);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Not connected to SignalR')),
      );
    }
  }

  @override
  void dispose() {
    // Disconnect from the SignalR hub
    _signalRService.disconnect();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SignalR Classic Example'),
      ),
      body: Column(
        children: [
          // Connection status
          Container(
            height: 200,
            padding: const EdgeInsets.all(16),
            color: _isConnected ? Colors.green.shade100 : Colors.red.shade100,
            child: Row(
              children: [
                Icon(
                  _isConnected ? Icons.check_circle : Icons.error,
                  color: _isConnected ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _isConnected
                        ? 'Connected (ID: $_connectionId)'
                        : 'Disconnected',
                    style: TextStyle(
                      color: _isConnected ? Colors.green.shade900 : Colors.red.shade900,
                    ),
                  ),
                ),
                Expanded(
                  child:  ElevatedButton(
                    onPressed: () async {
                      if (_isConnected) {
                        await _signalRService.disconnect();
                      } else {
                        await _signalRService.connect(
                          'https://www.betacinemas.vn',
                          hubName: 'chooseSeatHub',
                        );
                      }
                    },
                    child: Text(_isConnected ? 'Disconnect' : 'Connect'),
                  ),
                ),
              ],
            ),
          ),

          // Seat selection controls
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Select/Deselect Seat',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: 'Seat Index',
                          border: OutlineInputBorder(),
                        ),
                        onFieldSubmitted: (value) {
                          final seatIndex = int.tryParse(value);
                          if (seatIndex != null) {
                            _sendSeatUpdate(seatIndex, 1); // 1 = Select
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        // Example: Select seat with index 1
                        _sendSeatUpdate(1, 1); // 1 = Select
                      },
                      child: const Text('Select Seat 1'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        // Example: Deselect seat with index 1
                        _sendSeatUpdate(1, 0); // 0 = Deselect
                      },
                      child: const Text('Deselect Seat 1'),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Seat updates
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    'Seat Updates',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Expanded(
                  child: _seatUpdates.isEmpty
                      ? const Center(
                          child: Text('No seat updates yet'),
                        )
                      : ListView.builder(
                          itemCount: _seatUpdates.length,
                          itemBuilder: (context, index) {
                            final update = _seatUpdates[_seatUpdates.length - 1 - index];
                            return ListTile(
                              leading: Icon(
                                update.seatStatus == 1
                                    ? Icons.event_seat
                                    : Icons.event_seat_outlined,
                                color: update.seatStatus == 1
                                    ? Colors.green
                                    : Colors.grey,
                              ),
                              title: Text('Seat ${update.seatIndex}'),
                              subtitle: Text(
                                'Status: ${update.seatStatus == 1 ? 'Selected' : 'Deselected'} by ${update.connectionId}',
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
