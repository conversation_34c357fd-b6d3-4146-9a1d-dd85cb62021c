package vn.zenity.betacineplex.view.cenima

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.logD
import vn.zenity.betacineplex.model.NewModel
import vn.zenity.betacineplex.model.NewsModel
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class CenimaDetailPresenter : CenimaDetailContractor.Presenter {
    var po: Disposable? = null
    override fun getCinemaDetail(id: String) {
        this.view?.get()?.showLoading()
        po = APIClient.shared.cinemaAPI.getCinemaDetail(id)
                .applyOn()
                .subscribe({ data ->
                    data.Data?.let {
                        this.view?.get()?.showCinemaDetail(it)
                    }
                    this.view?.get()?.hideLoading()
                }, {
                    this.view?.get()?.hideLoading()
                })
    }

    override fun getListEventOfCinema(id: String) {
        val lang = App.shared().getCurrentLang()
        APIClient.shared.ecmAPI.getNewEvent(if (lang == "en") lang else "")
                .applyOn()
                .subscribe({
                    val data = it.Data?.filter { it != null } as? ArrayList<NewModel>
                    if (data?.size ?: 0 > 0) {
                        data?.get(0)?.CategoryId?.let {
                            APIClient.shared.ecmAPI.getNewForCategory(it, 3, 0).applyOn()
                                    .subscribe({
                                        this.view?.get()?.showListEvent(it.Data ?: return@subscribe)
                                    }, {
                                        logD(it.localizedMessage)
                                    })
                        }
                    }
                }, {

                })
    }

    private var view: WeakReference<CenimaDetailContractor.View?>? = null
    override fun attachView(view: CenimaDetailContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        po?.dispose()
        this.view?.clear()
        this.view = null
    }
}
