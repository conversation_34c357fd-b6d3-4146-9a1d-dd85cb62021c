{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a7d5a84800a049f10f27f4b3f64fdc72", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984056b56ab63187e646b06f3cb56c7908", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b5e13208ab5e87128a39d56773ec610", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a9dd23bcdd71d5a828d50d5f4eca95fe", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b5e13208ab5e87128a39d56773ec610", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987ff122a591fe378bcab6c1be15b8394a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980e6e1ccb2bd3dc1238e3b6c2f9f55936", "guid": "bfdfe7dc352907fc980b868725387e98d6f116c300a053aba13a080f6cffd9e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d3e594074e8923edf4c3fedc9dd6e04", "guid": "bfdfe7dc352907fc980b868725387e987af65cfb4bf7d8a423a5a04751907960", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886323e6a822585b219b53b25c20ece3d", "guid": "bfdfe7dc352907fc980b868725387e9836ebaf54a629e286b07144fec0f257ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897db8159e6b01646a7e88dc96c88bcc0", "guid": "bfdfe7dc352907fc980b868725387e988243222729642c14ae9c34d3f297981f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989698d9943e0f300c364946232786f528", "guid": "bfdfe7dc352907fc980b868725387e9857973d27db5a8d32d2b789539789e5b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872b166ec7a662dc3db768509087fdc40", "guid": "bfdfe7dc352907fc980b868725387e98bbe3b09236c6d80d245f11a6853998d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ad625734fec8708a8658588bd3b5a97", "guid": "bfdfe7dc352907fc980b868725387e9884ce1c0239ba81a5b249a901e7d90703", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e08b3c6eb14171adc06be71dc9b2d5b", "guid": "bfdfe7dc352907fc980b868725387e9891328a52e9b817e87e82efb6df930a5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804298ab7ff32bd59d738b884dbcd4bdd", "guid": "bfdfe7dc352907fc980b868725387e9878271927278b4ecaed4e00c48537d5c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbfbb88d726c3e5adade71059a9a0629", "guid": "bfdfe7dc352907fc980b868725387e98cad4d40ef398102e1a84157603cf4617", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b9f286e72c7029b53862cbceaefb379f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c62faaa671cdc91d182ab651599f79cc", "guid": "bfdfe7dc352907fc980b868725387e98d3e6d0ee059ea7b3d91b8f3063f93e1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98470cc17ba254f4776cea8bfcf3686832", "guid": "bfdfe7dc352907fc980b868725387e98f3a0486ea7281539f1eb4dbc2488022f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a5cc099aa53a43c67798aec7a4b8289", "guid": "bfdfe7dc352907fc980b868725387e985ec07d51ea7c4166a0f60fd8374e9e1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98360d96bf587129a13df76d5bc08862cd", "guid": "bfdfe7dc352907fc980b868725387e980db351a095d01d307f89ac8a0761637e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98589b20794bea193bc5c6539bfc31196f", "guid": "bfdfe7dc352907fc980b868725387e98ff8292e4621a680af14bb57af643763a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981edc44847e1e2d3eb300965318243c95", "guid": "bfdfe7dc352907fc980b868725387e9809211ae0292cfd2aea8d324f4dd96070"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983164930713a3bb0add0ca549117b8660", "guid": "bfdfe7dc352907fc980b868725387e98a100027e8a0ba7580563a997fb2b8e8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cb794ce9101842b5187168a0e345a66", "guid": "bfdfe7dc352907fc980b868725387e98a8bf092a35fe7df1d61457b739639bcb"}], "guid": "bfdfe7dc352907fc980b868725387e9835890100e1278d4912a0a960bc1e1798", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e989eb0b9349eae5cdf5963230d4af39bac"}], "guid": "bfdfe7dc352907fc980b868725387e98e62b94e67497d0d9d86e7bbf1288e19b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9837f145fa470c584eb77faec656d2013a", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e9839f52b65e3be06571da83c9298e2891a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}