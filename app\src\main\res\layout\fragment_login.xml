<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootCL"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/action_bar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="80dp"
            android:paddingBottom="?actionBarSize"
            android:padding="@dimen/padding_small">

            <vn.zenity.betacineplex.helper.view.BetaEditText
                android:id="@+id/edtEmail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/padding_small"
                android:layout_marginTop="50dp"
                app:layout_constraintTop_toTopOf="parent"
                android:inputType="textEmailAddress"
                app:btedHint="@string/email_or_username"
                app:btedIcon="@drawable/name" />

            <vn.zenity.betacineplex.helper.view.BetaEditText
                android:id="@+id/edtPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/padding_small"
                android:imeOptions="actionDone"
                android:inputType="textPassword"
                app:btedHint="@string/password"
                app:btedIcon="@drawable/password"
                app:layout_constraintTop_toBottomOf="@+id/edtEmail" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/forgotPass"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/padding_small"
                android:layout_marginTop="@dimen/padding_large"
                android:text="@string/forgot_password_underline"
                android:textColor="@color/colorPrimaryDark"
                android:textSize="@dimen/font_normal"
                app:fontFamily="@font/sanspro_regular"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/edtPassword" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnLogin"
                style="@style/ButtonOrange"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/padding_small"
                android:layout_marginRight="@dimen/padding_small"
                android:layout_marginTop="@dimen/padding_large"
                android:text="@string/login"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/forgotPass" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnLoginFacebook"
                style="@style/ButtonPrimary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/padding_small"
                android:layout_marginRight="@dimen/padding_small"
                android:layout_marginTop="@dimen/padding_large"
                android:text="@string/login_facebook"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btnLogin" />

            <LinearLayout
                android:id="@+id/orLl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/padding_small"
                android:layout_marginRight="@dimen/padding_small"
                android:layout_marginTop="40dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btnLoginFacebook">

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1"
                    android:background="@color/grayLine" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/or"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/margin_small"
                    android:gravity="center"
                    android:textColor="@color/grayLine"
                    android:text="@string/or"
                    android:textSize="@dimen/font_normal" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1"
                    android:background="@color/grayLine" />

            </LinearLayout>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/btnRegister"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginLeft="@dimen/padding_small"
                android:layout_marginRight="@dimen/padding_small"
                android:layout_marginTop="40dp"
                android:gravity="center"
                android:text="@string/register_beta"
                android:textColor="@color/textDark"
                android:textSize="@dimen/font_large"
                android:textStyle="bold"
                android:layout_marginBottom="40dp"
                app:fontFamily="@font/sanspro_regular"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/orLl"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/login"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>