{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9870c2cd99459d116ddcf41e6be3bf77df", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9897b0bbd5c235674d6fe4a3658b2c7135", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d3e014b32d0d93a7829ffae774e408a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98626c8442c6e6f8a61bd443025dfb72b0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d3e014b32d0d93a7829ffae774e408a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9857102d3bb9255000fd48afd90549a8e4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e6b9e4676b077eeb70029206e332f04", "guid": "bfdfe7dc352907fc980b868725387e9807f352eede6b935fbd13ef1042cf63f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea0d42e3cfccb7fd7366d4875e16e26c", "guid": "bfdfe7dc352907fc980b868725387e985f80be5d786127c283032e16eb74206d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5b1ec92ea87b4aeab197029866df42a", "guid": "bfdfe7dc352907fc980b868725387e980edf3655853edb8260118fd1fc1ad572"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a08b0ce4023616214ebe193287c4587b", "guid": "bfdfe7dc352907fc980b868725387e985ebdbc5b325e27a47c628191da52eb9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883a33fe7910e912e972e06e13e3d107a", "guid": "bfdfe7dc352907fc980b868725387e987d85aa7042129f8c75e652e1bb8391e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8638a36f21afbbdaa0246cdb23b873d", "guid": "bfdfe7dc352907fc980b868725387e98b6c0ed8978c9f37ec0b4d2b1b93494cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d94c2f4d3f812e419319621dc8beb9c", "guid": "bfdfe7dc352907fc980b868725387e98246520421030959d3655673d2209b273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98420271e715a7579dd9301aa77e207400", "guid": "bfdfe7dc352907fc980b868725387e9808e4b8c2d97b0277421a029627ac7fdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826c2a606cae2ca6bca2aa67a62c85ebd", "guid": "bfdfe7dc352907fc980b868725387e98975c099ed8abd1872bafa39b35db6a8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874e1ffae7c1c6e561a1b9d7d741c1468", "guid": "bfdfe7dc352907fc980b868725387e98c26e45c031e5306e401b9e3f16dbeee0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896d4dfc271390f155765a7f2787d2503", "guid": "bfdfe7dc352907fc980b868725387e98e9be997fffd2f7226ee00afd33e68d3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986803630e59b43270cb83d14c25ec5a1b", "guid": "bfdfe7dc352907fc980b868725387e98f2fd6ca49745bf73719a034b2c033593", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f3441619ca2e31337ff1ef214da9e55", "guid": "bfdfe7dc352907fc980b868725387e98048a0423249a4a5d786f58f3740e8102", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98054629e759eabe673b873012d320c7a2", "guid": "bfdfe7dc352907fc980b868725387e984edda363e3f9c2ee286c099c2e825eeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a55fb8dccd584294aaeb256e535488f5", "guid": "bfdfe7dc352907fc980b868725387e985f9c52750b333994491b8e545f0a22c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caa05c3f0fbc2697c19db8ed97ade6ca", "guid": "bfdfe7dc352907fc980b868725387e98e2927ff48cbfbe8cc171500a5cec99e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98633f021f4817e7bade40fa63fe4c134a", "guid": "bfdfe7dc352907fc980b868725387e9846ca13b1580086287b08ded3e64183db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5fc96cbce57f8b94fb3d613af7135d0", "guid": "bfdfe7dc352907fc980b868725387e98a60ef6ce5bb65fdfe9c33a3ccb73e72b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b83fa3ec0757495a9b6db6e72317d8ad", "guid": "bfdfe7dc352907fc980b868725387e9802400cc3d8c16583eb51dd3dc4d1024c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baefeee8e5fd3222ac7acaac3f0e1979", "guid": "bfdfe7dc352907fc980b868725387e98e87ff810f79b21e77fc41347305f0a66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fc4f606173a3fe611a4e56c7acaa492", "guid": "bfdfe7dc352907fc980b868725387e9896384a838deb9070aa0d6fa0f38eb136"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb420d74d39650a683c9431695559ac7", "guid": "bfdfe7dc352907fc980b868725387e98f553511ed097357eb2b4b42972b0ab64", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f580decc0271cbd3073ed3f53bf73143", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a70d80bf55c3493afdd3c54a83295f39", "guid": "bfdfe7dc352907fc980b868725387e98b82e330864881fefd94eb194618376d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ae7765516e630a000ffdff04ecaf636", "guid": "bfdfe7dc352907fc980b868725387e98d66fc57921c2742778b37b538710cfdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e77aeedd233c24fb785df7d244a59d0", "guid": "bfdfe7dc352907fc980b868725387e98e8b4175b6e7287ddfa87a9fcb243e57e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d49a73bc96aa8e6354e54b730ad4d39b", "guid": "bfdfe7dc352907fc980b868725387e980cb150606a589861bfba237fcf277c46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dff319c0c8efa3a8f24486907547992b", "guid": "bfdfe7dc352907fc980b868725387e98db07065cae1c98b0d56beec1a98a1601"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821d4c7c0d57ca77ddd19b0dd8e123691", "guid": "bfdfe7dc352907fc980b868725387e98d65f9658427dc6e27a460d85a26ec229"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98315e94e30dfcc5c81eb822d4be94a7d5", "guid": "bfdfe7dc352907fc980b868725387e9836a9575586a987f80fe512b8380f2ec1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98751436bae0c19f316bc3eb63302b9fb8", "guid": "bfdfe7dc352907fc980b868725387e986aac79c6250c983808eee0fe9fe64406"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbf30e4ab52643f25ec3135adcdba1a2", "guid": "bfdfe7dc352907fc980b868725387e9805cb23756f4a60c533f44218b0e0cd48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cc8a22b16a4357fb466fefa762b6816", "guid": "bfdfe7dc352907fc980b868725387e98ed564bbe3beb80a935703fc3f7c28555"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f51e303250eda4ceb16ec87949c0445f", "guid": "bfdfe7dc352907fc980b868725387e98d4596754bcab2ced8835df6d81c9935f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986249874d310b2ee5e4c3c8124c589ccb", "guid": "bfdfe7dc352907fc980b868725387e981b7d87e0e7026d6d469c94dfdf040c32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98352cca711167969012475612fda500b0", "guid": "bfdfe7dc352907fc980b868725387e98464ebd3a867d5d98bbf23207eaeeb7b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983568e4e79f0adeb16a8d23291aa32a78", "guid": "bfdfe7dc352907fc980b868725387e98662daf166c3497c62de7b0ceae624fa6"}], "guid": "bfdfe7dc352907fc980b868725387e98d91a8465328a19d5ac8d1f501ecbc42c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98c2cf69b0fe71b0563137ab8ad3510c39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d391d713992f1d4e99af11b30a3d8fcd", "guid": "bfdfe7dc352907fc980b868725387e989c30323acc81fded73d63c5fd897224c"}], "guid": "bfdfe7dc352907fc980b868725387e98785ec3d23e573f86d796e37e344c7d0a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9894d2c535c20885e2440951e2bf5c542f", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98bd509e21d5692b7bd5ea80d543622c6b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}