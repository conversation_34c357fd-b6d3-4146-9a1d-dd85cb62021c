package vn.zenity.betacineplex.view.user

import android.os.Bundle
import android.view.View
import kotlinx.android.synthetic.main.fragment_vouchercoupon.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView

/**
 * Created by Zenity.
 */

class VoucherCouponFragment : BaseFragment(), VoucherCouponContractor.View {

    private val presenter = VoucherCouponPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_vouchercoupon
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        coupon.setOnClickListener {
            openFragment(CouponFragment())
        }
        voucher.setOnClickListener {
            openFragment(VoucherFragment())
        }
    }
}
