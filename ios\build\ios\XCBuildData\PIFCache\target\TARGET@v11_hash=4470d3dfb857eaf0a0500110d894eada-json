{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba23d92032bfcbdbcc88292aed8d59d5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cff22cd63921df8edeabefca20d54d3c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cff22cd63921df8edeabefca20d54d3c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9893544eb2c046d56cab507d31a8c9b118", "guid": "bfdfe7dc352907fc980b868725387e982ea0ca19c0771fd6d266f2b66446b37d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867c0479a0df0dbd37a6ca1c3ab2a5cb9", "guid": "bfdfe7dc352907fc980b868725387e98f1887403300e04b839877af836998497", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a91a01db360d23e406e3d5cc0894c923", "guid": "bfdfe7dc352907fc980b868725387e9899c4b45e473fef94c750c44c19300e3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7f9d702a145041ad3711399d9db6ef7", "guid": "bfdfe7dc352907fc980b868725387e9801e957e9f2cdc171382139f7af1a5681", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883f0432f28ba40e3aa68ed2e31a2d15d", "guid": "bfdfe7dc352907fc980b868725387e98f9dcd7bfc2b70fc0334d5a2b029d2be4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f919d34ed5ffb4e6544b52c479aba98", "guid": "bfdfe7dc352907fc980b868725387e983c0142618f0c80e86891f682ede039d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984468bc00f924614c0ddbd6ae24f4abc8", "guid": "bfdfe7dc352907fc980b868725387e98a01fc3746b75fa29083dde20e4e057e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab2ff41f7fcdda4b97724e0115088798", "guid": "bfdfe7dc352907fc980b868725387e98cd73b22058278a9b4f651422cde8e6cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822e34c2db4766b703516ae74a3bad20b", "guid": "bfdfe7dc352907fc980b868725387e9858da164c6f05b97dc4f70eeaf0ab7f88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98340a5a4a2169368caacc2ba6c9e5ea8b", "guid": "bfdfe7dc352907fc980b868725387e9810ee0ac07ad1614d2a0f4f4692012057", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985017e08650f4bfb0e3e30ffb4fe8dfab", "guid": "bfdfe7dc352907fc980b868725387e9898f6f1d0225d5da11e073619b459ecbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803d392780508ff87adf3f9e19282dd60", "guid": "bfdfe7dc352907fc980b868725387e982567c545f94782ef792faee70a9f28e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98735bf9389c41cf57beed11ee6351c973", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856ec5e097b24415a0c720dcef6f181df", "guid": "bfdfe7dc352907fc980b868725387e98616c37b3aed90db7f9fdd15a498d9b27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851899736ec0932aba9b5c53d6e0b4403", "guid": "bfdfe7dc352907fc980b868725387e989ec464f79f509c315509332bb727e800", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0a92df710daeb26e6eb0e24fa51741f", "guid": "bfdfe7dc352907fc980b868725387e983249d575db8b5cbd53c0564d7472fd4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980053ffcf6327cca3b7601d31c5e36c6e", "guid": "bfdfe7dc352907fc980b868725387e980d76b669df9576a70807ec449fb952b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd09cfc196f7f7541dbef290e04fa16a", "guid": "bfdfe7dc352907fc980b868725387e98bc7c56555dd219b78ffdd88c372b232b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe5a5dc2ada539af1775a91b7ddcf471", "guid": "bfdfe7dc352907fc980b868725387e988e7018dc0c9e1f0c9f93b5994bcbc130", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e4e75707434f06fc07aa9d2db3a0a76", "guid": "bfdfe7dc352907fc980b868725387e98dcef7cb18e4d73ea8eef002919db5632", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2cf151a6ca1ac05665806d89d4a13c4", "guid": "bfdfe7dc352907fc980b868725387e98955da4966904666f6adbe480ce93a36f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894b423b9d50cbff885831d0bb078383d", "guid": "bfdfe7dc352907fc980b868725387e98232d0a9b2be5b70279bedd97f191826e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc0cfbc80bb09e54bcfae67b36e6f3d4", "guid": "bfdfe7dc352907fc980b868725387e987211db59fabb7296cf1568fb71a68255", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984b2056ed9b6ee8fe4b595d5b84b4da30", "guid": "bfdfe7dc352907fc980b868725387e98c8af4857ac32d849c3b447bcff03aa13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988af8ea9e6ef50c24c0aea5c2bad069cd", "guid": "bfdfe7dc352907fc980b868725387e98dc950c62ee46ede361cd98ae90dd27a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891c617def286a8176a562a0644699ca3", "guid": "bfdfe7dc352907fc980b868725387e983f0af89b0b8bda8241febbe4723f8c88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcfacbb03b6957a058870c8d8670124d", "guid": "bfdfe7dc352907fc980b868725387e981a333127dad81374abf6fe8c83600eee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98391505e4d61e211092ce1ab0b322af8e", "guid": "bfdfe7dc352907fc980b868725387e988f8e2ac76c48795c8610f17a754a59c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879ef7e021c3138a689b15703e17c2edc", "guid": "bfdfe7dc352907fc980b868725387e984b1e4946f1823465931d4e8492eb19a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846dd5f0e81c9781e4d20e3d8602f06bc", "guid": "bfdfe7dc352907fc980b868725387e98486c17b42526ba81b0e47a838401edd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be900c52a41a1ff928ff47d3dd3a2679", "guid": "bfdfe7dc352907fc980b868725387e98e24ce56a008cd8fb5a8cdae1e9811e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f51a2f519146e4fcd3d8d725984b8dfb", "guid": "bfdfe7dc352907fc980b868725387e98243ca78b2974c64cd01083596d8fe9a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bdc06fb54577b90de9751bbe2cab377", "guid": "bfdfe7dc352907fc980b868725387e98c41a340685df4d1ce3783021dcf54fc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e3ace8968e39e3c8fecc54252b32598", "guid": "bfdfe7dc352907fc980b868725387e98506a4bb5c9b087dedda179e379e974b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab9a8237dbfd8fa13de3e5969f26dbf9", "guid": "bfdfe7dc352907fc980b868725387e984463890dd80178e8bcdc0b520010feee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98958c09f4ad8dae37c503b36d748df714", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa168ceecae541d5660fe524a5815cc3", "guid": "bfdfe7dc352907fc980b868725387e9859e3b579695a572ae5f0cad67ec2876d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb2c85c9da522f6e79cdb458a8465525", "guid": "bfdfe7dc352907fc980b868725387e9885aba67d2d135f9806c9a05487a1a98c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dad78c6f535765bc7ec055f64f57d1fd", "guid": "bfdfe7dc352907fc980b868725387e985805bc10d6057738af5938ead2b69fee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98246e5ea4c32e36231a4ce8586385259b", "guid": "bfdfe7dc352907fc980b868725387e985658d819a1ceda9c7580d1657159b11a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987af45ce2599a387bf7afcc1c33794766", "guid": "bfdfe7dc352907fc980b868725387e98c8c90bc417199af9668b9601526e858b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801748373551bd622f350317421389631", "guid": "bfdfe7dc352907fc980b868725387e98a79aa5b656ffdfb7930448cc9a744fc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0e87a499241b96b9f39bd85d7088524", "guid": "bfdfe7dc352907fc980b868725387e9822c0d109ebdbc53ca874ef63b18251dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c1c7b9e1d50815616ffc797680f9f96", "guid": "bfdfe7dc352907fc980b868725387e983c3ab34f30d3bcea13309294f772a70c"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}