import 'package:flutter/material.dart';
import 'package:flutter_app/pages/cinema/_detail.dart';
import 'package:flutter_app/pages/cinema/model/cinema_model.dart';
import 'package:flutter_app/pages/cinema/widget/nearby_cinemas_widget.dart';
import 'package:flutter_app/service/location_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fl_location/fl_location.dart';

import '../../constants/index.dart';
import '../../core/index.dart';
import '../../cubit/index.dart';
import '../../utils/index.dart';
import '../other_tab/beta_cinema/_detail.dart';
import '../voucher/api/api_test.dart';

class ListFilmScreen extends StatefulWidget {
  final FilmType type;
  final bool isBooking;

  const ListFilmScreen({
    super.key,
    this.type = FilmType.nowShowing,
    this.isBooking = false,
  });

  @override
  State<ListFilmScreen> createState() => _ListFilmScreenState();
}

class _ListFilmScreenState extends State<ListFilmScreen> {
  final LocationService _locationService = LocationService();
  Location? _userPosition;
  bool _isLoadingLocation = false;
  List<Cinema> _nearbyCinemas = [];
  bool _isLoadingNearbyCinemas = false;

  @override
  void initState() {
    super.initState();
    _getUserLocation();
  }

  Future<void> _getUserLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      _userPosition = await _locationService.determinePosition(
        showError: true,
        context: context,
      );

      // If location is available, fetch nearby cinemas
      if (_userPosition != null) {
        await _fetchNearbyCinemas();
      }
    } catch (e) {
      print('Error getting location: $e');
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  Future<void> _fetchNearbyCinemas() async {
    if (_userPosition == null) return;

    setState(() {
      _isLoadingNearbyCinemas = true;
    });

    try {
      final result = await RepositoryProvider.of<Api>(context).cinema.getAllCinemas();
      if (result != null && result.data != null) {
        final List<dynamic> cinemaData = result.data['content'] ?? result.data;
        final List<Cinema> allCinemas = cinemaData.map((item) => Cinema.fromJson(item)).toList();

        // Calculate distance for each cinema
        for (var cinema in allCinemas) {
          if (cinema.hasValidCoordinates) {
            cinema.distance = _locationService.calculateDistance(
              _userPosition!.latitude,
              _userPosition!.longitude,
              cinema.latitudeAsDouble!,
              cinema.longitudeAsDouble!,
            );
          }
        }

        // Filter cinemas within 100km and sort by distance
        // final nearbyCinemas = allCinemas
        //     .where((cinema) => cinema.distance != null && cinema.distance! < 100)
        //     .toList();
        //
        // nearbyCinemas.sort((a, b) => a.distance!.compareTo(b.distance!));

        setState(() {
          _nearbyCinemas = allCinemas.take(6).toList();
        });
      }
    } catch (e) {
      print('Error fetching nearby cinemas: $e');
    } finally {
      setState(() {
        _isLoadingNearbyCinemas = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: (context) => BlocC<CityCinemaGroup>(),
        child: Scaffold(
          appBar: appBar(
              title: 'Rạp phim BETA',
              centerTitle: false,
              toolbarHeight: 50,
              isHideLeading: !widget.isBooking,
              leadingWidth: widget.isBooking ? null : 0,
              titleStyle: const TextStyle(color: Colors.white, fontSize: CFontSize.xl2),
              // leading: const SizedBox.shrink(),
              backgroundColor: Colors.transparent),
          backgroundColor: Colors.grey[100],
          body: BlocBuilder<BlocC<CityCinemaGroup>, BlocS<CityCinemaGroup>>(builder: (context, state) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: CSpace.sm),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: CSpace.sm,
                  // padding: const EdgeInsets.all(16),
                  children: [
                    // Nearby cinemas section (only show if user has location)
                    if (_userPosition != null)
                      _isLoadingNearbyCinemas
                          ? const Padding(
                              padding: EdgeInsets.all(CSpace.lg),
                              child: Center(
                                child: CircularProgressIndicator(),
                              ),
                            )
                          : _nearbyCinemas.isNotEmpty
                              ? NearbyCinemasWidget(
                                  nearbyCinemas: _nearbyCinemas,
                                  isBooking: widget.isBooking,
                                )
                              : const SizedBox.shrink(),

                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: CSpace.xl),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Chọn rạp theo khu vực'.toUpperCase(),
                              style: const TextStyle(fontSize: CFontSize.xl2, fontWeight: FontWeight.w500)),
                          // Location refresh button
                          _isLoadingLocation
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                              : IconButton(
                                  icon: const Icon(Icons.location_searching),
                                  onPressed: _getUserLocation,
                                  tooltip: 'Cập nhật vị trí',
                                ),
                        ],
                      ),
                    ),
                    Expanded(
                        child: WList<CityCinemaGroup>(
                      item: (content, index) {
                        // Calculate distance for each cinema if user position is available
                        if (_userPosition != null && content.listCinema != null) {
                          for (var cinema in content.listCinema!) {
                            if (cinema.hasValidCoordinates) {
                              cinema.distance = _locationService.calculateDistance(
                                _userPosition!.latitude,
                                _userPosition!.longitude,
                                cinema.latitudeAsDouble!,
                                cinema.longitudeAsDouble!,
                              );
                            }
                          }

                          // Sort cinemas by distance
                          content.listCinema!.sort((a, b) {
                            if (a.distance == null && b.distance == null) return 0;
                            if (a.distance == null) return 1;
                            if (b.distance == null) return -1;
                            return a.distance!.compareTo(b.distance!);
                          });
                        }

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  content.isExpanded = !content.isExpanded;
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(vertical: CSpace.xl3, horizontal: CSpace.xl3),
                                // margin: const EdgeInsets.symmetric(bottom: CSpace.sm),
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  // borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      content.cityName ?? "",
                                      style: const TextStyle(fontSize: 18),
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                          content.listCinema?.length.toString() ?? "",
                                          style: const TextStyle(fontSize: 16, color: Colors.blue),
                                        ),
                                        const HSpacer(CSpace.base),
                                        Icon(content.isExpanded ? Icons.expand_less : Icons.expand_more),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          const SizedBox(height: 8),
                          if (content.isExpanded)
                            content.listCinema!.isNotEmpty
                                ? GridView.builder(
                                    shrinkWrap: true,
                                    physics: const NeverScrollableScrollPhysics(),
                                    itemCount: content.listCinema?.length,
                                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 2,
                                      childAspectRatio: 1.2,
                                      crossAxisSpacing: 8,
                                      mainAxisSpacing: 8,
                                    ),
                                    itemBuilder: (context, index) {
                                      final cinema = content.listCinema![index];
                                      return InkWell(
                                        onTap: () {
                                          !widget.isBooking
                                              ? Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder: (context) => BlocProvider(
                                                      create: (context) => BlocC<ShowFilmModel>(),
                                                      child: ChooseCinemasView(
                                                        cinema: cinema,
                                                      ),
                                                    ),
                                                  ),
                                                )
                                              : Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder: (context) => CinemaDetailScreen(
                                                      initialCinema: cinema,
                                                    ),
                                                  ));
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(12),
                                            color: Colors.white,
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.grey.withAlpha(51), // 0.2 opacity
                                                blurRadius: 6,
                                              ),
                                            ],
                                          ),
                                          child: Column(
                                            children: [
                                              ClipRRect(
                                                borderRadius: const BorderRadius.vertical(
                                                  top: Radius.circular(12),
                                                ),
                                                child: imageNetwork(
                                                  url: '${ApiService.baseUrlImage}/${cinema.picture ?? ''}',
                                                  height: 100,
                                                  width: double.infinity,
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsets.all(8.0),
                                                child: Column(
                                                  children: [
                                                    Text(
                                                      cinema.name ?? '',
                                                      style: const TextStyle(
                                                          fontWeight: FontWeight.w600, fontSize: CFontSize.base),
                                                      overflow: TextOverflow.ellipsis,
                                                      maxLines: 2,
                                                      textAlign: TextAlign.center,
                                                    ),
                                                    if (cinema.formattedDistance != null)
                                                      Padding(
                                                        padding: const EdgeInsets.only(top: 4.0),
                                                        child: Row(
                                                          mainAxisAlignment: MainAxisAlignment.center,
                                                          children: [
                                                            const Icon(
                                                              Icons.location_on,
                                                              size: 14,
                                                              color: Colors.red,
                                                            ),
                                                            const SizedBox(width: 2),
                                                            Text(
                                                              cinema.formattedDistance!,
                                                              style: const TextStyle(
                                                                fontSize: 12,
                                                                color: Colors.grey,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  )
                                : const Padding(
                                    padding: EdgeInsets.all(8.0),
                                    child: Text('Không có rạp nào tại khu vực này.'),
                                  ),
                          if (content.isExpanded) const SizedBox(height: CSpace.lg),
                        ],
                      );
                      },
                      format: CityCinemaGroup.fromJson,
                      api: (filet, page, size, sort) => RepositoryProvider.of<Api>(context).cinema.getCinema(),
                    )),
                  ]),
            );
          }),
        ));
  }
}

enum FilmType {
  nowShowing,
  special,
}
