package vn.zenity.betacineplex.view.user

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class BookHistoryDetailPresenter : BookHistoryDetailContractor.Presenter {

    private var disposable: Disposable? = null
    override fun getDetails(id: String) {
        val accountId = Global.share().user?.AccountId ?: return
        view?.get()?.showLoading()
        disposable =
            APIClient.shared.transactionAPI.getPaymentHistoryDetail(accountId, id).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let {
                            view?.get()?.onGetDetailSuccess(it)
                        }
                    } else {
                        view?.get()?.showAlert(
                            it.Message ?: R.string.get_payment_history_error.getString()
                        )
                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()
                        ?.showAlert(it.message ?: R.string.get_payment_history_error.getString())
                    view?.get()?.hideLoading()
                })
    }


    private var view: WeakReference<BookHistoryDetailContractor.View?>? = null
    override fun attachView(view: BookHistoryDetailContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
