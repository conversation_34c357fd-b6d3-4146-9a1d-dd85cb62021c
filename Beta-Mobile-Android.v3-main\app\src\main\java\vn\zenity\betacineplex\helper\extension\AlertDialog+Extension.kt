package vn.zenity.betacineplex.helper.extension

import android.app.AlertDialog
import android.content.Context
import android.content.DialogInterface
import androidx.core.content.res.ResourcesCompat
import androidx.appcompat.widget.AppCompatTextView
import android.text.*
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.URLSpan
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.webkit.WebView
import android.widget.ArrayAdapter
import android.widget.GridView
import android.widget.TextView
import io.reactivex.disposables.Disposable
import kotlinx.android.synthetic.main.layout_confirm_with_highlight.view.*
import kotlinx.android.synthetic.main.layout_dialog_success.view.*
import kotlinx.android.synthetic.main.layout_dialog_success.view.tvFirstContent
import kotlinx.android.synthetic.main.layout_get_voucher_success.view.*
import kotlinx.android.synthetic.main.layout_register_success.view.*
import kotlinx.android.synthetic.main.layout_select_point_donate.view.*
import org.w3c.dom.Text
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.model.Cinema
import vn.zenity.betacineplex.model.CityModel
import vn.zenity.betacineplex.model.SeatScreenModel
import vn.zenity.betacineplex.model.VoucherCodeModel
import vn.zenity.betacineplex.view.auth.PaymentPolicyFragment
import java.lang.Exception
import java.lang.ref.WeakReference


/**
 * Created by tranduc on 1/8/18.
 */

fun showConfirm(context: Context, title: String? = null, content: String, rightButtonTitle: String = "Đồng ý", leftButtonTitle: String? = null, rightButtonClickHandler: (() -> Unit)? = null, cancelListener: (() -> Unit)? = null, cancelable : Boolean = true) {
    val dialog = AlertDialog.Builder(context)
    dialog.setCancelable(cancelable)
    dialog.setPositiveButton(rightButtonTitle, { dia, _ ->
        dia.dismiss()
        rightButtonClickHandler?.invoke()
    })
    title?.let {
        dialog.setTitle(title)
    }
    leftButtonTitle?.let {
        dialog.setNegativeButton(it, { dia, _ ->
            dia.dismiss()
        })
    }
    dialog.setOnCancelListener {
        cancelListener?.invoke()
    }
    dialog.setOnDismissListener {
        cancelListener?.invoke()
    }
    dialog.setMessage(content)
    dialog.create().show()
}

fun showCitiSelection(context: Context, title: String? = null, listCity: ArrayList<CityModel>, selectedListener: (CityModel) -> Unit) {
    val dialog = AlertDialog.Builder(context)
    dialog.setCancelable(true)
    dialog.setPositiveButton(R.string.close, { dia, _ ->
        dia.dismiss()
    })
    title?.let {
        dialog.setTitle(title)
    }

    val arrayAdapter = ArrayAdapter<CityModel>(context, android.R.layout.select_dialog_item)
    arrayAdapter.addAll(listCity)
    dialog.setAdapter(arrayAdapter, { dialogI, position ->
        selectedListener.invoke(listCity[position])
        dialogI.dismiss()
    })
    dialog.create().show()
}

fun showGenderSelection(context: Context, title: String?, selectedListener: (Int, String) -> Unit) {
    val dialog = AlertDialog.Builder(context)
    dialog.setCancelable(true)
    dialog.setPositiveButton(R.string.close, { dia, _ ->
        dia.dismiss()
    })
    title?.let {
        dialog.setTitle(title)
    }

    val arrayAdapter = ArrayAdapter<String>(context, android.R.layout.select_dialog_item)
    /**
     * { Id: 1, Name: "Nam" },
    { Id: 2, Name: "Nữ" },
    { Id: 3, Name: "Khác" }
     */
    val arrayGender = context.resources.getStringArray(R.array.gender_array)
    for (gender in arrayGender) {
        arrayAdapter.add(gender)
    }
//    arrayAdapter.add(context.getString(R.string.male))
//    arrayAdapter.add(context.getString(R.string.female))
//    arrayAdapter.add(context.getString(R.string.other))
//    arrayAdapter.add(context.getString(R.string.no_pulbic))
//    arrayAdapter.add(context.getString(R.string.unknown))
    dialog.setAdapter(arrayAdapter) { dialogI, position ->
        selectedListener.invoke(if (position > 3) 0 else position + 1, arrayAdapter.getItem(position) ?: "")
        dialogI.dismiss()
    }
    dialog.create().show()
}

fun showListSeatSelected(context: Context, listSeats: List<SeatScreenModel>) {
    val dialog = AlertDialog.Builder(context)
    dialog.setCancelable(true)
    dialog.setPositiveButton(R.string.close, { dia, _ ->
        dia.dismiss()
    })
    dialog.setTitle(R.string.seat_selected)

    val gView = GridView(context)
    gView.numColumns = 4
    val arrayAdapter = ArrayAdapter<String>(context, R.layout.item_seat_selected, android.R.id.text1)
    arrayAdapter.addAll(listSeats.map { it.SeatName })
    gView.adapter = arrayAdapter
    dialog.setView(gView)
    dialog.show()
}

fun showTicketPrice(context: Context, cinema: Cinema) {
    val dialog = AlertDialog.Builder(context)
    dialog.setCancelable(true)
    dialog.setPositiveButton(R.string.close, { dia, _ ->
        dia.dismiss()
    })
    dialog.setTitle(R.string.ticket_price)

    val wView = WebView(context)
    wView.loadUrl("http://dev.betacinemas.vn/gia-ve/gia-ve-d.htm")
    dialog.setView(wView)
    dialog.show()
}

fun BaseFragment.showNoticeAge(ageNumber: Int, agree: () -> Unit) {
    val dialog = AlertDialog.Builder(context)
    var dlog: AlertDialog? = null
    dialog.setCancelable(true)
    dialog.setPositiveButton(R.string.agree) { dia, _ ->
        agree.invoke()
        dia.dismiss()
    }
    dialog.setNegativeButton(R.string.cancel) { dia, _ ->
        dia.dismiss()
    }

    val textView = AppCompatTextView(context)
    context?.let {
        textView.typeface = ResourcesCompat.getFont(it, R.font.sanspro_regular)
    }

    val age = "$ageNumber ${R.string.age_above.getString()}"
    val term = getString(R.string.term_in_notice_age)
    val notice = getString(R.string.notice_age_booking, age, term)
//    val notice = getString(R.string.notice_age_booking_html, age, term)
    val textSpan = Spannable.Factory.getInstance().newSpannable(notice)
//    val textSpan = Html.fromHtml(notice)
    val startAge = notice.indexOf(age)
    textSpan.setSpan(object : ClickableSpan() {
        override fun onClick(ab: View) {
        }

        override fun updateDrawState(ds: TextPaint) {
            super.updateDrawState(ds)
            ds.color = R.color.textBlack.getColor()
            ds.linkColor = R.color.textBlack.getColor()
            context?.let {
                ds?.typeface = ResourcesCompat.getFont(it, R.font.sanspro_bold)
            }
            ds.isUnderlineText = false
        }

    }, startAge, startAge + age.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

    val startTerm = notice.indexOf(term)
    textSpan.setSpan(object : ClickableSpan() {
        override fun onClick(ab: View) {
            openFragment(PaymentPolicyFragment())
            dlog?.dismiss()
        }


        override fun updateDrawState(ds: TextPaint) {
            super.updateDrawState(ds)
            ds.color = R.color.colorPrimaryDark.getColor()
            ds.linkColor = R.color.colorPrimaryDark.getColor()
            ds.isUnderlineText = true
        }

    }, startTerm, startTerm + term.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

    val strBuilder = SpannableStringBuilder(textSpan)
    val urls = strBuilder.getSpans(0, notice.length, URLSpan::class.java)
    for (span in urls) {
        span.makeLinkClickable(strBuilder) {
            openFragment(PaymentPolicyFragment())
            dlog?.dismiss()
        }
    }
    textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16f)
    textView.movementMethod = LinkMovementMethod.getInstance()
    textView.setLinkTextColor(R.color.colorPrimary.getColor())
//    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//        textView.text = Html.fromHtml(notice, Html.FROM_HTML_MODE_COMPACT)
//    } else
//        textView.text = Html.fromHtml(notice)
    textView.text = textSpan
    val padding = dp2Px(16)
    textView.setPadding(padding, padding, padding, 0)
    dialog.setView(textView)
    dlog = dialog.create()
    dlog?.show()
}

fun URLSpan.makeLinkClickable(strBuilder: SpannableStringBuilder, click: () -> Unit) {
    val start = strBuilder.getSpanStart(this)
    val end = strBuilder.getSpanEnd(this)
    val flags = strBuilder.getSpanFlags(this)
    val clickable = object : ClickableSpan() {
        override fun onClick(view: View) {
            click.invoke()
        }
    }
    strBuilder.setSpan(clickable, start, end, flags)
    strBuilder.removeSpan(this)
}

fun showDialogRegisterSuccess(context: Context, cardNumber: String, finish: () -> Unit) {
    val dialog = AlertDialog.Builder(context)
    var dlog: AlertDialog? = null
    dialog.setCancelable(true)
    dialog.setTitle("")
    val view = LayoutInflater.from(context).inflate(R.layout.layout_register_success, null, false)
    view.tvCardNumber.text = cardNumber
    view.btnOk.setOnClickListener {
        dlog?.dismiss()
        finish.invoke()
    }
    dialog.setView(view)
    dlog = dialog.show()
}

fun showDialogSuccess(context: Context, title: String, firstContent: String, centerContent: String? = null, secondContent: String? = null, finish: () -> Unit) {
    val dialog = AlertDialog.Builder(context)
    var dlog: AlertDialog? = null
    dialog.setCancelable(true)
    dialog.setTitle("")
    val view = LayoutInflater.from(context).inflate(R.layout.layout_dialog_success, null, false)
    view.apply {
        tvTitle.text = title
        tvFirstContent.text = firstContent
        if (centerContent != null) {
            tvCenterContent.visible()
            tvCenterContent.text = centerContent
        } else {
            tvCenterContent.gone()
        }

        if (secondContent != null) {
            tvSecondContent.text = secondContent
            tvSecondContent.visible()
        } else {
            tvSecondContent.gone()
        }
        btnOkSuccess.click {
            finish.invoke()
            dlog?.dismiss()
        }
    }
    dialog.setView(view)
    dlog = dialog.show()
}

fun showConfirmGivePoint(context: Context, point: Int, name: String, email: String, finish: (Boolean) -> Unit) {
    val dialog = AlertDialog.Builder(context)
    var dlog: AlertDialog? = null
    dialog.setCancelable(true)
    dialog.setTitle("")
    val view = LayoutInflater.from(context).inflate(R.layout.layout_confirm_with_highlight, null, false)
    view.apply {
        tvHighlightContent.setTextWithSpecialText(R.string.confirm_give_point_content.getStringFormat(point), "$point ${R.string.point.getString().toLowerCase()}") {
            it.isUnderlineText = false
            it.typeface = context.let { it1 -> ResourcesCompat.getFont(it1, R.font.sanspro_bold) }
            it.color = R.color.textBlack.getColor()
            it.linkColor = R.color.textBlack.getColor()
        }
        tvHighlightEmail.text = "($email)"
        tvHighlightUser.text = name
        btnHighlightOk.click {
            dlog?.dismiss()
            finish.invoke(true)
        }
        btnHighlightCancel.click {
            dlog?.dismiss()
        }
    }
    dialog.setView(view)
    dlog = dialog.show()
}

fun showConfirmDonateVoucher(context: Context, voucherName: String, name: String, email: String, finish: (Boolean) -> Unit) {
    val dialog = AlertDialog.Builder(context)
    var dlog: AlertDialog? = null
    dialog.setCancelable(true)
    dialog.setTitle("")
    val view = LayoutInflater.from(context).inflate(R.layout.layout_confirm_with_highlight, null, false)
    view.apply {
        tvHighlightContent.setTextWithSpecialText(R.string.confirm_donate_voucher_content.getStringFormat(voucherName), voucherName) {
            it.isUnderlineText = false
            it.typeface = context.let { it1 -> ResourcesCompat.getFont(it1, R.font.sanspro_bold) }
            it.color = R.color.textBlack.getColor()
            it.linkColor = R.color.textBlack.getColor()
        }
        tvHighlightEmail.text = "($email)"
        tvHighlightUser.text = name
        btnHighlightOk.click {
            dlog?.dismiss()
            finish.invoke(true)
        }
        btnHighlightCancel.click {
            dlog?.dismiss()
        }
    }
    dialog.setView(view)
    dlog = dialog.show()
}

fun showDialogSelectPoint(context: Context, name: String, email: String, finish: (Int) -> Unit) {
    val dialog = AlertDialog.Builder(context)
    var dlog: AlertDialog? = null
    var disposable: Disposable? = null
    var point = 20
    val listPoints = listOf("5" , "10", "20", "30", "100")
    var isUseSelect = true
    var listViewPoints = listOf<TextView>()
    dialog.setCancelable(true)
    dialog.setTitle("")
    val view = LayoutInflater.from(context).inflate(R.layout.layout_select_point_donate, null, false)
    view.apply {
        tvDonateEmail.text = "($email)"
        tvDonateUser.text = name
        listViewPoints = listOf(tv5Points, tv10Points, tv20Points, tv30Points, tv100Points)
        listViewPoints.forEach {
            it.click {
                listViewPoints.forEach {
                    it.setBackgroundResource(R.drawable.border_gray_radius)
                    it.setTextColor(R.color.black.getColor())
                }
                (it as TextView).apply {
                    setBackgroundResource(R.drawable.shape_primary_radius)
                    setTextColor(R.color.white.getColor())
                }
                point = (it.tag as String).toInt()
            }
        }
        disposable = edtInputPoint.textChanges().applyOn().subscribe {
            if(TextUtils.isEmpty(it)) {
                if(!isUseSelect) {
                    point = 0
                }
                isUseSelect = true
            } else {
                if (isUseSelect) {
                    isUseSelect = false
                    listViewPoints.forEach {
                        it.setBackgroundResource(R.drawable.border_gray_radius)
                        it.setTextColor(R.color.black.getColor())
                    }
                }
                point = try {it.toInt()} catch (_: Exception) { 0 }
            }
        }
        btnDonateOk.click {
            if (point <= 0) {
                showConfirm(context, null, "Điểm tặng phải lớn hơn 0")
            } else {
                finish?.invoke(point)
                dlog?.dismiss()
            }
        }
        btnDonateCancel.click {
            disposable?.dispose()
            dlog?.dismiss()
        }
    }
    dialog.setView(view)
    dlog = dialog.show()
    dlog?.setOnDismissListener {
        disposable?.dispose()
    }
}
fun showConfirmWithHighlight(context: Context, content: String, highlight: String,
                             cancelable: Boolean = true,
                             rightButtonTitle: String = "Đồng ý", leftButtonTitle: String? = null,
                             rightButtonClickHandler: ((DialogInterface) -> Unit)? = null,
                             cancelListener: (() -> Unit)? = null) {
    val dialog = AlertDialog.Builder(context)
    dialog.setCancelable(cancelable)
    dialog.setPositiveButton(rightButtonTitle) { dia, _ ->
        if (cancelable)
            dia.dismiss()
        rightButtonClickHandler?.invoke(dia)
    }

    leftButtonTitle?.let {
        dialog.setNegativeButton(it) { dia, _ ->
            dia.dismiss()
        }
    }
    dialog.setOnCancelListener {
        cancelListener?.invoke()
    }
    dialog.setOnDismissListener {
        cancelListener?.invoke()
    }
    if (content.isEmpty() || highlight.isEmpty()) {
        dialog.setMessage(content)
    } else {
        val startT = content.indexOf(highlight)
        if (startT < 0) {
            dialog.setMessage(content)

        } else {
            val textSpan = SpannableString(content)
            textSpan.setSpan(object : ClickableSpan() {
                override fun onClick(widget: View) {
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.let {
                        it.isUnderlineText = false
                        it.typeface = context.let { it1 -> ResourcesCompat.getFont(it1, R.font.sanspro_bold) }
                        it.color = R.color.textBlack.getColor()
                        it.linkColor = R.color.textBlack.getColor()
                    }
                }
            }, startT, startT + highlight.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            dialog.setMessage(textSpan)
        }
    }
    dialog.create().show()
}


fun showGetVoucherSuccess(context: Context,
                          voucherCode: VoucherCodeModel,
                          finish: () -> Unit) {
    val dialog = AlertDialog.Builder(context)
    var dlog: AlertDialog? = null
    dialog.setCancelable(true)
    dialog.setTitle("")
    val view = LayoutInflater.from(context).inflate(R.layout.layout_get_voucher_success, null, false)
    view.apply {
        tvVoucherName.setTextWithSpecialText(voucherCode.FirstMessage ?: "", voucherCode.FirstMessageHighLight ?: "") {
            it.isUnderlineText = false
            it.typeface = context.let { it1 -> ResourcesCompat.getFont(it1, R.font.sanspro_bold) }
            it.color = R.color.textBlack.getColor()
            it.linkColor = R.color.textBlack.getColor()
        }

        tvVoucherCodeGot.setTextWithSpecialText(voucherCode.SecondMessage ?: "", voucherCode.SecondMessageHighLight ?: "") {
            it.isUnderlineText = false
            it.typeface = context.let { it1 -> ResourcesCompat.getFont(it1, R.font.sanspro_bold) }
            it.color = R.color.textBlack.getColor()
            it.linkColor = R.color.textBlack.getColor()
        }
        btnVoucherOK.click {
            dlog?.dismiss()
            finish()
        }
    }
    dialog.setView(view)
    dlog = dialog.show()
}
