# Mô tả Luồng Chạy Ứng dụng Booking iOS

Tài liệu này mô tả luồng hoạt động chính của ứng dụng Booking iOS.

## 1. Khởi động Ứng dụng

- **Điểm vào**: `AppDelegate.swift` (trong `App/`) là điểm khởi đầu của ứng dụng.
- **Khởi tạo ban đầu**: Trong `application(_:didFinishLaunchingWithOptions:)`, ứng dụng thực hiện các tác vụ khởi tạo:
    - C<PERSON>u hình các dịch vụ bên ngoài (Facebook SDK, IQKeyboardManager, Google Maps, Fabric/Crashlytics, Firebase, Mixpanel).
    - Đăng ký nhận thông báo đẩy (Push Notifications).
    - Thiết lập giao diện chung (`initAppearances` trong `App/AppDelegate+Appearance.swift`).
    - **Thiết lập luồng điều hướng chính**: G<PERSON><PERSON> hàm `initAppRoute()` (trong `App/AppDelegate+Initial.swift`).
- **Giao diện người dùng ban đầu**: `Resource/Base.lproj/Main.storyboard` dường như không được sử dụng cho luồng chính mà chỉ chứa một `TableViewController` cơ bản, có thể dùng cho mục đích thử nghiệm hoặc luồng phụ.

## 2. Luồng Điều hướng Chính (Main Navigation Flow)
### 2.1. Luồng Thông báo Đẩy (Push Notifications)

- **Khởi tạo và Đăng ký:**
  - Trong `AppDelegate.swift`, cụ thể là phần mở rộng `AppDelegate+Initial.swift`:
    - Hàm `initFirebase()`: Khởi tạo Firebase bằng cách gọi `FirebaseApp.configure()`.
    - Hàm `initNotification()`: Gọi `registerForPushNotifications()` để yêu cầu quyền gửi thông báo và đăng ký nhận thông báo đẩy. Đồng thời, đặt `Messaging.messaging().delegate = self` để xử lý token FCM.
  - `registerForPushNotifications()`:
    - Sử dụng `UNUserNotificationCenter.current().requestAuthorization` để xin quyền từ người dùng.
    - Nếu được cấp quyền, gọi `getNotificationSettings()`.
    - `getNotificationSettings()`: Kiểm tra trạng thái cấp quyền, nếu đã được cấp, gọi `UIApplication.shared.registerForRemoteNotifications()` để đăng ký với APNs.
    - Đặt `UNUserNotificationCenter.current().delegate = self` để xử lý các sự kiện liên quan đến thông báo.
- **Xử lý Token:**
  - `messaging(_:didReceiveRegistrationToken:)` (trong `MessagingDelegate`):
    - Nhận Firebase Cloud Messaging (FCM) token.
    - Lưu token này (`firebaseToken`).
    - In token ra console.
  - `application(_:didRegisterForRemoteNotificationsWithDeviceToken:)`:
    - Nhận device token từ APNs sau khi đăng ký thành công.
    - Lưu device token vào `UserDefaults`.
    - Nếu người dùng đã đăng nhập, gửi device token này lên server thông qua API `Account.registerDeviceToken` (`api/v1/erp/accounts/device-token/register`).
- **Xử lý Thông báo Nhận Được:**
  - `application(_:didReceiveRemoteNotification:fetchCompletionHandler:)` (trong `UNUserNotificationCenterDelegate` cho iOS 10+ khi app ở foreground hoặc background):
    - Được gọi khi nhận được thông báo đẩy.
    - In thông tin `userInfo` của thông báo ra console.
  - `userNotificationCenter(_:willPresent:withCompletionHandler:)` (trong `UNUserNotificationCenterDelegate` cho iOS 10+ khi app ở foreground):
    - Quyết định cách hiển thị thông báo khi app đang chạy ở foreground (thường là `.alert`, `.badge`, `.sound`).
  - `userNotificationCenter(_:didReceive:withCompletionHandler:)` (trong `UNUserNotificationCenterDelegate` cho iOS 10+ khi người dùng tương tác với thông báo):
    - Xử lý khi người dùng nhấn vào thông báo.
    - Lấy `ScreenCode` và `RefId` từ `userInfo` của thông báo.
    - Sử dụng `RouteManager` để điều hướng người dùng đến màn hình tương ứng dựa trên `ScreenCode` và `RefId`.
  - `application(_:didReceiveRemoteNotification:)` (phiên bản cũ hơn, hoặc khi app không ở foreground và không dùng `UNUserNotificationCenterDelegate` đầy đủ):
    - Xử lý thông báo khi app không ở trạng thái active.
    - Phân tích `userInfo` để lấy `id` (cho thông báo cũ) hoặc `sreenCode` và `refId` (cho thông báo mới) để điều hướng.
- **Nơi đặt:** Toàn bộ logic khởi tạo và xử lý thông báo đẩy được đặt trong <mcfile name="AppDelegate.swift" path="Booking/App/AppDelegate.swift"/> và chủ yếu trong phần mở rộng <mcfile name="AppDelegate+Initial.swift" path="Booking/App/AppDelegate+Initial.swift"/>.

- **Root View Controller**: `initAppRoute()` trong `App/AppDelegate+Initial.swift` khởi tạo `MainTabContainer` (trong `Class/Controller/MainTab/`) làm `rootViewController` của ứng dụng.
- **`Class/Controller/MainTab/MainTabContainer.swift`**: Đây là một `UITabBarController` tùy chỉnh (`MyTabController`) quản lý các tab chính của ứng dụng.
    - **Khởi tạo Tabs**: Trong hàm `setupView()`, `MainTabContainer` tạo ra 5 tab chính, mỗi tab là một `BaseNavigationViewController` chứa một View Controller gốc:
        1.  **Tab 1 (Trang chủ)**: `Class/Controller/Home/NewHomeViewController.swift`
        2.  **Tab 2 (Rạp phim)**: `Class/Controller/Cinema/CinemasViewController.swift`
        3.  **Tab 3 (Voucher của tôi)**: `Class/Controller/Voucher/MyVoucherViewController.swift`
        4.  **Tab 4 (Tin tức & Ưu đãi)**: `Class/Controller/Home/NewsAndDealsViewController.swift`
        5.  **Tab 5 (Khác)**: `Class/Controller/Other/TabOtherViewController.swift`
    - **Giao diện Tab Bar**: Sử dụng thư viện tùy chỉnh `MyTabController` để hiển thị tab bar.

## 3. Chức năng các Tab Chính

### 3.1. Tab 1: Trang chủ (`Class/Controller/Home/NewHomeViewController.swift`)

- **Thành phần phụ thuộc chính:**
    - **Views:** `RoundImageView` (Common/View/), `RoundButton` (Common/View/), `ImageSlideshow` (Lib/), `HomeCollectionViewCell` (Class/Controller/Home/Cell/)
    - **Models:** `FilmModel` (Class/Model/ResponseModel/), `NewsModel` (Class/Model/ResponseModel/), `Banner` (Class/Model/ResponseModel/)
    - **Managers:** `LocationManager` (Manager/Location/)
    - **Libraries:** `ObjectMapper`, `RxSwift`, `AlamofireImage`, `PullToRefreshKit`

- Hiển thị thông tin người dùng (avatar, tên, điểm, voucher) nếu đã đăng nhập, hoặc nút đăng nhập nếu chưa.
- Hiển thị banner quảng cáo (slideshow).
- Hiển thị danh sách phim theo các trạng thái: Đang chiếu (`showing`), Sắp chiếu (`comming`), Suất chiếu sớm (`early`). Người dùng có thể chuyển đổi giữa các trạng thái này.
- Cho phép người dùng kéo xuống để làm mới dữ liệu.
- Điều hướng đến màn hình chi tiết thành viên khi nhấn vào avatar.

- **Điều hướng khi chọn phim:**
    - Khi người dùng nhấn vào một bộ phim trong danh sách (Đang chiếu, Sắp chiếu, Suất chiếu sớm) tại `NewHomeViewController`:
        - **Màn hình đích:** Điều hướng đến `FilmDetailViewController` (đường dẫn: `Class/Controller/Film/FilmDetailViewController.swift`).
        - **Dữ liệu truyền đi:** `FilmModel` của bộ phim đã chọn được truyền vào thuộc tính `film` của `FilmDetailViewController`.
        - **Luồng Chi tiết Phim (`FilmDetailViewController`):**
            1.  **Khởi tạo và Hiển thị ban đầu:** 
                *   Màn hình được khởi tạo và nhận `FilmModel` (chứa thông tin cơ bản như `filmId`, tên phim, poster) từ `NewHomeViewController`.
                *   Hiển thị thông tin cơ bản này ngay lập tức (ví dụ: tên phim, poster).
            2.  **Gọi API lấy Chi tiết Phim:**
                *   Trong `viewDidLoad` hoặc `viewWillAppear`, gọi API `FilmProvider.rx.request(.detailFilm(filmId))` (tương ứng `GET api/v1/erp/films/{filmId}`)) để lấy thông tin chi tiết đầy đủ của phim.
                *   **Mục đích API:** Lấy dữ liệu đầy đủ bao gồm mô tả, đạo diễn, diễn viên, thể loại, thời lượng, danh sách trailer (YouTube ID), đánh giá, trạng thái phim, hình ảnh chi tiết, các phiên bản phim (2D, 3D), độ tuổi cho phép, v.v.
            3.  **Cập nhật và Hiển thị Thông tin Chi tiết:**
                *   Sau khi nhận được dữ liệu từ API, cập nhật `FilmModel` với thông tin chi tiết.
                *   Hiển thị các thông tin này lên giao diện: mô tả, thông tin diễn viên/đạo diễn, nút xem trailer (sử dụng `YoutubeParser` hoặc `youtube_ios_player_helper` để phát video), các nút chia sẻ, nút yêu thích.
                *   Nếu có nhiều trailer, có thể hiển thị danh sách trailer cho người dùng chọn.
                *   Nút "Đặt vé" (`btBooking`) được hiển thị.
            4.  **Xử lý nút "Đặt vé" (`actionBooking`):**
                *   Khi người dùng nhấn nút "Đặt vé":
                    *   **Kiểm tra đăng nhập:** Gọi `Account.isLogin()`.
                        *   Nếu chưa đăng nhập, điều hướng đến màn hình Đăng nhập (`LoginViewController` từ `Class/Controller/Authen/LoginViewController.swift`). Sau khi đăng nhập thành công, người dùng có thể được quay lại màn hình chi tiết phim hoặc luồng đặt vé (cần kiểm tra logic cụ thể).
                    *   Nếu đã đăng nhập:
                        *   **Điều hướng đến Chọn Rạp/Suất chiếu:**
                             *   Khởi tạo `CinemasViewController` (từ `Class/Controller/Cinema/CinemasViewController.swift`).
                            *   Truyền `FilmModel` (đã có thông tin chi tiết) vào thuộc tính `film` của `CinemasViewController`.
                            *   Đặt `isBooking = true` cho `CinemasViewController` để kích hoạt luồng đặt vé (thay vì chỉ xem thông tin rạp).
                            *   `CinemasViewController` sẽ được cấu hình để ưu tiên hiển thị các rạp đang chiếu bộ phim này.
                            *   **Luồng tiếp theo từ `CinemasViewController`:**
                                1.  Người dùng chọn một rạp từ danh sách trong `CinemasViewController`.
                                2.  Điều hướng đến `ChooseCinemasViewController` (đường dẫn: `Class/Controller/Cinema/ChooseCinemasViewController.swift`).
                                3.  **Dữ liệu truyền đi:** Cả `FilmModel` (từ `FilmDetailViewController`) và `CinemaModel` (rạp vừa được chọn) được truyền vào `ChooseCinemasViewController`.
                                4.  **Luồng trong `ChooseCinemasViewController` (khi có Phim và Rạp cụ thể):**
                                    *   **Khởi tạo:** Nhận `FilmModel` và `CinemaModel`.
                                    *   **Lấy Ngày có Suất chiếu cho Phim tại Rạp:**
                                        *   Gọi API để lấy danh sách các ngày có suất chiếu cho *bộ phim cụ thể* tại *rạp cụ thể* này. API có thể là `CinemaProvider.rx.request(.filmShowDate(cinemaId: cinema.CinemaId, filmId: film.filmId))` (ví dụ: `GET api/v1/erp/cinema/film/showdate?cinemaId={cinemaId}&filmId={filmId}`).
                                        *   Hiển thị các ngày này trên `CalendarHeaderView`.
                                    *   **Người dùng Chọn Ngày:**
                                        *   Người dùng chọn một ngày từ `CalendarHeaderView`.
                                    *   **Lấy Suất chiếu cho Phim, Rạp, Ngày đã chọn:**
                                        *   Gọi API để lấy danh sách các suất chiếu (`ShowModel`) cho *bộ phim cụ thể*, tại *rạp cụ thể*, vào *ngày đã chọn*. API có thể là `CinemaProvider.rx.request(.filmShowTime(cinemaId: cinema.CinemaId, filmId: film.filmId, showDate: dateString))` (ví dụ: `GET api/v1/erp/cinema/film/showtime?cinemaId={cinemaId}&filmId={filmId}&showDate={dateString}`).
                                        *   Hiển thị các suất chiếu này (ví dụ: trong `UITableView` hoặc `UICollectionView`), bao gồm thông tin giờ chiếu, định dạng (2D, 3D), ngôn ngữ, v.v.
                                    *   **Người dùng Chọn Suất chiếu:**
                                        *   Người dùng nhấn vào một suất chiếu cụ thể.
                                        *   Điều hướng đến `ChooseSeatViewController` (đường dẫn: `Class/Controller/Cinema/ChooseSeatViewController.swift`), truyền `ShowModel` của suất chiếu đã chọn.
- **API Endpoints chính:**
    - Lấy thông tin trang chủ (profile, điểm, voucher): `Account.homeInfo` (`api/v1/erp/accounts/info-hompage`)
    - Lấy banner: `Film.banner` (`api/v1/erp/banner-slider`)
    - Lấy danh sách phim (Đang chiếu, Sắp chiếu, Suất chiếu sớm): `Film.listFilm` (`GET api/v2/erp/films`) - Được gọi khi người dùng chuyển tab hoặc làm mới.
    - Lấy chi tiết phim (khi điều hướng sang `FilmDetailViewController`): `Film.detailFilm` (`GET api/v1/erp/films/{filmId}`)
- Điều hướng đến các màn hình khác dựa trên banner được nhấn.
- Sử dụng `LocationManager` để lấy vị trí người dùng (có thể ảnh hưởng đến việc hiển thị rạp gần đó hoặc các tính năng khác).

### 3.2. Tab 2: Rạp phim (`Class/Controller/Cinema/CinemasViewController.swift`)

- **Mô tả:** Hiển thị danh sách các rạp phim, cho phép người dùng xem thông tin chi tiết hoặc chọn rạp để đặt vé. Màn hình này có hai chế độ hiển thị chính: Rạp gần bạn và Theo khu vực.
- **Thành phần phụ thuộc chính:**
    - **Views:** `CinemaCollectionViewCell` (Class/Controller/Cinema/Cell/), `CinemaByProvinceTableViewCell` (Class/Controller/Cinema/Cell/), `LocalizableLabel` (Common/Localizable/)
    - **Models:** `CinemaModel` (Class/Model/ResponseModel/), `CinemaProvinceModel` (Class/Model/ResponseModel/)
    - **Managers:** `LocationManager` (Manager/Location/LocationManager.swift) - Để lấy vị trí hiện tại của người dùng.
    - **Controllers:** `ChooseCinemasViewController` (Class/Controller/Cinema/), `CinemaDetailViewController` (Class/Controller/Cinema/)
    - **API Endpoints chính:**
        - `GET /cinema/list`: Lấy danh sách rạp theo khu vực.
        - `GET /cinema/near`: Lấy danh sách rạp gần vị trí người dùng.
        - `GET /cinema/showdate`: Lấy danh sách ngày có suất chiếu của một rạp cụ thể (sử dụng trong `ChooseCinemasViewController`).
        - `GET /cinema/filmshow`: Lấy danh sách phim và suất chiếu của một rạp vào một ngày cụ thể (sử dụng trong `ChooseCinemasViewController`).
- **Luồng chính:**
    1.  **Khởi tạo & Yêu cầu vị trí**: Khi màn hình được tải (`viewDidLoad`), kiểm tra quyền truy cập vị trí. Nếu được phép, yêu cầu vị trí hiện tại thông qua `LocationManager`.
    2.  **Gọi API**: Gọi API `/cinema/near` (nếu có vị trí) hoặc `/cinema/list` để lấy danh sách rạp.
    3.  **Hiển thị**: Hiển thị danh sách rạp theo chế độ "Rạp gần bạn" hoặc "Theo khu vực" sử dụng `CinemaCollectionViewCell` hoặc `CinemaByProvinceTableViewCell`.
    4.  **Chọn rạp**: Khi người dùng chọn một rạp, điều hướng đến `ChooseCinemasViewController`.

#### 3.2.1. Chọn Suất chiếu (`Class/Controller/Cinema/ChooseCinemasViewController.swift`)

- **Mô tả:** Hiển thị lịch chiếu phim của một rạp đã chọn.
- **Thành phần phụ thuộc chính:**
    - **Views:** `CalendarHeaderView` (Common/View/CalendarHeaderView.swift), `FilmTimeTableViewCell` (Class/Controller/Cinema/Cell/FilmTimeTableViewCell.swift)
    - **Models:** `CinemaModel` (Class/Model/ResponseModel/CinemaModel.swift), `ShowModel` (Class/Model/ResponseModel/ShowModel.swift), `ShowFilmModel` (Class/Model/ResponseModel/ShowFilmModel.swift)
    - **Controllers:** `ChooseSeatViewController` (Class/Controller/Cinema/)
- **Luồng chính:**
    1.  **Hiển thị tên rạp**: Lấy thông tin `CinemaModel` từ màn hình trước và hiển thị tên rạp.
    2.  **Lấy danh sách ngày chiếu**: Gọi API `GET /cinema/showdate` để lấy các ngày có suất chiếu tại rạp.
    3.  **Hiển thị lịch**: Sử dụng `CalendarHeaderView` để hiển thị các ngày có suất chiếu, cho phép người dùng chọn ngày.
    4.  **Lấy danh sách phim và suất chiếu**: Khi người dùng chọn một ngày, gọi API `GET /cinema/filmshow` để lấy danh sách phim (`ShowFilmModel`) và các suất chiếu (`ShowModel`) tương ứng.
    5.  **Hiển thị danh sách phim và suất chiếu**: Sử dụng `UITableView` với `FilmTimeTableViewCell` để hiển thị thông tin phim (poster, tên, thể loại,...) và các suất chiếu khả dụng cho ngày đã chọn.
    6.  **Chọn suất chiếu**: Khi người dùng chọn một suất chiếu, điều hướng đến `ChooseSeatViewController`.

- **API Endpoints chính:**
    - `GET /cinema/showdate`: Lấy danh sách ngày có suất chiếu của rạp.
    - `GET /cinema/filmshow`: Lấy danh sách phim và suất chiếu theo ngày.
        *   **Khi có vị trí**: Sau khi nhận được tọa độ (latitude, longitude) từ `LocationManager`, gọi API `Cinema.listCinema` để lấy danh sách các rạp gần đó.
        *   **Luôn gọi**: Gọi API `Cinema.listCinemaByProvince` để lấy danh sách tất cả rạp phim được nhóm theo tỉnh/thành phố.
    3.  **Hiển thị dữ liệu**:
        *   **Rạp gần bạn**: Dữ liệu từ `Cinema.listCinema` được hiển thị trong một `UICollectionView` (`nearbyCollectionView`) sử dụng `CinemaCollectionViewCell`. Các rạp được sắp xếp theo khoảng cách tăng dần. Mục này chỉ hiển thị nếu có dữ liệu vị trí và API trả về kết quả.
        *   **Theo khu vực**: Dữ liệu từ `Cinema.listCinemaByProvince` được hiển thị trong một `UITableView` (`tableView`) sử dụng `CinemaByProvinceTableViewCell`. Mỗi tỉnh/thành phố là một section, người dùng có thể nhấn để mở rộng/thu gọn danh sách rạp trong khu vực đó.
    4.  **Tương tác người dùng**:
        *   **Chọn rạp**: Khi người dùng nhấn vào một rạp (trong `UICollectionView` hoặc `UITableView`):
            *   Nếu `isBooking` là `true` (thường là khi truy cập từ Tab Bar chính): Điều hướng đến `ChooseCinemasViewController` với thông tin rạp đã chọn để tiếp tục luồng đặt vé.
            *   Nếu `isBooking` là `false` (ví dụ: truy cập từ Tab "Khác"): Điều hướng đến `CinemaDetailViewController` để hiển thị thông tin chi tiết về rạp.
        *   **Làm mới**: Người dùng có thể kéo xuống để làm mới (`PullToRefreshKit`), kích hoạt lại việc lấy vị trí và gọi lại các API.
- **API Endpoints chính:**
    - **Lấy danh sách rạp gần đây**: `Cinema.listCinema` (`api/v1/erp/cinemas`)
        - **Phương thức**: GET
        - **Tham số**: `lat` (kinh độ), `long` (vĩ độ), `page`, `limit` (mặc định thường là 1 và 10).
        - **Mục đích**: Lấy danh sách các rạp phim gần tọa độ cung cấp.
    - **Lấy danh sách rạp theo tỉnh/thành phố**: `Cinema.listCinemaByProvince` (`api/v1/erp/cites/cinemas`)
        - **Phương thức**: GET
        - **Tham số**: Không có tham số bắt buộc rõ ràng trong URL (có thể có header hoặc tham số ngầm định).
        - **Mục đích**: Lấy danh sách tất cả các rạp phim, được nhóm theo tỉnh/thành.

- **Thành phần phụ thuộc chính:**
    - **Views:** `CinemaCollectionViewCell` (Class/Controller/Cinema/Cell/), `CinemaByProvinceTableViewCell` (Class/Controller/Cinema/Cell/), `LocalizableLabel` (Common/Localizable/)
    - **Models:** `CinemaModel`, `CinemaProvinceModel` (Class/Model/ResponseModel/)
    - **Managers:** `LocationManager` (Manager/Location/)
    - **Controllers:** `ChooseCinemasViewController`, `CinemaDetailViewController`
- **API Endpoints chính (được gọi từ màn hình này):**
    - `GET api/v1/erp/cinemas` (thông qua `CinemaProvider.rx.request(.listCinema)`): Lấy danh sách rạp gần vị trí.
        - **Tham số:** `lat`, `long` (từ `LocationManager`).
    - `GET api/v1/erp/cites/cinemas` (thông qua `CinemaProvider.rx.request(.listCinemaByProvince)`): Lấy danh sách rạp theo tỉnh/thành.

#### 3.2.1. Luồng con: Chọn Suất chiếu (`Class/Controller/Cinema/ChooseCinemasViewController.swift`)

- **Mô tả:** Màn hình cho phép người dùng chọn ngày và suất chiếu phim tại một rạp cụ thể đã chọn từ `CinemasViewController` (khi `isBooking = true`).
- **Đường dẫn:** `Class/Controller/Cinema/ChooseCinemasViewController.swift`
- **Input:** Nhận `CinemaModel` từ `CinemasViewController`.
- **Luồng chính:**
    1.  **Khởi tạo & Lấy ngày chiếu:**
        *   Hiển thị tên rạp (`lbCinemaName`).
        *   Gọi API để lấy danh sách các ngày có suất chiếu tại rạp này: `CinemaProvider.rx.request(.cinemaShowDate(cinema.CinemaId))` (Tương ứng API `GET api/v1/erp/cinema/showdate`).
        *   Hiển thị các ngày này trên `CalendarHeaderView`.
    2.  **Chọn ngày & Lấy suất chiếu:**
        *   Khi người dùng chọn một ngày trên `CalendarHeaderView`:
        *   Gọi API để lấy danh sách phim và suất chiếu cho ngày đã chọn: `CinemaProvider.rx.request(.cinemaFilmShow(cinema.CinemaId, dateString))` (Tương ứng API `GET api/v1/erp/cinema/filmshow`).
        *   Hiển thị kết quả trong `UITableView` sử dụng `FilmTimeTableViewCell` (`Class/Controller/Cinema/Cell/FilmTimeTableViewCell.swift`). Mỗi cell hiển thị thông tin một phim và các suất chiếu của phim đó trong ngày.
    3.  **Chọn suất chiếu:**
        *   Khi người dùng nhấn vào một suất chiếu cụ thể (trong `FilmTimeTableViewCell`):
        *   Kiểm tra xem suất chiếu có còn hợp lệ không (chưa quá giờ).
        *   Kiểm tra đăng nhập. Nếu chưa đăng nhập, điều hướng đến màn hình Login (`Class/Controller/Authen/LoginViewController.swift`).
        - **API sử dụng:**
            - **Endpoint:** `api/v1/erp/accounts/login` (Định nghĩa trong `Manager/Network/Moya/AccountAPI.swift`)
            - **Phương thức:** `POST`
            - **Parameters:** `userName` (email), `password`, `token` (recaptcha token - Mã nguồn có tích hợp thư viện ReCaptcha nhưng hiện tại đang truyền token rỗng, bỏ qua bước xác thực captcha)
        *    Nếu hợp lệ và đã đăng nhập, điều hướng đến `ChooseSeatViewController` (`Class/Controller/Cinema/ChooseSeatViewController.swift`) với thông tin `cinemaId`, `cinemaName`, `showTime` (ShowModel), `film` (FilmInformation), `listSeat` (ListSeatModel), `filmFormat`, `timeStartBooking`, `seatsPrice`.
- **Thành phần phụ thuộc chính:**
    - **Views:** `CalendarHeaderView` (Common/View/CalendarHeaderView/), `FilmTimeTableViewCell`
    - **Models:** `CinemaModel`, `ShowFilmModel`, `ShowModel` (Class/Model/ResponseModel/)
    - **Controllers:** `ChooseSeatViewController`, `LoginViewController`
- **API Endpoints chính (được gọi từ màn hình này):**
    - `GET api/v1/erp/cinema/showdate`: Lấy danh sách ngày có suất chiếu.
        - **Tham số:** `cinemaId`.
    - `GET api/v1/erp/cinema/filmshow`: Lấy danh sách phim và suất chiếu theo ngày.
        - **Tham số:** `cinemaId`, `date` (định dạng "yyyy-MM-dd").
        

### Luồng Xác Nhận VIP/Zoom (VIP Zoom Flow)

- **Mô tả:** Xử lý việc hiển thị thông báo và yêu cầu xác nhận từ người dùng khi họ chọn một suất chiếu đặc biệt (ví dụ: VIP, Zoom) có thể có các điều kiện hoặc thông tin riêng cần người dùng lưu ý trước khi tiếp tục.
- **Trigger:** Kích hoạt khi người dùng chọn một suất chiếu từ <mcsymbol name="ChooseCinemasViewController" path="Booking/Class/Controller/Cinema/ChooseCinemasViewController.swift" type="class"></mcsymbol> hoặc <mcsymbol name="FilmChooseTimeViewController" path="Booking/Class/Controller/Film/FilmChooseTimeViewController.swift" type="class"></mcsymbol> và suất chiếu đó được đánh dấu là có thông điệp đặc biệt (thường là các suất chiếu VIP hoặc Zoom).
- **Controller chính:** <mcfile name="ConfirmVipZoomViewController.swift" path="Booking/Class/Controller/Cinema/ConfirmVipZoomViewController.swift"></mcfile>
- **UI:** Hiển thị dưới dạng một `PopupDialog` với các thành phần:
    - Tiêu đề (ví dụ: "Thông báo", "Lưu ý").
    - Hình ảnh liên quan đến suất chiếu đặc biệt (nếu có, ví dụ: `pathImage` từ <mcsymbol name="ShowModel" type="class"></mcsymbol>).
    - Nội dung thông báo/điều kiện (ví dụ: `message` từ <mcsymbol name="ShowModel" type="class"></mcsymbol>).
    - Nút "Huỷ bỏ" (Cancel).
    - Nút "Đồng ý" (Yes) hoặc "Tiếp tục".
- **Luồng hoạt động:**
    1.  **Khởi tạo và Hiển thị:**
        *   Từ <mcsymbol name="ChooseCinemasViewController" path="Booking/Class/Controller/Cinema/ChooseCinemasViewController.swift" type="class"></mcsymbol> (trong hàm <mcsymbol name="updateMessage" filename="ChooseCinemasViewController.swift" path="Booking/Class/Controller/Cinema/ChooseCinemasViewController.swift" type="function"></mcsymbol>) hoặc <mcsymbol name="FilmChooseTimeViewController" path="Booking/Class/Controller/Film/FilmChooseTimeViewController.swift" type="class"></mcsymbol>, khi một suất chiếu đặc biệt được chọn, một instance của <mcsymbol name="ConfirmVipZoomViewController" path="Booking/Class/Controller/Cinema/ConfirmVipZoomViewController.swift" type="class"></mcsymbol> được tạo thông qua `UIStoryboard.cinema[.confirmVipZoom]`.
        *   Các thuộc tính `titleZoom`, `messageZoomLabel` (message), và `zoomImage` (pathImage) của <mcsymbol name="ConfirmVipZoomViewController" path="Booking/Class/Controller/Cinema/ConfirmVipZoomViewController.swift" type="class"></mcsymbol> được thiết lập dựa trên thông tin từ <mcsymbol name="ShowModel" type="class"></mcsymbol> của suất chiếu đó.
        *   <mcsymbol name="ConfirmVipZoomViewController" path="Booking/Class/Controller/Cinema/ConfirmVipZoomViewController.swift" type="class"></mcsymbol> được trình bày dưới dạng `PopupDialog`.
    2.  **Tương tác người dùng:**
        *   **Nút "Huỷ bỏ":** Đóng popup, người dùng quay lại màn hình chọn suất chiếu.
        *   **Nút "Đồng ý"/"Tiếp tục":**
            *   Kích hoạt hàm `setupLogin()` (trong <mcsymbol name="ChooseCinemasViewController" path="Booking/Class/Controller/Cinema/ChooseCinemasViewController.swift" type="class"></mcsymbol> hoặc <mcsymbol name="FilmChooseTimeViewController" path="Booking/Class/Controller/Film/FilmChooseTimeViewController.swift" type="class"></mcsymbol>).
            *   `setupLogin()` kiểm tra trạng thái đăng nhập của người dùng:
                *   Nếu đã đăng nhập: Điều hướng đến màn hình chọn ghế (<mcsymbol name="ChooseSeatViewController" path="Booking/Class/Controller/Cinema/ChooseSeatViewController.swift" type="class"></mcsymbol>).
                *   Nếu chưa đăng nhập: Điều hướng đến màn hình đăng nhập (<mcsymbol name="LoginViewController" path="Booking/Class/Controller/Account/LoginViewController.swift" type="class"></mcsymbol>). Sau khi đăng nhập thành công, người dùng có thể được điều hướng tiếp đến màn hình chọn ghế.
        *   **Chạm vào nội dung thông báo (nếu có link chính sách):**
            *   <mcsymbol name="ConfirmVipZoomViewController" path="Booking/Class/Controller/Cinema/ConfirmVipZoomViewController.swift" type="class"></mcsymbol> có một `UITapGestureRecognizer` trên `messageZoomLabel`.
            *   Nếu người dùng chạm vào phần link chính sách trong nội dung, phương thức <mcsymbol name="checkTouchOnPolicy" filename="ConfirmVipZoomViewController.swift" path="Booking/Class/Controller/Cinema/ConfirmVipZoomViewController.swift" type="function"></mcsymbol> được gọi.
            *   Nếu chạm đúng link, phương thức <mcsymbol name="openRule" filename="ConfirmVipZoomViewController.swift" path="Booking/Class/Controller/Cinema/ConfirmVipZoomViewController.swift" type="function"></mcsymbol> được gọi, điều hướng người dùng đến <mcsymbol name="OtherViewController" path="Booking/Class/Controller/Other/OtherViewController.swift" type="class"></mcsymbol> để hiển thị nội dung chính sách (`OtherViewControllerType.PolicyPayment`).
- **Input (cho <mcsymbol name="ConfirmVipZoomViewController" path="Booking/Class/Controller/Cinema/ConfirmVipZoomViewController.swift" type="class"></mcsymbol>):**
    - `titleZoom`: Tiêu đề của popup.
    - `message`: Nội dung thông báo/điều kiện.
    - `pathImage`: Đường dẫn đến hình ảnh (nếu có).
- **Output/Hành động tiếp theo:**
    - Điều hướng đến <mcsymbol name="ChooseSeatViewController" path="Booking/Class/Controller/Cinema/ChooseSeatViewController.swift" type="class"></mcsymbol> (nếu đồng ý và đã đăng nhập).
    - Điều hướng đến <mcsymbol name="LoginViewController" path="Booking/Class/Controller/Account/LoginViewController.swift" type="class"></mcsymbol> (nếu đồng ý và chưa đăng nhập).
    - Điều hướng đến <mcsymbol name="OtherViewController" path="Booking/Class/Controller/Other/OtherViewController.swift" type="class"></mcsymbol> (nếu xem chính sách).
    - Quay lại màn hình trước (nếu hủy bỏ).
- **Thành phần phụ thuộc:**
    - <mcsymbol name="ShowModel" type="class"></mcsymbol>: Cung cấp thông tin cho popup.
    - `PopupDialog`: Thư viện để hiển thị popup.
    - <mcsymbol name="ChooseCinemasViewController" path="Booking/Class/Controller/Cinema/ChooseCinemasViewController.swift" type="class"></mcsymbol>, <mcsymbol name="FilmChooseTimeViewController" path="Booking/Class/Controller/Film/FilmChooseTimeViewController.swift" type="class"></mcsymbol>: Nơi gọi và trình bày <mcsymbol name="ConfirmVipZoomViewController" path="Booking/Class/Controller/Cinema/ConfirmVipZoomViewController.swift" type="class"></mcsymbol>.
    - <mcsymbol name="LoginViewController" path="Booking/Class/Controller/Account/LoginViewController.swift" type="class"></mcsymbol>, <mcsymbol name="ChooseSeatViewController" path="Booking/Class/Controller/Cinema/ChooseSeatViewController.swift" type="class"></mcsymbol>, <mcsymbol name="OtherViewController" path="Booking/Class/Controller/Other/OtherViewController.swift" type="class"></mcsymbol>: Các màn hình điều hướng tiếp theo.

#### *******. Luồng con: Chọn Ghế Ngồi (`Class/Controller/Cinema/ChooseSeatViewController.swift`)

- **Mô tả:** Màn hình cho phép người dùng chọn ghế ngồi cho một suất chiếu cụ thể.
- **Input:** Nhận `cinemaId`, `cinemaName`, `showTime` (ShowModel), `film` (FilmInformation) từ `ChooseCinemasViewController` hoặc `FilmChooseTimeViewController`.
- **Luồng chính:**
    1.  **Khởi tạo:**
        *   Hiển thị thông tin phim, suất chiếu, tên rạp.
           *   Khởi tạo kết nối `SignalR` đến `chooseSeatHub` (được định nghĩa trong <mcfile name="Config.swift" path="Booking/Common/Config.swift"/> là `Config.SignalRURL`) để cập nhật trạng thái ghế real-time. Quá trình khởi tạo được thực hiện trong <mcsymbol name="ChooseSeatViewController" filename="ChooseSeatViewController.swift" path="Booking/Class/Controller/Cinema/ChooseSeatViewController.swift" startline="13" type="class"/>:
    *   Sử dụng `HubConnection(withUrl: Config.SignalRURL)` để tạo đối tượng `hubConnection`.
    *   Tạo một `hubProxy` với tên `chooseSeatHub` thông qua `hubConnection.createHubProxy(hubName: "chooseSeatHub")`.
    *   Đăng ký xử lý sự kiện `broadcastMessage` từ hub để nhận thông tin cập nhật trạng thái ghế từ các client khác.
    *   Định nghĩa các closures để xử lý các trạng thái của kết nối SignalR:
        *   `hubConnection.started`: Được gọi khi kết nối thành công.
        *   `hubConnection.reconnecting`: Được gọi khi kết nối đang cố gắng thiết lập lại.
        *   `hubConnection.reconnected`: Được gọi khi kết nối đã được thiết lập lại thành công.
        *   `hubConnection.closed`: Được gọi khi kết nối bị đóng.
        *   `hubConnection.connectionSlow`: Được gọi khi kết nối chậm.
        *   `hubConnection.error`: Được gọi khi có lỗi xảy ra với kết nối.
    *   Gọi `hubConnection.start()` để bắt đầu quá trình kết nối.
        *   Bắt đầu bộ đếm thời gian giữ ghế (ví dụ: `Config.TimeExpired` là 10 phút).
    2.  **Lấy Sơ đồ Ghế & Giá vé:**
        *   Gọi API `Film.seat` (thông qua `FilmProvider.rx.request(.seat(showTime.showId, cinemaId))`) để lấy sơ đồ ghế (`ListSeatModel` chứa danh sách `SeatModel`) và các loại vé (`TicketType`).
        *   Hiển thị sơ đồ ghế lên `UICollectionView` (sử dụng `ColumnLayout`).
        *   Hiển thị các loại giá vé (Thường, VIP, Couple).
    3.  **Chọn/Bỏ chọn ghế:**
        *   Người dùng tương tác với `UICollectionView` để chọn hoặc bỏ chọn ghế.
        *   Khi chọn/bỏ chọn:
            *   Cập nhật UI của ghế (ví dụ: đổi màu).
            *   Thêm/xóa `SeatModel` khỏi danh sách `selectedSeats`.
            *   Gửi thông điệp qua `SignalR` (`hub.invoke("selectSeat", arguments: [showTime.showId, seat.index, seatStatus])`) để thông báo cho các client khác về việc ghế đang được chọn/bỏ chọn.
            *   Tính toán lại tổng tiền (`totalPrice`).
            *   Hiển thị danh sách ghế đã chọn và tổng tiền.
    4.  **Nhận cập nhật từ SignalR:**
        *   Lắng nghe sự kiện `broadcastMessage` từ `chooseSeatHub`.
        *   Nếu có thông báo ghế được người khác chọn/đã bán/đã hủy chọn, cập nhật lại trạng thái của ghế đó trên UI.
    5.  **Hết thời gian giữ ghế:**
        *   Bộ đếm thời gian `timeLeftLabel` (được cập nhật mỗi giây) về 0.
        *   Hiển thị cảnh báo hết thời gian.
        *   Gọi `removeAllSeatsAndStop()` để hủy các ghế đã chọn qua SignalR (`hub.invoke("removeAllSeat", arguments: [showTime.showId])`).
        *   Điều hướng người dùng về màn hình trước hoặc màn hình chính.
    6.  **Xử lý nút "Tiếp tục" (hoặc tương tự):**
        *   Kiểm tra người dùng đã đăng nhập chưa. Nếu chưa, điều hướng đến `LoginViewController`.
        *   Nếu đã đăng nhập và có ghế được chọn:
            *   Điều hướng đến `PaymentViewController`.
            *   Truyền dữ liệu: `show` (ShowModel), `seat` (danh sách `SeatModel` đã chọn), `ticketType` (thông tin loại vé), `film` (FilmInformation), `cinemaId`, `cinemaName`, `listSeat` (ListSeatModel), `filmFormat`, `timeStartBooking` (thời điểm bắt đầu chọn ghế), `seatsPrice` (tổng tiền ghế).
- **Thành phần phụ thuộc chính:**
    - **Views:** `GradientView`, `ColumnLayout`.
    - **Models:** `ShowModel`, `FilmInformation`, `ListSeatModel`, `SeatModel`, `TicketType`.
    - **Libraries:** `SignalRSwift` (hoặc `SwiftSignalRClient`), `PopupDialog`.
    - **Controllers:** `PaymentViewController`, `LoginViewController`.
    - **Hình ảnh & Tài nguyên (từ `Resource/Assets.xcassets/Booking/`):**
        *   `ivFilmBanner`: Hiển thị banner của phim (hình ảnh được load từ URL).
        *   `ic_screen.pdf`: Hình ảnh biểu thị màn hình chiếu phim.
        *   Ghế thường: `ic_empty_normal_seat.pdf` (trống), `ic_select_normal_seat.pdf` (đang chọn), `ic_set_normal_seat.pdf` (người khác đang chọn), `ic_sold_normal_seat.pdf` (đã bán).
        *   Ghế VIP: `ic_empty_vip_seat.pdf` (trống), `ic_select_vip_seat.pdf` (đang chọn), `ic_set_vip_seat.pdf` (người khác đang chọn), `ic_sold_vip_seat.pdf` (đã bán).
        *   Ghế đôi: `ic_empty_couple_seat.pdf` (trống), `ic_select_couple_seat.pdf` (đang chọn), `ic_set_couple_seat.pdf` (người khác đang chọn), `ic_sold_couple_seat.pdf` (đã bán).
        *   `ic_process_vip_seat.pdf`: Có thể được sử dụng cho trạng thái ghế đang xử lý (cần xác minh thêm).
        *   Các nút điều hướng: `ic_back.imageset`.
- **API Endpoints & Real-time:**
    - **Lấy sơ đồ ghế:** `Film.seat` (ví dụ: `GET api/v1/erp/film/seatmap?showId=xxx&cinemaId=yyy`). Endpoint cụ thể cần xem trong `FilmAPI.swift`.
    - **SignalR Hub:** `chooseSeatHub` tại `Config.SignalRURL`.
        - **Phương thức gọi đi (Invoke):** `selectSeat`, `removeAllSeat`.
        - **Phương thức nhận (On):** `broadcastMessage`.

#### *******. Luồng con: Thanh toán (`Class/Controller/Payment/PaymentViewController.swift`)

- **Mô tả:** Màn hình xử lý việc thanh toán cho các vé đã chọn.
- **Input:** Nhận `show`, `seat`, `ticketType`, `film`, `cinemaId`, `cinemaName`, `listSeat`, `filmFormat`, `timeStartBooking`, `seatsPrice` từ `ChooseSeatViewController`.
- **Luồng chính:**
    1.  **Khởi tạo:**
        *   Thiết lập `WKWebView` để hiển thị cổng thanh toán.
        *   Đăng ký `WKScriptMessageHandler` (tên là `scriptHandler`) để nhận thông điệp từ JavaScript của trang web thanh toán.
        *   Đăng ký observer cho các sự kiện của app (ví dụ: `UIApplicationDidBecomeActive`) và notification từ các ví điện tử (Momo, ZaloPay) để kiểm tra lại trạng thái giao dịch.
    2.  **Tạo Giao dịch & Lấy Cổng Thanh toán:**
        *   Tạo `CreateBookingModel` từ thông tin nhận được (bao gồm `showId`, danh sách `SeatBookingModel` (ghế và loại vé), `expiredTime`).
        *   Gọi API `Film.booking` (thông qua `FilmProvider.rx.request(.booking(bookingModel))`) để tạo giao dịch trên server và nhận về nội dung HTML của cổng thanh toán hoặc URL để redirect.
        *   Lưu nội dung HTML này vào biến `webData`.
        *   Load nội dung HTML vào `WKWebView` với `baseURL` là `Config.BaseURL`.
    3.  **Tương tác với Cổng Thanh toán (trong `WKWebView`):**
        *   Người dùng thực hiện các bước trên giao diện web của cổng thanh toán (chọn phương thức, nhập thông tin thẻ/ví, OTP,...).
        *   `WKWebView` điều hướng qua các trang của cổng thanh toán.
    4.  **Xử lý Callback từ Web (qua `WKScriptMessageHandler` hoặc `WKNavigationDelegate`):**
        *   Cổng thanh toán (sau khi hoàn tất hoặc có lỗi) sẽ gọi JavaScript để gửi thông điệp đến native thông qua `window.webkit.messageHandlers.scriptHandler.postMessage(...)` hoặc redirect đến một URL đặc biệt.
        *   Hàm `userContentController(_:didReceive:)` sẽ nhận được thông điệp này.
        *   Phân tích thông điệp để biết trạng thái thanh toán (thành công, thất bại, chờ xử lý, phương thức thanh toán được chọn).
        *   **Ví dụ các kịch bản:**
            *   **Thanh toán thành công:** Web trả về trạng thái thành công. Điều hướng đến màn hình vé của tôi hoặc lịch sử giao dịch.
            *   **Thất bại:** Web trả về lỗi. Hiển thị thông báo lỗi.
            *   **Chọn ví điện tử (Momo, ZaloPay, AirPay):** Web có thể trả về thông tin cần thiết để mở ứng dụng ví. Sau đó, `PaymentViewController` sẽ lắng nghe `NotificationCenter` hoặc `applicationDidBecomeActive` để kiểm tra kết quả từ ứng dụng ví.
    5.  **Kiểm tra Trạng thái Giao dịch (cho ví điện tử):**
        *   Khi ứng dụng active trở lại (sau khi từ ví điện tử quay về) hoặc nhận được notification từ ví:
            *   Gọi các hàm như `checkAirPayPurchaseStatus()`, `checkMomoPurchaseStatus()`, `checkZaloPayPurchaseStatus()`.
            *   Các hàm này có thể gọi API của Beta Cinema hoặc API của ví để xác nhận trạng thái cuối cùng của giao dịch.
    6.  **Xử lý Nút Back:**
        *   Ưu tiên cho `WKWebView` tự xử lý back (`webView.canGoBack`).
        *   Nếu không thể back trong web và URL hiện tại không phải là trang thanh toán ban đầu, load lại `webData`.
        *   Nếu không, hiển thị cảnh báo hủy giao dịch và cho phép người dùng quay lại màn hình trước.
- **Thành phần phụ thuộc chính:**
    - **Views:** `WKWebView`.
    - **Models:** `ShowModel`, `SeatModel`, `TicketType`, `FilmInformation`, `ListSeatModel`, `CreateBookingModel`, `SeatBookingModel`.
    - **Libraries:** `WebKit`, `SwiftDate`, `PKHUD`.
- **API Endpoints chính:**
    - **Tạo giao dịch & lấy cổng thanh toán:** `Film.booking` (ví dụ: `POST api/v1/erp/orders/create` hoặc `POST api/v1/erp/payments/initiate`). Endpoint cụ thể cần xem trong `FilmAPI.swift`.
    - **Kiểm tra trạng thái giao dịch (có thể có):** Các API riêng để kiểm tra trạng thái với Momo, ZaloPay, AirPay nếu cổng thanh toán của Beta Cinema không tự xử lý callback cuối cùng. (Ví dụ: `Payment.checkMomoStatus`, `Payment.checkZaloPayStatus`).


#### 3.2.2. Luồng con: Xem Chi tiết Rạp (`Class/Controller/Cinema/CinemaDetailViewController.swift`)

- **Mô tả:** Hiển thị thông tin chi tiết về một rạp phim (địa chỉ, bản đồ, hình ảnh, tiện ích,...). Kích hoạt khi chọn rạp từ `CinemasViewController` với `isBooking = false`.
- **Đường dẫn:** `Class/Controller/Cinema/CinemaDetailViewController.swift`
- **Input:** Nhận `CinemaModel` từ `CinemasViewController`.
- **Luồng chính:** (Cần phân tích thêm `CinemaDetailViewController.swift` để biết chi tiết API và luồng cụ thể nếu cần).
- **API Endpoints chính:** (Cần xác định API lấy chi tiết rạp nếu có).

### 3.3. Tab 3: Voucher của tôi (`Class/Controller/Voucher/MyVoucherViewController.swift`)

- **Mô tả:** Quản lý các voucher mà người dùng sở hữu.
- **Thành phần phụ thuộc chính:**
    - **Views:** `MyVoucherTableViewCell` (Class/Controller/Voucher/Cell/), `GradientButton` (Common/View/)
    - **Models:** `VoucherModel` (Class/Model/ResponseModel/)
- **Luồng chính:**
    - Kiểm tra trạng thái đăng nhập. Nếu chưa đăng nhập, hiển thị thông báo yêu cầu đăng nhập.
    - Gọi API để lấy danh sách voucher của người dùng.
    - Hiển thị danh sách voucher trên `UITableView`. Nếu không có voucher, hiển thị trạng thái trống.
    - Cho phép người dùng xem chi tiết voucher, sử dụng voucher (`UseVoucherViewController`), tặng voucher (`DonateVoucherViewController`), thêm voucher mới (`AddVoucherViewController`), xem lịch sử voucher (`HistoryVoucherViewController`), hoặc xem voucher miễn phí (`FreeVoucherViewController`).
- **API Endpoints chính:**
    - Lấy danh sách voucher của người dùng: `Voucher.getVoucher` (`api/v2/erp/voucher`)
    - (Các màn hình con): `Voucher.registerVoucher`, `Voucher.donate`, `Voucher.history`, `Ecm.getFreeVoucher`

- **Thành phần phụ thuộc chính:**
    - **Views:** `MyVoucherTableViewCell` (Class/Controller/Voucher/Cell/), `GradientButton` (Common/View/)
    - **Models:** `VoucherModel` (Class/Model/ResponseModel/)
    - **Controllers:** `AddVoucherViewController`, `HistoryVoucherViewController`, `UseVoucherViewController`, `DonateVoucherViewController`, `NewsDetailViewController`, `FreeVoucherViewController` (đều trong `Class/Controller/Voucher/` hoặc `Class/Controller/Home/`)

- Yêu cầu đăng nhập để xem voucher.
- Hiển thị danh sách các voucher hiện có của người dùng (`VoucherModel`).
- Hiển thị trạng thái voucher (còn hạn, hết hạn).
- Sử dụng `MyVoucherTableViewCell` để hiển thị từng voucher.
- Cho phép người dùng:
    - **Thêm voucher**: Điều hướng đến `AddVoucherViewController`.
    - **Xem lịch sử voucher**: Điều hướng đến `HistoryVoucherViewController`.
    - **Sử dụng voucher**: Điều hướng đến `UseVoucherViewController`.
    - **Tặng voucher**: Điều hướng đến `DonateVoucherViewController`.
    - **Xem chi tiết voucher/khuyến mãi liên quan**: Điều hướng đến `NewsDetailViewController`.
    - **Nhận voucher miễn phí**: Điều hướng đến `FreeVoucherViewController` (cũng có thể truy cập từ nút `GradientButton` trên màn hình trống).
- Hiển thị màn hình trống (`emptyView`) nếu không có voucher hoặc chưa đăng nhập.

### 3.4. Tab 4: Tin tức & Ưu đãi (`Class/Controller/Home/NewsAndDealsViewController.swift` -> Sử dụng `ListNewsViewController`)

- **Mục đích:** Hiển thị các tin tức và chương trình khuyến mãi mới nhất từ Beta Cinemas.
- **Luồng chính:**
    1.  `NewsAndDealsViewController` được khởi tạo làm root view controller cho Tab 4.
    2.  Sử dụng `ScrollPager` để tạo 2 tab con: "Khuyến mãi" và "Tin tức".
    3.  Mỗi tab con sử dụng một instance của `ListNewsViewController` để hiển thị danh sách tương ứng.
    4.  **Lấy dữ liệu (API định nghĩa trong `Manager/Network/Moya/EcmAPI.swift`):**
        - **Bước 1: Lấy ID Danh mục:**
            - `NewsAndDealsViewController` gọi `Ecm.getNewEvent` (`api/v1/ecm/categories/news-events(/en)`) để lấy danh sách các *danh mục* (categories).
            - Nó giả định danh mục đầu tiên (`objects[0]`) là cho "Khuyến mãi" và danh mục thứ hai (`objects[1]`) là cho "Tin tức".
            - Các `categoryId` này được truyền cho các instance `ListNewsViewController` tương ứng.
            - *Lưu ý:* `ListNewsViewController` khi được dùng độc lập (không trong `NewsAndDealsViewController`) cũng có thể tự gọi `Ecm.getNewEvent` (cho Tin tức) hoặc `Ecm.getNewPromotion` (`api/v1/ecm/categories/news-promotion(/en)`) (cho Khuyến mãi) để lấy ID danh mục.
        - **Bước 2: Lấy Danh sách Bài viết:**
            - `ListNewsViewController` (cho cả Khuyến mãi và Tin tức) gọi `Ecm.getNewWithId(categoryId, pageSize, pageNumber)` (`api/v1/ecm/{categoryId}/news`) để lấy danh sách các *bài viết* (news items) thuộc về `categoryId` đã lấy ở Bước 1.
            - *Lưu ý:* Mặc dù có API `Ecm.getNewForCategory` được định nghĩa với cùng path, nhưng trong luồng này, `Ecm.getNewWithId` được sử dụng để lấy bài viết theo *ID danh mục*.
    5.  Hiển thị danh sách bài viết trong `UITableView` của `ListNewsViewController`.
    6.  **Tương tác người dùng:**
        - Cho phép người dùng chuyển đổi giữa các tab "Khuyến mãi" và "Tin tức".
        - Cho phép người dùng chọn một bài viết để xem chi tiết.
    7.  **Điều hướng:** Khi người dùng chọn một bài viết, điều hướng đến `NewsDetailViewController`.
- **Thành phần phụ thuộc chính:**
    - **Views:** `NewsTableViewCell`
    - **Models:** `NewModel` (cho danh mục), `NewsModel` (cho bài viết)
    - **Controllers:** `ListNewsViewController`, `NewsDetailViewController`
- **API Endpoints chính (Định nghĩa trong `Manager/Network/Moya/EcmAPI.swift`):**
    - `Ecm.getNewEvent`: Lấy danh sách *danh mục* Tin tức/Sự kiện. Gọi bởi `NewsAndDealsViewController` và `ListNewsViewController`.
    - `Ecm.getNewPromotion`: Lấy danh sách *danh mục* Khuyến mãi. Gọi bởi `ListNewsViewController` (khi đứng độc lập).
    - `Ecm.getNewWithId`: Lấy danh sách *bài viết* theo ID danh mục. Gọi bởi `ListNewsViewController`.

- **Thành phần phụ thuộc chính:**
    - **Views:** `ScrollPager` (Lib/ScrollPager/), `NewsTableViewCell` (Class/Controller/Home/Cell/)
    - **Models:** `NewModel` (Class/Model/ResponseModel/)
    - **Controllers:** `ListNewsViewController` (Class/Controller/Home/), `NewsDetailViewController` (Class/Controller/Home/)
    - **Libraries:** `RxSwift`

- Sử dụng `ScrollPager` để tạo giao diện tab chuyển đổi giữa "Ưu đãi" và "Tin tức".
- Mỗi tab thực chất là một instance của `ListNewsViewController` được cấu hình để hiển thị loại tin tức tương ứng (`.promotion` hoặc `.news`).
- `ListNewsViewController` hiển thị danh sách tin tức (`NewsModel`) sử dụng `NewsTableViewCell`.
- Điều hướng đến `NewsDetailViewController` khi người dùng chọn một tin tức.

- Sử dụng `Lib/ScrollPager/ScrollPager.swift` để hiển thị 2 tab con:
    - **Khuyến mãi**: Hiển thị danh sách các chương trình khuyến mãi (`Class/Controller/Home/ListNewsViewController.swift` với `type = .promotion`).
    - **Tin tức**: Hiển thị danh sách các tin tức (`Class/Controller/Home/ListNewsViewController.swift` với `type = .news`).
- Dữ liệu được tải động khi người dùng chuyển tab.
- Điều hướng đến màn hình chi tiết tin tức/khuyến mãi (`Class/Controller/Home/NewsDetailViewController.swift`) khi chọn một mục.

### 3.5. Tab 5: Khác (`Class/Controller/Other/TabOtherViewController.swift`)

- **Mô tả:** Màn hình "Khác" cung cấp lối vào các chức năng phụ của ứng dụng, bao gồm thông tin thành viên, cài đặt, thông báo, v.v.
- **Thành phần phụ thuộc chính (cho `TabOtherViewController`):
    - **Views:** `OthersCollectionViewCell` (Class/Controller/Other/Cell/ - cần xác minh tên cell chính xác)
    - **Models:** `OtherItem` (struct nội bộ để hiển thị các mục)
- **Luồng chính của `TabOtherViewController`:**
    1.  **Khởi tạo:** Hiển thị một `UICollectionView` với các mục được định nghĩa sẵn (Voucher miễn phí, Rạp phim, Thành viên, Thông báo, Tuyển dụng, Cài đặt).
    2.  **Chọn mục:** Khi người dùng nhấn vào một mục:
        - **"Thành viên" (`ic_other_member`):**
            - **Kiểm tra đăng nhập:** Nếu người dùng chưa đăng nhập (`Global.shared.user == nil`), điều hướng đến `LoginViewController` (`Class/Controller/Authen/LoginViewController.swift`).
            - **Đã đăng nhập:** Điều hướng đến `MemberViewController` (`Class/Controller/Member/MemberViewController.swift`).

#### 3.5.1. Luồng con: Thông tin Thành viên (`Class/Controller/Member/MemberViewController.swift`)

- **Mô tả:** Hiển thị thông tin chi tiết về tài khoản thành viên, điểm thưởng, lịch sử giao dịch và các tùy chọn liên quan đến tài khoản.
- **Đường dẫn file:** `Class/Controller/Member/MemberViewController.swift`
- **Input:** Không có input trực tiếp, lấy thông tin từ `Global.shared.user`.
- **Tài nguyên sử dụng (trong `MemberViewController` và các cell liên quan):
    - **Hình ảnh (từ `Assets.xcassets` hoặc được load từ URL):
        - Avatar người dùng (load từ URL hoặc `btAvatar`)
        - Banner (ví dụ: `ivBanner`)
        - Mã vạch thẻ thành viên (`ivBarCode` - tạo bằng `RSBarcodes_Swift`)
        - Hình ảnh thẻ thành viên (`ivMemberCard` - load từ URL)
        - Icon VIP (`ivVip` - load từ URL)
        - Các icon cho từng mục trong `UITableView` (ví dụ: icon cho Thông tin tài khoản, Đổi mật khẩu, v.v. - cần kiểm tra trong `SettingTableCell` hoặc cách `TableItem` được cấu hình).
    - **Views tùy chỉnh:**
        - `VisualEffectView` (cho hiệu ứng blur)
        - `UIProgressView` (`pvSpentMoney`)
        - Cell tùy chỉnh cho `UITableView` (ví dụ: `SettingTableCell`, `LogoutCell`)
- **Luồng chính:**
    1.  **Khởi tạo & Tải dữ liệu:**
        *   Hiển thị giao diện với `UITableView` và một `headerView` chứa thông tin tóm tắt (tên, hạng thẻ, điểm, mã vạch).
        *   Gọi API `Account.getProfileById` (hoặc `getProfileByCardNumber`) để lấy thông tin chi tiết mới nhất của người dùng nếu cần (trong `viewWillAppear` hoặc `loadData`).
        *   Gọi API `Account.getCardClass` để lấy thông tin về các hạng thẻ.
    2.  **Hiển thị thông tin:**
        *   Cập nhật `headerView` với tên (`lbName`), số thẻ (`lbCardNumber`), mã vạch (`ivBarCode`), hình ảnh thẻ (`ivMemberCard`), hạng VIP (`ivVip`), tổng chi tiêu (`lbTotalMoney`), tổng điểm (`lbTotalPoint`), tiến trình lên hạng (`pvSpentMoney`, `lbSpentMoney`), điểm sắp hết hạn (`lbRemainPoint`).
        *   Sử dụng `AlamofireImage` để tải các hình ảnh từ URL (avatar, banner, icon thẻ).
    3.  **Hiển thị Menu (`UITableView`):**
        *   Các mục menu được tạo dưới dạng `TableItem` và hiển thị trong `UITableView`:
            -   Điểm Beta (`Member.BetaPoint`)
            -   Giới thiệu bạn bè (`Member.Intro`)
            -   Lịch sử giao dịch (`Member.TransactionHistory`)
            -   Thẻ thành viên (`Member.MemberCard`)
            -   Thông tin tài khoản (`Member.AccountInfo`)
            -   Đổi mật khẩu (`Member.ChangePass`)
            -   Xóa tài khoản (`Member.DeleteAccount`)
            -   Đăng xuất (`LogoutCell`)
    4.  **Tương tác người dùng:**
        *   **Chọn "Thông tin tài khoản":** Điều hướng đến `AccountInfoViewController` (`Class/Controller/Member/AccountInfoViewController.swift`).
        *   **Chọn "Đổi mật khẩu":** Điều hướng đến `UpdatePasswordViewController` (`Class/Controller/Authen/UpdatePasswordViewController.swift`).
        *   **Chọn "Thẻ thành viên":** Điều hướng đến `MemberCardViewController` (`Class/Controller/Member/MemberCardViewController.swift`).
        *   **Chọn "Lịch sử giao dịch":** Điều hướng đến `TransactionHistoryViewController` (`Class/Controller/Member/TransactionHistoryViewController.swift`).
        *   **Chọn "Giới thiệu bạn bè":** Điều hướng đến `IntroFriendViewController` (`Class/Controller/IntroFriend/IntroFriendViewController.swift`).
        *   **Chọn "Điểm Beta":** Điều hướng đến `PointViewController` (`Class/Controller/Member/PointViewController.swift`).
        *   **Chọn "Xóa tài khoản":** Hiển thị cảnh báo và xử lý logic xóa tài khoản (có thể gọi API `Account.deleteAccount` - cần xác minh API endpoint này).
        *   **Chọn "Đăng xuất":** Gọi `Global.shared.logout()`, gọi API `Account.unRegisterDeviceToken` để hủy đăng ký device token, xóa thông tin người dùng và điều hướng về màn hình chính hoặc đăng nhập.
        *   **Nhấn vào Avatar (`btAvatar`):** Mở `ImagePickerController` để người dùng chọn hoặc chụp ảnh mới, sau đó gọi API `Account.uploadAvatar`.
- **API Endpoints chính (từ `AccountAPI.swift` và các file liên quan):**
- `POST api/v1/erp/accounts/login` (`Account.login`): API đăng nhập.
        - **Model Request:** `LoginModel` (`Class/Model/RequestModel/LoginModel.swift`)
            - `userName` (String): Email người dùng.
            - `password` (String): Mật khẩu.
            - `deviceId` (String?): ID thiết bị, lấy từ `UIDevice.current.identifierForVendor?.uuidString`.
            - `token` (String): ReCaptcha token (hiện tại đang truyền rỗng).
        - **Model Response (Lưu trữ thông tin người dùng sau khi đăng nhập thành công):** `UserModel` (`Class/Model/ResponseModel/UserModel.swift`)
            - Chứa các thông tin chi tiết về người dùng như `UserId`, `FullName`, `Email`, `Token`, `Picture`, `AvailablePoint`, `TotalPoint`, `ClassId`, `CardId`, v.v.
            - Dữ liệu này thường được lưu vào `Global.shared.user`.
    - `POST api/v1/erp/accounts/login` (`Account.login`): API đăng nhập.
        - **Model Request:** `LoginModel` (`Class/Model/RequestModel/LoginModel.swift`)
            - `userName` (String): Email người dùng.
            - `password` (String): Mật khẩu.
            - `deviceId` (String?): ID thiết bị, lấy từ `UIDevice.current.identifierForVendor?.uuidString`.
            - `token` (String): ReCaptcha token (hiện tại đang truyền rỗng).
    - `POST api/v1/erp/notifications/register-device-token` (`Account.registerDeviceToken`): Đăng ký device token để nhận thông báo đẩy. Được gọi sau khi đăng nhập thành công hoặc khi người dùng cho phép nhận thông báo.
        - **Tham số:** `DeviceId` (ID của thiết bị), `AccountId` (ID của tài khoản), `DeviceToken` (token nhận được từ APNS), `DeviceType` (mặc định là "ios").
    - `PUT api/v1/erp/notifications/unregister-device-token` (`Account.unRegisterDeviceToken`): Hủy đăng ký device token. Được gọi khi người dùng đăng xuất hoặc tắt nhận thông báo.
        - **Tham số:** `DeviceId`, `AccountId`, `DeviceToken`, `DeviceType`.
    -   `GET api/v1/erp/accounts/{id}` (`Account.getProfileById`): Lấy thông tin chi tiết người dùng.
        - **Tham số:** `id` (UserId của người dùng).
        - **Model Response:** `UserModel` (`Class/Model/ResponseModel/UserModel.swift`) - Được sử dụng để hiển thị thông tin chi tiết của người dùng trong các màn hình như `AccountInfoViewController.swift`.
    -   `GET api/v1/erp/card-class` (`Account.getCardClass`): Lấy danh sách các hạng thẻ thành viên.
        - **Model Response:** `CardClassModel` (`Class/Model/ResponseModel/CardClassModel.swift`)
            - Chứa thông tin về hạng thẻ như `CardId`, `Name`, `Code`, `TotalPointCondition`, `TotalPaymentCondition`, `UrlIcon`, v.v.

    -   `GET api/v1/erp/accounts/{id}` (`Account.getProfileById`): Lấy thông tin chi tiết người dùng.
    -   `GET api/v1/erp/card-class` (`Account.getCardClass`): Lấy danh sách các hạng thẻ thành viên.
    -   `PUT api/v1/erp/accounts/{id}/avatar` (`Account.uploadAvatar`): Tải lên ảnh đại diện.
    -   `GET api/v1/erp/transaction-history/{id}` (`Account.getTransactionHistory`): Lấy lịch sử giao dịch.
    -   (Có thể có các API khác cho điểm thưởng, giới thiệu bạn bè - cần kiểm tra các ViewController tương ứng).

#### 3.5.1.1. Luồng con: Cập nhật Thông tin Tài khoản (`Class/Controller/Member/AccountInfoViewController.swift`)

- **Mô tả:** Cho phép người dùng chỉnh sửa thông tin cá nhân.
- **Đường dẫn file:** `Class/Controller/Member/AccountInfoViewController.swift`
- **Input:** Lấy thông tin người dùng hiện tại từ `Global.shared.user`.
- **Tài nguyên sử dụng:**
    -   Các `InputTextField`, `PickerTextField`, `DateTextField` để nhập liệu.
- **Luồng chính:**
    1.  **Tải dữ liệu:** Hiển thị thông tin hiện tại của người dùng lên các trường nhập liệu (Email (chỉ đọc), Tên, Giới tính, Ngày sinh, Số điện thoại, Thành phố, Quận, Địa chỉ, CMND).
    2.  **Lấy danh sách Tỉnh/Thành, Quận/Huyện:** Gọi API (ví dụ: `Other.listCity`, `Other.listDistrict` - cần xác minh API này trong `OtherAPI.swift` hoặc tương đương) để điền vào `PickerTextField` cho Thành phố và Quận.
    3.  **Người dùng chỉnh sửa & Lưu:**
        *   Người dùng thay đổi thông tin.
        *   Nhấn nút "Cập nhật".
        *   **Validate dữ liệu:** Kiểm tra tính hợp lệ của các trường (tên không trống, SĐT đúng định dạng, ngày sinh hợp lệ, CMND hợp lệ nếu có).
        *   Gọi API `Account.updateProfile` (`PUT api/v1/erp/accounts/{id}`) với `RegisterModel` chứa thông tin đã cập nhật.
        *   Xử lý kết quả: Hiển thị thông báo thành công/thất bại, cập nhật `Global.shared.user` nếu thành công.
- **API Endpoints chính:**
    -   `PUT api/v1/erp/accounts/{id}` (`Account.updateProfile`): Cập nhật thông tin người dùng.
    -   API lấy danh sách tỉnh/thành phố và quận/huyện (cần xác định, ví dụ: `GET api/v1/erp/cites`, `GET api/v1/erp/districts`).


- **Mô tả:** Cung cấp các lối tắt truy cập nhanh đến các chức năng khác của ứng dụng.
- **Thành phần phụ thuộc chính:**
    - **Views:** `OthersCollectionViewCell` (Class/Controller/Other/Cell/)
- **Luồng chính:**
    - Hiển thị một lưới (`UICollectionView`) các biểu tượng chức năng.
    - Khi người dùng nhấn vào một biểu tượng, điều hướng đến màn hình chức năng tương ứng (ví dụ: Voucher miễn phí, Rạp phim, Thành viên, Thông báo, Tuyển dụng, Cài đặt).
- **API Endpoints chính:**
    - Không có API gọi trực tiếp từ màn hình này. Các API được gọi từ các màn hình chức năng được điều hướng tới (ví dụ: `Ecm.getFreeVoucher`, `Cinema.listCinema`, `Account.getProfileById`, `Ecm.getNotificationByUserID`, `Ecm.getRecruitment`).
- Hiển thị một lưới (CollectionView) các chức năng khác của ứng dụng:
    - **Voucher miễn phí** (`Class/Controller/Voucher/FreeVoucherViewController.swift`)
        - API: `Ecm.getFreeVoucher` (`api/v1/ecm/free-voucher`)
    - **Danh sách rạp phim** (`Class/Controller/Cinema/CinemasViewController.swift`)
        - APIs: `Cinema.listCinema` (`api/v1/erp/cinemas`), `Cinema.listCinemaByProvince` (`api/v1/erp/cites/cinemas`)
    - **Thông tin thành viên** (`Class/Controller/Member/MemberViewController.swift` - yêu cầu đăng nhập)
        - API: `Account.getProfileById` (`api/v1/erp/accounts/{id}`) (Cần xác minh thêm API cập nhật)
    - **Thông báo** (`Class/Controller/Other/NotificationViewController.swift`)
        - API: `Ecm.getNotificationByUserID` (`api/v1/ecm/notification`) (Cần xác minh)
    - **Tuyển dụng** (`Class/Controller/Other/RecruitmentViewController.swift`)
        - API: `Ecm.getRecruitment` (`api/v1/ecm/recruitment`)
    - **Cài đặt** (`Class/Controller/Setting/SettingViewController.swift`)
        - **Mô tả:** Cho phép người dùng cấu hình các cài đặt ứng dụng như ngôn ngữ, quyền truy cập vị trí, xem thông tin phiên bản, chính sách và liên hệ.
        - **Thành phần phụ thuộc chính:**
            - **Views:** `SwitchTableCell` (Class/Controller/Setting/Cell/), `CheckboxTableCell` (Class/Controller/Setting/Cell/), `SettingTableCell` (Class/Controller/Setting/Cell/), `TitleHeaderView` (Common/View/Header/)
            - **Managers:** `LocationManager` (Manager/Location/), `LanguageManager` (Manager/Language/)
            - **Controllers:** `FAQViewController` (Class/Controller/Setting/), `VersionInfoViewController` (Class/Controller/Setting/), `OtherViewController` (Class/Controller/Setting/)
            - **Models:** Không có model dữ liệu cụ thể, chủ yếu quản lý trạng thái cài đặt.
        - **Luồng chính:**
            1.  **Hiển thị danh sách cài đặt:** Load và hiển thị các mục cài đặt như Ngôn ngữ, Vị trí, FAQ, Phiên bản, Điều khoản sử dụng, Chính sách thanh toán, Chính sách bảo mật, Thông tin công ty.
            2.  **Thay đổi ngôn ngữ:** Cập nhật ngôn ngữ ứng dụng thông qua `LanguageManager`.
            3.  **Thay đổi quyền vị trí:** Mở cài đặt hệ thống để người dùng thay đổi quyền truy cập vị trí.
            4.  **Xem FAQ:** Điều hướng đến `FAQViewController` khi chọn mục "FAQ".
                - **API:**
                    - `Ecm.getTopic` (`GET api/v1/ecm/topics/{lang}`): Lấy danh sách chủ đề FAQ. Không yêu cầu tham số.
                    - (Trong `FAQDetailViewController`) `Ecm.getNewWithId` (`GET api/v1/ecm/{id}/news`): Lấy nội dung chi tiết FAQ. Tham số: `id` (String - `topicId`).
            5.  **Xem Điều khoản/Chính sách/Thông tin:** Điều hướng đến `OtherViewController` khi chọn các mục tương ứng (Điều khoản sử dụng, Chính sách thanh toán, Chính sách bảo mật, Thông tin công ty).
                - **API:**
                    - `Ecm.getTermId`/`getPaymentPolicyId`/`getSecurityId`/`getCompanyInfoId` (`GET api/v1/ecm/parameter`): Lấy ID nội dung tương ứng. Tham số: `code` (String - ví dụ: `mobile:app:dieukhoan:vi`
                    - Điều khoản sử dụng ( Ecm.getTermId ) :
                        - Tiếng Việt: "mobile:app:dieukhoan:vi"
                        - Tiếng Anh: "mobile:app:dieukhoan:en"
                    - Chính sách thanh toán ( Ecm.getPaymentPolicyId ) :
                        - Tiếng Việt: "mobile:app:dieukhoan-thanhtoan:vi"
                        - Tiếng Anh: "mobile:app:dieukhoan-thanhtoan:en"
                    - Chính sách bảo mật ( Ecm.getSecurityId ) :
                        - Tiếng Việt: "mobile:app:dieukhoan-baomat:vi"
                        - Tiếng Anh: "mobile:app:dieukhoan-baomat:en"
                    - Thông tin công ty ( Ecm.getCompanyInfoId ) :
                        - Tiếng Việt: "mobile:app:thongtin-congty:vi"
                        - Tiếng Anh: "mobile:app:thongtin-congty:en").
                    - `Ecm.getNewWithId` (`GET api/v1/ecm/{id}/news`): Lấy nội dung chi tiết. Tham số: `id` (String - ID lấy từ API trước).
            6.  **Xem thông tin phiên bản:** Điều hướng đến `VersionInfoViewController` khi chọn mục "Phiên bản".
                - **Lưu ý:** Màn hình này kiểm tra phiên bản mới bằng cách gọi trực tiếp API của App Store (`https://itunes.apple.com/lookup?bundleId=...`), không sử dụng API backend tùy chỉnh.
            1.  **Hiển thị cài đặt:** Sử dụng `UITableView` với các cell tùy chỉnh (`SwitchTableCell`, `CheckboxTableCell`, `SettingTableCell`) và header (`TitleHeaderView`) để hiển thị các mục cài đặt được nhóm theo section (Ngôn ngữ, Khác).
            2.  **Thay đổi ngôn ngữ:** Người dùng chọn Tiếng Việt hoặc Tiếng Anh (`CheckboxTableCell`). `LanguageManager` được sử dụng để lưu và áp dụng ngôn ngữ mới. Gửi thông báo `NSNotification.Name.ChangeLocalization` để cập nhật giao diện toàn ứng dụng.
            3.  **Bật/Tắt thông báo:** (Chức năng chưa hoàn thiện trong code) Sử dụng `SwitchTableCell` để bật/tắt thông báo.
            4.  **Bật/Tắt vị trí:** Sử dụng `SwitchTableCell` (`locationCell`). Tương tác với `LocationManager` để kiểm tra, yêu cầu quyền và bật/tắt theo dõi vị trí. Hiển thị cảnh báo nếu quyền bị từ chối.
            5.  **Xem FAQ:** Điều hướng đến `FAQViewController` khi chọn mục "Hỏi đáp".
            6.  **Xem thông tin phiên bản:** Điều hướng đến `VersionInfoViewController` khi chọn mục "Phiên bản".
            7.  **Xem chính sách & thông tin:** Điều hướng đến `OtherViewController` với các `OtherType` khác nhau (`.Term`, `.PolicyPayment`, `.Security`, `.CompanyInfo`) khi chọn các mục tương ứng (Điều khoản sử dụng, Chính sách thanh toán, Chính sách bảo mật, Thông tin công ty).
        - **API Endpoints:** Không gọi API trực tiếp trong màn hình này, nhưng các màn hình con (như `OtherViewController` để xem chính sách) có thể gọi API liên quan (ví dụ: `Ecm.getTerm`, `Ecm.getPolicyPayment`, `Ecm.getSecurity`, `Ecm.getCompanyInfo` - cần kiểm tra thêm trong `OtherViewController`). API `Account.logout` được đề cập trong `TabOtherViewController` nhưng không được gọi trực tiếp từ `SettingViewController`.
- Điều hướng đến màn hình tương ứng khi người dùng chọn một chức năng.
- **Thành phần phụ thuộc chính:**
    - **Views:** `OthersCollectionViewCell` (Class/Controller/Other/Cell/)
    - **Models:** `OtherItem` (struct định nghĩa nội bộ)
    - **Controllers:** `FreeVoucherViewController` (Class/Controller/Voucher/), `CinemasViewController` (Class/Controller/Cinema/), `MemberViewController` (Class/Controller/Member/), `NotificationViewController` (Class/Controller/Other/), `RecruitmentViewController` (Class/Controller/Other/), `SettingViewController` (Class/Controller/Setting/), `LoginViewController` (Class/Controller/Authen/)

- Hiển thị một lưới (`UICollectionView`) các chức năng khác của ứng dụng, sử dụng `OthersCollectionViewCell` để hiển thị từng mục.
- Dữ liệu cho các mục được lưu trữ trong một mảng các đối tượng `OtherItem`.
- Các chức năng bao gồm:
    - Voucher miễn phí
    - Danh sách rạp phim
    - Thông tin thành viên (yêu cầu đăng nhập, điều hướng đến `LoginViewController` nếu chưa đăng nhập)
    - Thông báo
    - Tuyển dụng
    - Cài đặt
- Điều hướng đến View Controller tương ứng khi người dùng chọn một chức năng.


## 4. Quản lý Điều hướng Phụ (`Manager/Router/RouteManager.swift`)

- `Manager/Router/RouteManager.swift` được sử dụng để xử lý các điều hướng phức tạp, đặc biệt là từ thông báo đẩy (Push Notifications) hoặc các liên kết sâu (Deep Links).
- Nó nhận vào một `RouteType` (enum xác định màn hình đích) và `params` (dữ liệu cần thiết cho màn hình đó).
- Dựa vào `RouteType`, nó sẽ khởi tạo và hiển thị View Controller tương ứng, thường là bằng cách `push` vào Navigation Controller hiện tại.
- Các `RouteType` bao gồm điều hướng đến chi tiết phim, chi tiết rạp, chi tiết khuyến mãi, danh sách voucher, điểm thưởng, chi tiết giao dịch, v.v.

## 5. Chi tiết Phim (`Class/Controller/Film/FilmDetailViewController.swift`)

- **Thành phần phụ thuộc chính:**
    - **Views:** `FilmDetailCell` (định nghĩa nội bộ), `FilmDescriptionTableCell` (Class/Controller/Film/Cell/), `NewsAndDealsCell` (Class/Controller/Film/Cell/), `RoundView` (Common/View/), `LocalizableButton` (Common/Localizable/)
    - **Models:** `FilmModel` (Class/Model/ResponseModel/), `NewsModel` (Class/Model/ResponseModel/), `TableItem`, `TableSection` (Helper/TableView/)
    - **Controllers:** `StickyHeaderViewController` (Base), `FilmChooseTimeViewController` (Class/Controller/Film/), `YoutubeViewController` (Class/Controller/Home/), `AVPlayerViewController` (AVKit)
    - **Libraries:** `AlamofireImage`, `UITableView_FDTemplateLayoutCell`, `SwiftDate`, `youtube_ios_player_helper`

- Hiển thị thông tin chi tiết về một bộ phim được chọn từ màn hình Trang chủ hoặc màn hình Rạp phim.
- **Kế thừa từ `StickyHeaderViewController`**: Cho phép header (chứa banner và logo phim) "dính" lại khi cuộn.
- Sử dụng `UITableView` với `SimpleTableViewDataSource` để hiển thị thông tin.
- Hiển thị thông tin cơ bản (đạo diễn, diễn viên, thể loại,...) sử dụng `FilmDetailCell`.
- Hiển thị mô tả ngắn về phim sử dụng `FilmDescriptionTableCell`.
- Cho phép xem trailer phim (sử dụng `youtube_ios_player_helper` cho Youtube hoặc `AVPlayerViewController` cho các link khác).
- Hiển thị cảnh báo độ tuổi (`RoundView`) nếu có.
- Hiển thị nút "Mua vé" (`LocalizableButton`, điều hướng đến luồng đặt vé) hoặc "Chia sẻ" (nếu phim chưa chiếu hoặc đang xem từ luồng không đặt vé).
- Có thể hiển thị các tin tức hoặc ưu đãi liên quan đến phim sử dụng `NewsAndDealsCell`.
- Điều hướng đến `FilmChooseTimeViewController` khi nhấn nút "Mua vé".

## 6. Tích hợp Flutter

- Dự án có thư mục `flutter_migration`, cho thấy một phần chức năng (có thể là luồng đặt vé) đang được hoặc đã được chuyển sang Flutter.
- Các file như `flutter_migration/film/film_booking_screen.dart`, `flutter_migration/film/ticket_booking_screen.dart` gợi ý về điều này.
- Các model request trong `flutter_migration/models/request/` (ví dụ: `flutter_migration/models/request/seat_booking_model.dart`) được sử dụng cho việc giao tiếp với API từ phía Flutter.

#### 3.2.1. Chọn Rạp & Suất chiếu (`Class/Controller/Cinema/ChooseCinemasViewController.swift`)

- **Mô tả:** Màn hình này được hiển thị khi người dùng chọn một rạp từ `CinemasViewController` trong luồng đặt vé (`isBooking = true`). Nhiệm vụ chính là cho phép người dùng chọn ngày và suất chiếu cho phim tại rạp đã chọn.
- **Luồng chính:**
    1. Nhận thông tin rạp (`CinemaModel`) từ `CinemasViewController`.
    2. Hiển thị thông tin chi tiết của rạp đã chọn.
    3. **Gọi API để lấy lịch chiếu phim theo rạp và ngày:**
        - *(Điền tên API endpoint và mô tả tại đây, ví dụ: `Film.getShowtimesByCinemaAndDate` (`api/vX/erp/cinemas/{cinema_id}/showtimes?date={date}`))*
        - **Phương thức:** *(GET/POST)*
        - **Tham số:** `cinema_id`, `date`
        - **Mục đích:** Lấy danh sách các phim và suất chiếu tương ứng tại rạp đã chọn cho ngày được chọn.
    4. Hiển thị danh sách các ngày có suất chiếu (thường là trong một tuần). Mặc định chọn ngày hiện tại.
    5. Hiển thị danh sách các phim đang chiếu tại rạp vào ngày đã chọn, cùng với các suất chiếu khả dụng.
    6. **Tương tác người dùng:**
        - Cho phép người dùng chọn ngày khác để xem lịch chiếu. Khi chọn ngày, gọi lại API lấy lịch chiếu.
        - Cho phép người dùng chọn một suất chiếu cụ thể.
    7. **Điều hướng:** Khi người dùng chọn một suất chiếu, điều hướng đến màn hình tiếp theo trong luồng đặt vé (ví dụ: `ChooseSeatViewController`) và truyền thông tin về phim, rạp, ngày, và suất chiếu đã chọn.
- **Thành phần phụ thuộc chính:**
    - **Views:** `ShowtimeCollectionViewCell`, `FilmWithShowtimesTableViewCell`
    - **Models:** `CinemaModel`, `FilmModel`, `ShowModel`
    - **Controllers:** `ChooseSeatScreen`
- **API Endpoints chính:**
    - `GET api/v1/erp/cinema/showdate`: Lấy danh sách ngày có suất chiếu.
    - `GET api/v1/erp/cinema/filmshow`: Lấy danh sách phim và suất chiếu theo ngày.

## 7. Luồng Chọn Ghế và Thanh Toán trong Flutter

### 7.1. Chọn Ghế (`lib/pages/cinema/choose/seat.dart`)

- **Mô tả:** Màn hình cho phép người dùng chọn ghế ngồi cho một suất chiếu cụ thể.
- **Input:** Nhận `cinemaId`, `cinemaName`, `showTime` (ShowModel), `film` (FilmModel) từ màn hình chọn suất chiếu.
- **Luồng chính:**
    1.  **Khởi tạo:**
        *   Hiển thị thông tin phim, suất chiếu, tên rạp.
        *   Khởi tạo kết nối `SignalR` đến `chooseSeatHub` để cập nhật trạng thái ghế real-time.
        *   Bắt đầu bộ đếm thời gian giữ ghế (5 phút).
        *   Lưu thời điểm bắt đầu chọn ghế (`timeStartBooking`).
    2.  **Lấy Sơ đồ Ghế & Giá vé:**
        *   Gọi API `Film.getShowSeat` để lấy sơ đồ ghế (`ListSeatModel` chứa danh sách `SeatModel`) và các loại vé (`TicketType`).
        *   Hiển thị sơ đồ ghế lên màn hình sử dụng `SeatGrid` widget.
        *   Hiển thị các loại giá vé (Thường, VIP, Couple).
    3.  **Chọn/Bỏ chọn ghế:**
        *   Người dùng tương tác với `SeatGrid` để chọn hoặc bỏ chọn ghế.
        *   Khi chọn/bỏ chọn:
            *   Cập nhật UI của ghế (đổi màu).
            *   Thêm/xóa `SeatModel` khỏi danh sách `_selectedSeats`.
            *   Gửi thông điệp qua `SignalR` (`_hubConnection!.invoke("SendMessage", args: [connectionId, showId, seatIndex, status])`) để thông báo cho các client khác về việc ghế đang được chọn/bỏ chọn.
            *   Tính toán lại tổng tiền (`_totalPrice`).
            *   Hiển thị danh sách ghế đã chọn và tổng tiền.
    4.  **Nhận cập nhật từ SignalR:**
        *   Lắng nghe sự kiện `ReceiveMessage` từ `chooseSeatHub`.
        *   Nếu có thông báo ghế được người khác chọn/đã bán/đã hủy chọn, cập nhật lại trạng thái của ghế đó trên UI.
    5.  **Kiểm tra hợp lệ khi chọn ghế:**
        *   Kiểm tra số lượng ghế tối đa (8 ghế).
        *   Kiểm tra không để trống một ghế đơn giữa các ghế đã chọn.
        *   Hiển thị thông báo lỗi nếu vi phạm các quy tắc.
    6.  **Hết thời gian giữ ghế:**
        *   Bộ đếm thời gian (được cập nhật mỗi giây) về 0.
        *   Hiển thị cảnh báo hết thời gian.
        *   Hủy các ghế đã chọn qua SignalR.
        *   Điều hướng người dùng về màn hình trước.
    7.  **Xử lý nút "Tiếp tục":**
        *   Kiểm tra người dùng đã chọn ghế chưa.
        *   Kiểm tra độ tuổi phù hợp với phim (C13, C16, C18).
        *   Nếu đã chọn ghế và phù hợp độ tuổi:
            *   Điều hướng đến `PaymentScreen`.
            *   Truyền dữ liệu: `cinemaId`, `cinemaName`, `showTime`, `film`, `selectedSeats`, `listSeat`, `totalPrice`, `remainingTime`, `timeStartBooking`.
- **Thành phần phụ thuộc chính:**
    - **Widgets:** `SeatGrid`, `SeatLegend`.
    - **Models:** `ShowModel`, `FilmModel`, `ListSeatModel`, `SeatModel`, `TicketType`.
    - **Libraries:** `signalr_netcore`, `flutter_bloc`.
    - **Screens:** `PaymentScreen`.
- **API Endpoints & Real-time:**
    - **Lấy sơ đồ ghế:** `Film.getShowSeat` (`GET api/v1/erp/shows/{id}`).
    - **SignalR Hub:** `chooseSeatHub` tại `ApiService.signalRUrl`.
        - **Phương thức gọi đi (Invoke):** `SendMessage`, `JoinGroup`.
        - **Phương thức nhận (On):** `ReceiveMessage`.

### 7.2. Thanh toán (`lib/pages/cinema/payment/payment_screen.dart`)

- **Mô tả:** Màn hình xử lý việc thanh toán cho các vé đã chọn.
- **Input:** Nhận `cinemaId`, `cinemaName`, `showTime`, `film`, `selectedSeats`, `listSeat`, `totalPrice`, `remainingTime`, `timeStartBooking` từ `ChooseSeatScreen`.
- **Luồng chính:**
    1.  **Khởi tạo:**
        *   Tiếp tục bộ đếm thời gian từ màn hình chọn ghế.
        *   Khởi tạo dữ liệu theo dõi (tracking) cho phân tích người dùng.
        *   Đếm số lượng ghế theo loại (thường, VIP, đôi).
    2.  **Tạo Giao dịch & Lấy Cổng Thanh toán:**
        *   Tạo `CreateBookingModel` từ thông tin nhận được (bao gồm `showId`, danh sách `SeatBookingModel`, `countDown`).
        *   Gọi API `Film.booking` để tạo giao dịch trên server và nhận về nội dung HTML của cổng thanh toán.
        *   Lưu nội dung HTML này vào biến `_webData`.
        *   Điều hướng đến `WebViewPaymentScreen` với dữ liệu HTML.
    3.  **Xử lý kết quả thanh toán:**
        *   Định nghĩa các callback để xử lý các trường hợp thanh toán:
            *   `_onPaymentSuccess`: Hiển thị thông báo thành công và điều hướng đến màn hình chi tiết giao dịch.
            *   `_onPaymentFailed`: Hiển thị thông báo lỗi và quay về màn hình chính.
            *   `_onPaymentWaiting`: Hiển thị thông báo đang chờ xử lý và điều hướng đến lịch sử giao dịch.
    4.  **Hết thời gian thanh toán:**
        *   Nếu bộ đếm thời gian về 0, hiển thị thông báo hết thời gian và quay về màn hình chọn ghế.
- **Thành phần phụ thuộc chính:**
    - **Widgets:** Các widget UI cơ bản của Flutter.
    - **Models:** `ShowModel`, `FilmModel`, `ListSeatModel`, `SeatModel`, `CreateBookingModel`, `SeatBookingModel`.
    - **Screens:** `WebViewPaymentScreen`, `TransactionDetailScreen`.
    - **Libraries:** `flutter_bloc`, `intl`.
- **API Endpoints chính:**
    - **Tạo giao dịch & lấy cổng thanh toán:** `Film.booking` (`POST api/v1/erp/orders/create`).

### 7.3. Giao diện Thanh toán Web (`lib/pages/cinema/payment/webview_payment.dart`)

- **Mô tả:** Màn hình hiển thị giao diện web của cổng thanh toán.
- **Input:** Nhận `htmlData`, `baseUrl`, `film`, `listSeat`, `cinemaId`, `cinemaName`, các callback xử lý kết quả thanh toán.
- **Luồng chính:**
    1.  **Khởi tạo WebView:**
        *   Thiết lập `WebViewController` để hiển thị cổng thanh toán.
        *   Đăng ký kênh JavaScript `Flutter` để nhận thông điệp từ trang web thanh toán.
        *   Load nội dung HTML vào WebView với `baseURL`.
    2.  **Tương tác với Cổng Thanh toán:**
        *   Người dùng thực hiện các bước trên giao diện web của cổng thanh toán.
        *   WebView điều hướng qua các trang của cổng thanh toán.
        *   Khi gặp URL của các ví điện tử (Momo, ZaloPay, AirPay), mở ứng dụng ví tương ứng.
    3.  **Cập nhật thông tin đặt vé:**
        *   Gọi JavaScript `getBookingInfo` để cập nhật thông tin phim, rạp, suất chiếu.
        *   Gọi JavaScript `getCustomerInfo` để cập nhật thông tin người dùng.
    4.  **Xử lý kết quả thanh toán:**
        *   Nhận thông điệp từ JavaScript thông qua kênh `Flutter`.
        *   Xử lý các loại thông điệp: `payment_susccess`, `payment_failed`, `booking_seat_failed`, `awaiting_payment`.
        *   Gọi các callback tương ứng để thông báo kết quả cho `PaymentScreen`.
    5.  **Kiểm tra trạng thái giao dịch (cho ví điện tử):**
        *   Khi ứng dụng active trở lại sau khi từ ví điện tử quay về:
            *   Gọi các hàm như `_checkAirPayPurchaseStatus()`, `_checkMomoPurchaseStatus()`, `_checkZaloPayPurchaseStatus()`.
            *   Các hàm này gọi JavaScript để xác nhận trạng thái cuối cùng của giao dịch.
    6.  **Xử lý nút Back:**
        *   Ưu tiên cho WebView tự xử lý back.
        *   Nếu không thể back trong web và URL hiện tại không phải là trang thanh toán ban đầu, load lại `htmlData`.
        *   Nếu không, hiển thị cảnh báo hủy giao dịch và cho phép người dùng quay lại màn hình trước.
- **Thành phần phụ thuộc chính:**
    - **Widgets:** `WebViewWidget`.
    - **Models:** `FilmModel`, `ListSeatModel`.
    - **Libraries:** `webview_flutter`, `url_launcher`, `intl`.
- **Tương tác JavaScript:**
    - **Gọi từ Flutter đến JavaScript:** `getBookingInfo`, `getCustomerInfo`, `getTransactionId`, `checkAirpayTransactionStatus`, `checkMomoTransactionStatus`, `checkZaloPayTransactionStatus`.
    - **Nhận từ JavaScript đến Flutter:** `payment_susccess`, `payment_failed`, `booking_seat_failed`, `awaiting_payment`, `policy`.

### 7.4. Chi tiết Giao dịch (`lib/pages/cinema/payment/transaction_detail_screen.dart`)

- **Mô tả:** Màn hình hiển thị chi tiết giao dịch sau khi thanh toán thành công.
- **Input:** Nhận `transactionId` và `backToHome` từ `PaymentScreen`.
- **Luồng chính:**
    1.  **Lấy thông tin giao dịch:**
        *   Gọi API `Film.getTransactionDetail` để lấy chi tiết giao dịch.
        *   Hiển thị thông tin giao dịch bao gồm: mã giao dịch, thông tin phim, thông tin vé, thông tin thanh toán, ngày giao dịch.
        *   Hiển thị mã QR vé nếu có.
    2.  **Điều hướng:**
        *   Nếu `backToHome` là `true`, khi người dùng nhấn nút back, điều hướng về màn hình chính.
        *   Nếu không, quay lại màn hình trước.
- **Thành phần phụ thuộc chính:**
    - **Widgets:** Các widget UI cơ bản của Flutter.
    - **Models:** `TransactionHistoryDetailModel`, `TicketTypeCopy`, `Payment`, `ComboModel`.
    - **Libraries:** `intl`.
- **API Endpoints chính:**
    - **Lấy chi tiết giao dịch:** `Film.getTransactionDetail` (`GET api/v1/erp/orders/{id}`).

- ... existing code ...
