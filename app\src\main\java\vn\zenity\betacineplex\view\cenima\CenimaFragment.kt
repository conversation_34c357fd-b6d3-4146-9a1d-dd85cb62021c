package vn.zenity.betacineplex.view.cenima

import android.annotation.SuppressLint
import android.location.Location
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import android.view.View
import android.view.ViewGroup
import com.thoughtbot.expandablerecyclerview.MultiTypeExpandableRecyclerViewAdapter
import com.thoughtbot.expandablerecyclerview.listeners.OnGroupClickListener
import com.thoughtbot.expandablerecyclerview.models.ExpandableGroup
import com.thoughtbot.expandablerecyclerview.viewholders.ChildViewHolder
import com.thoughtbot.expandablerecyclerview.viewholders.GroupViewHolder
import kotlinx.android.synthetic.main.fragment_cenima.*
import kotlinx.android.synthetic.main.item_area_in_list.view.*
import kotlinx.android.synthetic.main.item_area_title_in_list.view.*
import kotlinx.android.synthetic.main.item_cinema_in_list.view.*
import kotlinx.android.synthetic.main.item_cinema_in_near_me.view.*
import kotlinx.android.synthetic.main.item_cinema_near_me.view.*
import load
import loadRounded
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseActivity
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.model.AreaCinema
import vn.zenity.betacineplex.model.CinemaModel
import vn.zenity.betacineplex.model.CinemaProvinceModel
import vn.zenity.betacineplex.view.film.BookByCinemaFragment
import java.lang.ref.WeakReference
import java.util.*

/**
 * Created by Zenity.
 */

class CenimaFragment : BaseFragment(), CenimaContractor.View {

    override fun showListCinema(listNear: List<CinemaModel>, listArea: List<CinemaProvinceModel>) {
        val listData = mutableListOf<AreaCinema>()
        if (listNear.isNotEmpty()) {
            listData.add(AreaCinema(getString(R.string.cinema_near_you), mutableListOf(), TYPE_HEADER_TITLE))
            listData.add(AreaCinema("", listNear, TYPE_HEADER_NEAR_ME))
        }
        listData.add(AreaCinema(getString(R.string.select_cinema_by_area), mutableListOf(), TYPE_HEADER_TITLE))
        for (area in listArea) {
            listData.add(AreaCinema(area.CityName, area.ListCinema))
        }
        activity?.runOnUiThread {
            adapter = AreaAdapter(listData)
            recyclerView.adapter = adapter
        }
    }

    companion object {
        val TYPE_HEADER_TITLE = 0
        val TYPE_HEADER_NEAR_ME = 1
        val TYPE_HEADER_AREA = 2
        var TYPE_DETAIL = 0
        var TYPE_BOOKING = 1

        fun getInstance(type: Int = TYPE_DETAIL /*0: View Detail, 1: Book*/): CenimaFragment {
            val frag = CenimaFragment()
            frag.type = type
            return frag
        }
    }

    private val presenter = CenimaPresenter()
    private var adapter: AreaAdapter? = null
    var type = TYPE_DETAIL
    private var currentLocation: Location? = null

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_cenima
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this.context)
        currentLocation = (activity as BaseActivity).getMyLocation(true, true)
        presenter.getCinema(currentLocation)
        (activity as BaseActivity).listenerLocationChange.add(listenerLocation)
    }

    private val listenerLocation : WeakReference<(Location?) -> Unit> = WeakReference({ currentLocation ->
        this.currentLocation = currentLocation
        presenter.getCinema(currentLocation)
    })

    override fun onDestroyView() {
        super.onDestroyView()
        (activity as BaseActivity).listenerLocationChange.remove(listenerLocation)
    }
    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        adapter?.onSaveInstanceState(outState)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        adapter?.onRestoreInstanceState(savedInstanceState)
    }

    private fun openCinema(cinema: CinemaModel) {
        if (<EMAIL> == TYPE_BOOKING) {
            Tracking.share().selectTheater(context, cinema?.CinemaId, cinema?.Name)
            openFragment(BookByCinemaFragment.getInstance(cinema))
        } else {
            openFragment(CenimaDetailFragment.getInstance(cinema))
        }
    }

    inner class AreaAdapter(groups: MutableList<AreaCinema>?) : MultiTypeExpandableRecyclerViewAdapter<AreaHolder, CinemaHolder>(groups) {

        override fun getGroupViewType(position: Int, group: ExpandableGroup<*>): Int {
            return (group as? AreaCinema)?.type ?: TYPE_HEADER_AREA
        }

        override fun getChildViewType(position: Int, group: ExpandableGroup<*>?, childIndex: Int): Int {
            return if ((group as? AreaCinema)?.type == TYPE_HEADER_NEAR_ME) TYPE_HEADER_NEAR_ME else -1
        }

        override fun onCreateGroupViewHolder(parent: ViewGroup, viewType: Int): AreaHolder {
            if (viewType == TYPE_HEADER_TITLE) {
                val view = parent.inflate(R.layout.item_area_title_in_list)
                return AreaHolder(view)
            }
            if (viewType == TYPE_HEADER_NEAR_ME) {
                val view = parent.inflate(R.layout.item_cinema_near_me)
                return AreaHolder(view)
            }
            val view = parent.inflate(R.layout.item_area_in_list)
            return AreaHolder(view)
        }

        override fun onCreateChildViewHolder(parent: ViewGroup, viewType: Int): CinemaHolder {
            if (viewType == TYPE_HEADER_NEAR_ME) {
                val view = parent.inflate(R.layout.item_cinema_near_me)
                return CinemaHolder(view)
            } else {
                val view = parent.inflate(R.layout.item_cinema_in_list)
                return CinemaHolder(view)
            }
        }

        override fun onBindChildViewHolder(holder: CinemaHolder, flatPosition: Int, group: ExpandableGroup<*>, childIndex: Int) {
            if (getChildViewType(flatPosition, group, childIndex) == TYPE_HEADER_NEAR_ME) {
                holder.itemView.apply {
                    llCinemaNearMe.removeAllViews()
                    (group as? AreaCinema)?.cinemas?.forEach {cinema ->
                        val cinemaView = llCinemaNearMe.inflate(R.layout.item_cinema_in_near_me)
                        cinemaView.apply {
                            ivCinemaNearMe.loadRounded(cinema.Duong_dan_anh_dai_dien?.toImageUrl())
                            tvCinemaNearMeName.text = cinema.Name
                            val distance = cinema.getDistanceToCurrentLocation(currentLocation)
                            if (distance >= 10000f) {
                                tvCinemaNearMeDistance.text = ""
                            } else {
                                tvCinemaNearMeDistance.text = "${String.format(Locale.ENGLISH, "%.1f", distance)} km"
                            }
                            setOnClickListener {
                                openCinema(cinema)
                            }
                        }
                        llCinemaNearMe.addChild(cinemaView)
                    }
                }
                return
            }
            val leftIndex = childIndex * 2
            val rightIndex = childIndex * 2 + 1
            val cinemaLeft = (group as? AreaCinema)?.items?.getOrNull(leftIndex)
            val cinemaRight = (group as? AreaCinema)?.items?.getOrNull(rightIndex)
            holder.itemView.apply {
                if(cinemaLeft == null) {
                    clCinemaLeft.gone()
                    clCinemaRight.gone()
                } else {
                    clCinemaLeft.visible()
                    ivCinemaLeft.loadRounded(cinemaLeft.Duong_dan_anh_dai_dien?.toImageUrl())
                    tvCinemaLeftName.text = cinemaLeft.Name
                    val distance = cinemaLeft.getDistanceToCurrentLocation(currentLocation)
                    if (distance >= 10000f) {
                        tvCinemaLeftDistance.text = ""
                    } else {
                        tvCinemaLeftDistance.text = "${String.format(Locale.ENGLISH, "%.1f", distance)} km"
                    }
                    clCinemaLeft.click {
                        openCinema(cinemaLeft)
                    }
                }

                if(cinemaRight == null) {
                    clCinemaRight.invisible()
                } else {
                    clCinemaRight.visible()
                    ivCinemaRight.loadRounded(cinemaRight.Duong_dan_anh_dai_dien?.toImageUrl())
                    tvCinemaRightName.text = cinemaRight.Name
                    val distance = cinemaRight.getDistanceToCurrentLocation(currentLocation)
                    if (distance >= 10000f) {
                        tvCinemaRightDistance.text = ""
                    } else {
                        tvCinemaRightDistance.text = "${String.format(Locale.ENGLISH, "%.1f", distance)} km"
                    }
                    clCinemaRight.click {
                        openCinema(cinemaRight)
                    }
                }
            }
        }

        @SuppressLint("SetTextI18n")
        override fun onBindGroupViewHolder(holder: AreaHolder, flatPosition: Int, group: ExpandableGroup<*>) {
            val viewType = getGroupViewType(flatPosition, group)
            if (viewType == TYPE_HEADER_TITLE) {
                holder.itemView.tvTitleGroup.text = (group as? AreaCinema)?.title
                holder.setOnGroupClickListener(null)
                return
            }
            if (viewType == TYPE_HEADER_NEAR_ME) {
                holder.itemView.apply {
                    llCinemaNearMe.removeAllViews()
                    (group as? AreaCinema)?.cinemas?.forEach {cinema ->
                        val cinemaView = llCinemaNearMe.inflate(R.layout.item_cinema_in_near_me)
                        cinemaView.apply {
                            ivCinemaNearMe.loadRounded(cinema.Duong_dan_anh_dai_dien?.toImageUrl())
                            tvCinemaNearMeName.text = cinema.Name
                            val distance = cinema.getDistanceToCurrentLocation(currentLocation)
                            if (distance >= 10000f) {
                                tvCinemaNearMeDistance.text = ""
                            } else {
                                tvCinemaNearMeDistance.text = "${String.format(Locale.ENGLISH, "%.1f", distance)} km"
                            }
                            setOnClickListener {
                                openCinema(cinema)
                            }
                        }
                        llCinemaNearMe.addChild(cinemaView)
                    }
                }
                return
            }
            holder.itemView.tvAreaTitle.text = group.title
            holder.itemView.tvAreaCountCinema.text = "${(group as AreaCinema).cinemas?.size ?: 0}"
            holder.itemView.ivDropdown.visible()
        }

        override fun isGroup(viewType: Int): Boolean {
            return viewType == TYPE_HEADER_NEAR_ME || viewType == TYPE_HEADER_AREA || viewType == TYPE_HEADER_TITLE
        }

        override fun isChild(viewType: Int): Boolean {
            return viewType == -1
        }
    }

    inner class AreaHolder(itemView: View): GroupViewHolder(itemView) {

        override fun expand() {
            super.expand()
            itemView.ivDropdown?.setImageResource(R.drawable.ic_dropdown)
        }

        override fun collapse() {
            super.collapse()
            itemView.ivDropdown?.setImageResource(R.drawable.ic_zipup)
        }

        override fun setOnGroupClickListener(listener: OnGroupClickListener?) {
            super.setOnGroupClickListener(listener)
        }


    }
    inner class CinemaHolder(itemView: View): ChildViewHolder(itemView)
}
