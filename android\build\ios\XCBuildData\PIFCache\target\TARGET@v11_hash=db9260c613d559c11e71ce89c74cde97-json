{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bef1ff8f621f6797583fd7a266cabaa9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983c8d5e1aa4f4380b96817cda074d6a26", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860ac90c6fca353627e6619ba88f48e41", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dbe818b3305b507d5bdc3b02ae1e6122", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860ac90c6fca353627e6619ba88f48e41", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ba2d2510e98e06b5eaadedfcbc51542", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989dcd9ef1f7ca17e341ed497b8690e671", "guid": "bfdfe7dc352907fc980b868725387e98b945c24e843e6415260f084cba33a18b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec1d6d5daf7d4c211cf680d0f4f58f54", "guid": "bfdfe7dc352907fc980b868725387e98773698ee0f5496393ba822b731e7e333"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9f1c0c9254a2d0a21c07338d09ad423", "guid": "bfdfe7dc352907fc980b868725387e989bd8b61784ac2b5fa04d79add1114922"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812baf503e8cc0dceb71988bed761aae5", "guid": "bfdfe7dc352907fc980b868725387e98d30ffce227a306c23fe59ac6066c8d64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852825f934b8b078e03958fce4e48c3dc", "guid": "bfdfe7dc352907fc980b868725387e98c7152957953d4a4f13db82f778d43a1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856b36060a40eec7ae1df313219442e13", "guid": "bfdfe7dc352907fc980b868725387e9817bdc1625a291993e5187948c1065232"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef216bf4cb151e4df2fcf5591cea668c", "guid": "bfdfe7dc352907fc980b868725387e9889ddcfa57751c4c8ca1aecdff8d289ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acda295cb0ca3a24826926ffba6e7760", "guid": "bfdfe7dc352907fc980b868725387e98018aeaa9392657d51b82c48ac84d8a6c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb14ff3da0c8f0ddb1e36d53c43b0516", "guid": "bfdfe7dc352907fc980b868725387e98ff58736e0cb1024ccadeff4a00926677", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb1be11f6cb0ceed71f7d02d99fc30fa", "guid": "bfdfe7dc352907fc980b868725387e985e50980c287849904e25382d6a4d8711"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f60b5bf858a503b42f8a0ad63a82bf7", "guid": "bfdfe7dc352907fc980b868725387e98311d6ac1c1f60ef27f41c060ac42979a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d996935b57df21ecda36778ebd04ebc", "guid": "bfdfe7dc352907fc980b868725387e9824a32f85d42f76cfb7153c2ce939e59b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f01fc497de050bf7ff1cff7864e241b8", "guid": "bfdfe7dc352907fc980b868725387e98bc30fcefd8b16569bc86c2024ea3d622"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818169e901e3ecd7111bd42f00d0689b3", "guid": "bfdfe7dc352907fc980b868725387e987e6604c526d81dbfd2bffc6ef7a338ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e9c1e9ff28f85ae979f1333b4d3f231", "guid": "bfdfe7dc352907fc980b868725387e9897548de38a10671bd361a3e921c38d9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980288204af30b3a42d74b28cc9d033f8f", "guid": "bfdfe7dc352907fc980b868725387e981f824a39b58293b6f8793e053057f912"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1e4193456e4c867eb4aa0a0b49ef55e", "guid": "bfdfe7dc352907fc980b868725387e98c97672528d1f70e94d1354a7efe5b6b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988edcf8c533938259f1c995cf89c5b36d", "guid": "bfdfe7dc352907fc980b868725387e98ea692bee89ef6f28e0f3714362a8211b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c0bdc2099aa6a096ef8f3861f78fab6", "guid": "bfdfe7dc352907fc980b868725387e980af42e4acfd12d8a1241c7c8a6ff51e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98becf15ea8bcbb0dc794a62a220b49135", "guid": "bfdfe7dc352907fc980b868725387e9887f3c9bb320ad9e74b9ad77201c689a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98639ef74c59c329047255764c8ab5627d", "guid": "bfdfe7dc352907fc980b868725387e98f8f74d4c0c25fe5b1a125572b097aa4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a463ea5328f33b634a489c13bed7e086", "guid": "bfdfe7dc352907fc980b868725387e98a44ed29da1b54ea73c1a6acecb7d98d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9e2e7d4948e9b776b5725c9b7650435", "guid": "bfdfe7dc352907fc980b868725387e9860b34ce411ca780a26b93de06a0b0fae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6995843c0652854d73794f9cecbf78f", "guid": "bfdfe7dc352907fc980b868725387e98cfe0406bd508b3b337c98c826d960300"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a6db0c6cb93c423dca46d2f320ee804", "guid": "bfdfe7dc352907fc980b868725387e98edaacac9bd2cb1b7339a1ad4a5c9777f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981040b13dcaa5125fdf0e4201b4cdada9", "guid": "bfdfe7dc352907fc980b868725387e98569f0d5d40177d569534b9e20f9ac6a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4e2c6da99128a674224504b924acfa2", "guid": "bfdfe7dc352907fc980b868725387e98e49576dcc00a63b8eb0edd84cac4eded"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e9cb0eec1686fb1b0b14cba23590271", "guid": "bfdfe7dc352907fc980b868725387e98527d9346163fff8efc4cf273afb80b89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d2b43597c5e775f1ee62a872a4c1af2", "guid": "bfdfe7dc352907fc980b868725387e981b4965e3bbfbd8ad698214fc977ffd22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0679c6eba29810d10ba20a0db35675f", "guid": "bfdfe7dc352907fc980b868725387e98f3346dbc9b217a5470b9447dfaa5299b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7584780407fa23f73d865859bcd55d4", "guid": "bfdfe7dc352907fc980b868725387e9889a2c2dec5d3bcc3ca58518608d1b51e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982addef68152165195072300fc602bd7a", "guid": "bfdfe7dc352907fc980b868725387e98bffe060828448be6bd80569be5f50e11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893c9141319efd42c7d0689a6a00a60c7", "guid": "bfdfe7dc352907fc980b868725387e988e6df90c6d717a8ecb79e8be09f10fdb"}], "guid": "bfdfe7dc352907fc980b868725387e980750b82c5f0b33717be0156dfec862aa", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f3e48b51aaee67331cc0afec17993d6", "guid": "bfdfe7dc352907fc980b868725387e98d46e0ff8cc4253d68a0f730bbebe5f7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a7451fc3fbd567288a6cb5a81950b9d", "guid": "bfdfe7dc352907fc980b868725387e98156b29826dad2041e0dd60721ceb5b05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d322201c40105521ddec10903dc527a0", "guid": "bfdfe7dc352907fc980b868725387e985377fe1143c62c6c3ca4b5d39823a27f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9466b5691d16bfd7c405daf9128ac36", "guid": "bfdfe7dc352907fc980b868725387e98e6e1e55da319d44c4b1d164d7377b5e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d658686f809a6b6210746c5ccd52dc59", "guid": "bfdfe7dc352907fc980b868725387e983376172e0f90d0107e66e43b40535cc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a0d320decf712f026cb9fd3e479e1dc", "guid": "bfdfe7dc352907fc980b868725387e985dc85cc4d10ba002c8f4322e1caa9d80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98455331d0cdfe8b1df2e0459370d276c0", "guid": "bfdfe7dc352907fc980b868725387e9859bc7206672129914e1d1fb8bc606926"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987833507aedc23f1bad88944da037712e", "guid": "bfdfe7dc352907fc980b868725387e98b4f7fb13540a08dff9311132ae03b284"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98473cbff1ccbadd61a51ee41bc0ebe27b", "guid": "bfdfe7dc352907fc980b868725387e9815a38a6bd24c042c16486a97687d88c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d7a0b537d7bb3246308b4911ba42bde", "guid": "bfdfe7dc352907fc980b868725387e987832c426a12ad19c0615c21fce21c545"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895770072cee554a02c8f3b6a1be408f0", "guid": "bfdfe7dc352907fc980b868725387e98e05d4b70c40e303d11ade6656aa986f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832ab913b2f2096ae94c8b27abd447acc", "guid": "bfdfe7dc352907fc980b868725387e986e87ce56e6d4a56aad4ecc0d884683e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982eea806a021fffd0e0092a70c4e818e4", "guid": "bfdfe7dc352907fc980b868725387e980b1a8f4ed02ca50b65a5e6aada5f633f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae3f3602a65fc31052c6768a09a8ea7a", "guid": "bfdfe7dc352907fc980b868725387e98e9d493a6cccfb73f0dbd59fc3da68571"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8b90ef478cb64643d53e0a2c3825e97", "guid": "bfdfe7dc352907fc980b868725387e98c457657adbde97d89b3614c7ed4b0a27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813b6f81cac1c2f5b179aaf9a87acb9f6", "guid": "bfdfe7dc352907fc980b868725387e984dab4978581bb4fa51a85e2d4ad912a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885683d2164def3b6325e6a18246f5f7f", "guid": "bfdfe7dc352907fc980b868725387e9877ecc85fe3cf13246cd21945855871e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816204c83d663b0e51f01f07dc6d10857", "guid": "bfdfe7dc352907fc980b868725387e98d5a7e1219d6caec789c111d25da88cac"}], "guid": "bfdfe7dc352907fc980b868725387e98cb30afd8bf06410a2872238389f10108", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98b048272c55c88a35b6a02bf03dff2143"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "guid": "bfdfe7dc352907fc980b868725387e982d7517eb51eefab22af5deed0dc292b3"}], "guid": "bfdfe7dc352907fc980b868725387e98dc98d62336b640b3cb5bf3f159399edc", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98217c83b7dca97af26ad671e509aa2fed", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9820235908b17df0d0e9f3a94fc1ce8beb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}