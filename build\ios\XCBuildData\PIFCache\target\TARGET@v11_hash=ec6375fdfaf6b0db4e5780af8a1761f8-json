{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981d521e49afd07f3ade2a9e3a83fb1751", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985a18480debf0be8ce8b8b77dbbd2ba9c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9811761c4743f6188231a338e3ad962c51", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cf290076af163ebddb59394aa7aef875", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9811761c4743f6188231a338e3ad962c51", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869bcd512e597dee0d985b4ac3cc436cf", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860aae26063f686df6480704f6f3898f4", "guid": "bfdfe7dc352907fc980b868725387e986026e0b8e582ae00ae043467bf445ea0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d108700b867c0fb5501190beb61ce804", "guid": "bfdfe7dc352907fc980b868725387e9811f849e87ffd6ad025affb26dbb48e75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdb10af699e10527c89cf628460b603e", "guid": "bfdfe7dc352907fc980b868725387e9858a70dbad86c4d9ee172a89989d0fb9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c070edfade3504be5f260a5fe2f5bf1e", "guid": "bfdfe7dc352907fc980b868725387e98c5e6abff54418cf9cb7360dadedef202"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881a470aee4033b9114419d7887e57c0c", "guid": "bfdfe7dc352907fc980b868725387e98647f5addf7aeed14981fdff1aa5e61b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2b1e5edc0d7169a394640f881ec5532", "guid": "bfdfe7dc352907fc980b868725387e98134ccde000259c775cad72f55edec093"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987655af5e3507aadcede9258dcb679699", "guid": "bfdfe7dc352907fc980b868725387e984789b6541889948a09c3eb7cfd08cf11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe023f965d9913be9a3347b80b326644", "guid": "bfdfe7dc352907fc980b868725387e98cbbfbe6c1fd1028a126f1b110a17ef78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b49cf100782d987be6de492a7143acd4", "guid": "bfdfe7dc352907fc980b868725387e9843c0493f93c1c14d3f13dd35d395d6ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865f314c75b0d5495e532bb47de920ef1", "guid": "bfdfe7dc352907fc980b868725387e980d6cf2e23a4286af558d77d5c7b31786"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988445d5f80e3bb0fc533d43c27f0df2ad", "guid": "bfdfe7dc352907fc980b868725387e98b75d2ae6747e3f60e2f0616f18247819"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b817540bc95bf54d140b7ef9549e5bd9", "guid": "bfdfe7dc352907fc980b868725387e98510286f482180f617d6c2110821f0968", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882c8e9771a3d55fe7adec15a65a81eb6", "guid": "bfdfe7dc352907fc980b868725387e984d0e7db32d10f7c20fc6a4abb3364eda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed53ea3dcb3c133de40f6bb76e95b5ea", "guid": "bfdfe7dc352907fc980b868725387e98b614859f5ba1c7b0747431a0f9ea7679"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98286c118b59ffda25d4aec36dca1962ab", "guid": "bfdfe7dc352907fc980b868725387e98de1db5d8527b727048eaa0fa3ff8fdee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ead9778b78ed6ed60ca1e265172e002", "guid": "bfdfe7dc352907fc980b868725387e9804c6c69569eae908bd5df9a06f77343c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98877adbc25392cb00e94745205fb3f0ac", "guid": "bfdfe7dc352907fc980b868725387e98440fbf4f08bf017a869ac3e12e90422f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98253a7089f706a95e6e625f99ab8e9721", "guid": "bfdfe7dc352907fc980b868725387e9831d894a41f4928775527de4d6b958500"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9084678854b98e306767f3718f6d621", "guid": "bfdfe7dc352907fc980b868725387e988ebbfaf6fa65ddd646ea348ab5367a78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f4bacae31ed5901492aa5c598c0b5ac", "guid": "bfdfe7dc352907fc980b868725387e98dc5e83410b1255abab1f667fa08c0a18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98820b8daa70e66152909913441b8f41d4", "guid": "bfdfe7dc352907fc980b868725387e98d4f7cda38863715f78a1ef3802b09394"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a512a249e1de56e8d79176c34b5c0bb2", "guid": "bfdfe7dc352907fc980b868725387e983f26cc6e7ac3bd4c4ae2db0501fa9706", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983601dc1282a87272f8aa0d5c675e7a88", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c77faca91886e01d0bf3103cac812b38", "guid": "bfdfe7dc352907fc980b868725387e986243dc2bce1cf7fc7e185ab332bf9287"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ee4bf82d5619b391dc2b315feab2851", "guid": "bfdfe7dc352907fc980b868725387e981c5aac2b12ac8649850ba9b9910c74dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989340d0f0f7e263dc3c336d9b56c5edf6", "guid": "bfdfe7dc352907fc980b868725387e98b5d557040bd26bc2136e78d1c4bc9715"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98920aab2316b424a247a20549725b15db", "guid": "bfdfe7dc352907fc980b868725387e9820bfb7e38260c2be2413c8b6c85cd1dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98945567410e7818fa36a95729c2a10e5c", "guid": "bfdfe7dc352907fc980b868725387e98ef9143acedc9dadee885e2d4d1bc2216"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceaad49bc5fee75b7d1b01b7eb73e130", "guid": "bfdfe7dc352907fc980b868725387e9827b177a914b7a6a380328fa53fd384cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98714ee8c9dda46e17efba7121d8c40bf6", "guid": "bfdfe7dc352907fc980b868725387e98fdbce8023860cf9e1657d82e80ad0747"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df04c896dfb59a0fd2ae02fd6b253532", "guid": "bfdfe7dc352907fc980b868725387e985d6a137f617a765d4de8eea736296958"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98845ebc0bd101aca7ee16c854692b2bb9", "guid": "bfdfe7dc352907fc980b868725387e98644d76d3de97cf6408640b887a545262"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f239e97ccda1082c0629481942890c0", "guid": "bfdfe7dc352907fc980b868725387e98528f38d1e25d19c31060553c3467fedd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98201235b87e36f5368f148830c28da284", "guid": "bfdfe7dc352907fc980b868725387e98b37ace81c0ca402d84c30c7c30d6b278"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985be80d81efa767fd404fab5665824a95", "guid": "bfdfe7dc352907fc980b868725387e98c28d40f38d2c6ad6ce41bd27f155d63c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826f7a50ab24d6ddcce8f23a17f24d103", "guid": "bfdfe7dc352907fc980b868725387e98ad3e19818dfc65fbe520e4b10ee1e0f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4c8a3ad1eecf8dad67771a2537766b3", "guid": "bfdfe7dc352907fc980b868725387e98bf45e9828c0f63d52a0c1339b955be12"}], "guid": "bfdfe7dc352907fc980b868725387e983e9b7df11812e806b4265c98f81f65dd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98224c8120f50758c998529b28f3ee06f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d391d713992f1d4e99af11b30a3d8fcd", "guid": "bfdfe7dc352907fc980b868725387e982c19f707f85f7ca0862c8d191e7b7776"}], "guid": "bfdfe7dc352907fc980b868725387e9867abe7f2ee84729c5f3935e355295c21", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cee49f278f2034ebb7aad9ca3582a6b0", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98d1e5a2f5cd64b46b194b30f8ba7a940e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}