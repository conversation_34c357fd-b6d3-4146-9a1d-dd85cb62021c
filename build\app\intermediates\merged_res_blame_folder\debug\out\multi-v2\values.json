{"logs": [{"outputFile": "com.flutter.flutter.petro.app-mergeDebugResources-55:/values/values.xml", "map": [{"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\0e2fd2d0d3b3f462505a0fe60954a8a8\\transformed\\jetified-activity-1.9.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "345,373", "startColumns": "4,4", "startOffsets": "20810,22265", "endColumns": "41,59", "endOffsets": "20847,22320"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\10bd4d9e9b2285a923396ec591922feb\\transformed\\jetified-exoplayer-core-2.18.7\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "456,457,458,459,460,461,462,463,464", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "28735,28805,28867,28932,28996,29073,29138,29228,29312", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "28800,28862,28927,28991,29068,29133,29223,29307,29376"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\b3a78d3d4f57d7607051978acffd5ad0\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "82,83,84,85,236,237,454,466,467,468", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3720,3778,3844,3907,14047,14118,28597,29438,29505,29584", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3773,3839,3902,3964,14113,14185,28660,29500,29579,29648"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\48c9a35c82f156be1684c8ab76ef6b03\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "374", "startColumns": "4", "startOffsets": "22325", "endColumns": "53", "endOffsets": "22374"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\b65ab1a894c6b8dbf614ad54010261ad\\transformed\\jetified-play-services-base-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "90,91,92,93,94,95,96,97,435,436,437,438,439,440,441,442,444,445,446,447,448,449,450,451,452,3243,3716", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4215,4305,4385,4475,4565,4645,4726,4806,26226,26331,26512,26637,26744,26924,27047,27163,27433,27621,27726,27907,28032,28207,28355,28418,28480,182364,197079", "endLines": "90,91,92,93,94,95,96,97,435,436,437,438,439,440,441,442,444,445,446,447,448,449,450,451,452,3255,3734", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4300,4380,4470,4560,4640,4721,4801,4881,26326,26507,26632,26739,26919,27042,27158,27261,27616,27721,27902,28027,28202,28350,28413,28475,28554,182674,197491"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\fd8f94385d16413adb0e8f5b1a36f410\\transformed\\jetified-zxing-android-embedded-4.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,38,48,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,188,252,317,382,436,490,544,603,661,708,757,805,847,896,948,1006,1056,1110,1176,1246,1343,1460,1556,1717,1830,1916,2063,2162,2309,2368,2415,2559,2672,2772,3228,3593", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,37,47,54,57", "endColumns": "71,60,63,64,64,53,53,53,58,57,46,48,47,41,48,51,57,49,53,65,69,96,116,95,160,112,85,146,98,146,58,46,143,112,10,22,22,22", "endOffsets": "122,183,247,312,377,431,485,539,598,656,703,752,800,842,891,943,1001,1051,1105,1171,1241,1338,1455,1551,1712,1825,1911,2058,2157,2304,2363,2410,2554,2667,2767,3223,3588,3712"}, "to": {"startLines": "145,146,147,148,149,150,151,152,153,154,377,378,379,380,381,382,383,384,385,427,455,470,471,472,473,474,475,476,477,478,489,490,491,492,2217,4030,4040,4047", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7992,8064,8125,8189,8254,8319,8373,8427,8481,8540,22493,22540,22589,22637,22679,22728,22780,22838,22888,25649,28665,29735,29832,29949,30045,30206,30319,30405,30552,30651,31390,31449,31496,31640,145663,208920,209376,209741", "endLines": "145,146,147,148,149,150,151,152,153,154,377,378,379,380,381,382,383,384,385,427,455,470,471,472,473,474,475,476,477,478,489,490,491,492,2218,4039,4046,4049", "endColumns": "71,60,63,64,64,53,53,53,58,57,46,48,47,41,48,51,57,49,53,65,69,96,116,95,160,112,85,146,98,146,58,46,143,112,10,22,22,22", "endOffsets": "8059,8120,8184,8249,8314,8368,8422,8476,8535,8593,22535,22584,22632,22674,22723,22775,22833,22883,22937,25710,28730,29827,29944,30040,30201,30314,30400,30547,30646,30793,31444,31491,31635,31748,145758,209371,209736,209860"}}, {"source": "D:\\geneat\\beta-moible-flutter\\build\\flutter_inappwebview_android\\intermediates\\packaged_res\\debug\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,11,14", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,102,153,210,259,306,353,426,502", "endLines": "2,3,4,5,6,7,10,13,21", "endColumns": "46,50,56,48,46,46,12,12,12", "endOffsets": "97,148,205,254,301,348,421,497,949"}, "to": {"startLines": "421,422,423,424,425,479,498,1601,2097", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "25315,25362,25413,25470,25519,30798,32195,102704,136651", "endLines": "421,422,423,424,425,479,500,1603,2104", "endColumns": "46,50,56,48,46,46,12,12,12", "endOffsets": "25357,25408,25465,25514,25561,30840,32263,102775,137098"}}, {"source": "D:\\geneat\\beta-moible-flutter\\android\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "56", "endOffsets": "109"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "5567", "endColumns": "56", "endOffsets": "5619"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\0e502190d728b4ada7e3274fa39bc377\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "372", "startColumns": "4", "startOffsets": "22222", "endColumns": "42", "endOffsets": "22260"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\f6637771952336731f0e0751842691c1\\transformed\\jetified-android-maps-utils-3.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,7,10", "startColumns": "4,4,4,4", "startOffsets": "55,93,301,461", "endLines": "2,6,9,14", "endColumns": "37,12,12,12", "endOffsets": "88,296,456,708"}, "to": {"startLines": "329,2205,2209,2212", "startColumns": "4,4,4,4", "startOffsets": "20040,145043,145251,145411", "endLines": "329,2208,2211,2216", "endColumns": "37,12,12,12", "endOffsets": "20073,145246,145406,145658"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\66324e78beec671b05773d52d84e9d9a\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,88,89,121,122,238,239,240,241,242,243,244,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,336,337,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,393,428,429,430,431,432,433,434,485,2033,2034,2039,2042,2047,2200,2201,2880,2925,3095,3128,3158,3191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2715,2787,4084,4149,6296,6365,14190,14260,14328,14400,14470,14531,14605,15848,15909,15970,16032,16096,16158,16219,16287,16387,16447,16513,16586,16655,16712,16764,17712,17784,17860,17925,17984,18043,18103,18163,18223,18283,18343,18403,18463,18523,18583,18643,18702,18762,18822,18882,18942,19002,19062,19122,19182,19242,19302,19361,19421,19481,19540,19599,19658,19717,19776,20382,20417,21065,21120,21183,21238,21296,21354,21415,21478,21535,21586,21636,21697,21754,21820,21854,21889,23379,25715,25782,25854,25923,25992,26066,26138,31082,131872,131989,132256,132549,132816,144759,144831,167430,169404,177239,178970,179970,180652", "endLines": "29,70,71,88,89,121,122,238,239,240,241,242,243,244,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,336,337,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,393,428,429,430,431,432,433,434,485,2033,2037,2039,2045,2047,2200,2201,2885,2934,3127,3148,3190,3196", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2782,2870,4144,4210,6360,6423,14255,14323,14395,14465,14526,14600,14673,15904,15965,16027,16091,16153,16214,16282,16382,16442,16508,16581,16650,16707,16759,16821,17779,17855,17920,17979,18038,18098,18158,18218,18278,18338,18398,18458,18518,18578,18638,18697,18757,18817,18877,18937,18997,19057,19117,19177,19237,19297,19356,19416,19476,19535,19594,19653,19712,19771,19830,20412,20447,21115,21178,21233,21291,21349,21410,21473,21530,21581,21631,21692,21749,21815,21849,21884,21919,23444,25777,25849,25918,25987,26061,26133,26221,31148,131984,132185,132361,132745,132940,144826,144893,167628,169700,178965,179646,180647,180814"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\80c4d18896cdbc773b2f1f60789b5e42\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "426", "startColumns": "4", "startOffsets": "25566", "endColumns": "82", "endOffsets": "25644"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\dba738d2b33ccc893d17ddca3d0700d1\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,330,2323,2329,3747,3755,3770", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,20078,149286,149481,197808,198090,198704", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,330,2328,2333,3754,3769,3785", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,20133,149476,149634,198085,198699,199353"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\081664dd3597f3416efdf45ca7a75130\\transformed\\jetified-firebase-messaging-24.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "469", "startColumns": "4", "startOffsets": "29653", "endColumns": "81", "endOffsets": "29730"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\b088aec0414d8905b0878d7c76776e03\\transformed\\swiperefreshlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "24", "endOffsets": "287"}, "to": {"startLines": "3806", "startColumns": "4", "startOffsets": "199896", "endLines": "3809", "endColumns": "24", "endOffsets": "200062"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\e7b23cb7045a4238e7d3dac5d99ded32\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "247,248,249,257,258,259,335,3648", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14851,14910,14958,15625,15700,15776,20316,194749", "endLines": "247,248,249,257,258,259,335,3667", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14905,14953,15009,15695,15771,15843,20377,195539"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\0fd8cafb08e54981095cfb944a5eaa6b\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "375", "startColumns": "4", "startOffsets": "22379", "endColumns": "49", "endOffsets": "22424"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\1cc0be700c7357f503838b32dba7b93f\\transformed\\jetified-play-services-maps-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "167", "endLines": "66", "endColumns": "20", "endOffsets": "1669"}, "to": {"startLines": "3256", "startColumns": "4", "startOffsets": "182679", "endLines": "3318", "endColumns": "20", "endOffsets": "184181"}}, {"source": "D:\\geneat\\beta-moible-flutter\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1604,1608", "startColumns": "4,4", "startOffsets": "102780,102961", "endLines": "1607,1610", "endColumns": "12,12", "endOffsets": "102956,103125"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\3b03807329c558a737d9cb34e921c2d5\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "331,348,376,3149,3154", "startColumns": "4,4,4,4,4", "startOffsets": "20138,20954,22429,179651,179821", "endLines": "331,348,376,3153,3157", "endColumns": "56,64,63,24,24", "endOffsets": "20190,21014,22488,179816,179965"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\ea3b19e143447a44a4f630074d7ef7d3\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "4,27,28,59,60,61,63,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,109,110,111,112,113,114,115,116,117,118,119,120,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,245,246,250,251,252,253,254,255,256,282,283,284,285,286,287,288,289,325,326,327,328,334,343,344,349,371,386,387,388,389,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,484,493,494,495,496,497,501,509,510,514,518,522,527,533,540,544,548,553,557,561,565,569,573,577,583,587,593,597,603,607,612,616,619,623,629,633,639,643,649,652,656,660,664,668,672,673,674,675,678,681,684,687,691,692,693,694,695,698,700,702,704,709,710,714,720,724,725,727,739,740,744,750,754,755,756,760,787,791,792,796,824,996,1022,1193,1219,1250,1258,1264,1280,1302,1307,1312,1322,1331,1340,1344,1351,1370,1377,1378,1387,1390,1393,1397,1401,1405,1408,1409,1414,1419,1429,1434,1441,1447,1448,1451,1455,1460,1462,1464,1467,1470,1472,1476,1479,1486,1489,1492,1496,1498,1502,1504,1506,1508,1512,1520,1528,1540,1546,1555,1558,1569,1572,1573,1578,1579,1611,1680,1750,1751,1761,1770,1922,1924,1928,1931,1934,1937,1940,1943,1946,1949,1953,1956,1959,1962,1966,1969,1973,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1999,2001,2002,2003,2004,2005,2006,2007,2008,2010,2011,2013,2014,2016,2018,2019,2021,2022,2023,2024,2025,2026,2028,2029,2030,2031,2032,2049,2051,2053,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2069,2070,2071,2072,2073,2074,2075,2077,2081,2085,2086,2087,2088,2089,2090,2094,2095,2096,2105,2107,2109,2111,2113,2115,2116,2117,2118,2120,2122,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2138,2139,2140,2141,2143,2145,2146,2148,2149,2151,2153,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2168,2169,2170,2171,2173,2174,2175,2176,2177,2179,2181,2183,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2219,2294,2297,2300,2303,2317,2334,2376,2379,2408,2435,2444,2508,2876,2897,2935,3073,3197,3221,3227,3319,3340,3464,3492,3498,3642,3668,3735,3810,3910,3930,3985,3997,4023", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2214,2278,2348,2409,2484,2560,2637,2875,2960,3042,3118,3194,3271,3349,3455,3561,3640,3969,4026,4886,4960,5035,5100,5166,5226,5287,5359,5432,5499,5624,5683,5742,5801,5860,5919,5973,6027,6080,6134,6188,6242,6586,6660,6739,6812,6886,6957,7029,7101,7174,7231,7289,7362,7436,7510,7585,7657,7730,7800,7871,7931,8598,8667,8736,8806,8880,8956,9020,9097,9173,9250,9315,9384,9461,9536,9605,9673,9750,9816,9877,9974,10039,10108,10207,10278,10337,10395,10452,10511,10575,10646,10718,10790,10862,10934,11001,11069,11137,11196,11259,11323,11413,11504,11564,11630,11697,11763,11833,11897,11950,12017,12078,12145,12258,12316,12379,12444,12509,12584,12657,12729,12773,12820,12866,12915,12976,13037,13098,13160,13224,13288,13352,13417,13480,13540,13601,13667,13726,13786,13848,13919,13979,14678,14764,15014,15104,15191,15279,15361,15444,15534,17259,17311,17369,17414,17480,17544,17601,17658,19835,19892,19940,19989,20282,20714,20761,21019,22190,22942,23006,23068,23128,23449,23523,23593,23671,23725,23795,23880,23928,23974,24035,24098,24164,24228,24299,24362,24427,24491,24552,24613,24665,24738,24812,24881,24956,25030,25104,25245,31029,31753,31831,31921,32009,32105,32268,32850,32939,33186,33467,33719,34004,34397,34874,35096,35318,35594,35821,36051,36281,36511,36741,36968,37387,37613,38038,38268,38696,38915,39198,39406,39537,39764,40190,40415,40842,41063,41488,41608,41884,42185,42509,42800,43114,43251,43382,43487,43729,43896,44100,44308,44579,44691,44803,44908,45025,45239,45385,45525,45611,45959,46047,46293,46711,46960,47042,47140,47797,47897,48149,48573,48828,48922,49011,49248,51272,51514,51616,51869,54025,64706,66222,76917,78445,80202,80828,81248,82509,83774,84030,84266,84813,85307,85912,86110,86690,88058,88433,88551,89089,89246,89442,89715,89971,90141,90282,90346,90711,91078,91754,92018,92356,92709,92803,92989,93295,93557,93682,93809,94048,94259,94378,94571,94748,95203,95384,95506,95765,95878,96065,96167,96274,96403,96678,97186,97682,98559,98853,99423,99572,100304,100476,100560,100896,100988,103130,108361,113732,113794,114372,114956,122903,123016,123245,123405,123557,123728,123894,124063,124230,124393,124636,124806,124979,125150,125424,125623,125828,126158,126242,126338,126434,126532,126632,126734,126836,126938,127040,127142,127242,127338,127450,127579,127702,127833,127964,128062,128176,128270,128410,128544,128640,128752,128852,128968,129064,129176,129276,129416,129552,129716,129846,130004,130154,130295,130439,130574,130686,130836,130964,131092,131228,131360,131490,131620,131732,133012,133158,133302,133440,133506,133596,133672,133776,133866,133968,134076,134184,134284,134364,134456,134554,134664,134716,134794,134900,134992,135096,135206,135328,135491,135648,135728,135828,135918,136028,136118,136359,136453,136559,137103,137203,137315,137429,137545,137661,137755,137869,137981,138083,138203,138325,138407,138511,138631,138757,138855,138949,139037,139149,139265,139387,139499,139674,139790,139876,139968,140080,140204,140271,140397,140465,140593,140737,140865,140934,141029,141144,141257,141356,141465,141576,141687,141788,141893,141993,142123,142214,142337,142431,142543,142629,142733,142829,142917,143035,143139,143243,143369,143457,143565,143665,143755,143865,143949,144051,144135,144189,144253,144359,144445,144555,144639,145763,148379,148497,148612,148692,149053,149639,151043,151121,152465,153826,154214,157057,167295,168034,169705,176518,180819,181570,181832,184186,184565,188843,189697,189926,194534,195544,197496,200067,204191,204935,207066,207406,208717", "endLines": "4,27,28,59,60,61,63,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,109,110,111,112,113,114,115,116,117,118,119,120,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,245,246,250,251,252,253,254,255,256,282,283,284,285,286,287,288,289,325,326,327,328,334,343,344,349,371,386,387,388,389,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,484,493,494,495,496,497,508,509,513,517,521,526,532,539,543,547,552,556,560,564,568,572,576,582,586,592,596,602,606,611,615,618,622,628,632,638,642,648,651,655,659,663,667,671,672,673,674,677,680,683,686,690,691,692,693,694,697,699,701,703,708,709,713,719,723,724,726,738,739,743,749,753,754,755,759,786,790,791,795,823,995,1021,1192,1218,1249,1257,1263,1279,1301,1306,1311,1321,1330,1339,1343,1350,1369,1376,1377,1386,1389,1392,1396,1400,1404,1407,1408,1413,1418,1428,1433,1440,1446,1447,1450,1454,1459,1461,1463,1466,1469,1471,1475,1478,1485,1488,1491,1495,1497,1501,1503,1505,1507,1511,1519,1527,1539,1545,1554,1557,1568,1571,1572,1577,1578,1583,1679,1749,1750,1760,1769,1770,1923,1927,1930,1933,1936,1939,1942,1945,1948,1952,1955,1958,1961,1965,1968,1972,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1998,2000,2001,2002,2003,2004,2005,2006,2007,2009,2010,2012,2013,2015,2017,2018,2020,2021,2022,2023,2024,2025,2027,2028,2029,2030,2031,2032,2050,2052,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2068,2069,2070,2071,2072,2073,2074,2076,2080,2084,2085,2086,2087,2088,2089,2093,2094,2095,2096,2106,2108,2110,2112,2114,2115,2116,2117,2119,2121,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2137,2138,2139,2140,2142,2144,2145,2147,2148,2150,2152,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2167,2168,2169,2170,2172,2173,2174,2175,2176,2178,2180,2182,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2293,2296,2299,2302,2316,2322,2343,2378,2407,2434,2443,2507,2870,2879,2924,2962,3090,3220,3226,3232,3339,3463,3483,3497,3501,3647,3702,3746,3875,3929,3984,3996,4022,4029", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2136,2273,2343,2404,2479,2555,2632,2710,2955,3037,3113,3189,3266,3344,3450,3556,3635,3715,4021,4079,4955,5030,5095,5161,5221,5282,5354,5427,5494,5562,5678,5737,5796,5855,5914,5968,6022,6075,6129,6183,6237,6291,6655,6734,6807,6881,6952,7024,7096,7169,7226,7284,7357,7431,7505,7580,7652,7725,7795,7866,7926,7987,8662,8731,8801,8875,8951,9015,9092,9168,9245,9310,9379,9456,9531,9600,9668,9745,9811,9872,9969,10034,10103,10202,10273,10332,10390,10447,10506,10570,10641,10713,10785,10857,10929,10996,11064,11132,11191,11254,11318,11408,11499,11559,11625,11692,11758,11828,11892,11945,12012,12073,12140,12253,12311,12374,12439,12504,12579,12652,12724,12768,12815,12861,12910,12971,13032,13093,13155,13219,13283,13347,13412,13475,13535,13596,13662,13721,13781,13843,13914,13974,14042,14759,14846,15099,15186,15274,15356,15439,15529,15620,17306,17364,17409,17475,17539,17596,17653,17707,19887,19935,19984,20035,20311,20756,20805,21060,22217,23001,23063,23123,23180,23518,23588,23666,23720,23790,23875,23923,23969,24030,24093,24159,24223,24294,24357,24422,24486,24547,24608,24660,24733,24807,24876,24951,25025,25099,25240,25310,31077,31826,31916,32004,32100,32190,32845,32934,33181,33462,33714,33999,34392,34869,35091,35313,35589,35816,36046,36276,36506,36736,36963,37382,37608,38033,38263,38691,38910,39193,39401,39532,39759,40185,40410,40837,41058,41483,41603,41879,42180,42504,42795,43109,43246,43377,43482,43724,43891,44095,44303,44574,44686,44798,44903,45020,45234,45380,45520,45606,45954,46042,46288,46706,46955,47037,47135,47792,47892,48144,48568,48823,48917,49006,49243,51267,51509,51611,51864,54020,64701,66217,76912,78440,80197,80823,81243,82504,83769,84025,84261,84808,85302,85907,86105,86685,88053,88428,88546,89084,89241,89437,89710,89966,90136,90277,90341,90706,91073,91749,92013,92351,92704,92798,92984,93290,93552,93677,93804,94043,94254,94373,94566,94743,95198,95379,95501,95760,95873,96060,96162,96269,96398,96673,97181,97677,98554,98848,99418,99567,100299,100471,100555,100891,100983,101261,108356,113727,113789,114367,114951,115042,123011,123240,123400,123552,123723,123889,124058,124225,124388,124631,124801,124974,125145,125419,125618,125823,126153,126237,126333,126429,126527,126627,126729,126831,126933,127035,127137,127237,127333,127445,127574,127697,127828,127959,128057,128171,128265,128405,128539,128635,128747,128847,128963,129059,129171,129271,129411,129547,129711,129841,129999,130149,130290,130434,130569,130681,130831,130959,131087,131223,131355,131485,131615,131727,131867,133153,133297,133435,133501,133591,133667,133771,133861,133963,134071,134179,134279,134359,134451,134549,134659,134711,134789,134895,134987,135091,135201,135323,135486,135643,135723,135823,135913,136023,136113,136354,136448,136554,136646,137198,137310,137424,137540,137656,137750,137864,137976,138078,138198,138320,138402,138506,138626,138752,138850,138944,139032,139144,139260,139382,139494,139669,139785,139871,139963,140075,140199,140266,140392,140460,140588,140732,140860,140929,141024,141139,141252,141351,141460,141571,141682,141783,141888,141988,142118,142209,142332,142426,142538,142624,142728,142824,142912,143030,143134,143238,143364,143452,143560,143660,143750,143860,143944,144046,144130,144184,144248,144354,144440,144550,144634,144754,148374,148492,148607,148687,149048,149281,150151,151116,152460,153821,154209,157052,167105,167425,169399,171057,177085,181565,181827,182027,184560,188838,189444,189921,190072,194744,196622,197803,203088,204930,207061,207401,208712,208915"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\d552f6a7ccd94a500b0ef15a133e6620\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "390,443", "startColumns": "4,4", "startOffsets": "23185,27266", "endColumns": "67,166", "endOffsets": "23248,27428"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\5d18fffccc249c12ecbab6b8a87e0427\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "62,124,275,276,277,278,279,280,281,340,341,342,391,392,453,465,480,481,486,487,488,1584,1771,1774,1780,1786,1789,1795,1799,1802,1809,1815,1818,1824,1829,1834,1841,1843,1849,1855,1863,1868,1875,1880,1886,1890,1897,1901,1907,1913,1916,1920,1921,2871,2886,3053,3091,3233,3484,3502,3566,3576,3586,3593,3599,3703,3876,3893", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2141,6517,16826,16890,16945,17013,17080,17145,17202,20557,20605,20653,23253,23316,28559,29381,30845,30889,31153,31292,31342,101266,115047,115152,115397,115735,115881,116221,116433,116596,117003,117341,117464,117803,118042,118299,118670,118730,119068,119354,119803,120095,120483,120788,121132,121377,121707,121914,122182,122455,122599,122800,122847,167110,167633,175789,177090,182032,189449,190077,192002,192284,192589,192851,193111,196627,203093,203623", "endLines": "62,124,275,276,277,278,279,280,281,340,341,342,391,392,453,465,480,483,486,487,488,1600,1773,1779,1785,1788,1794,1798,1801,1808,1814,1817,1823,1828,1833,1840,1842,1848,1854,1862,1867,1874,1879,1885,1889,1896,1900,1906,1912,1915,1919,1920,1921,2875,2896,3072,3094,3242,3491,3565,3575,3585,3592,3598,3641,3715,3892,3909", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2209,6581,16885,16940,17008,17075,17140,17197,17254,20600,20648,20709,23311,23374,28592,29433,30884,31024,31287,31337,31385,102699,115147,115392,115730,115876,116216,116428,116591,116998,117336,117459,117798,118037,118294,118665,118725,119063,119349,119798,120090,120478,120783,121127,121372,121702,121909,122177,122450,122594,122795,122842,122898,167290,168029,176513,177234,182359,189692,191997,192279,192584,192846,193106,194529,197074,203618,204186"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\d0e385fe82401a0191f1cf9d187cd9f0\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2344,2360,2366,3786,3802", "startColumns": "4,4,4,4,4", "startOffsets": "150156,150581,150759,199358,199769", "endLines": "2359,2365,2375,3801,3805", "endColumns": "24,24,24,24,24", "endOffsets": "150576,150754,151038,199764,199891"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\96153ba6471db1591a8ad507cb8743e6\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "332,333,339,346,347,366,367,368,369,370", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "20195,20235,20514,20852,20907,21924,21978,22030,22079,22140", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "20230,20277,20552,20902,20949,21973,22025,22074,22135,22185"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\617dcfbe9c747e0a623f2b2928118718\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2202,2963,2969", "startColumns": "4,4,4,4", "startOffsets": "164,144898,171062,171273", "endLines": "3,2204,2968,3052", "endColumns": "60,12,24,24", "endOffsets": "220,145038,171268,175784"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\ef3b1689b5598393cd3c27b001894219\\transformed\\media-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,5,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,288,410,476,598,659,725", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "139,345,471,593,654,720,787"}, "to": {"startLines": "123,338,2038,2040,2041,2046,2048", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6428,20452,132190,132366,132488,132750,132945", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "6512,20509,132251,132483,132544,132811,133007"}}]}]}