import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
import 'package:flutter_app/pages/cinema/model/combo_list_model.dart';
import 'package:flutter_app/pages/cinema/model/list_seat_model.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:webview_flutter/webview_flutter.dart';
// #docregion platform_imports
// Import for Android features.
// Import for iOS/macOS features.


import '../../../cubit/index.dart';

/// Màn hình thanh toán WebView - tương ứng với PaymentViewController trong iOS
///
/// Màn hình này hiển thị trang web thanh toán và xử lý các tương tác thanh toán
/// <PERSON><PERSON> cũng theo dõi thông tin combo, voucher và các thông tin thanh toán khác
/// Tương ứng với PaymentViewController.swift trong repo iOS
class WebViewPaymentScreen extends StatefulWidget {
  final String htmlData;           // HTML data từ API booking
  final String baseUrl;            // Base URL cho WebView
  final FilmModel? film;           // Thông tin phim
  final ListSeatModel? listSeat;   // Thông tin ghế và suất chiếu
  final String? cinemaId;          // ID rạp chiếu phim
  final String? cinemaName;        // Tên rạp chiếu phim
  final Function(String?) onPaymentSuccess;       // Callback khi thanh toán thành công
  final Function(String?) onPaymentFailed;        // Callback khi thanh toán thất bại
  final Function() onPaymentWaiting;              // Callback khi đang chờ thanh toán
  final Function(String) onPaymentMethodSelected; // Callback khi chọn phương thức thanh toán
  final int? totalPrice;           // Tổng giá tiền

  const WebViewPaymentScreen({
    super.key,
    required this.htmlData,
    required this.baseUrl,
    this.film,
    this.listSeat,
    this.cinemaId,
    this.totalPrice,
    this.cinemaName,
    required this.onPaymentSuccess,
    required this.onPaymentFailed,
    required this.onPaymentWaiting,
    required this.onPaymentMethodSelected,
  });

  @override
  _WebViewPaymentScreenState createState() => _WebViewPaymentScreenState();
}

class _WebViewPaymentScreenState extends State<WebViewPaymentScreen> {
  /// The WebViewController for managing the WebView
  late WebViewController _webViewController;

  /// Loading state for the WebView
  bool _isLoading = true;

  /// Title of the current page in the WebView
  String? _title;

  /// Stores the AirPay order ID for tracking payment status
  String? _airPayOrderId;

  @override
  void initState() {
    super.initState();

    /// Initialize platform-specific WebView settings
    /// iOS and Android have different WebView implementations
    /// that need to be configured separately
    _initWebView();
  }

  void _initWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            if (progress == 100) {
              setState(() {
                _isLoading = false;
              });
            }
          },
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            _updateBookingInfo();
            _webViewController.getTitle().then((title) {
              setState(() {
                _title = title;
              });
            });
          },
          onWebResourceError: (WebResourceError error) {
            print('WebView error: ${error.description}');
          },
          /// Xử lý khi WebView yêu cầu điều hướng đến một URL mới - tương ứng với webView:decidePolicyFor:decisionHandler: trong iOS
          ///
          /// Phương thức này kiểm tra URL đích và quyết định cách xử lý:
          /// - Nếu là URL thanh toán bên ngoài (airpay, momo, zalopay), mở trong trình duyệt bên ngoài
          /// - Nếu là URL thanh toán nội địa hoặc quốc tế, cho phép điều hướng trong WebView
          /// - Nếu là URL khác, cho phép điều hướng trong WebView
          onNavigationRequest: (NavigationRequest request) async {
            final url = request.url;
            print("Navigation request to: $url");

            // Xử lý các URL thanh toán bên ngoài - tương ứng với iOS
            if (url.contains('airpay.vn') && await canLaunchUrl(Uri.parse(url))) {
              widget.onPaymentMethodSelected('airpay');
              _trackPayment('confirm');
              _launchExternalUrl(url);

              // Extract order_id from URL
              final uri = Uri.parse(url);
              _airPayOrderId = uri.queryParameters['order_id'];

              return NavigationDecision.prevent;
            } else if (url.contains('payment.momo') &&  await canLaunchUrl(Uri.parse(url))) {
              widget.onPaymentMethodSelected('momo');
              _trackPayment('confirm');
              _launchExternalUrl(url);
              return NavigationDecision.prevent;
            } else if (url.contains('gateway.zalopay.vn') &&  await canLaunchUrl(Uri.parse(url))) {
              widget.onPaymentMethodSelected('zalopay');
              _trackPayment('confirm');
              _launchExternalUrl(url);
              return NavigationDecision.prevent;
            } else if (url.contains('mtf.onepay.vn/onecomm-pay')) {
              widget.onPaymentMethodSelected('noidia');
              _trackPayment('confirm');
              return NavigationDecision.navigate;
            } else if (url.contains('mtf.onepay.vn/promotion/vpcpr.op')) {
              widget.onPaymentMethodSelected('quocte');
              _trackPayment('confirm');
              return NavigationDecision.navigate;
            }
            // Xử lý các URL Beta Voucher và Điểm BETA - không có trong iOS nhưng cần thiết cho Flutter
            else if (url.contains('voucher') || url.contains('coupon') || url.contains('beta-point')) {
              print("Navigating to voucher/coupon/beta-point page: $url");
              // Cho phép điều hướng trong WebView
              return NavigationDecision.navigate;
            }

            // Cho phép điều hướng đến các URL khác
            return NavigationDecision.navigate;
          },
        ),
      )
      ..addJavaScriptChannel(
        'Flutter',
        onMessageReceived: (JavaScriptMessage message) {
          _handleJavaScriptMessage(message.message);
        },
      )
      ..loadHtmlString(widget.htmlData, baseUrl: /*Uri.parse(*/widget.baseUrl/*)*/);
  }

  void _updateBookingInfo() {
    _webViewController.runJavaScriptReturningResult("screenType;").then((result) {
      if (result == "payment") {
        _loadBookingInfo();
      }
    }).catchError((error) {
      print('Error checking screen type: $error');
    });
  }

  /// Cập nhật thông tin đặt vé trong WebView - tương ứng với updateBookingInfo trong iOS
  ///
  /// Phương thức này gửi thông tin phim, rạp, ngày chiếu, giờ chiếu, combo, tổng tiền, phòng chiếu, poster phim
  /// và mã định dạng phim đến WebView để hiển thị cho người dùng
  void _loadBookingInfo() {
    final film = widget.film;
    final listSeat = widget.listSeat;

    if (film == null || listSeat == null) return;

    try {
      // Format date and time - tương ứng với iOS
      final dateFormat = DateFormat('dd/MM/yyyy');
      final timeFormat = DateFormat('HH:mm');

      DateTime? date;
      DateTime? time;

      try {
        // Trong iOS: date = listSeat?.NgayChieu?.toDate("yyyy-MM-dd'T'HH:mm:ss")
        // Trong iOS: time = listSeat?.GioChieu?.toDate("yyyy-MM-dd'T'HH:mm:ss")
        date = listSeat.ngayChieu != null ? DateTime.parse(listSeat.ngayChieu!) : null;
        time = listSeat.gioChieu != null ? DateTime.parse(listSeat.gioChieu!) : null;
      } catch (e) {
        print('Error parsing date/time: $e');
      }

      final dateStr = date != null ? dateFormat.format(date) : '';
      final timeStr = time != null ? timeFormat.format(time) : '';

      // Kiểm tra xem các biến JavaScript đã được khởi tạo chưa
      _webViewController.runJavaScriptReturningResult("typeof listCombo").then((result) {
        if (result == "undefined") {
          print("Warning: listCombo is undefined, initializing it");
          _webViewController.runJavaScript("var listCombo = [];").catchError((error) {
            print('Error initializing listCombo: $error');
          });
        }
      }).catchError((error) {
        print('Error checking listCombo: $error');
      });

      _webViewController.runJavaScriptReturningResult("typeof dataBooking").then((result) {
        if (result == "undefined") {
          print("Warning: dataBooking is undefined, initializing it");
          _webViewController.runJavaScript("var dataBooking = {};").catchError((error) {
            print('Error initializing dataBooking: $error');
          });
        }
      }).catchError((error) {
        print('Error checking dataBooking: $error');
      });

      // Tính toán combo - trong iOS là biến combo, mặc định là chuỗi rỗng
      const combo = '';

      // Build JavaScript method call - tương ứng chính xác với iOS
      final method = "getBookingInfo({'FilmName': '${film.getName()}', "
          "'FilmInfo': '${film.getFinalOptions()}', "
          "'CinemaName': '${listSeat.tenRap ?? ""}', "
          "'DateShow': '$dateStr', "
          "'ShowTime': '$timeStr', "
          "'Combo': '$combo', "
          "'TotalMoney': '${_formatCurrency(widget.totalPrice ?? 0)}', "
          "'Screen': '${listSeat.phongChieu ?? ""}', "
          "'FilmPoster': '${ApiService.baseUrlImage}/${film.MainPosterUrl ?? ""}', "
          "'FilmFormatCode': '${listSeat.filmFormatCode ?? ""}'});";

      print("Booking info method: $method");
      _webViewController.runJavaScript(method).catchError((error) {
        print('Error updating booking info: $error');
      });

      // Load customer info if available - tương ứng với iOS
      final user = context.read<AuthC>().state.user;
      if (user != null) {
        // Trong iOS: webView.evaluateJavaScript("getCustomerInfo({'customerId': '\(user.AccountId ?? "")', 'customerCard': '\(user.CardNumber ?? "")'});")
        final customerInfoMethod = "getCustomerInfo({'customerId': '${user.accountId ?? ""}', 'customerCard': '${user.cardNumber ?? ""}'});";
        print("Customer info method: $customerInfoMethod");
        _webViewController.runJavaScript(customerInfoMethod).catchError((error) {
          print('Error updating customer info: $error');
        });
      }
    } catch (e) {
      print('Error in loadBookingInfo: $e');
    }
  }

  String _formatCurrency(int amount) {
    final formatter = NumberFormat("#,###", "vi_VN");
    return formatter.format(amount);
  }

  /// Phân tích dữ liệu combo từ HTML - tương ứng với getComboByHtml trong iOS
  ///
  /// Phương thức này trích xuất thông tin combo từ HTML và trả về tổng số combo và tổng giá trị
  /// @return Mảng [comboTotal, comboAmount] chứa tổng số combo và tổng giá trị
  List<int?> getComboByHtml(String content) {
    ComboListModel? comboList;

    // Tìm và phân tích biến listCombo từ HTML
    const pattern = "var listCombo = JSON.parse(";
    final index = widget.htmlData.indexOf(pattern);
    if (index != -1) {
      var text = widget.htmlData.substring(index + pattern.length);
      const endPattern = ");";
      final endIndex = text.indexOf(endPattern);
      if (endIndex != -1) {
        text = text.substring(0, endIndex);
        text = text.replaceAll("'", "");

        try {
          comboList = ComboListModel.fromJsonString(text);
        } catch (e) {
          print('Error parsing combo list: $e');
        }
      }
    }

    int? comboTotal;
    int? comboAmount;

    if (comboList?.elements != null) {
      for (var cb in comboList!.elements!) {
        final comboName = cb.combo?.name;
        final comboPrice = cb.combo?.priceAfterVAT;

        if (comboName != null && comboPrice != null) {
          final nameIndex = content.indexOf(comboName);
          if (nameIndex != -1) {
            var text = content.substring(nameIndex + comboName.length);
            const quantityPattern = "combo-quantity";
            final quantityIndex = text.indexOf(quantityPattern);

            if (quantityIndex != -1) {
              text = text.substring(quantityIndex + quantityPattern.length);
              final startIndex = text.indexOf(">");

              if (startIndex != -1) {
                text = text.substring(startIndex + 1);
                final endIndex = text.indexOf("<");

                if (endIndex != -1) {
                  text = text.substring(0, endIndex);
                  final quantity = int.tryParse(text);

                  if (quantity != null) {
                    comboTotal = (comboTotal ?? 0) + quantity;
                    comboAmount = (comboAmount ?? 0) + (quantity * comboPrice);
                  }
                }
              }
            }
          }
        }
      }
    }

    return [comboTotal, comboAmount];
  }

  Future<void> _launchExternalUrl(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      print('Could not launch $url');
    }
  }

  void _handleJavaScriptMessage(String message) {
    print('Received message from JavaScript: $message');

    try {
      // Try to parse as JSON first
      final Map<String, dynamic> jsonData = jsonDecode(message);

      if (jsonData.containsKey('type')) {
        final String type = jsonData['type'];

        if (type == 'payment_success') {
          _trackPayment('success');
          _getTransactionId();
        } else if (type == 'payment_failed') {
          final String errorMsg = jsonData['message'] ?? 'Thanh toán thất bại';
          _trackPayment('fail', jsonData['code'], errorMsg);
          widget.onPaymentFailed(errorMsg);
        } else if (type == 'booking_seat_failed') {
          final String errorMsg = jsonData['message'] ?? 'Đặt ghế thất bại';
          widget.onPaymentFailed(errorMsg);
        } else if (type == 'awaiting_payment') {
          widget.onPaymentWaiting();
        } else if (type == 'confirm') {
          // Xử lý khi người dùng xác nhận thanh toán - tương ứng với iOS
          _webViewController.runJavaScriptReturningResult("document.documentElement.innerHTML.toString()").then((html) {
            if (html is String) {
              // Lấy thông tin combo từ HTML
              final comboInfo = getComboByHtml(html);
              _totalCombos = comboInfo[0];
              _totalComboAmount = comboInfo[1];

              // Lấy các thông tin khác từ HTML
              String? totalAmount = _getElementByClassName(html, 'total-money-name');
              totalAmount = totalAmount?.replaceAll(",", "").replaceAll("đ", "");
              _totalAmount = int.tryParse(totalAmount ?? "");

              String? discountAmount = _getElementByClassName(html, 'coupon-discount');
              discountAmount = discountAmount?.replaceAll(",", "").replaceAll("đ", "");
              _discountAmount = int.tryParse(discountAmount ?? "");

              String? paymentAmount = _getElementByClassName(html, 'money-need-pay');
              paymentAmount = paymentAmount?.replaceAll(",", "").replaceAll("đ", "");
              _paymentAmount = int.tryParse(paymentAmount ?? "");

              String? redeemVouchers = _getElementByClassName(html, 'beta-voucher-value');
              redeemVouchers = redeemVouchers?.replaceAll(",", "").replaceAll("đ", "");
              _redeemVouchers = int.tryParse(redeemVouchers ?? "");

              String? redeemPoints = _getElementByClassName(html, 'beta-point-value');
              redeemPoints = redeemPoints?.replaceAll(",", "").replaceAll("đ", "");
              _redeemPoints = int.tryParse(redeemPoints ?? "");

              print("Combo info: Total combos: $_totalCombos, Total amount: $_totalComboAmount");
              print("Payment info: Total: $_totalAmount, Discount: $_discountAmount, Payment: $_paymentAmount");
              print("Vouchers: $_redeemVouchers, Points: $_redeemPoints");

              // Gọi _trackPayment sau khi đã cập nhật các biến
              _trackPayment('confirm');
            }
          }).catchError((error) {
            print('Error getting HTML content: $error');
          });
        }
      }
    } catch (e) {
      // If not JSON, handle as string
      if (message == 'payment_susccess' || message == 'payment_success') {
        _trackPayment('success');
        _getTransactionId();
      } else if (message == 'payment_failed') {
        _trackPayment('fail', message, 'Thanh toán thất bại');
        widget.onPaymentFailed('Thanh toán thất bại');
      } else if (message == 'booking_seat_failed') {
        widget.onPaymentFailed('Đặt ghế thất bại');
      } else if (message == 'awaiting_payment') {
        widget.onPaymentWaiting();
      } else if (message == 'policy') {
        /// Navigate to terms and conditions page
        /// This opens the terms and conditions in a browser window
        _launchExternalUrl('${widget.baseUrl}/terms-and-conditions');
      }
    }
  }

  /// Lấy giá trị của phần tử HTML theo tên class - tương ứng với getElementByClassName trong iOS
  String? _getElementByClassName(String content, String className) {
    final pattern = 'class="$className"';
    final index = content.indexOf(pattern);
    if (index != -1) {
      var text = content.substring(index + pattern.length);
      final startIndex = text.indexOf(">");
      if (startIndex != -1) {
        text = text.substring(startIndex + 1);
        final endIndex = text.indexOf("<");
        if (endIndex != -1) {
          return text.substring(0, endIndex).trim();
        }
      }
    }
    return null;
  }

  void _getTransactionId() {
    _webViewController.runJavaScriptReturningResult("getTransactionId();").then((result) {
      if (result is String) {
        widget.onPaymentSuccess(result);
      } else {
        widget.onPaymentSuccess(null);
      }
    }).catchError((error) {
      print('Error getting transaction ID: $error');
      widget.onPaymentSuccess(null);
    });
  }

  // Biến để lưu trữ thông tin combo và thanh toán - tương ứng với iOS
  int? _totalCombos;
  int? _totalComboAmount;
  int? _totalAmount;
  int? _discountAmount;
  int? _paymentAmount;
  int? _redeemVouchers;
  int? _redeemPoints;

  /// Tracks payment events for analytics
  ///
  /// This method logs payment events to help track user behavior and payment success rates
  /// @param type The type of payment event (confirm, success, fail)
  /// @param errorCode Optional error code if payment failed
  /// @param errorMsg Optional error message if payment failed
  void _trackPayment(String type, [String? errorCode, String? errorMsg]) {
    final film = widget.film;
    final totalPrice = widget.totalPrice;
    final listSeat = widget.listSeat;

    // Tính toán số lượng ghế theo loại - tương ứng với iOS
    final normalSeats = listSeat?.showSeats?.where((seat) =>
      seat.seatType?.isNormal == true).length ?? 0;
    final vipSeats = listSeat?.showSeats?.where((seat) =>
      seat.seatType?.isVip == true).length ?? 0;
    final doubleSeats = listSeat?.showSeats?.where((seat) =>
      seat.seatType?.isCouple == true).length ?? 0;
    final totalSeats = normalSeats + vipSeats + doubleSeats;

    // Create event data
    final Map<String, dynamic> eventData = {
      'event': 'payment_$type',
      'payment_method': _title ?? 'unknown',
      'amount': totalPrice,
      'film_id': film?.FilmId,
      'film_name': film?.Name,
      'cinema_id': widget.cinemaId,
      'cinema_name': widget.cinemaName,
      'normal_seats': normalSeats,
      'vip_seats': vipSeats,
      'double_seats': doubleSeats,
      'total_seats': totalSeats,
      'total_amount': _totalAmount,
      'discount_amount': _discountAmount,
      'payment_amount': _paymentAmount,
      'total_combos': _totalCombos,
      'total_combo_amount': _totalComboAmount,
      'redeem_vouchers': _redeemVouchers,
      'redeem_points': _redeemPoints,
    };

    // Add error information if available
    if (errorCode != null) {
      eventData['error_code'] = errorCode;
    }

    if (errorMsg != null) {
      eventData['error_message'] = errorMsg;
    }

    // Log the event
    print('Payment tracking: $eventData');

    // In a real implementation, this would send the data to an analytics service
    // For example: FirebaseAnalytics.instance.logEvent(name: 'payment_$type', parameters: eventData);
  }

  @override
  Widget build(BuildContext context) {
    // Sử dụng WillPopScope vì PopScope có vấn đề với onPopInvokedWithResult
    // Mặc dù WillPopScope đã deprecated, nhưng nó vẫn hoạt động tốt và chúng ta đang tập trung vào việc sửa lỗi với WebView
    return WillPopScope(
      onWillPop: () async {
        final shouldPop = await _handleBackPress();
        return shouldPop;
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(_title ?? 'Thanh toán'),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          elevation: 2,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () async {
              final shouldPop = await _handleBackPress();
              if (shouldPop && context.mounted) {
                Navigator.of(context).pop();
              }
            },
          ),
        ),
        body: SafeArea(
          child: Stack(
            children: [
              WebViewWidget(controller: _webViewController),
              if (_isLoading)
                Container(
                  color: Colors.white.withAlpha(204), // 0.8 * 255 = 204
                  child: const Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Đang tải thông tin thanh toán...',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Xử lý khi người dùng nhấn nút back - tương ứng với leftButtonPressed trong iOS
  ///
  /// Phương thức này kiểm tra loại màn hình hiện tại và xử lý tương ứng:
  /// - Nếu đang ở màn hình voucher, coupon hoặc beta-point, gọi backToMain() để quay lại trang thanh toán
  /// - Nếu đang ở màn hình khác và có thể quay lại, gọi goBack()
  /// - Nếu đang ở URL khác với baseUrl, tải lại HTML gốc
  /// - Nếu đang ở trang thanh toán chính, hiển thị hộp thoại xác nhận hủy thanh toán
  Future<bool> _handleBackPress() async {
    try {
      // Kiểm tra loại màn hình hiện tại bằng cách gọi JavaScript screenType
      final screenType = await _webViewController.runJavaScriptReturningResult("screenType");

       // Chuyển đổi kết quả JavaScript thành chuỗi và loại bỏ dấu ngoặc kép
      final screenTypeStr = screenType.toString().replaceAll('"', '');
      print("Current screen type: $screenTypeStr");

      // Nếu đang ở màn hình voucher, coupon hoặc beta-point, gọi backToMain() để quay lại trang thanh toán
      if (screenTypeStr == "voucher" || screenTypeStr == "coupon" || screenTypeStr == "beta-point") {
        await _webViewController.runJavaScript("backToMain();");
        return false;
      }

      // Nếu đang ở màn hình khác và có thể quay lại
      if (await _webViewController.canGoBack()) {
        _webViewController.goBack();
        return false;
      } else {
        // Nếu đang ở URL khác với baseUrl, tải lại HTML gốc
        final currentUrl = await _webViewController.currentUrl();
        if (currentUrl != null && !currentUrl.contains(widget.baseUrl)) {
          _webViewController.loadHtmlString(widget.htmlData, baseUrl: widget.baseUrl);
          return false;
        } else {
          // Nếu đang ở trang thanh toán chính, hiển thị hộp thoại xác nhận hủy thanh toán
          bool shouldPop = false;

          await showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Hủy thanh toán'),
              content: const Text('Bạn có chắc chắn muốn hủy thanh toán không?'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Không'),
                ),
                TextButton(
                  onPressed: () {
                    shouldPop = true;
                    Navigator.of(context).pop();
                  },
                  child: const Text('Có'),
                ),
              ],
            ),
          );

          return shouldPop;
        }
      }
    } catch (e) {
      print("Error in _handleBackPress: $e");

      // Xử lý mặc định nếu có lỗi
      if (await _webViewController.canGoBack()) {
        _webViewController.goBack();
        return false;
      } else {
        return true;
      }
    }
  }
}
