import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/pages/Movie_schedule/_film_choose_time.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
import 'package:flutter_app/pages/Movie_schedule/model/banner_model.dart';
import 'package:flutter_app/pages/my_profile/member_screen.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../constants/index.dart';

class MovieSchedule extends StatefulWidget {
  const MovieSchedule({super.key});

  @override
  State<MovieSchedule> createState() => _MovieScheduleState();
}

class _MovieScheduleState extends State<MovieSchedule> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool? isShowing = true;
  bool isLoading = true;
  List<PromoModel> promotions = [];
  late final sFilm = RepositoryProvider.of<Api>(context).film;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this, initialIndex: 1);
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) return;

      // Determine if we're showing early films
      bool isEarly = _tabController.index == 2;

      setState(() {
        // Tab 0: Coming Soon, Tab 1: Now Showing, Tab 2: Early Showing
        isShowing = _tabController.index == 0 ? false : true;
      });

      isLoading = false;
      Future.delayed(const Duration(microseconds: 100)).then((value) {
        context.read<BlocC<FilmModel>>().setPage(
            page: 1,
            api: (filter, page, size, sort) => sFilm.getListFilm(
              isShowing: isShowing,
              isEarly: isEarly,
            ),
            format: FilmModel.fromJson);
        isLoading = true;
      });
    });

    loadData();
  }

  loadData() {
    sFilm.getBanner().then((value) {
      if (value != null) {
        final data = MData.fromJson(value.data, PromoModel.fromJson);
        setState(() {
          promotions = data.content.map((e) => e as PromoModel).toList();
        });
      } else {
        throw Exception('Failed to load films');
      }
    }).catchError(
      (e) {
        print(e);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load films: $e')),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return PreferredSize(
      preferredSize: Size(MediaQuery.of(context).size.width, 70),
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              Color(0xFF03599D),  // UIColor.gradientBg1 from iOS
              Color(0xFF3FB7F9),  // UIColor.gradientBg2 from iOS
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildUserProfileButton(context),
                Image.asset(
                  'assets/images/<EMAIL>',
                  height: 32,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMemberInfoItem({
    required String label,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 12,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserProfileButton(BuildContext context) {
    return BlocBuilder<AuthC, AuthS>(
      builder: (context, state) {
        if (state.status == AppStatus.success && state.user != null) {
          // User is logged in, show profile avatar and name
          return InkWell(
            onTap: () {
              context.pushNamed(CRoute.member);
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 1),
                      ),
                      child: ClipOval(
                        child: state.user!.avatarUrl.isNotEmpty
                            ? Image.network(
                                state.user!.avatarUrl,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) => Center(
                                  child: Text(
                                    state.user!.name.isNotEmpty ? state.user!.name.substring(0, 1).toUpperCase() : '',
                                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                                  ),
                                ),
                              )
                            : Center(
                                child: Text(
                                  state.user!.name.isNotEmpty ? state.user!.name.substring(0, 1).toUpperCase() : '',
                                  style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        RichText(
                          text: TextSpan(
                            children: [
                              const TextSpan(
                                text: 'Hi ',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                              TextSpan(
                                text: state.user!.name,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            _buildMemberInfoItem(
                              label: /*state.user!.classId ?? */'MEMBER',
                              icon: Icons.card_membership,
                              onTap: () {
                                context.pushNamed(CRoute.member);
                                //
                                // Navigator.push(
                                //   context,
                                //   MaterialPageRoute(builder: (context) => const MemberScreen()),
                                // );
                              },
                            ),
                            const SizedBox(width: 8),
                            _buildMemberInfoItem(
                              label: '${state.user!.totalPoint ?? 0}',
                              icon: Icons.star,
                              onTap: () {
                                context.pushNamed(CRoute.member);

                                // Navigator.push(
                                //   context,
                                //   MaterialPageRoute(builder: (context) => const MemberScreen()),
                                // );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],
            ),
          );
        } else {
          // User is not logged in, show login button
          return IconButton(
            icon: const Icon(
              Icons.account_circle,
              color: Colors.white,
              size: 28,
            ),
            onPressed: () {
              context.pushNamed(CRoute.login);
            },
          );
        }
      },
    );
  }

  Widget _buildAgeIcon(String? age) {
    switch (age) {
      case 'C13':
        return Image.asset('assets/c-13.png', width: 35, height: 35);
      case 'C16':
        return Image.asset('assets/c-16.png', width: 35, height: 35);
      case 'C18':
        return Image.asset('assets/c-18.png', width: 35, height: 35);
      case 'P':
        return Image.asset('assets/p.png', width: 35, height: 35);
      default:
        return const SizedBox.shrink();
    }
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: CSpace.base),
        child: Column(
          children: [
            TabBar(
              isScrollable: true,
              controller: _tabController,
              labelStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.black),
              tabs: const <Widget>[Tab(text: 'SẮP CHIẾU'), Tab(text: 'ĐANG CHIẾU'), Tab(text: 'SUẤT CHIẾU SỚM')],
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: CSpace.base),
                      child: PromoCarousel(
                        promos: promotions,
                      ),
                    ),
                    const SizedBox(height: 10),
                    BlocBuilder<BlocC<FilmModel>, BlocS<FilmModel>>(
                      builder: (context, state) => !isLoading
                          ? const Center(child: CircularProgressIndicator())
                          : WList<FilmModel>(
                              // inputDisplayType: InputDisplayType.inside,
                              onTap: (content) => Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => FilmChooseTimeScreen(
                                    film: content,
                                    fromHome: true,
                                  ),
                                ),
                              ),
                              physics: const NeverScrollableScrollPhysics(),
                              item: (film, index) {
                                return Column(
                                  children: [
                                    // const SizedBox(height: 10),
                                    Stack(
                                      children: [
                                        // Film Poster
                                        imageNetwork(
                                            url:
                                            '${ApiService.baseUrlImage}/${Uri.decodeComponent(film.MainPosterUrl ?? '')}',
                                            borderRadius: BorderRadius.circular(10)),
                                        // Hot Badge
                                        if (film.IsHot)
                                          Positioned(
                                            top: 0,
                                            right: 0,
                                            child: Image.asset('assets/icon/<EMAIL>', width: 50, height: 50),
                                          ),
                                        // Age Rating Badge
                                        Positioned(
                                          top: 5,
                                          left: 5,
                                          child: _buildAgeIcon(film.FilmRestrictAgeName),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 10),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: CSpace.sm),
                                      child: Text(
                                        film.Name ?? '',
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(fontFamily: "Oswald"),
                                      ),
                                    ),
                                    const VSpacer(CSpace.sm),
                                    Text('${film.Duration} Phút'),
                                  ],
                                );
                              },
                              displayMode: DisplayMode.grid,
                              format: FilmModel.fromJson,
                              api: (filet, page, size, sort) {
                                // Determine if we're showing early films
                                bool isEarly = _tabController.index == 2;
                                return sFilm.getListFilm(
                                  isShowing: isShowing,
                                  isEarly: isEarly,
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class PromoCarousel extends StatefulWidget {
  final List<PromoModel> promos;

  const PromoCarousel({super.key, required this.promos});

  @override
  State<PromoCarousel> createState() => _PromoCarouselState();
}

class _PromoCarouselState extends State<PromoCarousel> {
  final PageController _controller = PageController();
  int _currentPage = 0;
  Timer? _autoScrollTimer;

  @override
  void initState() {
    super.initState();

    _autoScrollTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_controller.hasClients) {
        int nextPage = (_currentPage + 1) % widget.promos.length;
        _controller.animateToPage(
          nextPage,
          duration: const Duration(milliseconds: 2000),
          curve: Curves.easeInOut,
        );
      }
    });

    _controller.addListener(() {
      final page = _controller.page?.round() ?? 0;
      if (page != _currentPage) {
        setState(() => _currentPage = page);
      }
    });
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SizedBox(
          height: 150,
          child: PageView.builder(
            controller: _controller,
            itemCount: widget.promos.length,
            itemBuilder: (context, index) {
              final promo = widget.promos[index];
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 0.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(CSpace.sm),
                  child: Image.network(
                    "${ApiService.baseUrlImage}/${promo.imageUrl}",
                    fit: BoxFit.cover,
                    width: double.infinity,
                  ),
                ),
              );
            },
          ),
        ),
        Positioned(
          bottom: 10,
          left: 10,
          child: // Indicator
              SmoothPageIndicator(
            controller: _controller,
            count: widget.promos.length,
            effect: const ExpandingDotsEffect(
              activeDotColor: Colors.blue,
              dotColor: Colors.grey,
              dotHeight: 8,
              dotWidth: 8,
              spacing: 6,
            ),
          ),
        )
      ],
    );
  }
}
