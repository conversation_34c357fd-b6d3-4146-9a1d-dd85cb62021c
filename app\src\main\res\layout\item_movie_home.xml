<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="280dp" android:layout_height="420dp">

    <androidx.cardview.widget.CardView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        app:cardCornerRadius="4dp"
        app:cardElevation="5dp"
        app:layout_constraintDimensionRatio="h, 10:15">

        <vn.zenity.betacineplex.helper.view.TopCropImageView
            android:id="@+id/imageView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:alpha="0.9"
                android:background="@drawable/shape_dark_gradient"
                app:layout_constraintTop_toTopOf="@+id/tvTitle"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"/>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivAgeWarning"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_normal"
                android:layout_marginTop="@dimen/margin_normal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/c_16"/>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/fillOrder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/margin_normal"
                android:layout_marginTop="@dimen/margin_normal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/fill"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvPosition"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="1"
                android:textColor="@color/textRed"
                android:textSize="@dimen/font_large"
                app:fontFamily="@font/sanspro_bold"
                app:layout_constraintBottom_toBottomOf="@+id/view01"
                app:layout_constraintLeft_toLeftOf="@+id/view01"
                app:layout_constraintRight_toRightOf="@+id/view01"
                app:layout_constraintTop_toTopOf="@+id/view01"/>

            <View
                android:id="@+id/view01"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="w, 1:1"
                app:layout_constraintLeft_toLeftOf="@+id/fillOrder"
                app:layout_constraintRight_toRightOf="@+id/fillOrder"
                app:layout_constraintTop_toTopOf="@+id/fillOrder"/>

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/btnPlay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/opacity"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="10dp"
                android:paddingLeft="@dimen/nav_header_vertical_spacing"
                android:paddingRight="@dimen/nav_header_vertical_spacing"
                tools:text="Pacific Rim: Trỗi Dậy"
                android:textColor="@color/white"
                android:textSize="@dimen/font_large"
                app:fontFamily="@font/oswald_bold"
                app:layout_constraintBottom_toTopOf="@+id/tvContent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/nav_header_vertical_spacing"
                android:paddingLeft="@dimen/nav_header_vertical_spacing"
                android:paddingRight="@dimen/nav_header_vertical_spacing"
                tools:text="Võ thuật, Viễn Tưởng  |  135 phút "
                android:textColor="@color/white"
                android:textSize="@dimen/font_normal"
                app:fontFamily="@font/sanspro_regular"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>