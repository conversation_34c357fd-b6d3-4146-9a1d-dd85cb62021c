<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" xmlns:tools="http://schemas.android.com/tools"
    android:padding="@dimen/padding_small"
    android:background="?attr/selectableItemBackground">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardBG"
        android:layout_width="0dp"
        android:layout_height="150dp"
        android:layout_marginTop="@dimen/padding_small"
        android:scaleType="fitStart"
        app:cardElevation="0dp"
        app:cardCornerRadius="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/card"
        android:layout_width="105dp"
        android:layout_height="166dp"
        android:layout_marginLeft="@dimen/padding_small"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:cardCornerRadius="@dimen/small_radius"
        app:cardElevation="1dp">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivBanner"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/grayBg"/>
    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/btnPlayTrailer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/opacity"
        app:layout_constraintLeft_toLeftOf="@+id/card"
        app:layout_constraintRight_toRightOf="@+id/card"
        app:layout_constraintTop_toTopOf="@+id/card"
        app:layout_constraintBottom_toBottomOf="@+id/card"
        android:elevation="1dp"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="@+id/cardBG"
        app:layout_constraintBottom_toBottomOf="@+id/cardBG"
        app:layout_constraintLeft_toRightOf="@+id/card"
        app:layout_constraintRight_toLeftOf="@+id/tvFilmTitle"
        android:scaleType="centerInside"
        app:tint="@color/grayBg"
        app:srcCompat="@drawable/bg_diveder"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFilmTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        tools:text="Pacific Rim: Trỗi Dậy"
        app:fontFamily="@font/oswald_bold"
        android:ellipsize="end"
        android:layout_marginTop="@dimen/padding_small"
        android:textSize="@dimen/font_normal"
        android:layout_marginLeft="26dp"
        app:layout_constraintTop_toTopOf="@+id/cardBG"
        app:layout_constraintRight_toLeftOf="@+id/fillSTT"
        app:layout_constraintLeft_toRightOf="@+id/card"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/fillSTT"
        android:layout_width="30dp"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/fill"
        app:layout_constraintTop_toTopOf="@+id/cardBG"
        app:layout_constraintEnd_toEndOf="@+id/cardBG"
        android:layout_marginRight="@dimen/margin_small"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFilm2d"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="(2D - LT)"
        app:fontFamily="@font/oswald_regular"
        android:ellipsize="end"
        android:textSize="@dimen/font_normal"
        app:layout_constraintTop_toBottomOf="@+id/tvFilmTitle"
        app:layout_constraintLeft_toLeftOf="@+id/tvFilmTitle"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivTypeAge"
        android:layout_width="36dp"
        android:layout_height="18dp"
        app:layout_constraintLeft_toRightOf="@+id/tvFilm2d"
        app:layout_constraintTop_toTopOf="@+id/tvFilm2d"
        app:layout_constraintBottom_toBottomOf="@+id/tvFilm2d"
        android:layout_marginLeft="@dimen/padding_small"
        app:srcCompat="@drawable/ic_age_c_13"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFilmType"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        tools:text="Võ thuật, Viễn Tưởng"
        app:fontFamily="@font/sanspro_regular"
        android:ellipsize="end"
        android:singleLine="true"
        android:textSize="@dimen/font_normal"
        app:layout_constraintTop_toBottomOf="@+id/tvFilm2d"
        app:layout_constraintRight_toRightOf="@+id/cardBG"
        app:layout_constraintLeft_toLeftOf="@+id/tvFilmTitle"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFilmDuration"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        tools:text="135 phút"
        app:fontFamily="@font/sanspro_regular"
        android:ellipsize="end"
        android:textSize="@dimen/font_normal"
        app:layout_constraintLeft_toLeftOf="@+id/tvFilmTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvFilmType"
        app:layout_constraintRight_toRightOf="@+id/cardBG"/>
</androidx.constraintlayout.widget.ConstraintLayout>