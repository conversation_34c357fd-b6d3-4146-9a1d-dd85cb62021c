<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/statusBarHeight"
        android:background="@drawable/color_toolbar" />

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/statusBar">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/forgot_password"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_small"
            app:srcCompat="@drawable/ic_menubuger_white" />
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="24dp"
        android:paddingLeft="24dp"
        android:paddingRight="24dp"
        android:paddingBottom="@dimen/padding_normal"
        android:gravity="center"
        android:text="@string/notice_forgot_password"/>

    <vn.zenity.betacineplex.helper.view.BetaEditText
        android:id="@+id/edtEmail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/margin_large"
        android:paddingEnd="@dimen/margin_large"
        android:inputType="textEmailAddress"
        app:btedIcon="@drawable/mail"
        app:btedHint="@string/email"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnConfirm"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/padding_large"
        android:text="@string/send"
        style="@style/ButtonPrimary"/>

</LinearLayout>