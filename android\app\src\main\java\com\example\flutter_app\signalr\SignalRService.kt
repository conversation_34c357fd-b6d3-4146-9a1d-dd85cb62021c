package com.example.flutter_app.signalr

import android.os.Handler
import android.os.Looper
import android.util.Log
import microsoft.aspnet.signalr.client.hubs.HubConnection
import microsoft.aspnet.signalr.client.hubs.HubProxy
import microsoft.aspnet.signalr.client.*
import microsoft.aspnet.signalr.client.transport.ServerSentEventsTransport
import microsoft.aspnet.signalr.client.Platform
import microsoft.aspnet.signalr.client.http.android.AndroidPlatformComponent
import java.util.concurrent.TimeUnit

/**
 * SignalR service for Android
 * Based on the SignalRService.kt from the Android repo
 */
class SignalRService {
    companion object {
        private var instance: SignalRService? = null
        
        fun getInstance(): SignalRService {
            if (instance == null) {
                instance = SignalRService()
            }
            return instance!!
        }
        
        private const val TAG = "SignalRService"
    }

    // Connection state enum
    enum class ConnectionState { CONNECTED, DISCONNECTED }

    // Connection objects
    private var hubConnection: HubConnection? = null
    private var hubProxy: HubProxy? = null
    
    // Connection state
    private var connectionState = ConnectionState.DISCONNECTED
    
    // Listeners
    private val connectionListeners = mutableListOf<(ConnectionState) -> Unit>()
    private val messageListeners = mutableListOf<(String, String, Int, Int) -> Unit>()
    
    // Reconnection handler
    private val handler = Handler(Looper.getMainLooper())
    private val reconnectRunnable = object : Runnable {
        override fun run() {
            if (hubConnection?.state == microsoft.aspnet.signalr.client.ConnectionState.Disconnected) {
                reconnect()
            }
            handler.postDelayed(this, 5 * 1000) // Check every 5 seconds
        }
    }

    /**
     * Start the SignalR connection
     * @param serverUrl The URL of the SignalR server
     * @param hubName The name of the hub to connect to
     * @param token Optional authentication token
     * @param callback Callback for connection result
     */
    fun startSignalR(serverUrl: String, hubName: String, token: String?, callback: (Boolean, String?) -> Unit) {
        // Load the Android platform component
        Platform.loadPlatformComponent(AndroidPlatformComponent())
        
        // Create the hub connection
        val url = if (serverUrl.endsWith("/signalr/hubs")) {
            serverUrl
        } else {
            val baseUrl = if (serverUrl.endsWith("/")) serverUrl else "$serverUrl/"
            "${baseUrl}signalr/hubs"
        }
        
        Log.d(TAG, "Connecting to SignalR at $url")
        
        // Create the hub connection with optional token
        hubConnection = if (token != null && token.isNotEmpty()) {
            HubConnection(url, "Bearer=$token", true, Logger { message, _ ->
                Log.d(TAG, message)
            })
        } else {
            HubConnection(url, null, true, Logger { message, _ ->
                Log.d(TAG, message)
            })
        }
        
        // Create the hub proxy
        hubProxy = hubConnection?.createHubProxy(hubName)
        
        // Set up the transport
        val clientTransport = ServerSentEventsTransport(hubConnection?.logger)
        
        // Start the connection
        val signalRFuture = hubConnection?.start(clientTransport)
        
        // Set up event handlers
        hubConnection?.received { jsonElement ->
            if (jsonElement.isJsonObject) {
                if (jsonElement.asJsonObject.has("A")) {
                    val ja = jsonElement.asJsonObject.getAsJsonArray("A")
                    try {
                        if (ja.size() >= 4) {
                            val connectionId = ja[0].asString
                            val showId = ja[1].asString
                            val seatIndex = ja[2].asInt
                            val status = ja[3].asInt
                            
                            // Notify listeners
                            messageListeners.forEach { listener ->
                                listener(connectionId, showId, seatIndex, status)
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing message: ${e.message}")
                    }
                }
            }
        }
        
        // Connected handler
        hubConnection?.connected {
            Log.d(TAG, "SignalR connected with ID: ${hubConnection?.connectionId}")
            connectionState = ConnectionState.CONNECTED
            handler.post(reconnectRunnable)
            
            // Notify listeners
            connectionListeners.forEach { listener ->
                listener(ConnectionState.CONNECTED)
            }
            
            // Callback with success
            callback(true, hubConnection?.connectionId)
        }
        
        // Closed handler
        hubConnection?.closed {
            Log.d(TAG, "SignalR connection closed")
            connectionState = ConnectionState.DISCONNECTED
            
            // Notify listeners
            connectionListeners.forEach { listener ->
                listener(ConnectionState.DISCONNECTED)
            }
        }
        
        // Error handler
        hubConnection?.error { error ->
            Log.e(TAG, "SignalR error: ${error?.message}")
            connectionState = ConnectionState.DISCONNECTED
            
            // Notify listeners
            connectionListeners.forEach { listener ->
                listener(ConnectionState.DISCONNECTED)
            }
        }
        
        // State changed handler
        hubConnection?.stateChanged { _, newState ->
            if (newState == microsoft.aspnet.signalr.client.ConnectionState.Disconnected) {
                Log.d(TAG, "SignalR state changed to disconnected")
            }
        }
        
        // Wait for the connection to complete
        try {
            signalRFuture?.done {
                Log.d(TAG, "SignalR connection done")
            }?.onError { error ->
                Log.e(TAG, "SignalR connection error: ${error.message}")
                callback(false, error.message)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error starting SignalR: ${e.message}")
            callback(false, e.message)
        }
    }

    /**
     * Reconnect to the SignalR hub
     */
    fun reconnect() {
        try {
            hubConnection?.stop()
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping connection for reconnect: ${e.message}")
        }
        
        // Wait a second before reconnecting
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                hubConnection?.start()
            } catch (e: Exception) {
                Log.e(TAG, "Error reconnecting: ${e.message}")
            }
        }, 1000)
    }

    /**
     * Subscribe to a SignalR event
     * @param event The event to subscribe to
     */
    fun subscribe(event: String) {
        hubProxy?.subscribe(event)
    }

    /**
     * Unsubscribe from a SignalR event
     * @param event The event to unsubscribe from
     */
    fun unsubscribe(event: String) {
        hubProxy?.removeSubscription(event)
    }

    /**
     * Invoke a method on the SignalR hub
     * @param event The method to invoke
     * @param args The arguments to pass to the method
     */
    fun invoke(event: String, args: List<Any>) {
        when (args.size) {
            1 -> hubProxy?.invoke(event, args[0])
            2 -> hubProxy?.invoke(event, args[0], args[1])
            3 -> hubProxy?.invoke(event, args[0], args[1], args[2])
            4 -> hubProxy?.invoke(event, args[0], args[1], args[2], args[3])
            else -> Log.e(TAG, "Unsupported number of arguments: ${args.size}")
        }
    }

    /**
     * Stop the SignalR connection
     */
    fun stop() {
        try {
            handler.removeCallbacks(reconnectRunnable)
            hubConnection?.stop()
            connectionState = ConnectionState.DISCONNECTED
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping SignalR: ${e.message}")
        }
    }

    /**
     * Get the connection ID
     * @return The connection ID or empty string if not connected
     */
    fun connectionId(): String {
        return hubConnection?.connectionId ?: ""
    }

    /**
     * Add a connection listener
     * @param listener The listener to add
     */
    fun addConnectionListener(listener: (ConnectionState) -> Unit) {
        connectionListeners.add(listener)
        listener(connectionState)
    }

    /**
     * Remove a connection listener
     * @param listener The listener to remove
     */
    fun removeConnectionListener(listener: (ConnectionState) -> Unit) {
        connectionListeners.remove(listener)
    }

    /**
     * Add a message listener
     * @param listener The listener to add
     */
    fun addMessageListener(listener: (String, String, Int, Int) -> Unit) {
        messageListeners.add(listener)
    }

    /**
     * Remove a message listener
     * @param listener The listener to remove
     */
    fun removeMessageListener(listener: (String, String, Int, Int) -> Unit) {
        messageListeners.remove(listener)
    }

    /**
     * Check if connected
     * @return True if connected, false otherwise
     */
    fun isConnected(): Boolean {
        return connectionState == ConnectionState.CONNECTED
    }
}
