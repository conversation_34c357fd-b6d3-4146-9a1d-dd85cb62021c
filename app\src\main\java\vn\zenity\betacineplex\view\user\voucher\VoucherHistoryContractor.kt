package vn.zenity.betacineplex.view.user.voucher

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.PaymentHistory
import vn.zenity.betacineplex.model.VoucherHistoryModel
import vn.zenity.betacineplex.model.VoucherModel

/**
 * Created by Zenity.
 */

interface VoucherHistoryContractor {
    interface View : IBaseView {
        fun showUseVoucherHistories(historis: List<VoucherHistoryModel>)
    }

    interface Presenter : IBasePresenter<View> {
        fun getUserVoucherHitories()
    }
}
