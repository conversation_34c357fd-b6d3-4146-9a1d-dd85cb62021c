package vn.zenity.betacineplex.view.cenima

import android.location.Location
import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.model.CinemaModel
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class CenimaPresenter : CenimaContractor.Presenter {

    private var disposable: Disposable? = null
    override fun getCinema(currentLocation: Location?) {
        view?.get()?.showLoading()
        var listNear = listOf<CinemaModel>()
        disposable = APIClient.shared.cinemaAPI.listCinema().applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let {
                            it.sortBy { it.getDistanceToCurrentLocation(currentLocation) }
                            listNear = it
                            listNear = listNear.filter { it.getDistanceToCurrentLocation(currentLocation) < 100f }
                        }
                        disposable = APIClient.shared.cinemaAPI.listCinemaByProvince().applyOn()
                                .subscribe({
                                    view?.get()?.showListCinema(listNear, (it.Data ?: listOf()))
                                    view?.get()?.hideLoading()
                                }, {
                                    view?.get()?.hideLoading()
                                })
                    } else {
                        view?.get()?.hideLoading()
                    }
                }, {
                    view?.get()?.hideLoading()
                })
//        listNear = listOf(Cinema("Beta Mỹ Đình"),
//                Cinema("Beta Thanh Xuân"),
//                Cinema("Beta Mai Động"),
//                Cinema("Beta Long Biên"),
//                Cinema("Beta Giáp Bát"))
//        val listArea = listOf(Area("Create 01", mutableListOf(Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình")))
//                , Area("Create 02", mutableListOf(Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình")))
//                , Area("Create 03", mutableListOf(Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình")))
//                , Area("Create 04", mutableListOf(Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình"), Cinema("Beta Mỹ Đình"))))
//        view?.get()?.showListCinema(listNear, listArea)
//        view?.get()?.hideLoading()

    }

    private var view: WeakReference<CenimaContractor.View?>? = null
    override fun attachView(view: CenimaContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
