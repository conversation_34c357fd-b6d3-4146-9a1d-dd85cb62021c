package vn.zenity.betacineplex.view.recruitment

import android.os.Build
import android.text.Html
import android.text.Spanned
import io.reactivex.Observable
import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.model.NewsModel
import vn.zenity.betacineplex.model.Notification
import vn.zenity.betacineplex.model.PolicyDetailModel
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class RecruitmentPresenter : RecruitmentContractor.Presenter {
    private var disposable: Disposable? = null
    override fun getListNotification(page: Int) {
        view?.get()?.showLoading()
        disposable = getCategories()/*.flatMap { items: ArrayList<NewsModel> ->
            val observables: List<Observable<NewsModel>> = items.map { getRecruitment(it.StorylineID ?: "") }
            Observable.zip(observables, { array ->
                return@zip array
            })
        }*/.applyOn()
                .subscribe({ list ->
                    view?.get()?.hideLoading()
                    var notifications: ArrayList<Notification> = arrayListOf()
                    list?.forEach { policy ->
                        val content = policy.Noi_dung_chi_tiet?.get(0)?.ParagraphData?.ParagraphContent
                        var text = ""
                        content?.let {
                            text = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                                Html.fromHtml(it, Html.FROM_HTML_MODE_COMPACT).toString()
                            } else
                                Html.fromHtml(it).toString()
                        }
                        val notification = Notification(policy.Tieu_de ?: "", text, policy.dateString, true, policy)
                        notifications.add(notification)
                    }
                    view?.get()?.showListNotifications(notifications)
                }, { error ->
                    view?.get()?.hideLoading()
                })
//        view?.get()?.showListNotifications(listOf(Notification("Tuyển dụng tháng 3", "Thông tin tuyển dụng tháng 3.", "30/03/2018"),
//                Notification("Tuyển dụng tháng 4", "Thông tin tuyển dụng tháng 4.", "30/04/2018")))
//        view?.get()?.hideLoading()
    }

    private var view: WeakReference<RecruitmentContractor.View?>? = null
    override fun attachView(view: RecruitmentContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.disposable?.dispose()
        this.view?.clear()
        this.view = null
    }

    private fun getCategories(): Observable<ArrayList<NewsModel>?> {
        val lang = App.shared().getCurrentLang()
        return APIClient.shared.ecmAPI.getRecruitment(if (lang == "en") lang else "vi").map{ it.Data }
    }

    private fun getRecruitment(id: String): Observable<NewsModel> {
        return APIClient.shared.ecmAPI.getNewForCategoryNoParams(id = id).map { it.Data }
    }
}
