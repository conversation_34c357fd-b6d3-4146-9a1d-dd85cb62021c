/// Payment constants - tương ứng với Constant.kt trong Android repo
class PaymentConstants {
  
  /// AirPay (ShopeePay) constants
  static const airPay = AirPayConstants();
  
  /// MoMo constants  
  static const momo = MomoConstants();
  
  /// ZaloPay constants
  static const zaloPay = ZaloPayConstants();
  
  /// Noi Dia (Domestic card) constants
  static const noiDia = NoiDiaConstants();
  
  /// Quoc Te (International card) constants
  static const quocTe = QuocTeConstants();
  
  /// Intent filter constants
  static const intentFilterMomo = 'vn.zenity.betacineplex.MOMO_RESULT';
  static const intentFilterZaloPay = 'vn.zenity.betacineplex.ZALOPAY_RESULT';
}

/// AirPay (ShopeePay) constants
class AirPayConstants {
  const AirPayConstants();
  
  static const String fullHost = 'airpay.vn';
  static const String orderId = 'order_id';
  static const String domain = 'airpay';
}

/// MoMo constants
class MomoConstants {
  const MomoConstants();
  
  static const String fullHost = 'payment.momo';
  static const String orderId = 'orderId';
  static const String resultCode = 'resultCode';
  static const String requestId = 'requestId';
  static const String transId = 'transId';
  static const String message = 'message';
  static const String responseTime = 'responseTime';
  static const String payType = 'payType';
  static const String extraData = 'extraData';
  static const String partnerCode = 'partnerCode';
  static const String domain = 'momo';
}

/// ZaloPay constants
class ZaloPayConstants {
  const ZaloPayConstants();
  
  static const String fullHost = 'gateway.zalopay.vn';
  static const String appId = 'appId';
  static const String appTransId = 'appTransId';
  static const String pmcId = 'pmcId';
  static const String bankCode = 'bankCode';
  static const String amount = 'amount';
  static const String dAmount = 'dAmount';
  static const String appStatus = 'appStatus';
  static const String checkSum = 'checkSum';
  static const String domain = 'zalopay';
}

/// Noi Dia (Domestic card) constants
class NoiDiaConstants {
  const NoiDiaConstants();
  
  static const String fullHost = 'mtf.onepay.vn/onecomm-pay';
  static const String domain = 'noidia';
}

/// Quoc Te (International card) constants
class QuocTeConstants {
  const QuocTeConstants();
  
  static const String fullHost = 'mtf.onepay.vn/promotion/vpcpr.op';
  static const String domain = 'quocte';
}

/// Screen types for back button handling
class ScreenTypes {
  static const String voucher = 'voucher';
  static const String coupon = 'coupon';
  static const String betaPoint = 'beta-point';
  static const String payment = 'payment';
}

/// JavaScript method names
class JavaScriptMethods {
  static const String getBookingInfo = 'getBookingInfo';
  static const String getCustomerInfo = 'getCustomerInfo';
  static const String getTransactionId = 'getTransactionId';
  static const String backToMain = 'backToMain';
  static const String screenType = 'screenType';
  
  // Payment status check methods
  static const String checkShopeePayTransactionStatus = 'checkShopeePayTransactionStatus';
  static const String checkMomoTransactionStatus = 'checkMomoTransactionStatus';
  static const String checkZaloPayTransactionStatus = 'checkZaloPayTransactionStatus';
}

/// Message types from JavaScript
class MessageTypes {
  static const String policy = 'policy';
  static const String paymentSuccess = 'payment_success';
  static const String paymentSusccess = 'payment_susccess'; // Typo in original
  static const String paymentFailed = 'payment_failed';
  static const String bookingSeatFailed = 'booking_seat_failed';
  static const String awaitingPayment = 'awaiting_payment';
  static const String timeout = 'timeout';
  static const String confirm = 'confirm';
}

/// Date format constants
class DateFormats {
  static const String default_ = 'yyyy-MM-dd HH:mm:ss';
  static const String dateSavis = 'dd/MM/yyyy';
  static const String hourMinute = 'HH:mm';
}
