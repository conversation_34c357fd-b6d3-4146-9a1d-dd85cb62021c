import Foundation
import Flutter

@objc public class SignalRPluginRegistrar: NSObject {
    @objc public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "com.betacinemas.signalr/methods", binaryMessenger: registrar.messenger())
        let eventChannel = FlutterEventChannel(name: "com.betacinemas.signalr/events", binaryMessenger: registrar.messenger())
        
        let instance = SignalRPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
        eventChannel.setStreamHandler(instance)
    }
}
