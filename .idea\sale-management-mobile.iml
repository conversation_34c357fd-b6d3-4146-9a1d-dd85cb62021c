<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/audio_session/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/audio_session/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/audio_session/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/audio_session/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/audio_session/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/audio_session/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/device_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/device_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/device_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/device_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/device_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/device_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/firebase_core/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/firebase_core/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/firebase_core/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/firebase_core/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/firebase_core/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/firebase_core/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/firebase_messaging/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/firebase_messaging/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/firebase_messaging/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/firebase_messaging/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/firebase_messaging/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/firebase_messaging/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/fl_location/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/fl_location/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/fl_location/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/fl_location/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/fl_location/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/fl_location/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_barcode_scanner/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_barcode_scanner/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_barcode_scanner/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_barcode_scanner/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_barcode_scanner/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_barcode_scanner/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_contacts/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_contacts/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_contacts/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_contacts/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_contacts/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_contacts/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_contacts/example_full/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_contacts/example_full/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_contacts/example_full/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_keyboard_visibility/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_keyboard_visibility/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_keyboard_visibility/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_keyboard_visibility/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_keyboard_visibility/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_keyboard_visibility/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/geocoding_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/geocoding_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/geocoding_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/geocoding_ios/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/geocoding_ios/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/geocoding_ios/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/google_maps_flutter_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/google_maps_flutter_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/google_maps_flutter_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/google_maps_flutter_ios/example/ios14/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/google_maps_flutter_ios/example/ios14/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/google_maps_flutter_ios/example/ios14/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/google_maps_flutter_ios/example/ios15/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/google_maps_flutter_ios/example/ios15/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/google_maps_flutter_ios/example/ios15/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/google_maps_flutter_ios/example/shared/maps_example_dart/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/google_maps_flutter_ios/example/shared/maps_example_dart/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/google_maps_flutter_ios/example/shared/maps_example_dart/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/image_gallery_saver/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/image_gallery_saver/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/image_gallery_saver/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/image_gallery_saver/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/image_gallery_saver/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/image_gallery_saver/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/image_picker_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/image_picker_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/image_picker_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/image_picker_ios/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/image_picker_ios/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/image_picker_ios/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/integration_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/integration_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/integration_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/integration_test/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/integration_test/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/integration_test/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/integration_test/integration_test_macos/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/integration_test/integration_test_macos/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/integration_test/integration_test_macos/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/just_audio/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/just_audio/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/just_audio/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/just_audio/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/just_audio/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/just_audio/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/open_filex/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/open_filex/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/open_filex/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/open_filex/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/open_filex/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/open_filex/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/package_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/package_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/package_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/package_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/package_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/package_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/path_provider_foundation/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/path_provider_foundation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/path_provider_foundation/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/path_provider_foundation/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/path_provider_foundation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/path_provider_foundation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/permission_handler_apple/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/permission_handler_apple/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/permission_handler_apple/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/permission_handler_apple/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/permission_handler_apple/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/permission_handler_apple/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/qr_code_scanner/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/qr_code_scanner/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/qr_code_scanner/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/qr_code_scanner/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/qr_code_scanner/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/qr_code_scanner/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/shared_preferences_foundation/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/shared_preferences_foundation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/shared_preferences_foundation/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/shared_preferences_foundation/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/shared_preferences_foundation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/shared_preferences_foundation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/sqflite_darwin/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/sqflite_darwin/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/sqflite_darwin/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/sqflite_darwin/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/sqflite_darwin/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/sqflite_darwin/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/url_launcher_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/url_launcher_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/url_launcher_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/url_launcher_ios/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/url_launcher_ios/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/url_launcher_ios/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/video_player_avfoundation/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/video_player_avfoundation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/video_player_avfoundation/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/video_player_avfoundation/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/video_player_avfoundation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/video_player_avfoundation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/wakelock_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/wakelock_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/wakelock_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/wakelock_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/wakelock_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/wakelock_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/webview_flutter_wkwebview/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/webview_flutter_wkwebview/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/webview_flutter_wkwebview/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/webview_flutter_wkwebview/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/webview_flutter_wkwebview/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/webview_flutter_wkwebview/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/syncfusion_flutter_pdfviewer/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/syncfusion_flutter_pdfviewer/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/syncfusion_flutter_pdfviewer/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/syncfusion_flutter_pdfviewer/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/syncfusion_flutter_pdfviewer/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/syncfusion_flutter_pdfviewer/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_inappwebview_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_inappwebview_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_inappwebview_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_inappwebview_ios/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_inappwebview_ios/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/flutter_inappwebview_ios/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/geolocator_apple/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/geolocator_apple/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/geolocator_apple/build" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/geolocator_apple/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/geolocator_apple/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/ios/.symlinks/plugins/geolocator_apple/example/build" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Dart SDK" level="project" />
    <orderEntry type="library" name="Flutter Plugins" level="project" />
    <orderEntry type="library" name="Dart Packages" level="project" />
  </component>
</module>