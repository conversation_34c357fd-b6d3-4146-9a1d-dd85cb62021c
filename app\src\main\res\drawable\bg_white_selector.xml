<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:state_enabled="false"
        android:drawable="@color/white" /> <!--button_disabled-->
    <item
        android:state_pressed="true"
        android:state_enabled="true"
        android:drawable="@color/lightGray" /> <!--button_pressed-->

    <item
        android:state_selected="true"
        android:state_enabled="true"
        android:drawable="@color/lightGray" /> <!--button_pressed-->
    <item
        android:state_focused="true"
        android:state_enabled="true"
        android:drawable="@color/white" /> <!--button_focused-->
    <item
        android:state_enabled="true"
        android:drawable="@color/white" /> <!--button_enabled-->
    <item android:drawable="@color/white"/>
</selector>