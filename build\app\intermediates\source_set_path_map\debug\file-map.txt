com.flutter.flutter.petro.app-jetified-firebase-messaging-24.0.0-0 D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\res
com.flutter.flutter.petro.app-jetified-activity-1.9.2-1 D:\android studio\android-gradle\caches\transforms-3\0e2fd2d0d3b3f462505a0fe60954a8a8\transformed\jetified-activity-1.9.2\res
com.flutter.flutter.petro.app-lifecycle-runtime-2.7.0-2 D:\android studio\android-gradle\caches\transforms-3\0e502190d728b4ada7e3274fa39bc377\transformed\lifecycle-runtime-2.7.0\res
com.flutter.flutter.petro.app-lifecycle-viewmodel-2.7.0-3 D:\android studio\android-gradle\caches\transforms-3\0fd8cafb08e54981095cfb944a5eaa6b\transformed\lifecycle-viewmodel-2.7.0\res
com.flutter.flutter.petro.app-jetified-exoplayer-core-2.18.7-4 D:\android studio\android-gradle\caches\transforms-3\10bd4d9e9b2285a923396ec591922feb\transformed\jetified-exoplayer-core-2.18.7\res
com.flutter.flutter.petro.app-jetified-core-1.0.0-5 D:\android studio\android-gradle\caches\transforms-3\18808df49342fe99a75fef099d0adc82\transformed\jetified-core-1.0.0\res
com.flutter.flutter.petro.app-jetified-play-services-maps-18.2.0-6 D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\res
com.flutter.flutter.petro.app-slidingpanelayout-1.2.0-7 D:\android studio\android-gradle\caches\transforms-3\26a60ada1f441fb9c504beff02890e3a\transformed\slidingpanelayout-1.2.0\res
com.flutter.flutter.petro.app-jetified-lifecycle-process-2.7.0-8 D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\res
com.flutter.flutter.petro.app-lifecycle-livedata-core-2.7.0-9 D:\android studio\android-gradle\caches\transforms-3\306c33704296a4fe5e2dcae2aa0510ce\transformed\lifecycle-livedata-core-2.7.0\res
com.flutter.flutter.petro.app-jetified-firebase-common-21.0.0-10 D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\res
com.flutter.flutter.petro.app-fragment-1.7.1-11 D:\android studio\android-gradle\caches\transforms-3\3b03807329c558a737d9cb34e921c2d5\transformed\fragment-1.7.1\res
com.flutter.flutter.petro.app-jetified-emoji2-1.2.0-12 D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\res
com.flutter.flutter.petro.app-jetified-lifecycle-livedata-core-ktx-2.7.0-13 D:\android studio\android-gradle\caches\transforms-3\43922f3e57c2c912eec57434cbb6744d\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.flutter.flutter.petro.app-jetified-savedstate-1.2.1-14 D:\android studio\android-gradle\caches\transforms-3\48c9a35c82f156be1684c8ab76ef6b03\transformed\jetified-savedstate-1.2.1\res
com.flutter.flutter.petro.app-jetified-datastore-1.0.0-15 D:\android studio\android-gradle\caches\transforms-3\512b2a25e16a1406e73ee3b5e6cd16c3\transformed\jetified-datastore-1.0.0\res
com.flutter.flutter.petro.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-16 D:\android studio\android-gradle\caches\transforms-3\5a935e7c71e47ade6b8134b2e4a99145\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.flutter.flutter.petro.app-preference-1.2.1-17 D:\android studio\android-gradle\caches\transforms-3\5d18fffccc249c12ecbab6b8a87e0427\transformed\preference-1.2.1\res
com.flutter.flutter.petro.app-coordinatorlayout-1.0.0-18 D:\android studio\android-gradle\caches\transforms-3\617dcfbe9c747e0a623f2b2928118718\transformed\coordinatorlayout-1.0.0\res
com.flutter.flutter.petro.app-core-1.13.1-19 D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\res
com.flutter.flutter.petro.app-jetified-media3-exoplayer-1.4.1-20 D:\android studio\android-gradle\caches\transforms-3\66dd79550d656daefbd5340e0b36eaa6\transformed\jetified-media3-exoplayer-1.4.1\res
com.flutter.flutter.petro.app-jetified-lifecycle-viewmodel-ktx-2.7.0-21 D:\android studio\android-gradle\caches\transforms-3\72213405becc4e3fee4f515f45917a7a\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.flutter.flutter.petro.app-localbroadcastmanager-1.1.0-22 D:\android studio\android-gradle\caches\transforms-3\780980c5fffe5f1d8911df0d661e93d9\transformed\localbroadcastmanager-1.1.0\res
com.flutter.flutter.petro.app-jetified-startup-runtime-1.1.1-23 D:\android studio\android-gradle\caches\transforms-3\80c4d18896cdbc773b2f1f60789b5e42\transformed\jetified-startup-runtime-1.1.1\res
com.flutter.flutter.petro.app-lifecycle-livedata-2.7.0-24 D:\android studio\android-gradle\caches\transforms-3\8225ffeb08d708e7efb1b852e11a728d\transformed\lifecycle-livedata-2.7.0\res
com.flutter.flutter.petro.app-jetified-datastore-preferences-1.0.0-25 D:\android studio\android-gradle\caches\transforms-3\84b849765ae3a0585085c897356abe50\transformed\jetified-datastore-preferences-1.0.0\res
com.flutter.flutter.petro.app-jetified-emoji2-views-helper-1.2.0-26 D:\android studio\android-gradle\caches\transforms-3\89f7ee5dec93d6fdbe46619f84004c7c\transformed\jetified-emoji2-views-helper-1.2.0\res
com.flutter.flutter.petro.app-transition-1.4.1-27 D:\android studio\android-gradle\caches\transforms-3\96153ba6471db1591a8ad507cb8743e6\transformed\transition-1.4.1\res
com.flutter.flutter.petro.app-jetified-core-ktx-1.13.1-28 D:\android studio\android-gradle\caches\transforms-3\a6627928ae6ca4effd2ae52bc4fb3c05\transformed\jetified-core-ktx-1.13.1\res
com.flutter.flutter.petro.app-jetified-activity-ktx-1.9.2-29 D:\android studio\android-gradle\caches\transforms-3\ae4c8d377bd5af017000a82e04b9cc6a\transformed\jetified-activity-ktx-1.9.2\res
com.flutter.flutter.petro.app-jetified-fragment-ktx-1.7.1-30 D:\android studio\android-gradle\caches\transforms-3\afb727d7255b74bcd93b4e4cfd6abce8\transformed\jetified-fragment-ktx-1.7.1\res
com.flutter.flutter.petro.app-swiperefreshlayout-1.1.0-31 D:\android studio\android-gradle\caches\transforms-3\b088aec0414d8905b0878d7c76776e03\transformed\swiperefreshlayout-1.1.0\res
com.flutter.flutter.petro.app-browser-1.8.0-32 D:\android studio\android-gradle\caches\transforms-3\b3a78d3d4f57d7607051978acffd5ad0\transformed\browser-1.8.0\res
com.flutter.flutter.petro.app-jetified-play-services-base-18.3.0-33 D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\res
com.flutter.flutter.petro.app-jetified-window-java-1.2.0-34 D:\android studio\android-gradle\caches\transforms-3\bd4dee573af8be3f16540957b9ce3ccb\transformed\jetified-window-java-1.2.0\res
com.flutter.flutter.petro.app-jetified-profileinstaller-1.3.1-35 D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\res
com.flutter.flutter.petro.app-jetified-appcompat-resources-1.6.1-36 D:\android studio\android-gradle\caches\transforms-3\d0e385fe82401a0191f1cf9d187cd9f0\transformed\jetified-appcompat-resources-1.6.1\res
com.flutter.flutter.petro.app-jetified-play-services-basement-18.3.0-37 D:\android studio\android-gradle\caches\transforms-3\d552f6a7ccd94a500b0ef15a133e6620\transformed\jetified-play-services-basement-18.3.0\res
com.flutter.flutter.petro.app-core-runtime-2.2.0-38 D:\android studio\android-gradle\caches\transforms-3\d5bd0ecfcad5ba0480f408282b227112\transformed\core-runtime-2.2.0\res
com.flutter.flutter.petro.app-webkit-1.12.1-39 D:\android studio\android-gradle\caches\transforms-3\d9352224f62c4de4a648980e9fa8307c\transformed\webkit-1.12.1\res
com.flutter.flutter.petro.app-jetified-window-1.2.0-40 D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\res
com.flutter.flutter.petro.app-jetified-lifecycle-runtime-ktx-2.7.0-41 D:\android studio\android-gradle\caches\transforms-3\df4c21b81a26f0ad57bb1ac65a9353ce\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.flutter.flutter.petro.app-jetified-annotation-experimental-1.4.1-42 D:\android studio\android-gradle\caches\transforms-3\e2f7dc12e3088048c76469886c0dfee3\transformed\jetified-annotation-experimental-1.4.1\res
com.flutter.flutter.petro.app-recyclerview-1.0.0-43 D:\android studio\android-gradle\caches\transforms-3\e7b23cb7045a4238e7d3dac5d99ded32\transformed\recyclerview-1.0.0\res
com.flutter.flutter.petro.app-appcompat-1.6.1-44 D:\android studio\android-gradle\caches\transforms-3\ea3b19e143447a44a4f630074d7ef7d3\transformed\appcompat-1.6.1\res
com.flutter.flutter.petro.app-media-1.7.0-45 D:\android studio\android-gradle\caches\transforms-3\ef3b1689b5598393cd3c27b001894219\transformed\media-1.7.0\res
com.flutter.flutter.petro.app-jetified-tracing-1.2.0-46 D:\android studio\android-gradle\caches\transforms-3\efe0694dace90ef39afbb4014ccbf9eb\transformed\jetified-tracing-1.2.0\res
com.flutter.flutter.petro.app-jetified-android-maps-utils-3.6.0-47 D:\android studio\android-gradle\caches\transforms-3\f6637771952336731f0e0751842691c1\transformed\jetified-android-maps-utils-3.6.0\res
com.flutter.flutter.petro.app-jetified-savedstate-ktx-1.2.1-48 D:\android studio\android-gradle\caches\transforms-3\f924149d08275535fbe2237a3517bce8\transformed\jetified-savedstate-ktx-1.2.1\res
com.flutter.flutter.petro.app-jetified-zxing-android-embedded-4.3.0-49 D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\res
com.flutter.flutter.petro.app-debug-50 D:\geneat\beta-moible-flutter\android\app\src\debug\res
com.flutter.flutter.petro.app-main-51 D:\geneat\beta-moible-flutter\android\app\src\main\res
com.flutter.flutter.petro.app-pngs-52 D:\geneat\beta-moible-flutter\build\app\generated\res\pngs\debug
com.flutter.flutter.petro.app-resValues-53 D:\geneat\beta-moible-flutter\build\app\generated\res\resValues\debug
com.flutter.flutter.petro.app-rs-54 D:\geneat\beta-moible-flutter\build\app\generated\res\rs\debug
com.flutter.flutter.petro.app-mergeDebugResources-55 D:\geneat\beta-moible-flutter\build\app\intermediates\incremental\debug\mergeDebugResources\merged.dir
com.flutter.flutter.petro.app-mergeDebugResources-56 D:\geneat\beta-moible-flutter\build\app\intermediates\incremental\debug\mergeDebugResources\stripped.dir
com.flutter.flutter.petro.app-merged_res-57 D:\geneat\beta-moible-flutter\build\app\intermediates\merged_res\debug
com.flutter.flutter.petro.app-packaged_res-58 D:\geneat\beta-moible-flutter\build\audio_session\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-59 D:\geneat\beta-moible-flutter\build\device_info_plus\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-60 D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-61 D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-62 D:\geneat\beta-moible-flutter\build\fl_location\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-63 D:\geneat\beta-moible-flutter\build\flutter_contacts\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-64 D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-65 D:\geneat\beta-moible-flutter\build\flutter_keyboard_visibility\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-66 D:\geneat\beta-moible-flutter\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-67 D:\geneat\beta-moible-flutter\build\geocoding_android\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-68 D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-69 D:\geneat\beta-moible-flutter\build\google_maps_flutter_android\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-70 D:\geneat\beta-moible-flutter\build\image_gallery_saver\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-71 D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-72 D:\geneat\beta-moible-flutter\build\integration_test\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-73 D:\geneat\beta-moible-flutter\build\just_audio\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-74 D:\geneat\beta-moible-flutter\build\open_filex\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-75 D:\geneat\beta-moible-flutter\build\package_info_plus\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-76 D:\geneat\beta-moible-flutter\build\path_provider_android\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-77 D:\geneat\beta-moible-flutter\build\permission_handler_android\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-78 D:\geneat\beta-moible-flutter\build\qr_code_scanner\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-79 D:\geneat\beta-moible-flutter\build\shared_preferences_android\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-80 D:\geneat\beta-moible-flutter\build\sqflite_android\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-81 D:\geneat\beta-moible-flutter\build\syncfusion_flutter_pdfviewer\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-82 D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-83 D:\geneat\beta-moible-flutter\build\video_player_android\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-84 D:\geneat\beta-moible-flutter\build\wakelock_plus\intermediates\packaged_res\debug
com.flutter.flutter.petro.app-packaged_res-85 D:\geneat\beta-moible-flutter\build\webview_flutter_android\intermediates\packaged_res\debug
