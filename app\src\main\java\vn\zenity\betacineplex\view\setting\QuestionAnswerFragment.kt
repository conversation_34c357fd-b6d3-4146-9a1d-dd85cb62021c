package vn.zenity.betacineplex.view.setting

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_questionanswer.*
import kotlinx.android.synthetic.main.item_question_category.view.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.TopicModel

/**
 * Created by Zenity.
 */

class QuestionAnswerFragment : BaseFragment(), QuestionAnswerContractor.View {
    override fun showListCategories(categories: List<TopicModel>) {
        adapter.questionCategories = categories
        activity?.runOnUiThread {
            adapter.notifyDataSetChanged()
        }
    }

    private lateinit var adapter: Adapter

    private val presenter = QuestionAnswerPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_questionanswer
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(context)
        adapter = Adapter(listOf())
        recyclerView.adapter = adapter
        presenter.getCategories()
    }

    private inner class Adapter(var questionCategories: List<TopicModel>) : RecyclerView.Adapter<Holder>() {

        override fun getItemCount(): Int {
            return questionCategories.size
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            holder.itemView.tvTitle.text = questionCategories[position].Title
            holder.itemView.setOnClickListener {
                openFragment(QADetailFragment.getInstance(questionCategories[position].FeedbackThreadId ?: "", questionCategories[position].Title))
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            val item = LayoutInflater.from(parent.context).inflate(R.layout.item_question_category, parent, false)
            return Holder(item)
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)
}
