package vn.zenity.betacineplex.view.user

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.VoucherModel

/**
 * Created by Zenity.
 */

interface CouponContractor {
    interface View : IBaseView {
        fun showListCoupon(items: List<VoucherModel>)
        fun registerCouponSuccess()
    }

    interface Presenter : IBasePresenter<View> {
        fun registerCoupon(code: String, pin: String)
        fun getListCoupon(accountId: String)
    }
}
