package vn.zenity.betacineplex.view.user

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.PaymentHistoryDetailModel

/**
 * Created by Zenity.
 */

interface BookHistoryDetailContractor {
    interface View : IBaseView {
        fun onGetDetailSuccess(model: PaymentHistoryDetailModel)
    }

    interface Presenter : IBasePresenter<View> {
        fun getDetails(id: String)
    }
}
