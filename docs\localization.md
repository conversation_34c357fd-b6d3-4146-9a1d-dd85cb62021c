# Localization Implementation

This document describes the localization implementation in the Beta Cinemas Flutter app.

## Overview

The app supports two languages:
- Vietnamese (default)
- English

The localization is implemented using the `easy_localization` package, which provides a simple way to manage translations and switch between languages.

## Translation Files

Translation files are stored in the `assets/translations` directory:
- `vi.json` - Vietnamese translations
- `en.json` - English translations

These files contain key-value pairs for all the text used in the app. The keys are organized in a hierarchical structure to make them easier to manage.

## Language Service

The `LanguageService` class (`lib/service/language_service.dart`) provides methods for managing language settings:

```dart
// Get the current language code (en or vi)
Future<String> getCurrentLanguage()

// Check if the current language is English
Future<bool> isEnglish()

// Set the language and update the app locale
Future<void> setLanguage(BuildContext context, String languageCode)

// Get the appropriate field value based on current language
String getLocalizedField(String? defaultField, String? englishField)

// Get the language parameter for API calls
Future<String> getLanguageParam()

// Get the language-specific URL suffix for API calls
Future<String> getLanguageUrlSuffix()
```

## Language Widgets

The app includes several widgets for language switching:

1. `LanguageSwitcher` (`lib/widgets/language_switcher.dart`) - A button with a language icon and optional label
2. `LanguageDropdown` (`lib/widgets/language_switcher.dart`) - A dropdown menu for language selection
3. `LanguageAppBarButton` (`lib/widgets/language_app_bar_button.dart`) - A language switcher button for the app bar
4. `LanguageFlagButton` (`lib/widgets/language_app_bar_button.dart`) - A language switcher button with a flag icon

## API Localization

API calls include language parameters to ensure that the correct language is used for server responses. This is implemented in the following ways:

1. HTTP headers:
   ```dart
   headers['language'] = language;
   headers['x-language'] = language;
   ```

2. Query parameters:
   ```dart
   queryParameters['language'] = language;
   ```

3. URL suffixes (for some endpoints):
   ```dart
   path += '/$language'; // e.g., '/en' or '/vi'
   ```

## Model Localization

Models with fields ending in `_F` contain English translations. The `getLocalizedField` method in `LanguageService` is used to get the appropriate field value based on the current language:

```dart
String getLocalizedField(String? defaultField, String? englishField) {
  final context = rootNavigatorKey.currentContext;
  if (context == null) return defaultField ?? '';
  
  final isEnglish = context.locale.languageCode == 'en';
  return isEnglish ? (englishField ?? defaultField ?? '') : (defaultField ?? '');
}
```

Example usage:
```dart
final name = languageService.getLocalizedField(model.name, model.name_F);
```

## Usage in Code

### Translating Text

To translate text, use the `tr()` extension method provided by `easy_localization`:

```dart
Text('Setting.Language'.tr())
```

### Switching Languages

To switch languages, use the `LanguageService`:

```dart
final languageService = LanguageService();
await languageService.setLanguage(context, 'en'); // Switch to English
await languageService.setLanguage(context, 'vi'); // Switch to Vietnamese
```

### Adding Language Switcher to App Bar

```dart
AppBar(
  title: Text('App Title'),
  actions: [
    LanguageAppBarButton(
      onLanguageChanged: () {
        // Refresh UI or perform other actions after language change
      },
    ),
  ],
)
```

### Using Localized Fields in Models

```dart
String? getName() {
  final languageService = LanguageService();
  return languageService.getLocalizedField(Name, Name_F);
}
```

## Adding New Translations

1. Add the new key-value pair to both `vi.json` and `en.json` files
2. Use the key with the `tr()` method in the code

## Best Practices

1. Always use the `tr()` method for text that should be localized
2. Organize translation keys in a hierarchical structure
3. Use the `LanguageService` for language-related operations
4. Include language parameters in API calls
5. Use the `getLocalizedField` method for model fields with `_F` suffix
