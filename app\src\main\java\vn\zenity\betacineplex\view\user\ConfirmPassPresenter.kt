package vn.zenity.betacineplex.view.user

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.DeviceHelper
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.model.RequestModel.LoginModel
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class ConfirmPassPresenter : ConfirmPassContractor.Presenter {

    private var disposable: Disposable? = null

    override fun confirmPassword(password: String) {
        if(password.isNotEmpty()) {
            this.view?.get()?.showLoading()
            val model = LoginModel(Global.share().user?.Email ?: "", password, "")
            model.DeviceId = DeviceHelper.shared.deviceId()
            disposable = APIClient.shared.accountAPI.confirmPassword(model).applyOn()
                    .subscribe({
                        if(it.Data?.Result == true) {
                            this.view?.get()?.confirmSuccess()
                        } else {
                            this.view?.get()?.showError(it.Message ?: R.string.PASSWORD_INCORRECT.getString(), false)
                        }
                        this.view?.get()?.hideLoading()
                    }, {
                        this.view?.get()?.showError(R.string.PASSWORD_INCORRECT.getString(), false)
                        this.view?.get()?.hideLoading()
                    })
        } else {
            this.view?.get()?.showError(R.string.password_not_valid.getString())
        }
    }

    private var view: WeakReference<ConfirmPassContractor.View?>? = null
    override fun attachView(view: ConfirmPassContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
