package vn.zenity.betacineplex.global

import android.content.Context
import android.os.Bundle
import androidx.core.app.NotificationManagerCompat
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.mixpanel.android.mpmetrics.MixpanelAPI
import org.json.JSONObject
import vn.zenity.betacineplex.helper.extension.toDate
import java.util.*

class Tracking {
    companion object {
        var instance: Tracking? = null
        var mixpanel: MixpanelAPI? = null
        var lastTheature: String? = null

        fun share(): Tracking {
            if (instance == null) {
                instance = Tracking()
            }
            return instance!!
        }
    }

    fun setToken(context: Context?, token:String ) {
        mixpanel = MixpanelAPI.getInstance(context, token)
    }

    fun track(context: Context?, event: String?, properties: JSONObject?) {
//        print(FirebaseApp.getInstance().options)
        val pushEnabled =  if (context == null) false else NotificationManagerCompat.from(context).areNotificationsEnabled()
        var distinctId = Global.share().user?.UserId
        if(distinctId.isNullOrEmpty() || distinctId == "1"){
            distinctId = Global.share().user?.AccountId
        }
        if (!distinctId.isNullOrEmpty() && distinctId != "1"){
            val create = Global.share().user?.CreatedOnDate;
            val avatar = Global.share().user?.Picture;
            val email = Global.share().user?.Email;
            val name = Global.share().user?.FullName;
            val phone = Global.share().user?.PhoneOffice;
            val gender = Global.share().user?.Gender;
            val dob = Global.share().user?.BirthDate;
            val city = Global.share().user?.AddressCity;
            val membershipClass = Global.share().user?.ClassName;
            val membershipPointAccumulated = Global.share().user?.TotalAccumulatedPoints;
            val membershipPointAvailable = Global.share().user?.AvailablePoint;
            val membershipTotalPoint = Global.share().user?.TotalPoint;
            val lastPurchaseDate = Date()

            val dobDate = Calendar.getInstance()
            var ddob: Int? = null
            var mdob: Int? = null
            var ydob: Int? = null
            dob?.toDate(Constant.DateFormat.default).let {
                if (it != null) {
                    dobDate.time =  it
                    ddob = dobDate.get(Calendar.DAY_OF_MONTH)
                    mdob = dobDate.get(Calendar.MONTH)
                    ydob = dobDate.get(Calendar.YEAR)
                }
            }
            //Mixpanel
            mixpanel?.identify(distinctId)
            val people = JSONObject()
            people.put("\$distinct_id", distinctId)
            people.put("\$create", create)
            people.put("\$avatar", avatar)
            people.put("\$email", email)
            people.put("\$name", name)
            people.put("\$phone", phone)
            people.put("gender", gender)
            people.put("dob", dob)
            people.put("ddob", ddob)
            people.put("mdob", mdob)
            people.put("ydob", ydob)
            people.put("membership_class", membershipClass)
            people.put("point_accumulated", membershipPointAccumulated)
            people.put("point_available", membershipPointAvailable)
            people.put("total_point", membershipTotalPoint)
            people.put("city", city)
            people.put("push_enabled", pushEnabled)
            people.put("user_id", distinctId)
            if (event == "pay_success") {
                people.put("last_purchase_date", lastPurchaseDate)
            }
            if(lastTheature != null){
                people.put("last_theature", lastTheature)
            }
            mixpanel?.people?.set(people)

            //Analytics
            if(context != null){
                FirebaseAnalytics.getInstance(context).setUserId(distinctId)
                FirebaseAnalytics.getInstance(context).setUserProperty("\$distinct_id",distinctId)
                FirebaseAnalytics.getInstance(context).setUserProperty("\$create",create)
                FirebaseAnalytics.getInstance(context).setUserProperty("\$avatar",avatar)
                FirebaseAnalytics.getInstance(context).setUserProperty("\$email",email)
                FirebaseAnalytics.getInstance(context).setUserProperty("\$name",name)
                FirebaseAnalytics.getInstance(context).setUserProperty("\$phone",phone)
                if(gender != null){
                    FirebaseAnalytics.getInstance(context).setUserProperty("gender",gender.toString())
                }
                if(lastTheature != null){
                    FirebaseAnalytics.getInstance(context).setUserProperty("last_theature", lastTheature)
                }
                FirebaseAnalytics.getInstance(context).setUserProperty("dob",dob)
                if(ddob!=null){
                    FirebaseAnalytics.getInstance(context).setUserProperty("ddob",ddob.toString())
                }
                if(mdob!=null){
                    FirebaseAnalytics.getInstance(context).setUserProperty("mdob",mdob.toString())
                }
                if(ydob!=null){
                    FirebaseAnalytics.getInstance(context).setUserProperty("ydob",ydob.toString())
                }
                FirebaseAnalytics.getInstance(context).setUserProperty("membership_class",membershipClass)
                if(membershipPointAccumulated!=null){
                    FirebaseAnalytics.getInstance(context).setUserProperty("point_accumulated",membershipPointAccumulated.toString())
                }
                if(membershipPointAvailable!=null){
                    FirebaseAnalytics.getInstance(context).setUserProperty("point_available",membershipPointAvailable.toString())
                }
                if(membershipTotalPoint!=null){
                    FirebaseAnalytics.getInstance(context).setUserProperty("total_point",membershipTotalPoint.toString())
                }
                FirebaseAnalytics.getInstance(context).setUserProperty("city",city)
                FirebaseAnalytics.getInstance(context).setUserProperty("push_enabled",pushEnabled.toString())
                FirebaseAnalytics.getInstance(context).setUserProperty("user_id", distinctId)
                if( event == "pay_success") {
                    FirebaseAnalytics.getInstance(context).setUserProperty("last_purchase_date",lastPurchaseDate.toString())
                }
            }

        }
        if(!event.isNullOrEmpty()){
            mixpanel?.track(event,properties)
            print("tracking: $event")
        }
        if(context != null && event != null){
            val bundle = Bundle()
            if(properties != null){
                for(k in properties.keys()){
                    when (val v = properties[k]) {
                        is String -> {
                            bundle.putString(k,v)
                        }
                        is Int -> {
                            bundle.putInt(k,v)
                        }
                        is Double -> {
                            bundle.putDouble(k,v)
                        }
                        is Date -> {
                            bundle.putString(k,v.toString())
                        }
                        is Boolean -> {
                            bundle.putBoolean(k,v)
                        }
                    }
                }
            }
            FirebaseAnalytics.getInstance(context).logEvent(event,bundle)
        }
    }


    fun authBegin(context: Context?, method: String?) {
        val prop = JSONObject()
        prop.put("method",method)
        track( context,"auth_begin", prop)
    }

    //user sign-up success
    fun authComplete(context:Context?,method: String?) {
        val prop = JSONObject()
        prop.put("method",method)
        track( context,"auth_complete", prop)
    }

    //user user select a theater
    fun selectTheater(context:Context?,cinemaId: String?, cinemaName: String?) {
        val prop = JSONObject()
        prop.put("cinema_id",cinemaId)
        prop.put("cinema_name",cinemaName)
        lastTheature = cinemaName
        track( context,"select_theater", prop)
    }

    //user user view movie details and showtimes
    fun selectMovie(context:Context?,movieId: String?, movieName: String?) {
        val prop = JSONObject()
        prop.put("movie_id",movieId)
        prop.put("movie_name",movieName)
        track( context,"select_movie", prop)
    }

    //user complete selecting a showtime
    fun selectShowtimeComplete(context:Context?,cinemaId: String?, cinemaName: String?, movieId: String?, movieName: String?, date: Date?, time: Date?) {
        val prop = JSONObject()
        prop.put("cinema_id",cinemaId)
        prop.put("cinema_name",cinemaName)
        prop.put("movie_id",movieId)
        prop.put("movie_name",movieName)
        prop.put("date",date)
        prop.put("time",time)
        track( context,"select_showtime_complete", prop)
    }

    //user complete selecting seats
    fun selectSeatComplete(context:Context?,cinemaId: String?, cinemaName: String?,
                           movieId: String?, movieName: String?, date: Date?, time: Date?,
                           screen: String?, normalSeats: Int?, vipSeats: Int?, doubleSeats: Int?,
                           totalSeats: Int?, totalAmount: Int? ) {
        val prop = JSONObject()
        prop.put("cinema_id",cinemaId)
        prop.put("cinema_name",cinemaName)
        prop.put("movie_id",movieId)
        prop.put("movie_name",movieName)
        prop.put("date",date)
        prop.put("time",time)
        prop.put("screen",screen)
        prop.put("normal_seats",normalSeats)
        prop.put("vip_seats",vipSeats)
        prop.put("double_seats",doubleSeats)
        prop.put("total_seats",totalSeats)
        prop.put("total_amount",totalAmount)
        track( context,"select_seat_complete", prop)
    }

    //user confirm payment
    fun confirmPayment(context:Context?,cinemaId: String?, cinemaName: String?,
                       movieId: String?, movieName: String?, date: Date?, time: Date?,
                       screen: String?, normalSeats: Int?, vipSeats: Int?, doubleSeats: Int?,
                       totalSeats: Int?, totalAmount: Int?, discountAmount: Int?, paymentAmount: Int?,
                       totalCombos: Int? ,totalComboAmount: Int?,redeemVouchers: Int?,redeemPoints: Int?,
                       paymentMethod: String?,orderId: String?, channel: String?   ) {
        val prop = JSONObject()
        prop.put("cinema_id",cinemaId)
        prop.put("cinema_name",cinemaName)
        prop.put("movie_id",movieId)
        prop.put("movie_name",movieName)
        prop.put("date",date)
        prop.put("time",time)
        prop.put("screen",screen)
        prop.put("normal_seats",normalSeats)
        prop.put("vip_seats",vipSeats)
        prop.put("double_seats",doubleSeats)
        prop.put("total_seats",totalSeats)
        prop.put("total_amount",totalAmount)
        prop.put("discount_amount",discountAmount)
        prop.put("payment_amount",paymentAmount)
        prop.put("total_combos",totalCombos)
        prop.put("total_combo_amount",totalComboAmount)
        prop.put("redeem_vouchers",redeemVouchers)
        prop.put("redeem_points",redeemPoints)
        prop.put("payment_method",paymentMethod)
        prop.put("order_id",orderId)
        prop.put("channel",channel)
        track( context,"confirm_payment", prop)
    }

    //user pay order success
    fun paySuccess(context:Context?,cinemaId: String?, cinemaName: String?,
                   movieId: String?, movieName: String?, date: Date?, time: Date?,
                   screen: String?, normalSeats: Int?, vipSeats: Int?, doubleSeats: Int?,
                   totalSeats: Int?, totalAmount: Int?, discountAmount: Int?, paymentAmount: Int?,
                   totalCombos: Int? ,totalComboAmount: Int?,redeemVouchers: Int?,redeemPoints: Int?,
                   paymentMethod: String?,orderId: String?, channel: String?   ) {
        val prop = JSONObject()
        prop.put("cinema_id",cinemaId)
        prop.put("cinema_name",cinemaName)
        prop.put("movie_id",movieId)
        prop.put("movie_name",movieName)
        prop.put("date",date)
        prop.put("time",time)
        prop.put("screen",screen)
        prop.put("normal_seats",normalSeats)
        prop.put("vip_seats",vipSeats)
        prop.put("double_seats",doubleSeats)
        prop.put("total_seats",totalSeats)
        prop.put("total_amount",totalAmount)
        prop.put("discount_amount",discountAmount)
        prop.put("payment_amount",paymentAmount)
        prop.put("total_combos",totalCombos)
        prop.put("total_combo_amount",totalComboAmount)
        prop.put("redeem_vouchers",redeemVouchers)
        prop.put("redeem_points",redeemPoints)
        prop.put("payment_method",paymentMethod)
        prop.put("order_id",orderId)
        prop.put("channel",channel)
        track( context,"pay_success", prop)
    }

    //user pay order fail
    fun payFail(context:Context?,cinemaId: String?, cinemaName: String?,
                movieId: String?, movieName: String?, date: Date?, time: Date?,
                screen: String?, normalSeats: Int?, vipSeats: Int?, doubleSeats: Int?,
                totalSeats: Int?, totalAmount: Int?, discountAmount: Int?, paymentAmount: Int?,
                totalCombos: Int? ,totalComboAmount: Int?,redeemVouchers: Int?,redeemPoints: Int?,
                paymentMethod: String?,orderId: String?, channel: String?,
                errorCode: String?, errorMsg:String?   ) {
        val prop = JSONObject()
        prop.put("cinema_id",cinemaId)
        prop.put("cinema_name",cinemaName)
        prop.put("movie_id",movieId)
        prop.put("movie_name",movieName)
        prop.put("date",date)
        prop.put("time",time)
        prop.put("screen",screen)
        prop.put("normal_seats",normalSeats)
        prop.put("vip_seats",vipSeats)
        prop.put("double_seats",doubleSeats)
        prop.put("total_seats",totalSeats)
        prop.put("total_amount",totalAmount)
        prop.put("discount_amount",discountAmount)
        prop.put("payment_amount",paymentAmount)
        prop.put("total_combos",totalCombos)
        prop.put("total_combo_amount",totalComboAmount)
        prop.put("redeem_vouchers",redeemVouchers)
        prop.put("redeem_points",redeemPoints)
        prop.put("payment_method",paymentMethod)
        prop.put("order_id",orderId)
        prop.put("channel",channel)
        prop.put("error_code",errorCode)
        prop.put("error_msg",errorMsg)
        track( context,"pay_fail", prop)
    }

    //logout
    fun logout(){
        mixpanel?.reset()
    }
}