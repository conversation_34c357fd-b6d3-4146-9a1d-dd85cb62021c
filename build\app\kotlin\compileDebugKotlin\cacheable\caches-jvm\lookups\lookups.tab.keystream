  GeneratedPluginRegistrant android.app.Activity  
SignalRPlugin android.app.Activity  Context android.content  GeneratedPluginRegistrant android.content.Context  
SignalRPlugin android.content.Context  GeneratedPluginRegistrant android.content.ContextWrapper  
SignalRPlugin android.content.ContextWrapper  Handler 
android.os  Looper 
android.os  post android.os.Handler  postDelayed android.os.Handler  removeCallbacks android.os.Handler  
getMainLooper android.os.Looper  Log android.util  d android.util.Log  e android.util.Log  GeneratedPluginRegistrant  android.view.ContextThemeWrapper  
SignalRPlugin  android.view.ContextThemeWrapper  NonNull androidx.annotation  AndroidPlatformComponent com.betacineplex  Any com.betacineplex  ConnectionState com.betacineplex  EventChannel com.betacineplex  	Exception com.betacineplex  
FlutterPlugin com.betacineplex  Handler com.betacineplex  HashMap com.betacineplex  
HubConnection com.betacineplex  HubProxy com.betacineplex  Int com.betacineplex  Log com.betacineplex  Looper com.betacineplex  
MethodCall com.betacineplex  
MethodChannel com.betacineplex  Platform com.betacineplex  ServerSentEventsTransport com.betacineplex  
SignalRPlugin com.betacineplex  String com.betacineplex  TAG com.betacineplex  Timer com.betacineplex  	TimerTask com.betacineplex  arrayOf com.betacineplex  
dataEventSink com.betacineplex  java com.betacineplex  mHubConnection com.betacineplex  	mHubProxy com.betacineplex  set com.betacineplex  	EventSink com.betacineplex.EventChannel  
StreamHandler com.betacineplex.EventChannel  FlutterPluginBinding com.betacineplex.FlutterPlugin  MethodCallHandler com.betacineplex.MethodChannel  Result com.betacineplex.MethodChannel  AndroidPlatformComponent com.betacineplex.SignalRPlugin  Any com.betacineplex.SignalRPlugin  ConnectionState com.betacineplex.SignalRPlugin  EventChannel com.betacineplex.SignalRPlugin  Handler com.betacineplex.SignalRPlugin  HashMap com.betacineplex.SignalRPlugin  
HubConnection com.betacineplex.SignalRPlugin  Log com.betacineplex.SignalRPlugin  Looper com.betacineplex.SignalRPlugin  
MethodChannel com.betacineplex.SignalRPlugin  Platform com.betacineplex.SignalRPlugin  ServerSentEventsTransport com.betacineplex.SignalRPlugin  TAG com.betacineplex.SignalRPlugin  Timer com.betacineplex.SignalRPlugin  arrayOf com.betacineplex.SignalRPlugin  connectionStateChannel com.betacineplex.SignalRPlugin  connectionStateEventSink com.betacineplex.SignalRPlugin  dataChannel com.betacineplex.SignalRPlugin  
dataEventSink com.betacineplex.SignalRPlugin  
defaultUrl com.betacineplex.SignalRPlugin  handler com.betacineplex.SignalRPlugin  hubName com.betacineplex.SignalRPlugin  java com.betacineplex.SignalRPlugin  mHubConnection com.betacineplex.SignalRPlugin  	mHubProxy com.betacineplex.SignalRPlugin  
methodChannel com.betacineplex.SignalRPlugin  sendSeat com.betacineplex.SignalRPlugin  set com.betacineplex.SignalRPlugin  setupEventHandlers com.betacineplex.SignalRPlugin  startKeepAliveTimer com.betacineplex.SignalRPlugin  startSignalR com.betacineplex.SignalRPlugin  stopSignalR com.betacineplex.SignalRPlugin  timer com.betacineplex.SignalRPlugin  FlutterActivity com.example.flutter_app  
FlutterEngine com.example.flutter_app  GeneratedPluginRegistrant com.example.flutter_app  MainActivity com.example.flutter_app  NonNull com.example.flutter_app  
SignalRPlugin com.example.flutter_app  GeneratedPluginRegistrant $com.example.flutter_app.MainActivity  
SignalRPlugin $com.example.flutter_app.MainActivity  AndroidPlatformComponent com.example.flutter_app.signalr  Any com.example.flutter_app.signalr  Boolean com.example.flutter_app.signalr  ConnectionState com.example.flutter_app.signalr  Context com.example.flutter_app.signalr  EVENT_CHANNEL_NAME com.example.flutter_app.signalr  EventChannel com.example.flutter_app.signalr  	Exception com.example.flutter_app.signalr  
FlutterPlugin com.example.flutter_app.signalr  Handler com.example.flutter_app.signalr  HashMap com.example.flutter_app.signalr  
HubConnection com.example.flutter_app.signalr  HubProxy com.example.flutter_app.signalr  Int com.example.flutter_app.signalr  List com.example.flutter_app.signalr  Log com.example.flutter_app.signalr  Logger com.example.flutter_app.signalr  Looper com.example.flutter_app.signalr  METHOD_CHANNEL_NAME com.example.flutter_app.signalr  
MethodCall com.example.flutter_app.signalr  MethodCallHandler com.example.flutter_app.signalr  
MethodChannel com.example.flutter_app.signalr  Platform com.example.flutter_app.signalr  Result com.example.flutter_app.signalr  Runnable com.example.flutter_app.signalr  ServerSentEventsTransport com.example.flutter_app.signalr  
SignalRPlugin com.example.flutter_app.signalr  SignalRService com.example.flutter_app.signalr  String com.example.flutter_app.signalr  TAG com.example.flutter_app.signalr  Unit com.example.flutter_app.signalr  endsWith com.example.flutter_app.signalr  forEach com.example.flutter_app.signalr  getInstance com.example.flutter_app.signalr  handler com.example.flutter_app.signalr  
hubConnection com.example.flutter_app.signalr  
isNotEmpty com.example.flutter_app.signalr  listOf com.example.flutter_app.signalr  	microsoft com.example.flutter_app.signalr  
mutableListOf com.example.flutter_app.signalr  	reconnect com.example.flutter_app.signalr  set com.example.flutter_app.signalr  	EventSink ,com.example.flutter_app.signalr.EventChannel  
StreamHandler ,com.example.flutter_app.signalr.EventChannel  FlutterPluginBinding -com.example.flutter_app.signalr.FlutterPlugin  Any -com.example.flutter_app.signalr.SignalRPlugin  Context -com.example.flutter_app.signalr.SignalRPlugin  EVENT_CHANNEL_NAME -com.example.flutter_app.signalr.SignalRPlugin  EventChannel -com.example.flutter_app.signalr.SignalRPlugin  	Exception -com.example.flutter_app.signalr.SignalRPlugin  
FlutterPlugin -com.example.flutter_app.signalr.SignalRPlugin  Handler -com.example.flutter_app.signalr.SignalRPlugin  HashMap -com.example.flutter_app.signalr.SignalRPlugin  List -com.example.flutter_app.signalr.SignalRPlugin  Log -com.example.flutter_app.signalr.SignalRPlugin  Looper -com.example.flutter_app.signalr.SignalRPlugin  METHOD_CHANNEL_NAME -com.example.flutter_app.signalr.SignalRPlugin  
MethodCall -com.example.flutter_app.signalr.SignalRPlugin  
MethodChannel -com.example.flutter_app.signalr.SignalRPlugin  Result -com.example.flutter_app.signalr.SignalRPlugin  SignalRService -com.example.flutter_app.signalr.SignalRPlugin  String -com.example.flutter_app.signalr.SignalRPlugin  TAG -com.example.flutter_app.signalr.SignalRPlugin  context -com.example.flutter_app.signalr.SignalRPlugin  eventChannel -com.example.flutter_app.signalr.SignalRPlugin  	eventSink -com.example.flutter_app.signalr.SignalRPlugin  getInstance -com.example.flutter_app.signalr.SignalRPlugin  listOf -com.example.flutter_app.signalr.SignalRPlugin  
methodChannel -com.example.flutter_app.signalr.SignalRPlugin  set -com.example.flutter_app.signalr.SignalRPlugin  signalRService -com.example.flutter_app.signalr.SignalRPlugin  EVENT_CHANNEL_NAME 7com.example.flutter_app.signalr.SignalRPlugin.Companion  EventChannel 7com.example.flutter_app.signalr.SignalRPlugin.Companion  Handler 7com.example.flutter_app.signalr.SignalRPlugin.Companion  HashMap 7com.example.flutter_app.signalr.SignalRPlugin.Companion  Log 7com.example.flutter_app.signalr.SignalRPlugin.Companion  Looper 7com.example.flutter_app.signalr.SignalRPlugin.Companion  METHOD_CHANNEL_NAME 7com.example.flutter_app.signalr.SignalRPlugin.Companion  
MethodChannel 7com.example.flutter_app.signalr.SignalRPlugin.Companion  SignalRService 7com.example.flutter_app.signalr.SignalRPlugin.Companion  TAG 7com.example.flutter_app.signalr.SignalRPlugin.Companion  getInstance 7com.example.flutter_app.signalr.SignalRPlugin.Companion  listOf 7com.example.flutter_app.signalr.SignalRPlugin.Companion  set 7com.example.flutter_app.signalr.SignalRPlugin.Companion  	EventSink :com.example.flutter_app.signalr.SignalRPlugin.EventChannel  FlutterPluginBinding ;com.example.flutter_app.signalr.SignalRPlugin.FlutterPlugin  AndroidPlatformComponent .com.example.flutter_app.signalr.SignalRService  Any .com.example.flutter_app.signalr.SignalRService  Boolean .com.example.flutter_app.signalr.SignalRService  	Companion .com.example.flutter_app.signalr.SignalRService  ConnectionState .com.example.flutter_app.signalr.SignalRService  	Exception .com.example.flutter_app.signalr.SignalRService  Handler .com.example.flutter_app.signalr.SignalRService  
HubConnection .com.example.flutter_app.signalr.SignalRService  HubProxy .com.example.flutter_app.signalr.SignalRService  Int .com.example.flutter_app.signalr.SignalRService  List .com.example.flutter_app.signalr.SignalRService  Log .com.example.flutter_app.signalr.SignalRService  Logger .com.example.flutter_app.signalr.SignalRService  Looper .com.example.flutter_app.signalr.SignalRService  Platform .com.example.flutter_app.signalr.SignalRService  Runnable .com.example.flutter_app.signalr.SignalRService  ServerSentEventsTransport .com.example.flutter_app.signalr.SignalRService  SignalRService .com.example.flutter_app.signalr.SignalRService  String .com.example.flutter_app.signalr.SignalRService  TAG .com.example.flutter_app.signalr.SignalRService  Unit .com.example.flutter_app.signalr.SignalRService  addConnectionListener .com.example.flutter_app.signalr.SignalRService  addMessageListener .com.example.flutter_app.signalr.SignalRService  connectionId .com.example.flutter_app.signalr.SignalRService  connectionListeners .com.example.flutter_app.signalr.SignalRService  connectionState .com.example.flutter_app.signalr.SignalRService  endsWith .com.example.flutter_app.signalr.SignalRService  getInstance .com.example.flutter_app.signalr.SignalRService  handler .com.example.flutter_app.signalr.SignalRService  
hubConnection .com.example.flutter_app.signalr.SignalRService  hubProxy .com.example.flutter_app.signalr.SignalRService  instance .com.example.flutter_app.signalr.SignalRService  invoke .com.example.flutter_app.signalr.SignalRService  isConnected .com.example.flutter_app.signalr.SignalRService  
isNotEmpty .com.example.flutter_app.signalr.SignalRService  messageListeners .com.example.flutter_app.signalr.SignalRService  	microsoft .com.example.flutter_app.signalr.SignalRService  
mutableListOf .com.example.flutter_app.signalr.SignalRService  	reconnect .com.example.flutter_app.signalr.SignalRService  reconnectRunnable .com.example.flutter_app.signalr.SignalRService  startSignalR .com.example.flutter_app.signalr.SignalRService  stop .com.example.flutter_app.signalr.SignalRService  	subscribe .com.example.flutter_app.signalr.SignalRService  unsubscribe .com.example.flutter_app.signalr.SignalRService  AndroidPlatformComponent 8com.example.flutter_app.signalr.SignalRService.Companion  ConnectionState 8com.example.flutter_app.signalr.SignalRService.Companion  Handler 8com.example.flutter_app.signalr.SignalRService.Companion  
HubConnection 8com.example.flutter_app.signalr.SignalRService.Companion  Log 8com.example.flutter_app.signalr.SignalRService.Companion  Logger 8com.example.flutter_app.signalr.SignalRService.Companion  Looper 8com.example.flutter_app.signalr.SignalRService.Companion  Platform 8com.example.flutter_app.signalr.SignalRService.Companion  ServerSentEventsTransport 8com.example.flutter_app.signalr.SignalRService.Companion  SignalRService 8com.example.flutter_app.signalr.SignalRService.Companion  TAG 8com.example.flutter_app.signalr.SignalRService.Companion  endsWith 8com.example.flutter_app.signalr.SignalRService.Companion  getInstance 8com.example.flutter_app.signalr.SignalRService.Companion  handler 8com.example.flutter_app.signalr.SignalRService.Companion  
hubConnection 8com.example.flutter_app.signalr.SignalRService.Companion  instance 8com.example.flutter_app.signalr.SignalRService.Companion  
isNotEmpty 8com.example.flutter_app.signalr.SignalRService.Companion  	microsoft 8com.example.flutter_app.signalr.SignalRService.Companion  
mutableListOf 8com.example.flutter_app.signalr.SignalRService.Companion  	reconnect 8com.example.flutter_app.signalr.SignalRService.Companion  	CONNECTED >com.example.flutter_app.signalr.SignalRService.ConnectionState  DISCONNECTED >com.example.flutter_app.signalr.SignalRService.ConnectionState  FlutterActivity com.flutter.flutter.petro  
FlutterEngine com.flutter.flutter.petro  MainActivity com.flutter.flutter.petro  
SignalRPlugin com.flutter.flutter.petro  
SignalRPlugin &com.flutter.flutter.petro.MainActivity  
JsonObject com.google.gson  get com.google.gson.JsonArray  size com.google.gson.JsonArray  asInt com.google.gson.JsonElement  asJsonObject com.google.gson.JsonElement  asString com.google.gson.JsonElement  getAsJsonArray com.google.gson.JsonElement  isJsonObject com.google.gson.JsonElement  getAsJsonArray com.google.gson.JsonObject  has com.google.gson.JsonObject  FlutterActivity io.flutter.embedding.android  GeneratedPluginRegistrant ,io.flutter.embedding.android.FlutterActivity  
SignalRPlugin ,io.flutter.embedding.android.FlutterActivity  configureFlutterEngine ,io.flutter.embedding.android.FlutterActivity  
FlutterEngine io.flutter.embedding.engine  plugins )io.flutter.embedding.engine.FlutterEngine  
FlutterPlugin #io.flutter.embedding.engine.plugins  PluginRegistry #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  add 2io.flutter.embedding.engine.plugins.PluginRegistry  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  success /io.flutter.plugin.common.EventChannel.EventSink  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  GeneratedPluginRegistrant io.flutter.plugins  registerWith ,io.flutter.plugins.GeneratedPluginRegistrant  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  HashMap 	java.util  Timer 	java.util  	TimerTask 	java.util  set java.util.HashMap  cancel java.util.Timer  scheduleAtFixedRate java.util.Timer  ConnectionState java.util.TimerTask  	Exception java.util.TimerTask  Log java.util.TimerTask  TAG java.util.TimerTask  mHubConnection java.util.TimerTask  	mHubProxy java.util.TimerTask  TimeUnit java.util.concurrent  Array kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function4 kotlin  Nothing kotlin  	Throwable kotlin  arrayOf kotlin  invoke kotlin.Function1  invoke kotlin.Function2  invoke kotlin.Function4  	compareTo 
kotlin.Int  times 
kotlin.Int  toString 
kotlin.Int  endsWith 
kotlin.String  
isNotEmpty 
kotlin.String  message kotlin.Throwable  List kotlin.collections  MutableList kotlin.collections  forEach kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  
mutableListOf kotlin.collections  set kotlin.collections  get kotlin.collections.List  size kotlin.collections.List  add kotlin.collections.MutableList  remove kotlin.collections.MutableList  endsWith 	kotlin.io  java 
kotlin.jvm  java kotlin.reflect.KClass  forEach kotlin.sequences  endsWith kotlin.text  forEach kotlin.text  
isNotEmpty kotlin.text  set kotlin.text  Action microsoft.aspnet.signalr.client  AndroidPlatformComponent microsoft.aspnet.signalr.client  Any microsoft.aspnet.signalr.client  Boolean microsoft.aspnet.signalr.client  ConnectionState microsoft.aspnet.signalr.client  
ErrorCallback microsoft.aspnet.signalr.client  	Exception microsoft.aspnet.signalr.client  Handler microsoft.aspnet.signalr.client  
HubConnection microsoft.aspnet.signalr.client  HubProxy microsoft.aspnet.signalr.client  Int microsoft.aspnet.signalr.client  List microsoft.aspnet.signalr.client  Log microsoft.aspnet.signalr.client  Logger microsoft.aspnet.signalr.client  Looper microsoft.aspnet.signalr.client  MessageReceivedHandler microsoft.aspnet.signalr.client  Platform microsoft.aspnet.signalr.client  Runnable microsoft.aspnet.signalr.client  ServerSentEventsTransport microsoft.aspnet.signalr.client  
SignalRFuture microsoft.aspnet.signalr.client  SignalRService microsoft.aspnet.signalr.client  StateChangedCallback microsoft.aspnet.signalr.client  String microsoft.aspnet.signalr.client  TAG microsoft.aspnet.signalr.client  Unit microsoft.aspnet.signalr.client  endsWith microsoft.aspnet.signalr.client  forEach microsoft.aspnet.signalr.client  handler microsoft.aspnet.signalr.client  
hubConnection microsoft.aspnet.signalr.client  
isNotEmpty microsoft.aspnet.signalr.client  	microsoft microsoft.aspnet.signalr.client  
mutableListOf microsoft.aspnet.signalr.client  	reconnect microsoft.aspnet.signalr.client  <SAM-CONSTRUCTOR> &microsoft.aspnet.signalr.client.Action  closed *microsoft.aspnet.signalr.client.Connection  	connected *microsoft.aspnet.signalr.client.Connection  connectionId *microsoft.aspnet.signalr.client.Connection  error *microsoft.aspnet.signalr.client.Connection  logger *microsoft.aspnet.signalr.client.Connection  received *microsoft.aspnet.signalr.client.Connection  reconnecting *microsoft.aspnet.signalr.client.Connection  start *microsoft.aspnet.signalr.client.Connection  state *microsoft.aspnet.signalr.client.Connection  stateChanged *microsoft.aspnet.signalr.client.Connection  stop *microsoft.aspnet.signalr.client.Connection  	Connected /microsoft.aspnet.signalr.client.ConnectionState  Disconnected /microsoft.aspnet.signalr.client.ConnectionState  <SAM-CONSTRUCTOR> -microsoft.aspnet.signalr.client.ErrorCallback  <SAM-CONSTRUCTOR> &microsoft.aspnet.signalr.client.Logger  <SAM-CONSTRUCTOR> 6microsoft.aspnet.signalr.client.MessageReceivedHandler  loadPlatformComponent (microsoft.aspnet.signalr.client.Platform  done -microsoft.aspnet.signalr.client.SignalRFuture  onError -microsoft.aspnet.signalr.client.SignalRFuture  <SAM-CONSTRUCTOR> 4microsoft.aspnet.signalr.client.StateChangedCallback  AndroidPlatformComponent ,microsoft.aspnet.signalr.client.http.android  
HubConnection $microsoft.aspnet.signalr.client.hubs  HubProxy $microsoft.aspnet.signalr.client.hubs  Subscription $microsoft.aspnet.signalr.client.hubs  SubscriptionHandler1 $microsoft.aspnet.signalr.client.hubs  closed 2microsoft.aspnet.signalr.client.hubs.HubConnection  	connected 2microsoft.aspnet.signalr.client.hubs.HubConnection  connectionId 2microsoft.aspnet.signalr.client.hubs.HubConnection  createHubProxy 2microsoft.aspnet.signalr.client.hubs.HubConnection  error 2microsoft.aspnet.signalr.client.hubs.HubConnection  logger 2microsoft.aspnet.signalr.client.hubs.HubConnection  received 2microsoft.aspnet.signalr.client.hubs.HubConnection  reconnecting 2microsoft.aspnet.signalr.client.hubs.HubConnection  start 2microsoft.aspnet.signalr.client.hubs.HubConnection  state 2microsoft.aspnet.signalr.client.hubs.HubConnection  stateChanged 2microsoft.aspnet.signalr.client.hubs.HubConnection  stop 2microsoft.aspnet.signalr.client.hubs.HubConnection  invoke -microsoft.aspnet.signalr.client.hubs.HubProxy  on -microsoft.aspnet.signalr.client.hubs.HubProxy  removeSubscription -microsoft.aspnet.signalr.client.hubs.HubProxy  	subscribe -microsoft.aspnet.signalr.client.hubs.HubProxy  <SAM-CONSTRUCTOR> 9microsoft.aspnet.signalr.client.hubs.SubscriptionHandler1  ServerSentEventsTransport )microsoft.aspnet.signalr.client.transport                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        