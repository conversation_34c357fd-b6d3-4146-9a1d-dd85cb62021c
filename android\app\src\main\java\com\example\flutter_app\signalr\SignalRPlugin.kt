package com.example.flutter_app.signalr

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

/**
 * Flutter plugin for SignalR
 * Handles communication between Flutter and the native SignalR implementation
 */
class SignalRPlugin : FlutterPlugin, MethodCallHandler, EventChannel.StreamHandler {
    companion object {
        private const val TAG = "SignalRPlugin"
        private const val METHOD_CHANNEL_NAME = "com.betacinemas.signalr/methods"
        private const val EVENT_CHANNEL_NAME = "com.betacinemas.signalr/events"
    }

    // Flutter channels
    private lateinit var methodChannel: MethodChannel
    private lateinit var eventChannel: EventChannel
    
    // SignalR service
    private val signalRService = SignalRService.getInstance()
    
    // Event sink for sending events to Flutter
    private var eventSink: EventChannel.EventSink? = null
    
    // Context
    private lateinit var context: Context

    /**
     * Called when the plugin is attached to the Flutter engine
     */
    override fun onAttachedToEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        context = binding.applicationContext
        
        // Set up method channel
        methodChannel = MethodChannel(binding.binaryMessenger, METHOD_CHANNEL_NAME)
        methodChannel.setMethodCallHandler(this)
        
        // Set up event channel
        eventChannel = EventChannel(binding.binaryMessenger, EVENT_CHANNEL_NAME)
        eventChannel.setStreamHandler(this)
        
        Log.d(TAG, "SignalR plugin attached to engine")
    }

    /**
     * Called when the plugin is detached from the Flutter engine
     */
    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        methodChannel.setMethodCallHandler(null)
        eventChannel.setStreamHandler(null)
        signalRService.stop()
        Log.d(TAG, "SignalR plugin detached from engine")
    }

    /**
     * Handle method calls from Flutter
     */
    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "connect" -> {
                val url = call.argument<String>("url")
                val hubName = call.argument<String>("hubName") ?: "chooseSeatHub"
                val token = call.argument<String>("token")
                
                if (url == null) {
                    result.error("INVALID_ARGUMENT", "URL is required", null)
                    return
                }
                
                signalRService.startSignalR(url, hubName, token) { success, connectionId ->
                    if (success) {
                        val resultMap = HashMap<String, Any?>()
                        resultMap["connectionId"] = connectionId
                        result.success(resultMap)
                    } else {
                        result.error("CONNECTION_FAILED", "Failed to connect to SignalR hub", connectionId)
                    }
                }
            }
            "disconnect" -> {
                signalRService.stop()
                result.success(null)
            }
            "isConnected" -> {
                result.success(signalRService.isConnected())
            }
            "invoke" -> {
                val method = call.argument<String>("method")
                val arguments = call.argument<List<Any>>("arguments") ?: listOf()
                
                if (method == null) {
                    result.error("INVALID_ARGUMENT", "Method is required", null)
                    return
                }
                
                try {
                    signalRService.invoke(method, arguments)
                    result.success(null)
                } catch (e: Exception) {
                    result.error("INVOKE_FAILED", "Failed to invoke method: ${e.message}", null)
                }
            }
            "on" -> {
                val method = call.argument<String>("method")
                
                if (method == null) {
                    result.error("INVALID_ARGUMENT", "Method is required", null)
                    return
                }
                
                try {
                    signalRService.subscribe(method)
                    result.success(null)
                } catch (e: Exception) {
                    result.error("SUBSCRIBE_FAILED", "Failed to subscribe to method: ${e.message}", null)
                }
            }
            "off" -> {
                val method = call.argument<String>("method")
                
                if (method == null) {
                    result.error("INVALID_ARGUMENT", "Method is required", null)
                    return
                }
                
                try {
                    signalRService.unsubscribe(method)
                    result.success(null)
                } catch (e: Exception) {
                    result.error("UNSUBSCRIBE_FAILED", "Failed to unsubscribe from method: ${e.message}", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    /**
     * Called when the Flutter side starts listening to the event channel
     */
    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        eventSink = events
        
        // Add connection listener
        signalRService.addConnectionListener { state ->
            val eventMap = HashMap<String, Any>()
            eventMap["event"] = "connectionState"
            eventMap["connected"] = state == SignalRService.ConnectionState.CONNECTED
            eventMap["connectionId"] = signalRService.connectionId()
            
            // Send event on main thread
            Handler(Looper.getMainLooper()).post {
                eventSink?.success(eventMap)
            }
        }
        
        // Add message listener
        signalRService.addMessageListener { connectionId, showId, seatIndex, status ->
            val eventMap = HashMap<String, Any>()
            eventMap["event"] = "hubMethod"
            eventMap["method"] = "broadcastMessage"
            eventMap["arguments"] = listOf(connectionId, showId, seatIndex, status)
            
            // Send event on main thread
            Handler(Looper.getMainLooper()).post {
                eventSink?.success(eventMap)
            }
        }
    }

    /**
     * Called when the Flutter side stops listening to the event channel
     */
    override fun onCancel(arguments: Any?) {
        eventSink = null
    }
}
