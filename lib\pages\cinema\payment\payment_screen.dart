import 'dart:async';
import 'dart:convert';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/cubit/auth.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
import 'package:flutter_app/pages/cinema/model/cinema_model.dart';
import 'package:flutter_app/pages/cinema/model/create_booking_model.dart';
import 'package:flutter_app/pages/cinema/model/list_seat_model.dart';
import 'package:flutter_app/pages/cinema/model/seat_booking_model.dart';
import 'package:flutter_app/pages/cinema/model/seat_model.dart';
import 'package:flutter_app/pages/cinema/model/ticket_type.dart';
import 'package:flutter_app/pages/cinema/payment/transaction_detail_screen.dart';
import 'package:flutter_app/pages/cinema/payment/webview_payment.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../utils/index.dart';

/// Luồng thanh toán - tương ứng với PaymentViewController trong iOS:
/// 1. Người dùng chọn ghế trong màn hình chọn ghế
/// 2. Người dùng chuyển đến màn hình thanh toán (màn hình này)
/// 3. Hệ thống tạo đơn đặt vé với các ghế đã chọn
/// 4. Người dùng được hiển thị các phương thức thanh toán trong WebView
/// 5. Người dùng hoàn tất thanh toán thông qua phương thức đã chọn
/// 6. Khi thanh toán thành công, người dùng được hiển thị thông báo thành công và chi tiết giao dịch
/// 7. Khi thanh toán thất bại, người dùng được hiển thị thông báo lỗi
/// 8. Khi thanh toán đang chờ xử lý, người dùng được hiển thị thông báo chờ
///
/// Các phương thức thanh toán được hỗ trợ:
/// - AirPay (ShopeePay)
/// - MoMo
/// - ZaloPay
/// - Thẻ ngân hàng nội địa (ATM)
/// - Thẻ tín dụng/ghi nợ quốc tế (Visa, Mastercard, v.v.)
///
/// Màn hình này cũng xử lý combo, voucher và các thông tin thanh toán khác
/// Tương ứng với PaymentViewController.swift trong repo iOS

class PaymentScreen extends StatefulWidget {
  final String? cinemaId;
  final String? cinemaName;
  final ShowModel? showTime;
  final FilmModel? film;
  final List<SeatModel> selectedSeats;
  final ListSeatModel? listSeat;
  final int totalPrice;
  final int remainingTime;
  final double timeStartBooking;

  const PaymentScreen({
    super.key,
    this.cinemaId,
    this.cinemaName,
    this.showTime,
    this.film,
    required this.selectedSeats,
    this.listSeat,
    required this.totalPrice,
    required this.remainingTime,
    required this.timeStartBooking,
  });

  @override
  _PaymentScreenState createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  bool _isLoading = false;
  String? _error;
  Timer? _timer;
  int _remainingTime = 0;
  String? _webData;
  String? _paymentMethod;

  // Payment tracking data
  String? _movieId;
  String? _movieName;
  DateTime? _date;
  DateTime? _time;
  String? _screen;
  int? _normalSeats = 0;
  int? _vipSeats = 0;
  int? _doubleSeats = 0;
  int? _totalSeats = 0;
  int? _totalAmount = 0;
  int? _discountAmount = 0;
  int? _paymentAmount = 0;
  int? _totalCombos = 0;
  int? _totalComboAmount = 0;
  int? _redeemVouchers = 0;
  int? _redeemPoints = 0;

  @override
  void initState() {
    super.initState();
    _remainingTime = widget.remainingTime;
    _startTimer();
    _createBooking();

    // Initialize tracking data
    _movieId = widget.film?.FilmId;
    _movieName = widget.film?.getName();
    _date = widget.listSeat?.ngayChieu != null
        ? DateTime.tryParse(widget.listSeat!.ngayChieu!)
        : null;
    _time = widget.listSeat?.gioChieu != null
        ? DateTime.tryParse(widget.listSeat!.gioChieu!)
        : null;
    _screen = widget.listSeat?.phongChieu;
    _totalSeats = widget.selectedSeats.length;
    _totalAmount = widget.totalPrice;
    _paymentAmount = widget.totalPrice;

    // Count seat types
    for (var seat in widget.selectedSeats) {
      if (seat.seatTypeEnum == SeatType.NORMAL) {
        _normalSeats = (_normalSeats ?? 0) + 1;
      } else if (seat.seatTypeEnum == SeatType.VIP) {
        _vipSeats = (_vipSeats ?? 0) + 1;
      } else if (seat.seatTypeEnum == SeatType.COUPLE) {
        _doubleSeats = (_doubleSeats ?? 0) + 1;
      }
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingTime > 0) {
          _remainingTime--;
        } else {
          _timer?.cancel();
          _showTimeoutDialog();
        }
      });
    });
  }

  void _showTimeoutDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Hết thời gian'),
        content: const Text('Thời gian thanh toán đã hết. Vui lòng thử lại.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Future<void> _createBooking() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final ticketTypes = widget.listSeat?.ticketTypes;
      if (widget.showTime?.showId == null ||
          widget.selectedSeats.isEmpty ||
          ticketTypes == null) {
        setState(() {
          _isLoading = false;
          _error = "Không đủ thông tin để tạo đơn hàng";
        });
        return;
      }

      final normalTicket = ticketTypes.firstWhere(
        (ticket) => ticket.isNormal,
        orElse: () => TicketType(price: 0)
      );

      final vipTicket = ticketTypes.firstWhere(
        (ticket) => ticket.isVip,
        orElse: () => TicketType(price: 0)
      );

      final coupleTicket = ticketTypes.firstWhere(
        (ticket) => ticket.isCouple,
        orElse: () => TicketType(price: 0)
      );

      // Tạo danh sách ghế đã chọn - đảm bảo khớp với iOS
      final seatBookingList = widget.selectedSeats.map((seat) {
        TicketType ticketType;
        String seatType = "STARDAR"; // Chú ý: iOS sử dụng "STARDAR" thay vì "STANDARD"

        // Xác định loại ghế và loại vé tương ứng
        if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
          ticketType = vipTicket;
          seatType = "VIP";
        } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
          ticketType = coupleTicket;
          seatType = "DOUBLE";
        } else {
          ticketType = normalTicket;
        }

        // In thông tin ghế để debug
        print("Seat: ${seat.seatNumber}, Type: $seatType, TicketTypeId: ${ticketType.ticketTypeId}, Price: ${ticketType.price}");

        return SeatBookingModel(
          seatIndex: seat.seatIndex ?? 0,
          seatName: seat.seatNumber ?? "", // Sử dụng seatNumber thay vì SeatName
          seatType: seatType,
          ticketTypeId: ticketType.ticketTypeId ?? "",
          price: ticketType.price ?? 0,
        );
      }).toList();

      // Calculate expiration time - match iOS format exactly
      String expiredTime;
      try {
        // iOS uses: Date.init(timeIntervalSince1970: timeStartBooking + Config.TimeExpired).toServerString()
        // Where Config.TimeExpired is 300 seconds (5 minutes)
        final expirationTimeMillis = (widget.timeStartBooking + 300) * 1000;
        final expirationTime = DateTime.fromMillisecondsSinceEpoch(expirationTimeMillis.toInt());

        // iOS toServerString() format is "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
        final DateFormat formatter = DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        expiredTime = formatter.format(expirationTime.toUtc()); // Convert to UTC to match iOS

        print("Expiration time: $expiredTime");
      } catch (e) {
        print("Error calculating expiration time: $e");
        // Use current time + 5 minutes as fallback
        final fallbackTime = DateTime.now().add(const Duration(minutes: 5)).toUtc();
        final DateFormat formatter = DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        expiredTime = formatter.format(fallbackTime);
      }

      final bookingModel = CreateBookingModel(
        showId: widget.showTime!.showId!,
        seats: seatBookingList,
        countDown: expiredTime,
      );

      // In ra JSON để kiểm tra
      final jsonData = bookingModel.toJson();
      print("Booking JSON: ${jsonEncode(jsonData)}");

      final api = RepositoryProvider.of<Api>(context).film;
      final response = await api.booking(body: jsonData);

      if (response != null && response.isSuccess) {
        // Kiểm tra xem dữ liệu có phải là HTML không
        final data = response.data;
        if (data is String && (data.trim().startsWith('<') || data.contains('<html'))) {
          // Lưu HTML gốc
          setState(() {
            _webData = data;
            _isLoading = false;
          });

          // Kiểm tra các biến JavaScript trong HTML
          // if (data.contains('var listCombo = JSON.parse(\'null\')') ||
          //     data.contains('var dataBooking = JSON.parse(\'null\')')) {
          //   print('Warning: listCombo or dataBooking is null in HTML response');
          // }

          // Navigate to WebView payment page
          _navigateToWebPayment();
        } else {
          setState(() {
            _isLoading = false;
            _error = "Định dạng phản hồi không hợp lệ";
          });
          print('Invalid response format: $data');
        }
      } else {
        setState(() {
          _isLoading = false;
          _error = response?.message ?? "Không thể tạo đơn hàng";
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = "Lỗi: $e";
      });
    }
  }

  void _navigateToWebPayment() {
    if (_webData == null) return;

    // Sử dụng Future.delayed để đảm bảo UI được cập nhật trước khi chuyển trang
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => WebViewPaymentScreen(
            htmlData: _webData!,
            baseUrl: ApiService.baseUrl,
            film: widget.film,
            totalPrice: widget.totalPrice,
            listSeat: widget.listSeat,
            cinemaId: widget.cinemaId,
            cinemaName: widget.cinemaName,
            onPaymentSuccess: _onPaymentSuccess,
            onPaymentFailed: _onPaymentFailed,
            onPaymentWaiting: _onPaymentWaiting,
            onPaymentMethodSelected: (method) {
              setState(() {
                _paymentMethod = method;

                // Cập nhật dữ liệu theo dõi thanh toán
                _trackPayment(method, 'confirm');
              });
            },
          ),
        ),
      );
    });
  }

  /// Theo dõi sự kiện thanh toán
  void _trackPayment(String method, String type, [String? errorCode, String? errorMsg]) {
    // Tạo dữ liệu sự kiện
    final Map<String, dynamic> eventData = {
      'event': 'payment_$type',
      'payment_method': method,
      'amount': widget.totalPrice,
      'film_id': _movieId,
      'film_name': _movieName,
      'cinema_id': widget.cinemaId,
      'cinema_name': widget.cinemaName,
      'normal_seats': _normalSeats,
      'vip_seats': _vipSeats,
      'double_seats': _doubleSeats,
      'total_seats': _totalSeats,
    };

    // Thêm thông tin lỗi nếu có
    if (errorCode != null) {
      eventData['error_code'] = errorCode;
    }

    if (errorMsg != null) {
      eventData['error_message'] = errorMsg;
    }

    // Ghi nhật ký sự kiện
    print('Payment tracking: $eventData');

    // Trong triển khai thực tế, điều này sẽ gửi dữ liệu đến dịch vụ phân tích
    // Ví dụ: FirebaseAnalytics.instance.logEvent(name: 'payment_$type', parameters: eventData);
  }

  void _onPaymentSuccess(String? transactionId) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Thanh toán thành công'),
        content: const Text('Cảm ơn bạn đã đặt vé. Vé của bạn đã được gửi đến email đăng ký.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (transactionId != null) {
                _navigateToTransactionDetail(transactionId);
              } else {
                Navigator.of(context).popUntil((route) => route.isFirst);
              }
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _navigateToTransactionDetail(String transactionId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TransactionDetailScreen(
          transactionId: transactionId,
          backToHome: true,
        ),
      ),
    );
  }

  void _onPaymentFailed(String? errorMessage) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Thanh toán thất bại'),
        content: Text(errorMessage ?? 'Đã xảy ra lỗi trong quá trình thanh toán. Vui lòng thử lại.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _onPaymentWaiting() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Đang chờ thanh toán'),
        content: const Text('Giao dịch của bạn đang được xử lý. Vui lòng kiểm tra lịch sử giao dịch.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();

              /// Navigate to transaction history
              /// In a real implementation, this would navigate to a transaction history screen
              /// For now, we just return to the home screen
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Thanh toán'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          Center(
            child: Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: _remainingTime < 60 ? Colors.red.shade700 : Colors.green,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  _formatTime(_remainingTime),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 15,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
            ),
            const SizedBox(height: 20),
            Text(
              'Đang tạo đơn hàng...',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade800,
              ),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.red.shade700,
                size: 64,
              ),
              const SizedBox(height: 16),
              Text(
                'Lỗi',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                child: const Text('Quay lại'),
              ),
            ],
          ),
        ),
      );
    }

    if (_webData == null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.amber.shade700,
                size: 64,
              ),
              const SizedBox(height: 16),
              Text(
                'Không thể tải thông tin thanh toán',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _createBooking,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                child: const Text('Thử lại'),
              ),
            ],
          ),
        ),
      );
    }

    // Nếu đã có dữ liệu web nhưng chưa chuyển đến trang thanh toán
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
          ),
          const SizedBox(height: 20),
          Text(
            'Đang chuyển đến trang thanh toán...',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Vui lòng đợi trong giây lát',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }
}
