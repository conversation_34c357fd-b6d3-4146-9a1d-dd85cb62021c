{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9815759f0cc06eb2249ece211057a91687", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aa7f290d1cdca29056d461041ff183b3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a56e350076219f544c73f769bcc9ef6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9868101e474494c6b00a9686f8e76596be", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a56e350076219f544c73f769bcc9ef6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98da47870742c90b78e47337908d9b078d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a8d52ce7b39735ca13af781186216c7a", "guid": "bfdfe7dc352907fc980b868725387e987ff6992f1c8dfdd406b78e384e150977", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cc52dccfec7682d9f9f6960abaca2dbf", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9836c27eeaf4abaaef80b74c5ee1803b75", "guid": "bfdfe7dc352907fc980b868725387e98c53e653f879fa07727053a6977695241"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaf00ef33749b1ebf2050ffb869682ca", "guid": "bfdfe7dc352907fc980b868725387e98821228483d4c1e9ae1b1f94af537f0f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875d0745fdacb31e170836ed1abea6c30", "guid": "bfdfe7dc352907fc980b868725387e98ea95bcb7591c4d390e30f4d77cce95a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d05efbc79452026b204ed6f3e64c0e6c", "guid": "bfdfe7dc352907fc980b868725387e98159874cc5d86da5c7d7ea1e47db6ff55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98521f19ed0a59c89cb5c28aeed749a5b7", "guid": "bfdfe7dc352907fc980b868725387e9877f2df532f044e02373b7112c0fc2fa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a738d43896aea8639625815e7db18b78", "guid": "bfdfe7dc352907fc980b868725387e988152488fd3ff9245055dda30dcf6e2b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf534e2561ca45c397c0084bcf3ff1e8", "guid": "bfdfe7dc352907fc980b868725387e98d9fe2a1f3d52b533b38f828091618be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b20a45b5b1fdd467f501b0422d29a5a", "guid": "bfdfe7dc352907fc980b868725387e98582abd7fc11cc9aab24a3bf6993f273e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a61c03538e84de825f580c0fede83287", "guid": "bfdfe7dc352907fc980b868725387e9869647aad1d49bb49df736de66ac6d0ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a15378f0890ac8fc6e2d4d9a84e4303", "guid": "bfdfe7dc352907fc980b868725387e9857521f3667e3469112804869b2ac67ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee2e594fb85522a186bc2acac93948d0", "guid": "bfdfe7dc352907fc980b868725387e98c6907da1164227d4d01f78c48abb67fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6fa9d63076a9dd03394d94cf597ba01", "guid": "bfdfe7dc352907fc980b868725387e981fcf6fbd357c2dad2ab158a3516f7dc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c6a8d5010e7bb3f3ab2e49fb3972d05", "guid": "bfdfe7dc352907fc980b868725387e98524bde508f016a52f966ff5713e7c170"}], "guid": "bfdfe7dc352907fc980b868725387e98a074d58a244717a718df62305af59ac8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98300432b30f967092bfa3740b48b404c8"}], "guid": "bfdfe7dc352907fc980b868725387e985757dbe3b3a90fe8f17b4c052ab3fa8e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9840769b8463c412f9bb65d242548cacda", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e986bbc690499778400cc2d95f0fffd2f46", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}