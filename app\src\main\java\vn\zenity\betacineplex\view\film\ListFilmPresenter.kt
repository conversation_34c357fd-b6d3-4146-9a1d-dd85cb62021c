package vn.zenity.betacineplex.view.film

import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.model.Film
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class ListFilmPresenter : ListFilmContractor.Presenter {
    override fun getListFilm(type: Int) {
        this.view?.get()?.showLoading()
        if (type == 3) {
            APIClient.shared.filmAPI.showFilm(true).applyOn().subscribe({ response ->
//                response.Data?.sortBy { (it.Film?.Order ?: "100").toInt() }
                response.Data?.let {
                    val listSneakShow = it.map { it.Film!! }
                    this.view?.get()?.showListFilm(listSneakShow)
                }
                this.view?.get()?.hideLoading()
            }, { error ->
                this.view?.get()?.showError(error.message ?: R.string.error_server.getString())
                this.view?.get()?.hideLoading()
            })
            return
        }
        APIClient.shared.filmAPI.getListFilm(type == 1).applyOn().subscribe({ response ->
//            response.Data?.sortBy { (it.Order ?: "100").toInt() }
            response.Data?.let {
                this.view?.get()?.showListFilm(it)
            }
            this.view?.get()?.hideLoading()
        }, { error ->
            this.view?.get()?.showError(error.message ?: R.string.error_server.getString())
            this.view?.get()?.hideLoading()
        })
    }

    private var view: WeakReference<ListFilmContractor.View?>? = null
    override fun attachView(view: ListFilmContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.view?.clear()
        this.view = null
    }
}
