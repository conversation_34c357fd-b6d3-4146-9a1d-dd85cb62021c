{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a92db14bc6d9e738ac55764c6a43066c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988a5133944d10336009a1abeede681e8a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980850d562ef13edd69d7c17ba90a4139a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986bdcf562469ec0246dd00192828a4c1b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980850d562ef13edd69d7c17ba90a4139a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98697b92938401a2624a5fff8d5e2c3612", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a98b4c6cca8d16de69c9c55bfcd91633", "guid": "bfdfe7dc352907fc980b868725387e98019acc21d61388ab9e4fcc9cbded7816", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98068b01bd16627d9f5932abfeca679a59", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b0054e04c76ded41cf3da78984b1554b", "guid": "bfdfe7dc352907fc980b868725387e98bf8d4502f111e745977a125e82663e09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d521320294c578c5de915fe5c51b4a0f", "guid": "bfdfe7dc352907fc980b868725387e9861fbf333208ca8e6fa75c22f61a240c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982577591ae89c5bc3f1304344139402fd", "guid": "bfdfe7dc352907fc980b868725387e98f4c3e0e326f6a5724396ce8a181b753e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98844a3246c99ccb4f5d3952a7c41da7c9", "guid": "bfdfe7dc352907fc980b868725387e9844776ab212ad57038582dd1200407296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fdd40ea6c80e5550eaa618dabeba5f4", "guid": "bfdfe7dc352907fc980b868725387e982aaa77ea6593af787508131a8e12a625"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb02a7a84492f4480c378bf03c246c1b", "guid": "bfdfe7dc352907fc980b868725387e9845db51ec75a0b10802090eb4fdd5dbdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98309d236eff6fffc37300db098f50daa9", "guid": "bfdfe7dc352907fc980b868725387e989b3a6b001ace6ec69d80bc96519d00d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980728d52606ada579e9018a35bb2ffb43", "guid": "bfdfe7dc352907fc980b868725387e989f89772b7a3d71689cfe40a93ce59a06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1e8be9c89fa3ab8df541306672570e2", "guid": "bfdfe7dc352907fc980b868725387e98c1b33613ba672a3c13a9a1cca7dd683b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853e479bf8540254ffc74571c62c5511a", "guid": "bfdfe7dc352907fc980b868725387e98fcf534553ee799e28b41196a2f9dc2d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845a08e5b899458c563464b3286b58e03", "guid": "bfdfe7dc352907fc980b868725387e98e282483a063491a3d2b40a94fbc1c71e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c4a4ed250b73216016ced6b3da24c3f", "guid": "bfdfe7dc352907fc980b868725387e988dd56273a0332d22cb396c0d9e7c2afc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98221415ee59de4c3e2c5cf5e864c8c845", "guid": "bfdfe7dc352907fc980b868725387e9861421c130f472c965edbf80fc4dd69db"}], "guid": "bfdfe7dc352907fc980b868725387e98fcc0048f67baed3e01593dba54e28a8a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9827a36d5c410fb1866bcf3c7a4264c485"}], "guid": "bfdfe7dc352907fc980b868725387e981445f72b969e38b1da048b81c85b972e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d4c12b3b43e174a6252ee20e6245fb89", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98635d2f1510527e581c2e73b65ece70bb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}