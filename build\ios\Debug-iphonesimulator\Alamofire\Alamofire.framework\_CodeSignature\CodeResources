<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/Alamofire-Swift.h</key>
		<data>
		eaqW6Tl0QEgo8nrJbC33AdHCXo8=
		</data>
		<key>Headers/Alamofire-umbrella.h</key>
		<data>
		mHfdAFXADntqiLfvjq4XWpdZQHk=
		</data>
		<key>Info.plist</key>
		<data>
		Zc9ZVcPOOSa84XE8zEJ7ob1sZ70=
		</data>
		<key>Modules/Alamofire.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		M6sFH9WTkhTAANcAypd29SMngss=
		</data>
		<key>Modules/Alamofire.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		9EAxS+1op8tAuuo4ZtqAh7igTmo=
		</data>
		<key>Modules/Alamofire.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/Alamofire.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		heD/wQlv9T6X8Gv1On8tJtxbPt0=
		</data>
		<key>Modules/Alamofire.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		RExnEdUWlO+fnyv6G/SvX6OBJE4=
		</data>
		<key>Modules/Alamofire.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/Alamofire.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		4kpFErkZkKcaLfq/o57e69cs7MM=
		</data>
		<key>Modules/Alamofire.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		2zY/6NfNyQYOawLe3Cn2hzoKLK0=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		QlNOSMmbw71Hf5fYKt9AMVzXtnc=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/Alamofire-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			eaqW6Tl0QEgo8nrJbC33AdHCXo8=
			</data>
			<key>hash2</key>
			<data>
			TGpwUOinXb7Xecs5lORwXt0fib8ZSzAQy0Cjkj7+UCo=
			</data>
		</dict>
		<key>Headers/Alamofire-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			mHfdAFXADntqiLfvjq4XWpdZQHk=
			</data>
			<key>hash2</key>
			<data>
			xjVegAu7WgIX6x6hDM6tQd3ZedQH8S5veIqpM/WycHg=
			</data>
		</dict>
		<key>Modules/Alamofire.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			M6sFH9WTkhTAANcAypd29SMngss=
			</data>
			<key>hash2</key>
			<data>
			jS3oIWgdRaJX2xx+v1538tO8lIUp3fjWkAoqAtZgvmI=
			</data>
		</dict>
		<key>Modules/Alamofire.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			9EAxS+1op8tAuuo4ZtqAh7igTmo=
			</data>
			<key>hash2</key>
			<data>
			TZ/wMEq3nBDeDhTM/pKepCI8bPEjIaHhbyhZ1+VgkZc=
			</data>
		</dict>
		<key>Modules/Alamofire.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/Alamofire.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			heD/wQlv9T6X8Gv1On8tJtxbPt0=
			</data>
			<key>hash2</key>
			<data>
			hLMDTgiTBlUOK0Semfuqvqic36FKomaHU3gaiaEVOcc=
			</data>
		</dict>
		<key>Modules/Alamofire.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			RExnEdUWlO+fnyv6G/SvX6OBJE4=
			</data>
			<key>hash2</key>
			<data>
			enqrOkeKZ1jWwzdjreDk1EsYoZ1dH3s/Xdp9S1ZA3Ns=
			</data>
		</dict>
		<key>Modules/Alamofire.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/Alamofire.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			4kpFErkZkKcaLfq/o57e69cs7MM=
			</data>
			<key>hash2</key>
			<data>
			nW5HlYzabZjnQyCHwkxxXoF8xRaWK5is0NzKi+1ii+k=
			</data>
		</dict>
		<key>Modules/Alamofire.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			2zY/6NfNyQYOawLe3Cn2hzoKLK0=
			</data>
			<key>hash2</key>
			<data>
			fAJtj97qyWSEYp65yWdL7LeF5Rc97gEPvlKX3Ap1iso=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			QlNOSMmbw71Hf5fYKt9AMVzXtnc=
			</data>
			<key>hash2</key>
			<data>
			EWRParlYn8/p2HpG6FCC19DWQc8KcsdENRSP52g9kxg=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
