package vn.zenity.betacineplex.view.auth

import android.os.Bundle
import android.view.View
import kotlinx.android.synthetic.main.fragment_forgotpassword.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.helper.extension.isEmail

/**
 * Created by Zenity.
 */

class ForgotPasswordFragment : BaseFragment(), ForgotPasswordContractor.View {
    override fun showError(message: String) {
        activity?.runOnUiThread {
            showNotice(message)
        }
    }

    override fun showForgotSuccess(message: String) {
        activity?.runOnUiThread {
            showNotice(message) {
                back()
            }
        }
    }

    private val presenter = ForgotPasswordPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_forgotpassword
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        btnConfirm?.setOnClickListener {
            val email = edtEmail.text.trim()
            if (email.isEmail()) {
                presenter.forgotPassword(email)
            } else {
                showNotice(getString(R.string.error_email_is_invalidate))
            }
        }
    }
}
