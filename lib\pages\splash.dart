import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '/constants/index.dart';
import '/cubit/index.dart';
import '/firebase_options.dart';
import '/service/notification_manager.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    // initFirebase();
    context.read<AuthC>().check(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: BlocConsumer<AuthC, AuthS>(
          listenWhen: (oldState, newState) => newState.status != AppStatus.init,
          listener: (context, state) {
            Future.delayed(const Duration(milliseconds: 30), () {
              CSpace.setScreenSize(context);
                GoRouter.of(context).goNamed(CRoute.home);
            });
          },
          builder: (context, state) => Center(
                child: CIcon.logo,
              )),
    );
  }

  /// Initialize Firebase and Notification - đã được move vào main.dart
  /// Method này giữ lại để tương thích, nhưng logic chính đã chuyển sang NotificationManager
  Future<RemoteMessage?> initFirebase() async {
    try {
      // Firebase đã được initialize trong main.dart
      debugPrint('🔔 Firebase already initialized in main.dart');

      // Initialize notification manager - tương ứng với iOS notification setup
      await NotificationManager.instance.initialize();

      // Get initial message if app was opened from notification
      final RemoteMessage? message = await FirebaseMessaging.instance.getInitialMessage();
      if (message != null) {
        debugPrint('🔔 App opened from notification: ${message.messageId}');
        _handleMessage(message);
      }

      return message;
    } catch (e) {
      debugPrint('❌ Error in initFirebase: $e');
    }
    return null;
  }

  void _handleMessage(RemoteMessage message) {
    if (message.data['id'] != null && message.data['id'] != '') {
      // Navigator.pushNamedAndRemoveUntil(
      //   Utils.navigatorKey.currentState!.context,
      //   RoutesName.PROPERTY_SEE_DETAILS,
      //   arguments: message.data['id'],
      //       (route) => false,
      // );
    }
  }
}
