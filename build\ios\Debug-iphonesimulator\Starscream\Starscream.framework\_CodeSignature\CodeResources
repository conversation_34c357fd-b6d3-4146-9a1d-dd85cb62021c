<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/Starscream-Swift.h</key>
		<data>
		eiSxiN5XzMhm5P1p46lwrYkph/A=
		</data>
		<key>Headers/Starscream-umbrella.h</key>
		<data>
		IPhBvO0wnp5Nc1N0CzN3fyjKBvA=
		</data>
		<key>Info.plist</key>
		<data>
		kw+XDEp3qcBkI4lwfyMQXQ4Cukc=
		</data>
		<key>Modules/Starscream.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		DWeHY00wuH3TcW2E7vD+eiiUl3w=
		</data>
		<key>Modules/Starscream.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		OZk4l5WIgVJ67pj9rdma40wrmzs=
		</data>
		<key>Modules/Starscream.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/Starscream.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		jgSc17IZAck/GFp+42yj07zESJs=
		</data>
		<key>Modules/Starscream.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		CsLd/E/Ux4v0n4z51yF3ypJ3238=
		</data>
		<key>Modules/Starscream.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/Starscream.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		3bDDEaO4ANFtpNmDiLlvQ/NBnd0=
		</data>
		<key>Modules/Starscream.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		aQqQhIb4nqyUWRV3k5in1r+2P/A=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		4rkeJ0OOw7iDx1Ji4XdvIDgJHsU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/Starscream-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			eiSxiN5XzMhm5P1p46lwrYkph/A=
			</data>
			<key>hash2</key>
			<data>
			E4GfYmtklK5tzA/Jc+8YB56hQOW3thlGfapgkzsi26E=
			</data>
		</dict>
		<key>Headers/Starscream-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			IPhBvO0wnp5Nc1N0CzN3fyjKBvA=
			</data>
			<key>hash2</key>
			<data>
			LP7mg5UEuvJYcCeuR4hduxtHTsyNDfC/SuRf2jq/vaA=
			</data>
		</dict>
		<key>Modules/Starscream.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			DWeHY00wuH3TcW2E7vD+eiiUl3w=
			</data>
			<key>hash2</key>
			<data>
			vhjqJs8L0x9wtz47ahOActcqXQCSV74V7zYVJtNP69U=
			</data>
		</dict>
		<key>Modules/Starscream.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			OZk4l5WIgVJ67pj9rdma40wrmzs=
			</data>
			<key>hash2</key>
			<data>
			uVL5alAM34dgGfmS1rDUOXHbd72eyzjF/ZNWZMicUss=
			</data>
		</dict>
		<key>Modules/Starscream.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/Starscream.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			jgSc17IZAck/GFp+42yj07zESJs=
			</data>
			<key>hash2</key>
			<data>
			qhaol0bNGM0Mj4/1kujxOCT53yEKmbB3YpkpEQlW0Ls=
			</data>
		</dict>
		<key>Modules/Starscream.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			CsLd/E/Ux4v0n4z51yF3ypJ3238=
			</data>
			<key>hash2</key>
			<data>
			StLWg6bszgFx95p7XlwQTWXuRxJwQvvsvuMSeIP8dt0=
			</data>
		</dict>
		<key>Modules/Starscream.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/Starscream.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			3bDDEaO4ANFtpNmDiLlvQ/NBnd0=
			</data>
			<key>hash2</key>
			<data>
			eXMmzY4un+hS2sjcgSjeFbcIdRIrs3Xek4Ltx7CCZEo=
			</data>
		</dict>
		<key>Modules/Starscream.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			aQqQhIb4nqyUWRV3k5in1r+2P/A=
			</data>
			<key>hash2</key>
			<data>
			BwWykQAeIFmlUiFLuoTuN5sMwJGo9ibAxTmw6Q+p5yQ=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			4rkeJ0OOw7iDx1Ji4XdvIDgJHsU=
			</data>
			<key>hash2</key>
			<data>
			cl85M5bVPywHVE0ShxcFBoxHBo1v4EupmMVc+t/Yhhk=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
