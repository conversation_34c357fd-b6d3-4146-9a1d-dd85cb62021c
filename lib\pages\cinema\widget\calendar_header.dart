import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../constants/index.dart';

class CalendarHeaderView extends StatelessWidget {
  final List<DateTime> dates;
  final DateTime? selectedDate;
  final Function(DateTime) onDateSelected;

  const CalendarHeaderView({super.key,
    required this.dates,
    required this.onDateSelected,
    this.selectedDate,
  });

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: dates.length,
        itemBuilder: (context, index) {
          final date = dates[index];
          final isSelected = selectedDate != null &&
              DateUtils.isSameDay(selectedDate, date);
          final isToday = DateUtils.isSameDay(today, date);

          return GestureDetector(
            onTap: () => onDateSelected(date),
            child: Container(
              width: 60,
              margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 10),
              decoration: BoxDecoration(
                color: isSelected ? Colors.red.shade50 : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    DateFormat('dd').format(date),
                    style: TextStyle(
                      fontSize: CFontSize.xl2,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.red : Colors.black54,
                    ),
                  ),
                  Text(
                    isToday
                        ? "Hôm nay"
                        : DateFormat('MM-E').format(date),
                    style: TextStyle(
                      // fontSize: CFontSize.base,
                      color: isSelected ? Colors.red : Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
