{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98193e2f8ae16200aebd481430d9f99da8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bacc8ef16e09e93000a7c4175e35ce4a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9800827be1df83d79f76925f97e3d64d70", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bb55d705169895d903e8893c5cf40d76", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9800827be1df83d79f76925f97e3d64d70", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984aa0dfb40d789387943f370a5f09f08c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9812b42d909a0472e8757a37215a345f99", "guid": "bfdfe7dc352907fc980b868725387e980317afa76c2698742f1b1fbca30032fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e5b915a7cadaae2e2a5df317816ed99", "guid": "bfdfe7dc352907fc980b868725387e98dd426a2ec02a37b40d162f1c56999f4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec50e7ea690d511e40d5e1692f855433", "guid": "bfdfe7dc352907fc980b868725387e981bb2af73aeca210c3ef6a14a39004de8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989808cc455a450311d1526a70b596c8ee", "guid": "bfdfe7dc352907fc980b868725387e9867be4dd7c272dffc3f4607720b705bda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b73ce91ad288a9bbb088c8ca895b80c0", "guid": "bfdfe7dc352907fc980b868725387e98072405dbd3b7ea0b53af0e7fe904fcc4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c14aa881dd47a846552e4cc0dc71cfa8", "guid": "bfdfe7dc352907fc980b868725387e98a0280d48342e4bd298a79752e6000571", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6c279334a772d8771095cbdfadc1db1", "guid": "bfdfe7dc352907fc980b868725387e98cea7b010af5467a451e22d384f37c152", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a8ac51339e8f32e39514e68d5c4318c", "guid": "bfdfe7dc352907fc980b868725387e9882c63c32ed6aae1986dd874300430da8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981670a0e44f8f653c00d4453fd2a0e109", "guid": "bfdfe7dc352907fc980b868725387e981f33c831ab7e048bc188e3372ee337df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5cd58b03c8ee39e0ea685deca9e0dcc", "guid": "bfdfe7dc352907fc980b868725387e9827cad64563ba9305763b54411346504c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892cf08e2a3bb9c4de3d317debe679d56", "guid": "bfdfe7dc352907fc980b868725387e98e988748fddda16863f5d499f5b8eca9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bc8562546043dfdf954de71e641c8dc", "guid": "bfdfe7dc352907fc980b868725387e9863a43327caeca764a09c5be570b0e99f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e768a580c9ebd5e75faf55fb0d465fff", "guid": "bfdfe7dc352907fc980b868725387e987aff9146c9e945f0b431414246e5b71a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c7ba0ea5c94fe2d69412f28422fe00e", "guid": "bfdfe7dc352907fc980b868725387e987b861f5db06636f100cd72017b044e4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f81493b00af270a1fc36c99404972b4d", "guid": "bfdfe7dc352907fc980b868725387e987e604a6390ec0b9c3f384d9662f0025b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae9828c82c6f00768096fd3fb1824c47", "guid": "bfdfe7dc352907fc980b868725387e98c0dbb4d57615858778304a9e0ddd2caa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d86b1917a1a956d3bb98b877730a1718", "guid": "bfdfe7dc352907fc980b868725387e98762b2eef9c418a0fd846c304065a753e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e37357f0bcb0ddc9e6f936a7ab18829", "guid": "bfdfe7dc352907fc980b868725387e98ccd3170cc0afe4514a74caeb374d3b1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986306787a2731d3e17c9f25913a54b82f", "guid": "bfdfe7dc352907fc980b868725387e98ae9fa0d7fb43882234b876dacd8a7c82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bca7843759862d9c4ebd002af0a8879d", "guid": "bfdfe7dc352907fc980b868725387e98da392e6307351501bfcbe091dd585f37", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822f8ba892695d5b1fca5fc23e27c20ca", "guid": "bfdfe7dc352907fc980b868725387e9805c03c22da04594ff68822069d19d68b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864b6ee85e397b612cea0c361d6fa4e87", "guid": "bfdfe7dc352907fc980b868725387e98c9a086693c483a444e4212a6d5ae4d18", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e14019a0900fcb0d8ebdb0f1df9100d2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c158a77cd8adcd7bad0a099500be85e", "guid": "bfdfe7dc352907fc980b868725387e98e66fb3b1ad173627766783d20bfab8da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e18d1e1991a15f68a7344a463a9b7c6a", "guid": "bfdfe7dc352907fc980b868725387e985dcd5fd116995eb6b4b9aad38d62c5e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1b83e9bef38d18c9669fdb7155af249", "guid": "bfdfe7dc352907fc980b868725387e987f633a70a8ec9b17cb9bdc24e63eaa16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849756d6f5496258fe7bd9211150fb1d1", "guid": "bfdfe7dc352907fc980b868725387e98633946ab8071ff5be5db1173046b43f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce68a11395d7bb67c626b08fab932a63", "guid": "bfdfe7dc352907fc980b868725387e98dd4c84e57ffadd884dd6c24e85464dee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98562a1d83db972011b5c91fdbc634a1aa", "guid": "bfdfe7dc352907fc980b868725387e983aa4b9cf1617a952ac913492a3a2761a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e5c747747e3af8f37b47c7cebec7284", "guid": "bfdfe7dc352907fc980b868725387e9860fb4f1a50aec60e8afa80f5be24a341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8cdb87ffb8cee4f0ec013620fca6a3", "guid": "bfdfe7dc352907fc980b868725387e9809d4e0a24f030a50396a26df882194e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc9c7000f0ac977ec36c1fbf37318670", "guid": "bfdfe7dc352907fc980b868725387e9838d19fd5d49ae52460cb0316329313e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850060f17ddef13711625864a4a2f197e", "guid": "bfdfe7dc352907fc980b868725387e98c7dda174d05070436ea0879f8ae84af2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c844606c4987a5cab97c5621ad88d394", "guid": "bfdfe7dc352907fc980b868725387e9811d345c3207cfef2805ce073e65c6edd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bacd8913200131dc37132f6a9b2f6a08", "guid": "bfdfe7dc352907fc980b868725387e9843e2ec4502d072c10e55fd336543e0e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e085e17cc9ff3aebd454b3a946ba47df", "guid": "bfdfe7dc352907fc980b868725387e98831bbe0148f5b05f5727cdaf7c22ec0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a44c9cca4d0d02181c514311ef02ae5", "guid": "bfdfe7dc352907fc980b868725387e98788048587cadb5b9879f71c4e25f89de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983661f0e71118b3374e026793593bf639", "guid": "bfdfe7dc352907fc980b868725387e98bb0a7d18ea11522b8f752eec09fa403b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980265c27c067daae9712f5137b503b212", "guid": "bfdfe7dc352907fc980b868725387e98e44714fc151aabf4088dbe91fda02250"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cc7f8728fda962b28a83ac643d8b60d", "guid": "bfdfe7dc352907fc980b868725387e98fa1b4a6bf0732f5f54a138e893b808bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98804ddd2eb21a1aa8700dd6e87334326a", "guid": "bfdfe7dc352907fc980b868725387e98c3031cafb3c9585d05f44695c3919889"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b28370f208e023f838399da5b24cf121", "guid": "bfdfe7dc352907fc980b868725387e9816695a94d58e8530da51595e3e66b534"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e91711d185f31666ba0f360ec6c4178c", "guid": "bfdfe7dc352907fc980b868725387e983a1ff7b897b1fd0375854c549ca56c49"}], "guid": "bfdfe7dc352907fc980b868725387e98c4ed5e51e5b0783663e8862b96c916d1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9878826ae69eecd58c5cc6440e30d272f8"}], "guid": "bfdfe7dc352907fc980b868725387e98ae299a436e53d6f1d66120e37b088a9d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9827a40cdbe03b968ff9a7b0836b4665f7", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e985ed3cf6a3f8973f7cbc88aa7c8e9790f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}