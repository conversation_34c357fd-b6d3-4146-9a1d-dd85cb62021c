package vn.zenity.betacineplex.view.cenima

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MarkerOptions
import kotlinx.android.synthetic.main.fragment_cenimadetail.*
import kotlinx.android.synthetic.main.item_event_home.view.*
import kotlinx.android.synthetic.main.item_header_cinema_detail.view.*
import load
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseActivity
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.helper.extension.setTextWithSpecialText
import vn.zenity.betacineplex.helper.extension.toImageUrl
import vn.zenity.betacineplex.helper.view.PopupFragment
import vn.zenity.betacineplex.model.CinemaModel
import vn.zenity.betacineplex.model.NewsModel
import vn.zenity.betacineplex.view.HomeActivity
import vn.zenity.betacineplex.view.event.EventDetailFragment
import vn.zenity.betacineplex.view.event.EventFragment
import vn.zenity.betacineplex.view.film.BookByCinemaFragment
import java.util.*

/**
 * Created by Zenity.
 */

class CenimaDetailFragment : BaseFragment(), CenimaDetailContractor.View {

    override fun showListEvent(listEvents: List<NewsModel>) {
        this.listEvents = listEvents
        activity?.runOnUiThread {
            adapter.notifyDataSetChanged()
        }
    }

    override fun showCinemaDetail(cinema: CinemaModel) {
        this.cinema = cinema
        activity?.runOnUiThread {
            showData()
        }
        presenter.getListEventOfCinema(cinema.CinemaId)
    }

    companion object {
        fun getInstance(cinema: CinemaModel?): CenimaDetailFragment {
            val frag = CenimaDetailFragment()
            frag.cinema = cinema
            return frag
        }
        fun getInstance(cinema: String?): CenimaDetailFragment {
            val frag = CenimaDetailFragment()
            frag.cinemaId = cinema
            return frag
        }
    }

    private val presenter = CenimaDetailPresenter()
    private var listEvents = listOf<NewsModel>()
    private lateinit var adapter: Adapter
    private var cinema: CinemaModel? = null
    private var cinemaId: String? = null

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_cenimadetail
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (cinema == null) {
            cinemaId?.let {
                presenter.getCinemaDetail(it)
            }
            return
        }
        showData(true)
    }

    private fun showData(needReloadData: Boolean = false) {
        if (recyclerView.adapter == null) {
            adapter = Adapter()
            recyclerView.layoutManager = LinearLayoutManager(this.context)
            recyclerView.adapter = adapter
            btnBack.setOnClickListener {
                back()
            }
        } else {
            recyclerView?.adapter?.notifyItemChanged(0)
        }
        (activity as? BaseActivity)?.getMyLocation(true, true)
        if (needReloadData)
            presenter.getCinemaDetail(cinema?.CinemaId ?: "id")
    }

    private inner class Adapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

        private val TYPE_EVENT = 1
        private val TYPE_HEADER = 0

        override fun getItemCount(): Int {
            return 1 + if (listEvents.size > 3) 3 else listEvents.size
        }

        override fun getItemViewType(position: Int): Int {
            if (position == 0) return TYPE_HEADER
            return TYPE_EVENT
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (getItemViewType(position) == TYPE_HEADER) {
                holder.itemView.ivBanner.load(cinema?.Duong_dan_anh_dai_dien?.toImageUrl())
                holder.itemView.tvCinemaName.text = cinema?.Name
                val addressText = R.string.address_haicham.getString()
                holder.itemView.tvAddress.setTextWithSpecialText("$addressText ${cinema?.Address}", addressText) {
                    it.typeface = <EMAIL>?.let { it1 -> ResourcesCompat.getFont(it1, R.font.sanspro_bold) }
                    it.color = holder.itemView.tvAddress.currentTextColor
                    it.isUnderlineText = false
                }

                holder.itemView.priceTicket.setOnClickListener {
                    PopupFragment.getInstance(CinemaPriceFragment.getInstance(cinema?.NewsId)).showPopup(activity?.supportFragmentManager
                            ?: (childFragmentManager))
                }
                holder.itemView.sessionTime.setOnClickListener {
                    Tracking.share().selectTheater(context, cinema?.CinemaId, cinema?.Name)
                    openFragment(BookByCinemaFragment.getInstance(cinema))
                }

                holder.itemView.call.setOnClickListener {
                    (activity as? HomeActivity)?.call(cinema?.PhoneNumber
                            ?: return@setOnClickListener)
                }
                holder.itemView.viewAllPromotion.setOnClickListener {
                    openFragment(EventFragment())
                }
                (holder as? HeaderHolder)?.bindMapView()
            } else {
                val event = listEvents[position - 1]
                holder.itemView.ivEvent.load(event.Duong_dan_anh_dai_dien?.toImageUrl())
                holder.itemView.tvEventTitle.text = event.Tieu_de
                holder.itemView.setOnClickListener {
                    openFragment(EventDetailFragment.getInstance(event))
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            if (viewType == TYPE_HEADER) {
                val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_header_cinema_detail, parent, false)
                val viewHolder = HeaderHolder(itemView)
                return viewHolder
            } else {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_event_home, parent, false)
                return Holder(item)
            }
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)
    private inner class HeaderHolder(itemView: View) : RecyclerView.ViewHolder(itemView), OnMapReadyCallback {

        private var lat = 21.0101209
        private var lng = 105.7738637

        override fun onMapReady(p0: GoogleMap?) {
            val markerOptions = MarkerOptions()
            markerOptions.title(cinema?.Name ?: "Beta Cineplex")
            lat = try {
                cinema?.Latitude?.toDouble() ?: lat
            } catch (_: Exception) {
                lat
            }
            lng = try {
                cinema?.Longtitude?.toDouble() ?: lng
            } catch (_: Exception) {
                lng
            }
            markerOptions.position(LatLng(lat, lng))
            p0?.addMarker(markerOptions)
            p0?.moveCamera(CameraUpdateFactory.newLatLngZoom(LatLng(lat, lng), 16f))
            p0?.setOnMapClickListener {
                direction()
            }
        }

        fun bindMapView() {
            itemView.mapView.postDelayed({
                itemView.mapView.onCreate(null)
                itemView.mapView.onResume()
                itemView.mapView.getMapAsync(this)
            }, 1000)
            itemView.tvRedirect.setOnClickListener {
                direction()
            }
        }

        fun direction() {
            val gmmIntentUri = Uri.parse(String.format(Locale.ENGLISH, "google.navigation:q=%f,%f", lat, lng))

            // Create an Intent from gmmIntentUri. Set the action to ACTION_VIEW
            var mapIntent = Intent(Intent.ACTION_VIEW, gmmIntentUri)
            // Make the Intent explicit by setting the Google Maps package
            if ((activity as? BaseActivity)?.isHasPlayService() == true) {
//                gmmIntentUri = Uri.parse(String.format(Locale.ENGLISH, "geo:q=%f,%f", lat, lng))
                mapIntent.`package` = "com.google.android.apps.maps"
                mapIntent = Intent(Intent.ACTION_VIEW, gmmIntentUri)
            }

            // Attempt to start an activity that can handle the Intent
            try {
                activity?.startActivity(mapIntent)
            } catch (ex: Exception) {
                if ((activity as? BaseActivity)?.isHasPlayService() == true) {
                    val intent = Intent(Intent.ACTION_VIEW,
                            Uri.parse(String.format(Locale.ENGLISH, "http://maps.google.com/maps?daddr=%f,%f", lat, lng)))
                    activity?.startActivity(intent)
                    return
                }
                val intent = Intent(Intent.ACTION_VIEW,
                        Uri.parse(String.format(Locale.ENGLISH, "http://maps.google.com/maps?daddr=%f,%f", lat, lng)))
                activity?.startActivity(intent)
                ex.printStackTrace()
            }

        }
    }
}
