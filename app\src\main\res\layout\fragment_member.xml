<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/grayBg">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivBanner"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                app:layout_constraintTop_toTopOf="parent"
                android:background="@color/textDark"
                android:scaleType="centerCrop" />

            <View
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:alpha="0.6"
                android:background="@drawable/shape_primary_gradient_top_down" />

            <vn.zenity.betacineplex.helper.view.CurveView
                android:id="@+id/curve"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                app:cvColor="@color/grayBg"
                app:layout_constraintBottom_toBottomOf="@+id/ivBanner" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/margin_small"
                android:orientation="vertical"
                app:layout_constraintTop_toTopOf="@+id/curve">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_white_shadow_radius_5dp"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/padding_small"
                    android:paddingLeft="@dimen/padding_normal"
                    android:paddingRight="@dimen/padding_normal"
                    android:paddingTop="70dp">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvUsername"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        tools:text="Đỗ Ngọc Vinh"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_extra_large"
                        app:fontFamily="@font/oswald_regular" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/padding_normal"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/member_card"
                            android:textColor="@color/textDark"
                            android:textSize="@dimen/font_normal"
                            app:fontFamily="@font/sanspro_regular" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvMemberCardNumber"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="right"
                            tools:text="9000050001150323"
                            android:textColor="@color/textDark"
                            android:textSize="@dimen/font_extra_large"
                            app:fontFamily="@font/sanspro_regular" />

                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivCardCode"
                        android:layout_width="match_parent"
                        android:layout_height="90dp"
                        android:scaleType="fitXY" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginTop="@dimen/margin_large"
                        android:background="@color/grayLine" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvTitleSpend"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/margin_normal"
                            android:gravity="center_horizontal"
                            android:text="@string/total_spend"
                            android:textColor="#494c62"
                            android:textSize="@dimen/font_small"
                            app:fontFamily="@font/sanspro_regular"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toLeftOf="@+id/line4"
                            app:layout_constraintTop_toTopOf="parent" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvTotalSpend"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/margin_normal"
                            android:layout_marginTop="@dimen/margin_normal"
                            android:gravity="center_horizontal"
                            tools:text="150.000đ"
                            android:textColor="@color/colorPrimaryDark"
                            android:textSize="@dimen/font_extra_large"
                            app:fontFamily="@font/sanspro_bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toLeftOf="@+id/line4"
                            app:layout_constraintTop_toBottomOf="@+id/tvTitleSpend" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvRewardPoints"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/margin_normal"
                            android:layout_marginTop="@dimen/margin_normal"
                            android:gravity="center_horizontal"
                            tools:text="77"
                            android:textColor="@color/colorPrimaryDark"
                            android:textSize="@dimen/font_extra_large"
                            app:fontFamily="@font/sanspro_bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@+id/line4"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tvTitleRewardPoints" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvTitleRewardPoints"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal"
                            android:text="@string/reward_points"
                            android:textColor="#494c62"
                            android:textSize="@dimen/font_small"
                            app:fontFamily="@font/sanspro_regular"
                            app:layout_constraintLeft_toRightOf="@+id/line4"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/tvTitleSpend" />

                        <View
                            android:id="@+id/line4"
                            android:layout_width="0.5dp"
                            android:layout_height="0dp"
                            android:background="@color/grayLine"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/grayLine" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvRewardPoint"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="14sp"
                        android:textColor="@color/textff3377"
                        android:layout_marginTop="15dp"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:gravity="center"
                        android:maxLines="2"
                        app:fontFamily="@font/sanspro_regular"
                        tools:text="17"/>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_normal">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/ivIcMember"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            app:layout_constraintLeft_toLeftOf="parent"
                            android:scaleType="centerInside"
                            android:adjustViewBounds="true"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_member" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/noticeUpgradeVIP"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:padding="@dimen/padding_small"
                            android:text="@string/notice_upgrade_to_vip"
                            android:textSize="@dimen/font_small"
                            app:fontFamily="@font/sanspro_regular"
                            app:layout_constraintBottom_toBottomOf="@+id/ivIcVip"
                            app:layout_constraintLeft_toRightOf="@+id/ivIcMember"
                            app:layout_constraintRight_toLeftOf="@+id/ivIcVip"
                            app:layout_constraintTop_toTopOf="@+id/ivIcVip" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/ivIcVip"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:scaleType="centerInside"
                            android:adjustViewBounds="true"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_vip" />

                        <ProgressBar
                            android:id="@+id/progressBarVip"
                            style="@style/Base.Widget.AppCompat.ProgressBar.Horizontal"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/margin_small"
                            android:max="3000000"
                            android:progress="100000"
                            app:layout_constraintTop_toBottomOf="@+id/ivIcVip" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvTotalSpendProgress"
                            android:layout_width="wrap_content"
                            android:layout_height="20dp"
                            tools:text="3.000.000"
                            android:textColor="@color/textDark"
                            android:textSize="@dimen/font_small"
                            app:fontFamily="@font/sanspro_bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintRight_toRightOf="@+id/progressBarVip"
                            app:layout_constraintTop_toBottomOf="@+id/progressBarVip" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvVIPSpendProgress"
                            android:layout_width="wrap_content"
                            android:layout_height="20dp"
                            tools:text="0"
                            android:textColor="@color/textDark"
                            android:textSize="@dimen/font_small"
                            app:fontFamily="@font/sanspro_bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="@+id/progressBarVip"
                            app:layout_constraintTop_toBottomOf="@+id/progressBarVip" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>

                <androidx.cardview.widget.CardView
                    android:id="@+id/betaPoint"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/margin_small"
                    app:cardCornerRadius="3dp"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/TextContent"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:padding="15dp"
                            android:text="@string/beta_point"
                            android:textColor="@color/black" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:padding="12dp"
                            app:srcCompat="@drawable/ic_arrow_right_bg_gray" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/shareFriends"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/margin_small"
                    app:cardCornerRadius="3dp"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/TextContent"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:padding="15dp"
                            android:text="@string/share_friends"
                            android:textColor="@color/black" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:padding="12dp"
                            app:srcCompat="@drawable/ic_arrow_right_bg_gray" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/paymentHistory"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/margin_small"
                    app:cardCornerRadius="3dp"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/TextContent"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:padding="15dp"
                            android:text="@string/payment_history"
                            android:textColor="@color/black" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:padding="12dp"
                            app:srcCompat="@drawable/ic_arrow_right_bg_gray" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/memberCard"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/margin_small"
                    app:cardCornerRadius="3dp"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/TextContent"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:padding="15dp"
                            android:text="@string/member_card"
                            android:textColor="@color/black" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:padding="12dp"
                            app:srcCompat="@drawable/ic_arrow_right_bg_gray" />
                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/account"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/margin_small"
                    app:cardCornerRadius="3dp"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/TextContent"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:padding="15dp"
                            android:text="@string/account_info"
                            android:textColor="@color/black" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:padding="12dp"
                            app:srcCompat="@drawable/ic_arrow_right_bg_gray" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/changPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/margin_small"
                    app:cardCornerRadius="3dp"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            style="@style/TextContent"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:padding="15dp"
                            android:text="@string/change_password"
                            android:textColor="@color/black" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:padding="12dp"
                            app:srcCompat="@drawable/ic_arrow_right_bg_gray" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/btnLogout"
                    style="@style/TextContent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/margin_normal"
                    android:layout_marginTop="@dimen/margin_normal"
                    android:background="?attr/selectableItemBackground"
                    android:padding="@dimen/padding_normal"
                    android:text="@string/logout"
                    android:textColor="#fd2802" />
            </LinearLayout>

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/ivAvatar"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:adjustViewBounds="false"
                android:cropToPadding="false"
                android:src="@color/grayLine"
                app:civ_border_color="@color/white"
                app:civ_border_width="1dp"
                app:civ_fill_color="@color/grayLine"
                app:layout_constraintBottom_toBottomOf="@+id/curve"
                app:layout_constraintLeft_toLeftOf="@id/curve"
                app:layout_constraintRight_toRightOf="@+id/curve"
                app:layout_constraintTop_toTopOf="@+id/curve" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivSelectImage"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginBottom="1dp"
                android:layout_marginEnd="6dp"
                android:layout_marginStart="6dp"
                app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
                app:layout_constraintDimensionRatio="w, 28:90"
                app:layout_constraintLeft_toLeftOf="@+id/ivAvatar"
                app:layout_constraintRight_toRightOf="@+id/ivAvatar"
                app:srcCompat="@drawable/ic_addimage" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:text="@string/select_image"
                android:textColor="@color/white"
                android:textSize="@dimen/font_small"
                app:fontFamily="@font/sanspro_regular"
                app:layout_constraintBottom_toBottomOf="@+id/ivSelectImage"
                app:layout_constraintLeft_toLeftOf="@+id/ivSelectImage"
                app:layout_constraintRight_toRightOf="@+id/ivSelectImage"
                app:layout_constraintTop_toTopOf="@+id/ivSelectImage" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <View
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:alpha="0.8"
        android:background="@drawable/shape_dark_gradient"
        android:rotation="180" />

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:layout_marginTop="@dimen/statusBarHeight"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/member_beta"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_small"
            app:srcCompat="@drawable/ic_menubuger_white" />
    </LinearLayout>

</RelativeLayout>
