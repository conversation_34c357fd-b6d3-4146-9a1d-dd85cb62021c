package vn.zenity.betacineplex.view.film

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_listfilmbook.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.helper.extension.getString

/**
 * Created by Zenity.
 */

class ListFilmBookFragment : BaseFragment(), ListFilmBookContractor.View {

    private val presenter = ListFilmBookPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_listfilmbook
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewPager.adapter = PAdapter(childFragmentManager)
        tabLayout.setupWithViewPager(viewPager)
    }

    inner class PAdapter(fm: androidx.fragment.app.FragmentManager): FragmentPagerAdapter(fm) {
        override fun getItem(position: Int): androidx.fragment.app.Fragment {
            if (position == 0) {
                return ListFilmFragment.getInstance(1)
            }
            return ListFilmFragment.getInstance(3)
        }

        override fun getCount() = 2

        override fun getPageTitle(position: Int): CharSequence {
            if (position == 0) {
                return R.string.now_showing.getString()
            }
            return R.string.sneak_show.getString()
        }
    }
}
