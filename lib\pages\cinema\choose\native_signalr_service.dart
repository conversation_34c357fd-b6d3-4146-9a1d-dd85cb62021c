import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/service/native_signalr_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../constants/index.dart';

/// A wrapper for the native SignalR service specifically for seat selection
class NativeSeatSignalRService {
  // Singleton pattern
  static NativeSeatSignalRService? _instance;
  static NativeSeatSignalRService get instance {
    _instance ??= NativeSeatSignalRService._internal();
    return _instance!;
  }

  NativeSeatSignalRService._internal();

  // The native SignalR service
  final NativeSignalRService _signalRService = NativeSignalRService();

  // Hub name
  final String _hubName = 'chooseSeatHub';

  // Events
  final String _broadcastMessageEvent = 'broadcastMessage';
  final String _sendMessageEvent =
      'sendMessage'; // Phải khớp với iOS: "sendMessage"

  // <PERSON>ác biến này giữ lại để tham khảo nhưng không sử dụng vì iOS không cần
  // final String _joinGroupEvent = 'JoinGroup';      // Phải khớp với iOS: "JoinGroup"
  // final String _leaveGroupEvent = 'LeaveGroup';    // Phải khớp với iOS: "LeaveGroup"

  // Listeners
  final List<Function(bool)> _connectionListeners = [];
  final List<Function(SeatSignalrResponse)> _dataListeners = [];

  // Connection status
  bool _isConnected = false;
  String? _connectionId;
  String? _connectedUrl; // Lưu URL đã kết nối
  StreamSubscription<SignalREvent>? _eventSubscription;

  // Cơ chế tự động kết nối lại
  Timer? _reconnectTimer;
  final Duration _reconnectInterval = const Duration(seconds: 5);

  /// Start the SignalR connection
  Future<void> startSignalR({String? customUrl}) async {
    if (!Platform.isIOS) {
      _logWarning('Native SignalR is only supported on iOS');
      return;
    }

    _logInfo('Starting SignalR connection...');

    try {
      // Set up event listener
      _eventSubscription = _signalRService.events.listen(_handleSignalREvent);

      // Sử dụng URL chính xác như trong iOS: Config.SignalRURL = "https://www.betacinemas.vn/signalr/hubs"
      // Không cần thêm "/signalr" vì URL đã đầy đủ
      final hubUrl = customUrl ?? 'https://www.betacinemas.vn/signalr/hubs';

      // Get the auth token
      final token = await _getAccessToken();

      _logInfo('token: $token');
      _logInfo('Connecting to SignalR hub at $hubUrl');

      // Connect to the hub
      // Kiểm tra xem URL đã có "/signalr/hubs" chưa
      final hasSignalRHubs = hubUrl.contains("/signalr/hubs");
      _logInfo('URL contains "/signalr/hubs": $hasSignalRHubs');

      // Nếu URL đã có "/signalr/hubs", không cần thêm "/signalr"
      final useDefaultUrl = !hasSignalRHubs;
      _logInfo('Using useDefaultUrl: $useDefaultUrl');

      // Đặt _isConnected = true trước khi kết nối để tránh race condition
      // Nếu kết nối thất bại, _isConnected sẽ được đặt lại thành false trong catch block
      _isConnected = true;
      _notifyConnectionListeners(true);

      await _signalRService.connect(
        hubUrl,
        headers: {
          'Authorization': 'Bearer $token',
        },
        useDefaultUrl:
            useDefaultUrl, // Chỉ đặt true nếu URL chưa có "/signalr/hubs"
      );

      _connectionId = _signalRService.connectionId;
      _connectedUrl = hubUrl; // Lưu URL đã kết nối

      _logInfo('SignalR connected with ID: $_connectionId');
      _logInfo('Connected to URL: $_connectedUrl');

      // Đăng ký lắng nghe sự kiện broadcastMessage
      await _signalRService.on('broadcastMessage', (arguments) {
        _logInfo('Received broadcastMessage event: $arguments');
        _handleBroadcastMessage(arguments);
      });

      // Bắt đầu timer kết nối lại để đảm bảo kết nối được duy trì
      _startReconnectTimer();

      // Register for the ReceiveMessage method
      await _signalRService.on(_broadcastMessageEvent, _handleBroadcastMessage);
    } catch (e) {
      _logError('Error starting SignalR: $e');

      // Đặt lại _isConnected = false nếu kết nối thất bại
      _isConnected = false;
      _connectionId = null;
      _notifyConnectionListeners(false);

      rethrow;
    }
  }

  /// Handle SignalR events
  void _handleSignalREvent(SignalREvent event) {
    if (event is SignalRConnectionEvent) {
      // Kiểm tra xem trạng thái kết nối có thay đổi không
      final wasConnected = _isConnected;
      final oldConnectionId = _connectionId;

      _isConnected = event.connected;
      _connectionId = event.connectionId;

      // Nếu đã kết nối và ID kết nối không thay đổi, không cần xử lý lại
      if (_isConnected && oldConnectionId == _connectionId && wasConnected) {
        _logInfo('Ignoring duplicate connection event for ID: $_connectionId');
        return;
      }

      _notifyConnectionListeners(_isConnected);

      if (_isConnected) {
        _logInfo('SignalR connected with ID: $_connectionId');
        if (_connectedUrl != null) {
          _logInfo('Connected to URL: $_connectedUrl');
        }

        // Chỉ đăng ký sự kiện nếu trước đó chưa kết nối hoặc ID kết nối đã thay đổi
        if (!wasConnected || oldConnectionId != _connectionId) {
          _logInfo('New connection - registering event handlers');

          // Đăng ký sự kiện broadcastMessage
          _signalRService.on('broadcastMessage', (arguments) {
            _logInfo('Received broadcastMessage event: $arguments');
            _handleBroadcastMessage(arguments);
          }).catchError((error) {
            _logError('Error registering broadcastMessage handler: $error');
          });
        }
      } else {
        _logInfo('SignalR disconnected');
        // Reset URL when disconnected
        _connectedUrl = null;

        // Bắt đầu timer kết nối lại nếu đã từng kết nối thành công trước đó
        if (_connectionId != null) {
          _logInfo(
              'Starting reconnect timer because connection was previously established');
          _startReconnectTimer();
        } else {
          _logInfo(
              'Not starting reconnect timer because connection was never established');
        }
      }
    } else if (event is SignalRErrorEvent) {
      _logError('SignalR error: ${event.errorCode} - ${event.errorMessage}');

      // Nếu có lỗi và đã từng kết nối thành công trước đó, thử kết nối lại
      if (!_isConnected && _connectionId != null) {
        _logInfo(
            'Starting reconnect timer after error because connection was previously established');
        _startReconnectTimer();
      } else if (!_isConnected) {
        _logInfo(
            'Not starting reconnect timer after error because connection was never established');
      }
    }
  }

  /// Handle broadcast messages from the server
  void _handleBroadcastMessage(List<dynamic> parameters) {
    if (parameters.length >= 4) {
      try {
        final connectionId = parameters[0].toString();
        final showId = parameters[1].toString();
        final seatIndex = int.parse(parameters[2].toString());
        final seatStatus = int.parse(parameters[3].toString());

        _logInfo(
            'Received broadcast message: connectionId=$connectionId, showId=$showId, seatIndex=$seatIndex, status=$seatStatus');

        // Skip messages from this client
        if (connectionId == _connectionId) {
          _logInfo('Skipping message from this client');
          return;
        }

        final data = SeatSignalrResponse(
          connectionId,
          showId,
          seatIndex,
          seatStatus,
        );

        // Notify all listeners
        for (var listener in _dataListeners) {
          listener(data);
        }
      } catch (e) {
        _logWarning('Error handling broadcast message: $e');
      }
    } else {
      _logWarning('Received invalid broadcast message: $parameters');
    }
  }

  /// Join a group - Phương thức này không cần thiết vì iOS không sử dụng
  /// Giữ lại để tương thích với mã hiện tại, nhưng không thực sự gọi API
  Future<void> joinGroup(String groupName) async {
    if (!Platform.isIOS || !_isConnected) return;

    // Không gọi API, chỉ ghi log
    _logInfo('Phương thức joinGroup không cần thiết - iOS không sử dụng');
    _logInfo(
        'Chỉ cần lắng nghe sự kiện broadcastMessage và lọc theo showId: $groupName');

    // Không gọi API để tránh lỗi "There was a bad response from the server"
    // await _signalRService.invoke(_joinGroupEvent, arguments: [groupName]);
  }

  /// Leave a group - Phương thức này không cần thiết vì iOS không sử dụng
  /// Giữ lại để tương thích với mã hiện tại, nhưng không thực sự gọi API
  Future<void> leaveGroup(String groupName) async {
    if (!Platform.isIOS || !_isConnected) return;

    // Không gọi API, chỉ ghi log
    _logInfo('Phương thức leaveGroup không cần thiết - iOS không sử dụng');

    // Không gọi API để tránh lỗi "There was a bad response from the server"
    // await _signalRService.invoke(_leaveGroupEvent, arguments: [groupName]);
  }

  /// Send a seat update
  Future<void> sendSeat(String showId, int seatIndex, int status) async {
    if (!Platform.isIOS || !_isConnected) return;

    try {
      _logInfo(
          'Sending seat status: showId=$showId, seatIndex=$seatIndex, status=$status');

      final args = [
        _connectionId ?? '',
        showId,
        seatIndex.toString(),
        status.toString()
      ];

      await _signalRService.invoke(_sendMessageEvent, arguments: args);
      _logInfo('Sent seat status successfully');
    } catch (e) {
      _logError('Error sending seat status: $e');
      rethrow;
    }
  }

  /// Stop the SignalR connection
  Future<void> stop() async {
    if (!Platform.isIOS) return;

    try {
      // Hủy timer kết nối lại
      _reconnectTimer?.cancel();
      _reconnectTimer = null;

      _eventSubscription?.cancel();
      await _signalRService.disconnect();
      _isConnected = false;
      _connectionId = null;
      _connectedUrl = null; // Reset URL khi ngắt kết nối
      _notifyConnectionListeners(false);

      _logInfo('SignalR connection stopped');
    } catch (e) {
      _logWarning('Error stopping SignalR: $e');
    }
  }

  /// Get the connection ID
  String? get connectionId => _connectionId;

  /// Check if connected
  bool get isConnected => _isConnected;

  /// Add a connection listener
  void addConnectionListener(Function(bool) listener) {
    _connectionListeners.add(listener);
    listener(_isConnected);
  }

  /// Remove a connection listener
  void removeConnectionListener(Function(bool) listener) {
    _connectionListeners.remove(listener);
  }

  /// Add a data listener
  void addDataListener(Function(SeatSignalrResponse) listener) {
    _dataListeners.add(listener);
  }

  /// Remove a data listener
  void removeDataListener(Function(SeatSignalrResponse) listener) {
    _dataListeners.remove(listener);
  }

  /// Notify all connection listeners
  void _notifyConnectionListeners(bool isConnected) {
    for (var listener in _connectionListeners) {
      listener(isConnected);
    }
  }

  /// Get the access token
  Future<String> _getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(CPref.token) ?? '';
  }

  /// Log an info message
  void _logInfo(String message) {
    debugPrint('NativeSignalR INFO: $message');
  }

  /// Log a warning message
  void _logWarning(String message) {
    debugPrint('NativeSignalR WARNING: $message');
  }

  /// Log an error message
  void _logError(String message) {
    debugPrint('NativeSignalR ERROR: $message');
  }

  /// Bắt đầu timer kết nối lại
  void _startReconnectTimer() {
    _logInfo('Starting reconnect timer');

    // Hủy timer cũ nếu có
    _reconnectTimer?.cancel();

    // Tạo timer mới
    _reconnectTimer = Timer.periodic(_reconnectInterval, (timer) async {
      if (!_isConnected) {
        _logInfo('Reconnect timer triggered - attempting to reconnect');
        try {
          // Thử kết nối lại
          await startSignalR(customUrl: _connectedUrl);
        } catch (e) {
          _logError('Error reconnecting: $e');
        }
      }
    });

    _logInfo('Reconnect timer started');
  }

  /// Dispose of resources
  void dispose() {
    // Hủy timer kết nối lại
    _reconnectTimer?.cancel();
    _reconnectTimer = null;

    stop();
    _connectionListeners.clear();
    _dataListeners.clear();
  }
}

/// Model for SignalR seat responses
class SeatSignalrResponse {
  final String connectionId;
  final String showId;
  final int seatIndex;
  final int seatStatus;

  SeatSignalrResponse(
    this.connectionId,
    this.showId,
    this.seatIndex,
    this.seatStatus,
  );
}
