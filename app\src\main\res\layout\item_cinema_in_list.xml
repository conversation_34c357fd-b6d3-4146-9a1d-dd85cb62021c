<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginTop="8dp"
    android:orientation="horizontal"
    android:baselineAligned="false">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clCinemaLeft"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="4dp"
        android:background="?attr/selectableItemBackground">

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:background="@drawable/shape_white_radius"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivCinemaLeft"
            android:layout_width="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="h, 175:86"
            tools:src="@drawable/test_event"
            android:scaleType="centerCrop"/>

        <Space
            android:id="@+id/spaceLeft"
            android:layout_width="0dp"
            android:layout_height="1dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/ivCinemaLeft"
            android:layout_marginBottom="5dp"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCinemaLeftName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/spaceLeft"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:textColor="@color/text494c62"
            android:background="@color/white"
            android:textSize="16sp"
            app:fontFamily="@font/sanspro_bold"
            android:gravity="center"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:paddingTop="8dp"
            android:paddingBottom="2dp"
            tools:text="Beta Cineplex My Dinh"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCinemaLeftDistance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/text03599d"
            app:layout_constraintTop_toBottomOf="@+id/tvCinemaLeftName"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:textSize="14sp"
            app:fontFamily="@font/sanspro_regular"
            android:gravity="center"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:paddingBottom="8dp"
            tools:text="13km"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clCinemaRight"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginRight="8dp"
        android:layout_marginLeft="4dp"
        android:background="?attr/selectableItemBackground">

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:background="@drawable/shape_white_radius"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivCinemaRight"
            android:layout_width="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="h, 175:86"
            tools:src="@drawable/test_event"
            android:scaleType="centerCrop"/>

        <Space
            android:id="@+id/spaceRight"
            android:layout_width="0dp"
            android:layout_height="1dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/ivCinemaRight"
            android:layout_marginBottom="5dp"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCinemaRightName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/spaceRight"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:textColor="@color/text494c62"
            android:textSize="16sp"
            android:background="@color/white"
            app:fontFamily="@font/sanspro_bold"
            android:gravity="center"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:paddingTop="8dp"
            android:paddingBottom="2dp"
            tools:text="Beta Cineplex My Dinh"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCinemaRightDistance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/text03599d"
            app:layout_constraintTop_toBottomOf="@+id/tvCinemaRightName"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:textSize="14sp"
            app:fontFamily="@font/sanspro_regular"
            android:gravity="center"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:paddingBottom="8dp"
            tools:text="13km"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>