{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a28309da1a8acb06bc64a001ab53bc1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984d40196547b8a604299d17a747714755", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29fca69aeeb124f2c6ac314a1a39ba5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a9f593e2c1b1a7db14aac2b1b81856b5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29fca69aeeb124f2c6ac314a1a39ba5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985750fdc08637d5dbdfe3ed382e169ca7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98631782c2685c7862de60809270208d62", "guid": "bfdfe7dc352907fc980b868725387e9855d6a3eba673c2eb76b4db493671fc7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984027195107d43c1f071f9926545ac60f", "guid": "bfdfe7dc352907fc980b868725387e98f30f6b9da91c446e2cb2cf1c5bcd61dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd8f13df474218c88ec3926a7801153f", "guid": "bfdfe7dc352907fc980b868725387e980c5c24395a29075e1f14c09f36900500"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b85c93da6106c4e5efa6a08876e63b5", "guid": "bfdfe7dc352907fc980b868725387e983792f67ca4248eb65419d81cf06daf2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98910e0ab3e8c0216881b6916491477742", "guid": "bfdfe7dc352907fc980b868725387e984c8144c920e58c9c9d2a7ee98d9e3c43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac772056a5e808a24e79bd2471090756", "guid": "bfdfe7dc352907fc980b868725387e98f1bd0e720d8c4e29113a2126cfceec27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea4726a42c24d20602c746f0408601ce", "guid": "bfdfe7dc352907fc980b868725387e9841357607bf9986fee7448c8869e480fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e71b9fb191e566f9e7648dd4f03d5200", "guid": "bfdfe7dc352907fc980b868725387e98590c8730cfa782aad7867a4521a58dcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821340b9002c1e290fa16b029d0684118", "guid": "bfdfe7dc352907fc980b868725387e989c62161f322a80ee92556af5614d0ebb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833dfe2589fd24db59d87ebf7dd6a175d", "guid": "bfdfe7dc352907fc980b868725387e98caf04c295c0d40256c27ae619073faee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3f8a3234a22b50c631af3633175a080", "guid": "bfdfe7dc352907fc980b868725387e98d7feed38834bd1ca9a3dd0af020e6568"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a65cfe2883f278571a61623a6233d3f", "guid": "bfdfe7dc352907fc980b868725387e98f07c28dc857fd55b464a5daf76a31c7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a644b004340aee478a4b7b06be52982", "guid": "bfdfe7dc352907fc980b868725387e987be52c2e91f760a8d076225c2207af02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4b8cc806a24654d2003ce1679318754", "guid": "bfdfe7dc352907fc980b868725387e98aa294e05a54a64886463a155efbfd63c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f95a722e98dfa311d872b8907571a678", "guid": "bfdfe7dc352907fc980b868725387e98ba4724dccc5b708017e87da1897567e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d761f456882e46db71fe2666a258e2b3", "guid": "bfdfe7dc352907fc980b868725387e98b1945164f36abf4ba85ca6e485882d2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862c36e4f06c614894c4b0448debd7a1f", "guid": "bfdfe7dc352907fc980b868725387e981adcdf50f88efce6c2cd5c7635526db6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1259085296b9533943c06ce82cd88b8", "guid": "bfdfe7dc352907fc980b868725387e98bfa4b29a68c2932df0a2800e59c473b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b10deaa2af629cf571a4cddc3d476d67", "guid": "bfdfe7dc352907fc980b868725387e9883beee818acf43d2734acd2179768c34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7345491ba85e9a568e3770d8ea9694c", "guid": "bfdfe7dc352907fc980b868725387e989a48a08dd375c446dc706d9afd1949ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe5707f24ecf36a9cc1f901066296997", "guid": "bfdfe7dc352907fc980b868725387e981a969922e21b030c834083b9fcdce936"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98173e8e6b49334e4b5adbf6ea881f9f54", "guid": "bfdfe7dc352907fc980b868725387e98c9b8d59abebaa83353305bb5af260d1c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9862239af39a9d14e4cfef707991134a3c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9846e5cafe601eafc4be23851cd01b3ace", "guid": "bfdfe7dc352907fc980b868725387e98d2055e74613fcf2d9a42f42713c8b2ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f0bb3d160be76547ed33bd4802f83cf", "guid": "bfdfe7dc352907fc980b868725387e980df956bc69dd5653a197f2ce840dc28a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980deb5c6b9d61d9bc69053e002710e566", "guid": "bfdfe7dc352907fc980b868725387e9804e4ea33264039f7a1d972b26c1855c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98825b2f5a9c9346f70459019feb7e4e41", "guid": "bfdfe7dc352907fc980b868725387e98647210bc6985fa8c545c9010e9224511"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf3908c334d74c661bb3050bdcaff238", "guid": "bfdfe7dc352907fc980b868725387e98a8c6152577f678880f5911d2094a7ed6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7f9a33aa127eeec096e445888477476", "guid": "bfdfe7dc352907fc980b868725387e98c5c8a544109eee54b561de2520df8224"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fa3985877ba9b63b645e05649aaef60", "guid": "bfdfe7dc352907fc980b868725387e988caf181a6cccaecea91f71faecb3a14d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a378d20e89a371eaf844ab615b93e01", "guid": "bfdfe7dc352907fc980b868725387e9812c958e616537189e18d1051d29cd0d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f768c9d8000167e02e7b4b57bbcb8b36", "guid": "bfdfe7dc352907fc980b868725387e9811a7d310261d110727ac07695a987d2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e42aad3556214fa29403152e733f444d", "guid": "bfdfe7dc352907fc980b868725387e98c1da27483e9e3a41052ae9c5be1773af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980274403461e17c58fb91aee732e2ce9a", "guid": "bfdfe7dc352907fc980b868725387e989ce4bc15ca0ceaea2cbe31803aa093b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cccf834debc9f1c37b31327e609914c6", "guid": "bfdfe7dc352907fc980b868725387e9803b76191fa7fef173895229c7a790781"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851a9dc6f361fac56038e05f07561e29b", "guid": "bfdfe7dc352907fc980b868725387e98124e01e29b0374c62413a37cc6404364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dba6997aa51055a6b44d568f572f3353", "guid": "bfdfe7dc352907fc980b868725387e98dcb70bdc18a8d22d52dd7c15ce64a79c"}], "guid": "bfdfe7dc352907fc980b868725387e98371c1614e1ac56547922d033ab93f109", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98bbfc1541cd897ed4cb7f3627ba54a63c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d391d713992f1d4e99af11b30a3d8fcd", "guid": "bfdfe7dc352907fc980b868725387e98002c489c14c657424985c5eff3b24bb4"}], "guid": "bfdfe7dc352907fc980b868725387e98ca14afb1014e8f39e814be422c60db0b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e8858461ef8b69dcbd5f45293a918fe8", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9877563a5d965dd96bd17ec3efed7f4154", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}