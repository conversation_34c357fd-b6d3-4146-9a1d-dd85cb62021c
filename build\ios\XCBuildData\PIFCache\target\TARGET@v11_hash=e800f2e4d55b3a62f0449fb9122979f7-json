{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98445595ba42ac1af2686db04c51a9db82", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b2f4b25a8d90636f47ae32244238e77c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98238b1440d002209c0201ca66ec051106", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984e4d86d70890c21ed16cf87923a2cf2b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98238b1440d002209c0201ca66ec051106", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b7fb43e5cb40c6c38d1a3e45c7031a6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98395ee0b06c88304ae5eeae9da133432b", "guid": "bfdfe7dc352907fc980b868725387e98193cbc37df47ca6bd1f7b2987d581868", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98609b9a67877d693942e8feaed4aad15a", "guid": "bfdfe7dc352907fc980b868725387e981c77104c961383e47ab5bc0ee51362be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ef8214864c798601d7da97f99c9e053", "guid": "bfdfe7dc352907fc980b868725387e983b9e66575394fd880d61297cab95ad0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1b4e984405875865048ef76365fb454", "guid": "bfdfe7dc352907fc980b868725387e98bcf4c9beab448fb2d7149cd3b63963a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ab2b1ee585d2bfbb4246acfed07c1e6", "guid": "bfdfe7dc352907fc980b868725387e987c0a191d08bda3e5833bf846878d0b72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0383cdaf59aeb417e3daacc400a6c4a", "guid": "bfdfe7dc352907fc980b868725387e986c2a7ffc1545121fc8cd04a3d7b29d5a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7fb97eb2ba04643ec004de0c221548b", "guid": "bfdfe7dc352907fc980b868725387e98ebc431f31b4ba40239ec86964236fe84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e93500d3c62bc3b3a9ff8a3af566064", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98908f8677be6f13047394fd63caac90b6", "guid": "bfdfe7dc352907fc980b868725387e98df3885b7b8c513219172a52557fe731e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c3f712da02ea1e337e28430a1d25a21", "guid": "bfdfe7dc352907fc980b868725387e9854e6ca851f63ed3cc2e8e08edc65479a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98438b140f44278d65fad10935c27fb3b2", "guid": "bfdfe7dc352907fc980b868725387e98a45a633473bf72558a786ad97ba60a42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987244c668319235321fc86fcdcd07290a", "guid": "bfdfe7dc352907fc980b868725387e986e7fdef0912afe567091baba0ec28fcc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ba58268ee286fa44d93bf36351750129", "guid": "bfdfe7dc352907fc980b868725387e98865d81ee9ba06a26516e000cf4f66bc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0e07a6682e13bd9ebf2b4bfd6410c2b", "guid": "bfdfe7dc352907fc980b868725387e9865cd37c44f872c70257508efae941891"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98482cb5433d17f22f8c77411cb5f01e10", "guid": "bfdfe7dc352907fc980b868725387e98b62591ad28ed7a89009145d6d76a21c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98363cd11aa4fbe88dce0e1b3a9e3093b5", "guid": "bfdfe7dc352907fc980b868725387e98f0dfb7ca0ca36190fab8c20d10f6e2f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98165ddab8e7cd7fd0e51749aeab9bf4c4", "guid": "bfdfe7dc352907fc980b868725387e981cee213e907a8cfbe7a4bca267a59968"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b57e301d06d287f8f54313d38fffe663", "guid": "bfdfe7dc352907fc980b868725387e98443745df8a468cbf6c91df114c6b4892"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccf7949e045d7ceb306e3590b0427af0", "guid": "bfdfe7dc352907fc980b868725387e98381aacafdc460bf824e5a270b95c9bde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a474ec490d5de9c32697fb1fcfe71cb5", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b06b4caace4df88fcfb3bb9648a12ca4", "guid": "bfdfe7dc352907fc980b868725387e982af67cb7b567ab7fa318a91c32ade8c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bf32ac0e59be4b6da62894902c0fcc1", "guid": "bfdfe7dc352907fc980b868725387e988e3ec702f10fd46b525974c712062460"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812364bc77794dfec941fea162be64899", "guid": "bfdfe7dc352907fc980b868725387e98f812a7af6d7843cd1aafbed23a7cc2b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f96971c63ecb42f904c5b58082df7eb", "guid": "bfdfe7dc352907fc980b868725387e98b05d9a098385ea3e71527283300ee4b9"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}