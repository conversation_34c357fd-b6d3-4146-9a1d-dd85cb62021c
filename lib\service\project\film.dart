import 'dart:convert';

import 'package:http/http.dart';

import '../../models/index.dart';
import '../index.dart';
import '../language_service.dart';

class SFilm {
  late String endpoint;
  late Map<String, String> headers;
  late Future<MApi?> Function({required Response result}) checkAuth;

  SFilm(this.endpoint, this.headers, this.checkAuth);

  // Tương đương với case .listFilm(Bool?)
  Future<MApi?> getListFilm({bool? isShowing, bool isEarly = false /*, String? lang*/
      }) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();

    // Update language header
    // headers['language'] = language;
    // headers['x-language'] = language;

    Map<String, dynamic> queryParameters = {};
    if (isShowing != null) {
      queryParameters['isShowing'] = isShowing.toString();
    }
    // Add language parameter
    // queryParameters['language'] = language;

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v2/erp/films',
        headers: headers,
        queryParameters: queryParameters,
      ),
    ).then((response) {
      // If isEarly is true, filter the response to only include films with HasSneakShow = true
      if (isEarly && response != null && response.data != null) {
        final data = response.data;
        if (data is Map && data.containsKey('content') && data['content'] is List) {
          final List filteredContent =
              (data['content'] as List).where((film) => film is Map && film['HasSneakShow'] == true).toList();

          // Create a new response with the filtered content
          final filteredData = Map<String, dynamic>.from(data);
          filteredData['content'] = filteredContent;
          filteredData['totalElements'] = filteredContent.length;
          filteredData['numberOfElements'] = filteredContent.length;

          return response.copyWith(data: filteredData);
        }
      }
      return response;
    });
  }

  Future<MApi?> getFilmDetail({required String id /*, String? lang*/
      }) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();
    //
    // // Update language header
    // headers['language'] = language;
    // headers['x-language'] = language;

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v2/erp/films/$id',
        headers: headers,
        queryParameters: {},
      ),
    );
  }

  // Tương đương với case .showFilm(Bool?)
  Future<MApi?> getShowFilm({bool? sneakShow /*, String? lang*/
      }) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();
    //
    // // Update language header
    // headers['language'] = language;
    // headers['x-language'] = language;

    Map<String, dynamic> queryParameters = {
      // 'language': language
    };
    if (sneakShow != null) {
      queryParameters['sneakShow'] = sneakShow.toString();
    }
    return checkAuth(
        result: await BaseHttp.get(
            url: '$endpoint/api/v1/erp/shows/films', headers: headers, queryParameters: queryParameters));
  }

  // Tương đương với case .filmShowDate(String)
  Future<MApi?> getFilmShowDate({required String id /*, String? lang*/
      }) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();
    //
    // // Update language header
    // headers['language'] = language;
    // headers['x-language'] = language;

    return checkAuth(
      result: await BaseHttp.get(url: '$endpoint/api/v2/erp/films/$id/show-dates', headers: headers, queryParameters: {
        /*'language': language*/
      }),
    );
  }

  // Tương đương với case .filmShow(String, String)
  Future<MApi?> getFilmShow({required String id, required String dateShow /*, String? lang*/
      }) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();
    //
    // // Update language header
    // headers['language'] = language;
    // headers['x-language'] = language;

    return checkAuth(
      result: await BaseHttp.get(
          url: '$endpoint/api/v2/erp/films/$id/shows', // Sửa lại path
          headers: headers,
          queryParameters: {
            'dateShow': dateShow,
            // 'language': language
          }),
    );
  }

  // Tương đương với case .showSeat(String)
  Future<MApi?> getShowSeat({required String id /*, String? lang*/
      }) async {
    final languageService = LanguageService();
    final language = await languageService.getCurrentLanguage();

    // Update language header
    headers['language'] = language;

    return checkAuth(
      result: await BaseHttp.get(
          url: '$endpoint/api/v1/erp/shows/%7B$id%7D', // Sửa lại path
          headers: headers,
          queryParameters: {
            /*'language': language*/
          }),
    );
  }

  // Tương đương với case .booking(CreateBookingModel)
  Future<MApi?> booking({required Map<String, dynamic> body /*, String? lang*/
      }) async {

    final response = await BaseHttp.post(url: '$endpoint/booking', headers: {...headers, "Accept":"application/json"}, body: body);

    // Kiểm tra nếu phản hồi là HTML
    final contentType = response.headers['Content-type'] ?? '';
    if (contentType.contains('text/html') || response.body.trim().startsWith('<')) {
      // Trả về phản hồi HTML gốc
      return MApi(
        code: response.statusCode,
        message: 'Success',
        data: response.body,
        isSuccess: response.statusCode >= 200 && response.statusCode < 300,
      );
    }

    // Nếu không phải HTML, xử lý như bình thường
    return checkAuth(result: response);
  }

  // Get transaction detail
  Future<MApi?> getTransactionDetail({required String id /*, String? lang*/
      }) async {
    return checkAuth(
      result: await BaseHttp.get(url: '$endpoint/api/v1/erp/orders/$id', headers: headers, queryParameters: {
        /*'language': language*/
      }),
    );
  }

  // Tương đương với case .banner
  Future<MApi?> getBanner({String? lang}) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();
    //
    // // Update language header
    // headers['language'] = language;
    // headers['x-language'] = language;

    return checkAuth(
      result: await BaseHttp.get(url: '$endpoint/api/v1/erp/banner-slider', headers: headers, queryParameters: {
        /*'language': language*/
      }),
    );
  }
}
