package vn.zenity.betacineplex.helper.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;

import vn.zenity.betacineplex.R;
import vn.zenity.betacineplex.helper.extension.Resource_ExtensionKt;

public class CurveView extends View {

    private Paint paint;
    private Path path;
    private int bgColor = Color.RED;
    private float smoth = 1f/2f;
    private float smoothHeight = 0;
    private boolean isReverse = false;

    public CurveView(Context context) {
        super(context);
        init(context, null);
    }

    public CurveView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public CurveView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    public void init(Context context, AttributeSet attrs) {
        TypedArray styledAttributes = context.obtainStyledAttributes(attrs, R.styleable.CurveView, 0, 0);
        bgColor = styledAttributes.getColor(R.styleable.CurveView_cvColor, bgColor);
        isReverse = styledAttributes.getBoolean(R.styleable.CurveView_cvReverse, false);
        smoth = styledAttributes.getFloat(R.styleable.CurveView_cvCurveSmooth, 4f/5f);
        smoothHeight = styledAttributes.getDimension(R.styleable.CurveView_cvSmooth, 0f);
        styledAttributes.recycle();

        path = new Path();

        paint = new Paint();
        paint.setAntiAlias(true);
        paint.setDither(true);
        paint.setStyle(Paint.Style.FILL);
        paint.setColor(bgColor);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        canvas.drawColor(Color.TRANSPARENT);
        paint.setShader(null);

        float width = getWidth();
        float height = getHeight();
        smoothHeight = (smoothHeight > 0) ? smoothHeight : smoth * height;

        if (isReverse) {
            path.moveTo(0, height);

            path.lineTo(0, 0);

            path.lineTo(width, 0);

            path.lineTo(width, height);

            path.cubicTo(4f*width/6f, height - smoothHeight, 2f*width/6f, height - smoothHeight, 0, height);

            paint.setColor(bgColor);
            paint.setStyle(Paint.Style.FILL);
            canvas.drawPath(path, paint);
            return;
        }

        path.moveTo(0, 0);

        path.lineTo(0, height);

        path.lineTo(width, height);

        path.lineTo(width, 0);

        path.cubicTo(4f*width/6f, smoothHeight, 2f*width/6f, smoothHeight, 0, 0);

        paint.setColor(bgColor);
        paint.setStyle(Paint.Style.FILL);
        canvas.drawPath(path, paint);

    }

}