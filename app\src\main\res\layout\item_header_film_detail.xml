<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <vn.zenity.betacineplex.helper.view.TopCropImageView
        android:id="@+id/ivBanner"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/llFilmDetail"
        android:background="@color/textDark"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/btnPlay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/ivBanner"
        app:layout_constraintLeft_toLeftOf="@+id/ivBanner"
        app:layout_constraintRight_toRightOf="@+id/ivBanner"
        app:layout_constraintTop_toTopOf="@+id/ivBanner"
        app:srcCompat="@drawable/opacity"/>

    <View
        android:id="@+id/viewTopDetail"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:layout_constraintBottom_toTopOf="@+id/llFilmDetail"/>

    <vn.zenity.betacineplex.helper.view.CurveView
        android:id="@+id/curve"
        android:layout_width="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_height="0dp"
        android:visibility="visible"
        app:cvColor="@color/grayBg"
        app:cvSmooth="40dp"
        app:cvReverse="false"
        app:layout_constraintTop_toTopOf="@+id/viewTopDetail"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <LinearLayout
        android:id="@+id/llFilmDetail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/activity_vertical_margin"
        android:layout_marginRight="@dimen/activity_vertical_margin"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/cardPoster"
        app:layout_constraintLeft_toRightOf="@+id/cardPoster"
        app:layout_constraintRight_toRightOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvFilmTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="Pacific Rim: Trỗi Pacific Rim Rim: Trỗi Pacific Rim"
            android:maxLines="2"
            android:ellipsize="end"
            android:textSize="@dimen/font_large"
            android:textColor="@color/textBlack"
            app:fontFamily="@font/oswald_bold"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvFilmTypeSub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_small"
            tools:text="(2D - Lồng Tiếng)"
            android:textSize="@dimen/font_normal"
            tools:visibility="gone"
            android:textColor="@color/textBlack"
            app:fontFamily="@font/oswald_regular"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvNotive"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_small"
            android:visibility="visible"
            android:background="@drawable/bg_line_rounded_gray_text"
            android:paddingEnd="@dimen/padding_small"
            android:paddingStart="@dimen/padding_small"
            android:text="@string/age_notice"
            android:textSize="@dimen/font_small"
            android:textColor="@color/textBlack"
            app:fontFamily="@font/sanspro_regular"/>
    </LinearLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/cardPoster"
        android:layout_width="130dp"
        android:layout_height="205.7dp"
        android:layout_marginStart="@dimen/nav_header_vertical_spacing"
        android:layout_marginTop="200dp"
        app:cardBackgroundColor="@color/grayBg"
        app:cardCornerRadius="@dimen/radius_small"
        app:cardElevation="1dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivPoster"
            android:layout_width="130dp"
            android:layout_height="205.7dp"/>
    </androidx.cardview.widget.CardView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/detailCl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_normal"
        android:orientation="vertical"
        android:padding="@dimen/padding_small"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cardPoster">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDirectorsTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/director"
            android:textAllCaps="true"
            android:textSize="@dimen/font_normal"
            app:fontFamily="@font/sanspro_bold"
            android:textColor="@color/textBlack"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDirectors"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/margin_large"
            tools:text="Steven S. DeKnight"
            android:textSize="@dimen/font_normal"
            android:textColor="@color/black"
            app:fontFamily="@font/sanspro_regular"
            app:layout_constraintLeft_toRightOf="@+id/tvDateStartTitle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvDirectorsTitle"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCastTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/cast"
            android:textAllCaps="true"
            android:textSize="@dimen/font_normal"
            app:layout_constraintLeft_toLeftOf="parent"
            app:fontFamily="@font/sanspro_bold"
            android:textColor="@color/textBlack"
            app:layout_constraintTop_toBottomOf="@+id/tvDirectors"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCast"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/margin_large"
            tools:text="John Boyega, Scott Eatwood, Jing Tian"
            android:textSize="@dimen/font_normal"
            android:textColor="@color/black"
            app:fontFamily="@font/sanspro_regular"
            app:layout_constraintLeft_toRightOf="@+id/tvDateStartTitle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvCastTitle"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvKindTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/kind"
            android:textAllCaps="true"
            android:textSize="@dimen/font_normal"
            app:layout_constraintLeft_toLeftOf="parent"
            android:textColor="@color/textBlack"
            app:fontFamily="@font/sanspro_bold"
            app:layout_constraintTop_toBottomOf="@+id/tvCast"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvKind"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/margin_large"
            tools:text="Hành động, Viễn tưởng"
            android:textSize="@dimen/font_normal"
            android:textColor="@color/black"
            app:fontFamily="@font/sanspro_regular"
            app:layout_constraintLeft_toRightOf="@+id/tvDateStartTitle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvKindTitle"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDurationTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/duration"
            android:textAllCaps="true"
            android:textSize="@dimen/font_normal"
            app:layout_constraintLeft_toLeftOf="parent"
            android:textColor="@color/textBlack"
            app:fontFamily="@font/sanspro_bold"
            app:layout_constraintTop_toBottomOf="@+id/tvKind"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDuration"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/margin_large"
            tools:text="135 phút"
            android:textSize="@dimen/font_normal"
            android:textColor="@color/black"
            app:fontFamily="@font/sanspro_regular"
            app:layout_constraintLeft_toRightOf="@+id/tvDateStartTitle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvDurationTitle"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLangTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/language"
            android:textAllCaps="true"
            android:textSize="@dimen/font_normal"
            app:layout_constraintLeft_toLeftOf="parent"
            android:textColor="@color/textBlack"
            app:fontFamily="@font/sanspro_bold"
            app:layout_constraintTop_toBottomOf="@+id/tvDuration"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvLang"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/margin_large"
            tools:text="Tiếng Anh"
            android:textSize="@dimen/font_normal"
            android:textColor="@color/black"
            app:fontFamily="@font/sanspro_regular"
            app:layout_constraintLeft_toRightOf="@+id/tvDateStartTitle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvLangTitle"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDateStartTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/date_start"
            android:textAllCaps="true"
            android:textSize="@dimen/font_normal"
            app:layout_constraintLeft_toLeftOf="parent"
            android:textColor="@color/textBlack"
            app:fontFamily="@font/sanspro_bold"
            app:layout_constraintTop_toBottomOf="@+id/tvLang"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDateStart"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/margin_large"
            tools:text="24/04/2018"
            android:textSize="@dimen/font_normal"
            android:textColor="@color/black"
            app:fontFamily="@font/sanspro_regular"
            app:layout_constraintLeft_toRightOf="@+id/tvDateStartTitle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvDateStartTitle"/>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <vn.zenity.betacineplex.helper.view.JustifiedTextView
        android:id="@+id/tvDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/nav_header_vertical_spacing"
        tools:text="Lấy bối cảnh 10 năm sau những sự kiện đã diễn ra ở phần 1, Jake Pentecost – truyền nhân duy nhất của huyền thoại Stacker Pentecost đã thực hiện lời hứa với cha mình, gia nhập nhóm người điều khiển Jaeger gồm có Lambert (Scott Eastwood) và Amara chỉ mới 15 tuổi (Cailee Spaeny), cùng nhau xây dựng nên một chiến tuyến chống lại Kaiju. Cuộc xung đột toàn cầu kéo dài giữa những quái vật âm mưu phá hủy thế giới và những robot khổng lồ do con người chế tạo ra nhằm đánh bại lũ quái vật hứa hẹn sẽ là một cuộc chiến cam go và kịch tính khi lũ quái vật giờ đây đã tiến hóa vượt bậc cả về thể lực và trí lực."
        android:textSize="@dimen/font_normal"
        android:textColor="@color/black"
        app:fontFamily="@font/sanspro_regular"
        android:layout_marginLeft="@dimen/padding_small"
        android:layout_marginRight="@dimen/padding_small"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/detailCl"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/titleKMll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/padding_small"
        app:layout_constraintTop_toBottomOf="@+id/tvDescription">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/promotionNew"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/promotion_new"
            android:textAllCaps="true"
            android:textColor="@color/textBlack"
            app:layout_constraintLeft_toLeftOf="parent"
            android:textSize="@dimen/font_normal"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <View
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:layout_marginEnd="@dimen/padding_small"
            android:background="@color/grayLine"
            app:layout_constraintBottom_toBottomOf="@+id/promotionNew"
            app:layout_constraintLeft_toRightOf="@+id/promotionNew"
            app:layout_constraintRight_toRightOf="@+id/viewAllPromotion"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/viewAllPromotion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_line_rounded_colorapp"
            android:paddingBottom="5dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:paddingTop="5dp"
            android:text="@string/All"
            app:layout_constraintTop_toTopOf="@+id/promotionNew"
            app:layout_constraintBottom_toBottomOf="@+id/promotionNew"
            android:textColor="@color/colorPrimaryDark"
            app:layout_constraintRight_toRightOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>