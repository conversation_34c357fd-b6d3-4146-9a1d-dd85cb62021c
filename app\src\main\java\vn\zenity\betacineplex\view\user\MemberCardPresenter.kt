package vn.zenity.betacineplex.view.user

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class MemberCardPresenter : MemberCardContractor.Presenter {
    private var disposable: Disposable? = null
    override fun getMemberCard(accountId: String) {
        view?.get()?.showLoading()
        disposable = APIClient.shared.accountAPI.getListCardMember(accountId).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let {
                            view?.get()?.showMemberCard(it)
                        }
                    }else {
                        it.Message?.let {
                            this.view?.get()?.showError(it, false)
                        }
                    }
                    this.view?.get()?.hideLoading()
                }, {
                    it.message?.let {
                        this.view?.get()?.showError(it, false)
                    }
                    this.view?.get()?.hideLoading()
                })
    }


    private var view: WeakReference<MemberCardContractor.View?>? = null
    override fun attachView(view: MemberCardContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
