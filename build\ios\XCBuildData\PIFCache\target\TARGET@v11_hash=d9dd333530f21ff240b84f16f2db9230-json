{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9848cf260bf926ea827f13b7ee9fdd4607", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986174f7e6357a6a0ad0e53e7fed52dcf7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc07b9acaa1f3e393fddd8bc11b9695e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981490a90658abf7b78abed2d5972a50b6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc07b9acaa1f3e393fddd8bc11b9695e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9861990e3086e080598f5c0306489ebd5b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98350d11f114a2fdac601dca5aee0f8f63", "guid": "bfdfe7dc352907fc980b868725387e98e7c60ca6bb669653cd2154d29ecf2ca6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819382a498e9b14b2806b43b7cfec1bdc", "guid": "bfdfe7dc352907fc980b868725387e9852ae99a566432e0ec69856f6fa586aba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e379b5806837710a7258bab619100f5", "guid": "bfdfe7dc352907fc980b868725387e98f05a02126843348a2ef7c13e6a41aec7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed0d1828d86902a6e0de10922b43cf69", "guid": "bfdfe7dc352907fc980b868725387e985baca97e184ff005edaf013bbd0c278a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e83314914c6b8afa6f3613092f0279f", "guid": "bfdfe7dc352907fc980b868725387e980f26de0850da3224c3582c5294b906ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98799caebeb6d36a64600eb6583a44bb83", "guid": "bfdfe7dc352907fc980b868725387e98e52d554c6fe6eee8f47f23ff133d1841"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f989d5b3d1cf70907b0441f332171a0", "guid": "bfdfe7dc352907fc980b868725387e98f5a2044f42decaf2a359ac5dd8835752"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828a1de62d8883a750bb6436d793c760b", "guid": "bfdfe7dc352907fc980b868725387e98959e81ff92a462d43ad025a4080d70a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989af64591bd1a9fafc148b870ab0a1678", "guid": "bfdfe7dc352907fc980b868725387e984c65851091bb82cf74b09e57ff1d1850", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982db125911d92d5e377c31bc39b025def", "guid": "bfdfe7dc352907fc980b868725387e98c34b51fb27ad9ed344f388554d4c53cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1ab92fbbb0b309bdc7095e9fdf154cf", "guid": "bfdfe7dc352907fc980b868725387e981c60025127c9211aa05407fa9a355e39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd93fbe14a3e87c998ff48dcea8291fd", "guid": "bfdfe7dc352907fc980b868725387e98baf188fa0605d7aeb48d4a0336679045", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98588f087c78360235717de755d5a5adb4", "guid": "bfdfe7dc352907fc980b868725387e98b2f2a882ba37de904b45723f5b2ccbeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eca88999367dd79d1ea38d6d6fd635e", "guid": "bfdfe7dc352907fc980b868725387e981862cd1a21da6a360d6d436bcde97b7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8460e54545a79fae3b70bce0dbe8366", "guid": "bfdfe7dc352907fc980b868725387e98f1c9376458165539bdd37482a0feaffb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5acc41df0a6a8feab7176afef069c3e", "guid": "bfdfe7dc352907fc980b868725387e9815f121f71d613550d1826ff7eb4e3549"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983eb7d8b86e6609e32d2db15dbd95467b", "guid": "bfdfe7dc352907fc980b868725387e98f21e05f3060c9696346593c9b1f22aec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fc7b71540293e592c8094aa1cb248a3", "guid": "bfdfe7dc352907fc980b868725387e98f81e0df1ff2f0598cc771f077514d505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988250f303182da9c450f3c0c205f39a74", "guid": "bfdfe7dc352907fc980b868725387e9852e7084cb7619880947e942fe3c07182"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986003d06536e2ba1ce44af39047199b5f", "guid": "bfdfe7dc352907fc980b868725387e9873929b9dc48dd8ce8830217ebc0a4c09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8de8881f7f52bfb44af0ddb0d32855f", "guid": "bfdfe7dc352907fc980b868725387e98410de756ab057f1b332a84bccf8f5293"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860d9a4e4f312c49b49533e6919ad29d7", "guid": "bfdfe7dc352907fc980b868725387e98e1af549d0c2d7b32a686b268075c03b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a71762966b9f33b34a06ee71a378b9b", "guid": "bfdfe7dc352907fc980b868725387e989dfb29048299c5f1120e6417a315a1ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd896f5d13d892b904ef21a672c9dc86", "guid": "bfdfe7dc352907fc980b868725387e98126c9a9fc8a5bac9819d57ec6c7cdaa7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba13e70bbc25701eb4a75f9b899479cd", "guid": "bfdfe7dc352907fc980b868725387e989edab445c434efc2a169ef73573ad843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844cba3fd70474a5c3c181feafc072578", "guid": "bfdfe7dc352907fc980b868725387e98c098e58d40ecdde2dccc3fb512767c7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801ffd53c5512d830f16d117a7328c8bb", "guid": "bfdfe7dc352907fc980b868725387e98421a27d75c2f70cf4f3ff26a29cc1540"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b24da4b69e9123682b2361b112191bf", "guid": "bfdfe7dc352907fc980b868725387e98762743a2827c540832d0136f0181841b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f067f32b96acafa5cf582dd2a63d6bf", "guid": "bfdfe7dc352907fc980b868725387e9848cb5482c8fb927d272e073e04a060c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ebaffe14c69988ec72cbc6b8ebb1b63", "guid": "bfdfe7dc352907fc980b868725387e98c709456007390570e9051ec16edc84c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaff72d931c3ea5eb78344b603f0d491", "guid": "bfdfe7dc352907fc980b868725387e98b580172ec5059b41e39aa497be36f89d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bf1f448ca4e8dcddaa2c62c6dc6d08b", "guid": "bfdfe7dc352907fc980b868725387e98e8f41f60cd152c1736bcba152b09f830"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808b55ee28f8224d9c3d3750498ec3978", "guid": "bfdfe7dc352907fc980b868725387e9828b84640abca61ebfe159c160f2ff45c"}], "guid": "bfdfe7dc352907fc980b868725387e9815a8186f8721ac20fb368691cfcf2dc5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9849f88c0a441ef21d5ed25032208d1e0c", "guid": "bfdfe7dc352907fc980b868725387e98783ebe0605e28e02801b5cf73517bd33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b49ea4cef444205705d9d56c03a5cdd", "guid": "bfdfe7dc352907fc980b868725387e989b5a53ac203f7c366183294d9470e282"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859c05c7500daf42f434dad9dcfd840d1", "guid": "bfdfe7dc352907fc980b868725387e9857d4a5326728ff64848317d300b805aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885e0406679ad4ced99d1c3fc5b5811ea", "guid": "bfdfe7dc352907fc980b868725387e98f5137ee41101ccba312fb3f747e1963f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884dc4b05ac28450a3a380ae326549f67", "guid": "bfdfe7dc352907fc980b868725387e98a234cd5c473385c48dff276c160ee576"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f59c0d4bc728be7314486e46c4e8a86", "guid": "bfdfe7dc352907fc980b868725387e988cf16b80ffeeb59de37c8213bf777cbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feb35e7b4057951d0558983d4685895c", "guid": "bfdfe7dc352907fc980b868725387e983f66b41738f7cf9b9003012683b4c4be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986daadedd4b2561388a2216db5d0e4cda", "guid": "bfdfe7dc352907fc980b868725387e9885d2f2472614527ed73cca7301f7e0da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2ad8a93389e9955ff9c22662e47fc78", "guid": "bfdfe7dc352907fc980b868725387e9812b793de85148eccb21ecfde44b6501c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff1f67096b7df987bdee35a4752a1f2", "guid": "bfdfe7dc352907fc980b868725387e98d20e760625ba6f38fc8a38f3b60b95d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989286821e2e62663b3a2811232544bae5", "guid": "bfdfe7dc352907fc980b868725387e98bf2890a5fa6f7777a847e2d5bb9924b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c935c88c4fd8a1970e12ed8d3161ee0", "guid": "bfdfe7dc352907fc980b868725387e983729a3eac8c6b04f03cf1867b79a0c8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c013b876d621d351388873dc34f603", "guid": "bfdfe7dc352907fc980b868725387e98272c5b0338092d0fbccb57a2e1cd8edd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841e3512d9fff715a48b8a092d47653d0", "guid": "bfdfe7dc352907fc980b868725387e982acb2924f7069716d4f15a1a45d12338"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f426834f3995251edd43587bb12ba5e0", "guid": "bfdfe7dc352907fc980b868725387e9852a7623512133c2483793dcb40bff8a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988de8eefd2151a1753ee33c748e2299b8", "guid": "bfdfe7dc352907fc980b868725387e98487211753544e1fb34b01830f7d1b9d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827941adf7b1d9a14f6d49e9a18fdabaa", "guid": "bfdfe7dc352907fc980b868725387e98b130dac954ea364728b8174b9e1440bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98288705a47fe52578c3e9ced90f3826d3", "guid": "bfdfe7dc352907fc980b868725387e984633e27265d8473876ea3e42a0d7da28"}], "guid": "bfdfe7dc352907fc980b868725387e988f884363bdd1c579853074e65dfce0da", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98ddfe7a266500507adf04ab23f5320912"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "guid": "bfdfe7dc352907fc980b868725387e987ab24bc63da14b6b22d3ba47ac2b5cde"}], "guid": "bfdfe7dc352907fc980b868725387e98776ba5bd468a0878613708ea7a8d3ab3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fbe8d97c1ad2782794e6915de9f13d73", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98113037a9bfb95bf3e817346bda39d081", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}