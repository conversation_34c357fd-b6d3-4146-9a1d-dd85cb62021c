import Foundation
import SignalRSwift
import SwiftSignal<PERSON>lient

/// <PERSON><PERSON><PERSON> vụ SignalR sử dụng cả hai package SignalRSwift và SwiftSignalRClient
/// giống như trong repo iOS gốc
@objc public class SignalRService: NSObject {
    // MARK: - Properties

    // Connection properties
    private var hubConnection: SignalRSwift.HubConnection?
    private var hub : SignalRSwift.HubProxy?
    private var connectionId: String?
    private var isConnected: Bool = false
    private var baseUrl: String = ""
    private var reconnectTimer: Timer?

    // Callback for connection state changes
    private var connectionStateCallback: ((Bool) -> Void)?

    // Callback for receiving messages
    private var messageCallback: ((String, String, Int, Int) -> Void)?

    // Enable logging
    private let enableLogging: Bool

    // MARK: - Initialization

    @objc public init(enableLogging: Bool = true) {
        self.enableLogging = enableLogging
        super.init()
        self.log("SignalRService initialized")
    }

    // MARK: - Logging

    private func log(_ message: String) {
        if enableLogging {
            print("SignalRService: \(message)")
        }
    }

    // MARK: - Connection Management

    /// Connect to a SignalR hub
    /// - Parameters:
    ///   - url: The URL of the SignalR hub
    ///   - headers: Optional headers to include in the connection
    ///   - completion: Callback with connection result
    @objc public func connect(url: String, headers: [String: String], completion: @escaping (Bool, String?) -> Void) {
        // Close existing connection if any
        disconnect()

        self.baseUrl = url
        self.log("Connecting to URL: \(url)")

        // Create a new hub connection
        hubConnection = HubConnection(withUrl: url)

        // Set up connection callbacks
        hubConnection?.started = { [weak self] in
            guard let self = self else { return }
            self.isConnected = true
            self.connectionId = self.hubConnection?.connectionId
            self.log("Connected with ID: \(self.connectionId ?? "unknown")")
            self.connectionStateCallback?(true)
            completion(true, nil)
        }

        hubConnection?.reconnecting = { [weak self] in
            guard let self = self else { return }
            self.log("Reconnecting...")
        }

        hubConnection?.reconnected = { [weak self] in
            guard let self = self else { return }
            self.log("Reconnected")
            self.isConnected = true
            self.connectionId = self.hubConnection?.connectionId
            self.connectionStateCallback?(true)
        }

        hubConnection?.closed = { [weak self] in
            guard let self = self else { return }
            self.log("Disconnected")
            self.isConnected = false
            self.connectionStateCallback?(false)
            self.startReconnectTimer()
        }

        hubConnection?.connectionSlow = { [weak self] in
            guard let self = self else { return }
            self.log("Connection slow")
        }

        hubConnection?.error = { [weak self] error in
            guard let self = self else { return }
          self.log("Connection error: \(error.localizedDescription ?? "Unknown error")")
            completion(false, error.localizedDescription ?? "Unknown error")
        }

        // Create hub proxy
        hub = hubConnection?.createHubProxy(hubName: "chooseSeatHub")

        // Register for broadcastMessage event
        hub?.on(eventName: "broadcastMessage") { [weak self] (args) in
            guard let self = self else { return }

            self.log("Received broadcastMessage: \(args)")

            let connectionId = args[safe: 0] as? String
            let showId = args[safe: 1] as? String
            let seatIndexStr = args[safe: 2] as? String
            let statusStr = args[safe: 3] as? String

            // Skip messages from this client
            if connectionId == self.connectionId {
                self.log("Skipping message from this client")
                return
            }

            // Parse seat index and status
            if let showId = showId,
               let seatIndexStr = seatIndexStr,
               let statusStr = statusStr,
               let seatIndex = Int(seatIndexStr),
               let status = Int(statusStr) {

                self.log("Parsed message: connectionId=\(connectionId ?? "unknown"), showId=\(showId), seatIndex=\(seatIndex), status=\(status)")

                // Call the message callback
                DispatchQueue.main.async {
                    self.messageCallback?(connectionId ?? "", showId, seatIndex, status)
                }
            } else {
                self.log("Failed to parse message: \(args)")
            }
        }

        // Add custom headers if provided
        if !headers.isEmpty {
            hubConnection?.headers = headers
        }

        // Start the connection
        hubConnection?.start()
    }

    /// Disconnect from the SignalR hub
    @objc public func disconnect() {
        log("Disconnecting")
        reconnectTimer?.invalidate()
        reconnectTimer = nil

        if hubConnection != nil {
            hubConnection?.stop()
            hubConnection = nil
            hub = nil
            isConnected = false
            connectionId = nil
            connectionStateCallback?(false)
        }
    }

    /// Start the reconnect timer
    private func startReconnectTimer() {
        log("Starting reconnect timer")

        // Hủy timer cũ nếu có
        reconnectTimer?.invalidate()

        // Chỉ kết nối lại nếu đã từng kết nối thành công trước đó
        if connectionId != nil {
            reconnectTimer = Timer.scheduledTimer(withTimeInterval: 5, repeats: false) { [weak self] _ in
                guard let self = self, !self.isConnected else { return }

                // Try to reconnect
                self.log("Attempting to reconnect")

                if let url = self.hubConnection?.url {
                    self.connect(url: url, headers: self.hubConnection?.headers ?? [:]) { success, error in
                        if success {
                            self.log("Reconnected successfully")
                        } else {
                            self.log("Reconnection failed: \(error ?? "Unknown error")")
                            // Try again later
                            self.startReconnectTimer()
                        }
                    }
                }
            }
        } else {
            log("Not starting reconnect timer because connection was never established")
        }
    }

    // MARK: - Message Handling

    /// Set the callback for connection state changes
    /// - Parameter callback: The callback to call when the connection state changes
    @objc public func setConnectionStateCallback(_ callback: @escaping (Bool) -> Void) {
        connectionStateCallback = callback
    }

    /// Set the callback for receiving messages
    /// - Parameter callback: The callback to call when a message is received
    @objc public func setMessageCallback(_ callback: @escaping (String, String, Int, Int) -> Void) {
        messageCallback = callback
    }

    /// Send a message to the SignalR hub
    /// - Parameters:
    ///   - connectionId: The connection ID
    ///   - showId: The show ID
    ///   - seatIndex: The seat index
    ///   - status: The seat status
    ///   - completion: Callback with result
    @objc public func sendMessage(connectionId: String, showId: String, seatIndex: Int, status: Int, completion: @escaping (Error?) -> Void) {
        guard isConnected, let hub = hub else {
            completion(NSError(domain: "SignalRService", code: -1, userInfo: [NSLocalizedDescriptionKey: "Not connected"]))
            return
        }

        log("Sending message: connectionId=\(connectionId), showId=\(showId), seatIndex=\(seatIndex), status=\(status)")

        do {
            try hub.invoke(method: "sendMessage", withArgs: [connectionId, showId, "\(seatIndex)", "\(status)"]) { response, error in
                if let error = error {
                    self.log("Error sending message: \(error.localizedDescription)")
                    completion(error)
                } else {
                    self.log("Message sent successfully")
                    completion(nil)
                }
            }
        } catch {
            log("Error invoking method: \(error.localizedDescription)")
            completion(error)
        }
    }

    /// Get the connection ID
    /// - Returns: The connection ID
    @objc public func getConnectionId() -> String? {
        return connectionId
    }

    /// Check if connected
    /// - Returns: True if connected, false otherwise
    @objc public func isConnectedStatus() -> Bool {
        return isConnected
    }
}

// MARK: - Array Extension

extension Array {
    subscript(safe index: Int) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}
