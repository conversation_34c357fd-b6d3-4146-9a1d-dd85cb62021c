package vn.zenity.betacineplex.helper.extension

import KeyboardHelper
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.app.Activity
import android.graphics.Color
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.MotionEvent
import android.view.View
import android.view.View.GONE
import android.view.ViewGroup
import android.view.animation.AlphaAnimation
import android.webkit.WebView
import android.widget.EditText
import android.widget.LinearLayout
import androidx.annotation.UiThread
import androidx.appcompat.widget.AppCompatTextView
import com.google.android.material.tabs.TabLayout
import io.reactivex.Observable
import io.reactivex.subjects.PublishSubject
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.global.Constant
import java.lang.reflect.Field
import java.time.LocalDate
import java.util.*


/**
 * Created by tranduc on 1/5/18.
 */
fun View.visible(isVisible: Boolean = true) {
    visibility = if (isVisible) View.VISIBLE else View.GONE
}

fun View.gone() {
    visibility = View.GONE
}

fun View.invisible() {
    visibility = View.INVISIBLE
}

fun View.isVisble(): Boolean = this.visibility == View.VISIBLE

@UiThread
inline fun View.fadeOut() {
    fadeOut(500)
}

@UiThread
inline fun View.fadeIn() {
    fadeIn(500)
}

@UiThread
inline fun View.fadeIn(duration: Long) {
    this.clearAnimation()
    val anim = AlphaAnimation(this.alpha, 1.0f)
    anim.duration = duration
    this.startAnimation(anim)
}

@UiThread
inline fun View.fadeOut(duration: Long) {
    this.clearAnimation()
    val anim = AlphaAnimation(this.alpha, 0.0f)
    anim.duration = duration
    this.startAnimation(anim)
}

inline fun View.children(): List<View> {
    if (this !is ViewGroup) return listOf()
    else {
        return children
    }
}

fun View.removeChildAt(position: Int): Boolean {
    if (this !is ViewGroup) return false
    else {
        if (position >= children.size) return false
        this.removeViewAt(position)
        return true
    }
}

fun View.addChild(view: View) {
    if (this is ViewGroup) {
        this.addView(view)
    }
}


fun View.setupHiddenKeyboard(activity: Activity) {
    // Set up touch listener for non-text box views to hide keyboard.
    if (this !is EditText) {
        this.setOnTouchListener { _, motion ->
            if (motion.action == MotionEvent.ACTION_UP && (motion.eventTime - motion.downTime) <= 200) {
                KeyboardHelper.shared.hideSoftKeyboard(activity)
                false
            } else {
//                motion.action == MotionEvent.ACTION_UP
                false
            }
        }
    }

    //If a layout container, iterate over children and seed recursion.
    if (this is ViewGroup) {
        for (i in 0 until this.childCount) {
            val innerView = this.getChildAt(i)
            innerView.setupHiddenKeyboard(activity)
        }
    }
}

fun EditText.showETError(error: String) {
    this.error = error
    this.requestFocus()
}

fun View.goneBottomToTop() {
    this.animate().translationY(0f - this.height.toFloat()).alpha(0.0f).setDuration(300)
        .setListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                gone()
            }
        })
}

fun View.visibleTopToBottom() {
    visible()
    this.animate().translationY(0f).alpha(1.0f).setDuration(300)
        .setListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                visible()
            }
        })
}

fun View.visibleSlideFromTop() {
    this.animate().translationY(-this.height.toFloat()).setDuration(0)
        .setListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                visible()
                animate().translationY(0.0f).duration = 300
            }
        })
}

fun View.hideSlideToTop() {
    this.animate().translationY(-this.height.toFloat()).setDuration(300)
        .setListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                gone()
            }
        })
}

fun TabLayout.setUpIndicatorWidth() {
    val tabLayoutClass = this.javaClass
    var tabStrip: Field? = null
    try {
        tabStrip = tabLayoutClass.getDeclaredField("mTabStrip")
        tabStrip!!.isAccessible = true
    } catch (e: NoSuchFieldException) {
        e.printStackTrace()
    }

    var layout: LinearLayout? = null
    try {
        if (tabStrip != null) {
            layout = tabStrip.get(this) as LinearLayout
        }
        if (layout != null) {
            for (i in 0 until layout.childCount) {
                val child = layout.getChildAt(i)
                val params =
                    LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.MATCH_PARENT, 1f)
                val dp8 = dp2Px(this.context, 2)
//                val padding = if (child.width > 0) ((child.width - dp8) / 2f).toInt() else dp8
                child.setPadding(dp8, 0, dp8, 0)
                params.marginStart = 0
                params.marginEnd = 0
                child.layoutParams = params
                child.invalidate()
            }
        }
    } catch (e: IllegalAccessException) {
        e.printStackTrace()
    }
}

fun WebView.loadBetaHtml(
    content: String,
    zoomable: Boolean = false,
    backgroundColor: String = "#F3F3F3",
    padding: String = "10px"
) {
    this.settings.textZoom = 300
    this.settings.loadsImagesAutomatically = true
    this.settings.useWideViewPort = true
    this.settings.javaScriptEnabled = true
    this.settings.loadWithOverviewMode = true
    if (zoomable) {
        this.settings.textZoom = 100
        this.settings.useWideViewPort = true
        this.settings.loadWithOverviewMode = true
        this.settings.builtInZoomControls = true
        this.settings.setSupportZoom(true)
    }
    this.loadDataWithBaseURL(
        BuildConfig.BASE_URL,
        content.toHtml(backgroundColor = backgroundColor, padding = padding),
        "text/html",
        "utf-8",
        null
    )
}

fun View.click(click: (View) -> Unit) {
    setOnClickListener {
        click(it)
    }
}

fun List<View>.click(click: (View) -> Unit) {
    forEach {
        it.click(click)
    }
}

fun EditText.textChanges(): Observable<String> {
    val subject: PublishSubject<String> = PublishSubject.create()
    return subject.doOnSubscribe {
        this.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(p0: Editable?) {

            }

            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence, p1: Int, p2: Int, p3: Int) {
                subject.onNext(p0.toString())
            }

        })
        this.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewDetachedFromWindow(p0: View) {
                subject.onComplete()
            }

            override fun onViewAttachedToWindow(p0: View) {
            }

        })
    }
}


fun AppCompatTextView.spannable(
    content: String, highlight: String, color: Int?, highlightColor: Int?
) {
    val sb = SpannableString(content)
    sb.setSpan(
        ForegroundColorSpan(color ?: Color.RED), 0, content.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    sb.setSpan(
        ForegroundColorSpan(highlightColor ?: Color.BLUE),
        content.indexOf(highlight),
        content.indexOf(highlight) + highlight.length,
        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    text = sb
}

fun AppCompatTextView.rewardPoint(point: Int, expiredOn: String) {
    if (point == 0 || expiredOn.isEmpty()) {
        visibility = GONE
        return
    }
    val content = R.string.remain_point.getStringFormat(point, expiredOn.dateConvertFormat(Constant.DateFormat.defaultFull, Constant.DateFormat.dateSavis))
    spannable(content, "$point", R.color.textff3377.getColor(), R.color.text03599d.getColor())
}