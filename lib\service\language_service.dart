import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '/constants/index.dart';

/// Service to manage language settings across the app
class LanguageService {
  static final LanguageService _instance = LanguageService._internal();

  factory LanguageService() {
    return _instance;
  }

  LanguageService._internal();

  static const String _languageKey = 'app_language';

  /// Get the current language code (en or vi)
  Future<String> getCurrentLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_languageKey) ?? 'vi'; // Default to Vietnamese
  }

  /// Check if the current language is English
  Future<bool> isEnglish() async {
    final currentLang = await getCurrentLanguage();
    return currentLang == 'en';
  }

  /// Set the language and update the app locale
  Future<void> setLanguage(BuildContext context, String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);

    // Update the app locale
    await context.setLocale(Locale(languageCode));
  }

  /// Get the appropriate field value based on current language
  /// For models with fields ending with "_F" for English
  String getLocalizedField(String? defaultField, String? englishField) {
    final context = rootNavigatorKey.currentContext;
    if (context == null) return defaultField ?? '';

    final isEnglish = context.locale.languageCode == 'en';
    return isEnglish ? (englishField ?? defaultField ?? '') : (defaultField ?? '');
  }

  /// Get the language parameter for API calls
  Future<String> getLanguageParam() async {
    return await isEnglish() ? 'en' : 'vi';
  }

  /// Get the language-specific URL suffix for API calls
  Future<String> getLanguageUrlSuffix() async {
    return await isEnglish() ? '/en' : '/vi';
  }
}

// Use the existing rootNavigatorKey from constants/src/routes.dart
