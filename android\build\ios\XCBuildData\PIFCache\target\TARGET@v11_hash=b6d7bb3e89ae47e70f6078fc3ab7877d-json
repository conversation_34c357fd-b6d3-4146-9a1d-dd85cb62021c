{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d261391d15b566eea2a4d92726a7543e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981746d325bcd17884bf4b3c8937b9839f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988788409978b3b999e5b491a287b7eff9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981bc6c3a42b9834c27ffbe8b90a3178d1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988788409978b3b999e5b491a287b7eff9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983f8ed0dc2bc6d226197bf93c9249569b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983bca6a42889b369121ecac7d7cf659c3", "guid": "bfdfe7dc352907fc980b868725387e98ac87d6c6e1fbab4e30f5803f88eebcce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839a6399b4b06c844be472b79129e402c", "guid": "bfdfe7dc352907fc980b868725387e983ae8816ebfcc77f8beef0f4d0798323b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815d6fba893720a0e1335d605920a6190", "guid": "bfdfe7dc352907fc980b868725387e98e968d8e30dd2ed56acbdd36a4b523374", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e6391b64f2b15bc64ca0ecb31ef8dc4", "guid": "bfdfe7dc352907fc980b868725387e98dc66ab2bffe24afb4872c14f02d59ded", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b99454e6704caa15e1b6eeada620368", "guid": "bfdfe7dc352907fc980b868725387e989975474ad0ab44733a1125490276b310", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7ce6a5abe7f3a59282baf0e1c35b6cc", "guid": "bfdfe7dc352907fc980b868725387e9898d4c3e71f365d10f32675af8f8f6b11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7535df2bc389cf1d5c384ae074c69ef", "guid": "bfdfe7dc352907fc980b868725387e982919dd233fae8ad4198f75f7afcf94b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb3fd641186b91fcb1e5266552d046f2", "guid": "bfdfe7dc352907fc980b868725387e987037409e1eb2367474b4f016b5716172", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad17aee0b71dfa278d0dd9b3b0db6f21", "guid": "bfdfe7dc352907fc980b868725387e98c649e6307e4f036cf06c73cd8ab10a44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f719bceb2dba101185f701914673568", "guid": "bfdfe7dc352907fc980b868725387e98bb5177e15c5d300753830087b8fdaf6c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878b5c50c1614ac3942e8ad477afb559a", "guid": "bfdfe7dc352907fc980b868725387e988f1b2aa1675f299b4a2c67173fb2ed7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98465ef542d8b39e4c5e58333491af93be", "guid": "bfdfe7dc352907fc980b868725387e9898292be656967dc58f30d96954c0b3a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893ea2d3c9522db9e8d12fa7e743262cc", "guid": "bfdfe7dc352907fc980b868725387e98475ead55e2911c5c3373bc5313d32c8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98defed71a42be925a4f633b2fa6cb26c7", "guid": "bfdfe7dc352907fc980b868725387e98bc5c8bb8afc01b55fec9bf049ebe7f60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2ac5675680fa3c568173c9e1b1bf429", "guid": "bfdfe7dc352907fc980b868725387e989b6a47a4681875ef3b793ac3fe413e65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ca198be0ae48defeb472aabda9730ba", "guid": "bfdfe7dc352907fc980b868725387e98ab0f534cdf9a972a8c2238e0044f9a60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1ebd0365e193f5b7b7305c98f7e32ff", "guid": "bfdfe7dc352907fc980b868725387e98b369714cbe26cc670fa88d344098e012", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987201d7bff39055b9ba010cafeb8fa0db", "guid": "bfdfe7dc352907fc980b868725387e9887f61cd32b545c7cfce1f0b8323b8c79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be978942147007b2b7b69894a77d8e40", "guid": "bfdfe7dc352907fc980b868725387e98af69aeb35196a6f1518e513ab777ee9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af274d3bce5511363abdbe711151fe9b", "guid": "bfdfe7dc352907fc980b868725387e98b1f479a1d79830a1628e45ca607a4bd2", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff3390308389f5b863346636a84e74f8", "guid": "bfdfe7dc352907fc980b868725387e985460c4e2223c8b0149887b7233dded8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98497b8fabdbad9d3f7e2b359bb60ceb82", "guid": "bfdfe7dc352907fc980b868725387e980b027638fd5117fcbd21859324688766", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987426dd2df160f273857621798585ecbc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984aac0320dc402cf49a50e12343c5b2ff", "guid": "bfdfe7dc352907fc980b868725387e98acb009b3b16ac5cd6df94e6cbb0dafe8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989077a7885ee413490ef7cf46e8b11e5f", "guid": "bfdfe7dc352907fc980b868725387e9899c227519a15584e460c4f2c16339220"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98babb958d0a05ba280609f6d9772810fd", "guid": "bfdfe7dc352907fc980b868725387e98d8eeaa09a6e9ad5dbf191e62ad95a908"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811ac78869a79e7b85f2ecf6db5ce698e", "guid": "bfdfe7dc352907fc980b868725387e98995c787ef4b3b243581b6b2e0ca32565"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804a7e418268858a0f1efbef1ba1cf23b", "guid": "bfdfe7dc352907fc980b868725387e983d38a745c4b01e1bae67943032885115"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984301a8626335180ec878dd3b0fde82ba", "guid": "bfdfe7dc352907fc980b868725387e98501c27885dcbae6400226137c968865f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c54bb7c143e57fc103b362b55857fe2a", "guid": "bfdfe7dc352907fc980b868725387e98ee0e11e1aaaea5a716d3a38402d328ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1746545036417a8b2fddaed8990a89", "guid": "bfdfe7dc352907fc980b868725387e9821200dae59620a97062abf51131a33b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832b34dd402cd167c11df069a4996d1f0", "guid": "bfdfe7dc352907fc980b868725387e9866324c7b4d5f99ad54f7fefcaf00f418"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856c2c3d77ecb6ea8ad5e1d0fb8633741", "guid": "bfdfe7dc352907fc980b868725387e987f6367dbcf5a75d6e644224eb4b77617"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b64e247dd3350bad79c59af9d601b4ab", "guid": "bfdfe7dc352907fc980b868725387e98739118dedfa90db4ce2f38575041266c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a07c8e30f1f191d48888d7628fdbc678", "guid": "bfdfe7dc352907fc980b868725387e98c6bbed0b9f867a18706daa8360370a98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838dc9c6f44bde185c1c6d55db74f8aef", "guid": "bfdfe7dc352907fc980b868725387e981d1622f82d7c34183db285ec96f22384"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b5c23870953d3f1893f05bac005dd14", "guid": "bfdfe7dc352907fc980b868725387e9875d8f4776df49f42a127195cddf4ecb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4cf85af3a68292f5200a2f5b06f859f", "guid": "bfdfe7dc352907fc980b868725387e98b059c689f82a0c257deb0afa707efbbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ed653041f5428d38ba61ea11d3250db", "guid": "bfdfe7dc352907fc980b868725387e9876f46326aea7aba79537da4ea1fb9e38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f86761ff5818f6075451f936e938a73", "guid": "bfdfe7dc352907fc980b868725387e98bcdb4d35d1a6a52d07e89a19edbaad63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d652d41063b66269150fd3dc47722b0", "guid": "bfdfe7dc352907fc980b868725387e98b6e416d629faa62591bfc1c1f149c709"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba10b4e94817a514f781dba73efaf556", "guid": "bfdfe7dc352907fc980b868725387e984f07ee64793da1bac5ef4978891fc3c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3377e5d50146da59117ac6d1b41673e", "guid": "bfdfe7dc352907fc980b868725387e986cde1e9b48b978eee5c73c0a4e92a8d0"}], "guid": "bfdfe7dc352907fc980b868725387e98647622d66c3121257d4434107fcfc92c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e987384c18b804adb22d6aa269b4f45eb31"}], "guid": "bfdfe7dc352907fc980b868725387e98e54647a459fc21ad3d7d29835964986e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a95a116c17385d0c4909f1efbbfec441", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98b2dea7614a3e57622cf781c33df03be9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}