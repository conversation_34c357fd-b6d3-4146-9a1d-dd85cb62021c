import 'dart:async';
import 'package:flutter/services.dart';
import '../constants/payment_constants.dart';

/// Service để handle payment status từ external apps
/// Tương ứng với BroadcastReceiver trong Android repo
class PaymentStatusService {
  static const MethodChannel _channel = MethodChannel('payment_status_channel');
  static const EventChannel _eventChannel = EventChannel('payment_status_events');
  
  static PaymentStatusService? _instance;
  static PaymentStatusService get instance => _instance ??= PaymentStatusService._();
  
  PaymentStatusService._();
  
  StreamSubscription<dynamic>? _subscription;
  Function(PaymentStatusResult)? _onPaymentStatusReceived;
  
  /// Initialize payment status listener
  void initialize() {
    _subscription = _eventChannel.receiveBroadcastStream().listen(
      _handlePaymentStatus,
      onError: (error) {
        print('Payment status error: $error');
      },
    );
  }
  
  /// Set callback for payment status updates
  void setPaymentStatusCallback(Function(PaymentStatusResult) callback) {
    _onPaymentStatusReceived = callback;
  }
  
  /// Handle payment status from platform
  void _handlePaymentStatus(dynamic data) {
    if (data is Map<String, dynamic>) {
      final result = PaymentStatusResult.fromMap(data);
      _onPaymentStatusReceived?.call(result);
    }
  }
  
  /// Register for MoMo payment status
  Future<void> registerMomoPayment() async {
    try {
      await _channel.invokeMethod('registerMomoPayment');
    } catch (e) {
      print('Error registering MoMo payment: $e');
    }
  }
  
  /// Register for ZaloPay payment status
  Future<void> registerZaloPayPayment() async {
    try {
      await _channel.invokeMethod('registerZaloPayPayment');
    } catch (e) {
      print('Error registering ZaloPay payment: $e');
    }
  }
  
  /// Unregister payment listeners
  Future<void> unregister() async {
    try {
      await _channel.invokeMethod('unregisterPayment');
    } catch (e) {
      print('Error unregistering payment: $e');
    }
  }
  
  /// Dispose service
  void dispose() {
    _subscription?.cancel();
    _subscription = null;
    _onPaymentStatusReceived = null;
  }
}

/// Payment status result từ external apps
class PaymentStatusResult {
  final PaymentProvider provider;
  final Map<String, String> data;
  
  PaymentStatusResult({
    required this.provider,
    required this.data,
  });
  
  factory PaymentStatusResult.fromMap(Map<String, dynamic> map) {
    final providerStr = map['provider'] as String? ?? '';
    PaymentProvider provider;
    
    switch (providerStr) {
      case 'momo':
        provider = PaymentProvider.momo;
        break;
      case 'zalopay':
        provider = PaymentProvider.zaloPay;
        break;
      case 'airpay':
        provider = PaymentProvider.airPay;
        break;
      default:
        provider = PaymentProvider.unknown;
    }
    
    final data = Map<String, String>.from(map['data'] as Map? ?? {});
    
    return PaymentStatusResult(
      provider: provider,
      data: data,
    );
  }
  
  /// Get MoMo specific data
  MomoPaymentData? get momoData {
    if (provider != PaymentProvider.momo) return null;
    return MomoPaymentData.fromMap(data);
  }
  
  /// Get ZaloPay specific data
  ZaloPayPaymentData? get zaloPayData {
    if (provider != PaymentProvider.zaloPay) return null;
    return ZaloPayPaymentData.fromMap(data);
  }
  
  /// Get AirPay specific data
  AirPayPaymentData? get airPayData {
    if (provider != PaymentProvider.airPay) return null;
    return AirPayPaymentData.fromMap(data);
  }
}

enum PaymentProvider {
  momo,
  zaloPay,
  airPay,
  unknown,
}

/// MoMo payment data
class MomoPaymentData {
  final String? orderId;
  final String? resultCode;
  final String? requestId;
  final String? transId;
  final String? message;
  final String? responseTime;
  final String? payType;
  final String? extraData;
  final String? partnerCode;
  
  MomoPaymentData({
    this.orderId,
    this.resultCode,
    this.requestId,
    this.transId,
    this.message,
    this.responseTime,
    this.payType,
    this.extraData,
    this.partnerCode,
  });
  
  factory MomoPaymentData.fromMap(Map<String, String> map) {
    return MomoPaymentData(
      orderId: map[MomoConstants.orderId],
      resultCode: map[MomoConstants.resultCode],
      requestId: map[MomoConstants.requestId],
      transId: map[MomoConstants.transId],
      message: map[MomoConstants.message],
      responseTime: map[MomoConstants.responseTime],
      payType: map[MomoConstants.payType],
      extraData: map[MomoConstants.extraData],
      partnerCode: map[MomoConstants.partnerCode],
    );
  }
  
  /// Generate JavaScript call for MoMo status check
  String generateJavaScriptCall() {
    return "${JavaScriptMethods.checkMomoTransactionStatus}('$orderId', '$resultCode', '$requestId', '$transId', '$message', '$responseTime', '$payType', '$extraData', '$partnerCode');";
  }
}

/// ZaloPay payment data
class ZaloPayPaymentData {
  final String? appId;
  final String? appTransId;
  final String? pmcId;
  final String? bankCode;
  final String? amount;
  final String? dAmount;
  final String? appStatus;
  final String? checkSum;
  
  ZaloPayPaymentData({
    this.appId,
    this.appTransId,
    this.pmcId,
    this.bankCode,
    this.amount,
    this.dAmount,
    this.appStatus,
    this.checkSum,
  });
  
  factory ZaloPayPaymentData.fromMap(Map<String, String> map) {
    return ZaloPayPaymentData(
      appId: map[ZaloPayConstants.appId],
      appTransId: map[ZaloPayConstants.appTransId],
      pmcId: map[ZaloPayConstants.pmcId],
      bankCode: map[ZaloPayConstants.bankCode],
      amount: map[ZaloPayConstants.amount],
      dAmount: map[ZaloPayConstants.dAmount],
      appStatus: map[ZaloPayConstants.appStatus],
      checkSum: map[ZaloPayConstants.checkSum],
    );
  }
  
  /// Generate JavaScript call for ZaloPay status check
  String generateJavaScriptCall() {
    return "${JavaScriptMethods.checkZaloPayTransactionStatus}('$appId', '$appTransId', '$pmcId', '$bankCode', '$amount', '$dAmount', '$appStatus', '$checkSum');";
  }
}

/// AirPay payment data
class AirPayPaymentData {
  final String? orderId;
  
  AirPayPaymentData({this.orderId});
  
  factory AirPayPaymentData.fromMap(Map<String, String> map) {
    return AirPayPaymentData(
      orderId: map[AirPayConstants.orderId],
    );
  }
  
  /// Generate JavaScript call for AirPay status check
  String generateJavaScriptCall() {
    return "${JavaScriptMethods.checkShopeePayTransactionStatus}('$orderId');";
  }
}
