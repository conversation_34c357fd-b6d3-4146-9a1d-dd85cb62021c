package vn.zenity.betacineplex.Manager.Network

import io.reactivex.Single
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query
import vn.zenity.betacineplex.model.*

/**
 * Created by tinhvv on 4/8/18.
 */
interface TransactionAPI {
    @GET("/api/v1/erp/transaction-history/{id}") // Lịch sử giao dịch của user
    fun getPaymentHistory(@Path("id") accountId: String, @Query("stringFilter") filter: String = ""): Single<DDKCReponse<List<PaymentHistory>>>

    @GET("api/v2/erp/transaction-history/{accountId}/{tranId}")
    fun getPaymentHistoryDetail(@Path("accountId") accountId: String, @Path("tranId") tranId: String): Single<DDKCReponse<PaymentHistoryDetailModel>>
}