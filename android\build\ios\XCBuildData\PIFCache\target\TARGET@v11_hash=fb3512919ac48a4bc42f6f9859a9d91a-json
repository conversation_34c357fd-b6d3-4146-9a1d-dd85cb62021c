{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98869b51186bc821cb6ac8ff727347ee4d", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseCoreInternal", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseCoreInternal", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "FirebaseCoreInternal_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98280501cac83ee8fc7d3fca000ddb10cb", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f362a276f4286a9d84b7a8df431f46c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseCoreInternal", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseCoreInternal", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "PRODUCT_NAME": "FirebaseCoreInternal_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98156e77fb6a4c0dd36ef949d2f94ad21a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f362a276f4286a9d84b7a8df431f46c", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseCoreInternal", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseCoreInternal", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "PRODUCT_NAME": "FirebaseCoreInternal_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9841e24dd4eac63ca8d8aae9bddb23f2cf", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e989b74ff666472bded3a900f25516c9148", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98dbc9a45d684a60cb2eac2507778615a9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d6db300760a068d613fa52fd6f370d", "guid": "bfdfe7dc352907fc980b868725387e98c9b6a9b86befd2500d8ed3f4ad8532cb"}], "guid": "bfdfe7dc352907fc980b868725387e980056168ff923b1fdc83c842445ecaa0d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98c4db3ee10fd3aea38cd0fd6d5693c776", "name": "FirebaseCoreInternal_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}