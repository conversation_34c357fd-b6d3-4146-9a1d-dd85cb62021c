{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984fc5368535943567a6061e2317422930", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c11b0b6520cef1caeacc71bd0809e415", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833fbf11c00f7a2c7ec1616927f3b2831", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98823520eb8f9260e32f29b429795849e1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833fbf11c00f7a2c7ec1616927f3b2831", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ccd2444ab7dea9c58e07627c8bbc0c3f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ae5381c860a00d46f99ea89dd71fe959", "guid": "bfdfe7dc352907fc980b868725387e9856c11f83ee1d90051ea2ee867c5f6d7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f83a8905b6056801a0de79309e994f2f", "guid": "bfdfe7dc352907fc980b868725387e9822132d2ba98626d0147128f362e84746"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bd9340ff69d5513fc8a3252a6e61d39", "guid": "bfdfe7dc352907fc980b868725387e984a4743cc6381b4a2e7b5827b96e8c6b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fd883ccf1b66f619bbae5255c9cc822", "guid": "bfdfe7dc352907fc980b868725387e985efc4f012e1f8b49438d45879c3aec5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d959adb455821d132d65ce35801f4d0c", "guid": "bfdfe7dc352907fc980b868725387e98309c7e85ff31a510e0122b72824b3720"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98939163e97c57ba9a746e8577269b8c97", "guid": "bfdfe7dc352907fc980b868725387e9807c6850406c9a7ced0a52e2c1afefd5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870f316d77a0bfc96c57421d0de2ef81d", "guid": "bfdfe7dc352907fc980b868725387e98316f08844052870170167dbffba1ffc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4050ced09e9ece2165716d25422438f", "guid": "bfdfe7dc352907fc980b868725387e98535781677e4665c01b7506038d933b8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a51fcb65e790c488a7f267a974c79a4", "guid": "bfdfe7dc352907fc980b868725387e9821fd01d2625e20fe48b7015c3794e601"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c906486056a9bdcb134da19a3e546cd", "guid": "bfdfe7dc352907fc980b868725387e98615e37df16d26def08a3a2f8fea7f08e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98724c6b0f30b3c53662c58de02812d7fc", "guid": "bfdfe7dc352907fc980b868725387e98f823952845a9d30f6735b441deda52e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dbb5d55dac4f6e66c592dabddf6cc3d", "guid": "bfdfe7dc352907fc980b868725387e9872bcb9f3cb11d4d13d0499d3212c62d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d5e8c8a03068a76e30e3e9f49c1c5a0", "guid": "bfdfe7dc352907fc980b868725387e98af9e05993bc38b2d0e345abb845b32ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1679736483668fe46353431ad028b17", "guid": "bfdfe7dc352907fc980b868725387e98a27708178b9d0938ddefe4faa6b0f394"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac731ab082d58c48b393635cc588406f", "guid": "bfdfe7dc352907fc980b868725387e98759ae033ff3a6358c8112f40a7202d09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874985e409a2b596ed33564aae9013912", "guid": "bfdfe7dc352907fc980b868725387e988858a5b9d2303ab7e67b7fa607928b7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884d90c38ca08d272994717bb3f9df745", "guid": "bfdfe7dc352907fc980b868725387e9848b65db6b6aa7b56129e851ab446004b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9bb7927e193307526aa6842456ac73e", "guid": "bfdfe7dc352907fc980b868725387e9870b73a8c4599eee68ded0888cfcc8ce4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980baedf3fdfc11c1fa9d01d84a8c78565", "guid": "bfdfe7dc352907fc980b868725387e98102efdfc3e530f12d5ce858b11befa15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd4e9abb16fcba0b10d05b1738b32cae", "guid": "bfdfe7dc352907fc980b868725387e98a32e36b95bc914c45ea736460b9b6ed0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c1a2b4c87ece24f5085c421bf89a7c1", "guid": "bfdfe7dc352907fc980b868725387e9865f63d98f4392b75f43b79d6224af515"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fe24cb5598b2e8066b508a44f80d559", "guid": "bfdfe7dc352907fc980b868725387e98b3c855ae4990578c9abb79668a99fb0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d6c9acc64cf3c47e5582a9956746292", "guid": "bfdfe7dc352907fc980b868725387e98ec7473c0612c105f4aedfe4ec6311fc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e76ae7f241767d5552c3f1d0cb72277a", "guid": "bfdfe7dc352907fc980b868725387e982b6ca6c233ffbb0fa24e97923039fd5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98600cc841574f8f111dd3bea2d29420ef", "guid": "bfdfe7dc352907fc980b868725387e986fe9a38febba505549067cadcb1a0d38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afb24e8a10afe339715e7e1793025679", "guid": "bfdfe7dc352907fc980b868725387e984ac3be319482ec4560773e7e1afb7e18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989abb68f5423bac4bc235807bc9b6e5c3", "guid": "bfdfe7dc352907fc980b868725387e98067eec386e70f6732fbe814bae61d652"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859324900ad3b16a29597bd076718defc", "guid": "bfdfe7dc352907fc980b868725387e98f1fd0f8ea083d2b3b2c1b61820eb74a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856eaefeb9333233a5fdce0e7f4e5de96", "guid": "bfdfe7dc352907fc980b868725387e984cc359f69a633d43bb1688918998c1e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cac6b7f9578320a18eab668fbce90d02", "guid": "bfdfe7dc352907fc980b868725387e9863aecd8db90d1876cf9a395d775da7d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876998274434473abc2c6853f014e8cb8", "guid": "bfdfe7dc352907fc980b868725387e98d34130fe5a63d1d8c81a15cdab0e4dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98609cfd6aa724665b2ad62df15f56b5a9", "guid": "bfdfe7dc352907fc980b868725387e984f026833d0a5e7c20157f243d69fa919", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fd7a35ea7dcfcd3e8ee715b0346cdd5", "guid": "bfdfe7dc352907fc980b868725387e9858d0c56dd842c1584eeebb386f5dbcf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aba8a2dfe7ce0ca58f69b4ac70f2d112", "guid": "bfdfe7dc352907fc980b868725387e98300b40e124837e26ad7330df5f066151"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b1e916b99d0de53236dee004ce38f40", "guid": "bfdfe7dc352907fc980b868725387e9884c3da3657cbbc866202ee2cafe02bc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983de64f6dbab104a78552eb0b35a5cc76", "guid": "bfdfe7dc352907fc980b868725387e988cfbdea27c62100e0edf2e17e1090f77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5e7766291ba72ca5ee26d791d773ea5", "guid": "bfdfe7dc352907fc980b868725387e98a88196024bd078a888b971289ef45ec2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae8e3584d32cb7183ea507dbdc4128c5", "guid": "bfdfe7dc352907fc980b868725387e986640089a0e384cee54bee427a385712a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce37e39334dd55df6a0d1116d137e910", "guid": "bfdfe7dc352907fc980b868725387e989b52b406ca5bacff4eb06e109de4e6d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e890da22a1555f6fb8c0116c7ef2e93a", "guid": "bfdfe7dc352907fc980b868725387e988f135c999d4d5235d06e648002754857"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844ba5712e6c3f7b40dfacf924b47bb1b", "guid": "bfdfe7dc352907fc980b868725387e98e84037d0b248b2408ace9d5e76e2b553"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb9e99c30d5f6ea7522aeabe30a6e8a4", "guid": "bfdfe7dc352907fc980b868725387e98c381c99b1bc983522d1968b375aea6ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fa09bc603af8bcc73dfc1d6aa65fbad", "guid": "bfdfe7dc352907fc980b868725387e98cd70865648c6ffd2f86b61e745c4ebb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7a2c423cdba60c75d88a442d83ecc87", "guid": "bfdfe7dc352907fc980b868725387e98cf066fc5720f42bece9f72bd6757b475"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f6f1aab6ea79158395fa998e54ccb8a", "guid": "bfdfe7dc352907fc980b868725387e98a6b9ff5d69e13b1096d1a9ab5b008fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c53ed063daa824d59bed4ffa7e4169", "guid": "bfdfe7dc352907fc980b868725387e9885c5217185be2432ccedda9d15e88aa0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc3e5de3b16285637a18756dc300b616", "guid": "bfdfe7dc352907fc980b868725387e98a8954aa9b5e7da4135824d3ece5eb51a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98853b80f4845fed00028b0a04c2adcad4", "guid": "bfdfe7dc352907fc980b868725387e980119b7c4a84411a6a034506c53334af1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980aea58ba8309ac71b69d7b5321f3d1a7", "guid": "bfdfe7dc352907fc980b868725387e98090ca73da4d4985713de106d1d34e0c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a491f04d1b9ac87439e00cae2597c04", "guid": "bfdfe7dc352907fc980b868725387e982b53ec6ff8bfea51345628ce6f6ee097"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b670b4e99f478f66b30cfccc8ae54ec", "guid": "bfdfe7dc352907fc980b868725387e98fc33e378d765b5d184a63c68a04c31c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a25c68b139101dcfad0532fa80e8328", "guid": "bfdfe7dc352907fc980b868725387e989a5be9afd5cfcab9eeb6394cad186dd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837c54ddf3a431e0289d85f1d11d52716", "guid": "bfdfe7dc352907fc980b868725387e98f3f5edadb770c68f6f9a50031579b5e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f1c2149ead91dc22641c66bf12ffd56", "guid": "bfdfe7dc352907fc980b868725387e986993314ff6839e8a5fff53b314ee6b9b"}], "guid": "bfdfe7dc352907fc980b868725387e98e0b5c62f2e657fd7c0babac7ff5ff1be", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980b264493efea7d6c99ac4f8634ffc79b", "guid": "bfdfe7dc352907fc980b868725387e98a94988d32437fb3229bb2d1bec43af81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bd22206f177418794ebcd10fb93b6d6", "guid": "bfdfe7dc352907fc980b868725387e9858f70edf9f0dfd62940865a69ab6e764"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f717a490d337d6bc1161c88f6c139b63", "guid": "bfdfe7dc352907fc980b868725387e980b64017c2d64467a34b03aa93d4add24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f168bad7e413f109d73c6174454dbadf", "guid": "bfdfe7dc352907fc980b868725387e9827cb361a213bb3ab7d9b03563b213a34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2dec47ca6b133e329486ba4d7297f47", "guid": "bfdfe7dc352907fc980b868725387e9821a579b0fa51c08e144b7e700609e46a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819ab0fa966c9798c89550fe672d0e8b8", "guid": "bfdfe7dc352907fc980b868725387e984bac393c6a2e23c96df96399f47e6685"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983708bbc45873983e57b473627fb2a2c2", "guid": "bfdfe7dc352907fc980b868725387e980f23c001f9b01c8ee46f5b3e2dd3dccc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fca0558fe09c071825b88c4ba8404460", "guid": "bfdfe7dc352907fc980b868725387e98c360be6aba4a4d118151bcdebc4fdf0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d85e68d5372f1168a30c9057b66019b", "guid": "bfdfe7dc352907fc980b868725387e9860950e70979def24132788b2037fb34b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8e42109540c1a5106cfbf3a9d3cef91", "guid": "bfdfe7dc352907fc980b868725387e9818184cd27f8ee4ded469bd5cf726efc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7397ef3c9f98d1c7c3fbeb4183aa876", "guid": "bfdfe7dc352907fc980b868725387e9892b97d1a3419eb989772a6eb47295095"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7e95b57a9e08bbfb2537be106fef953", "guid": "bfdfe7dc352907fc980b868725387e98f1e22590f16848ce86c8a3ec7eb1ae09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981db551cb6525b1506b7fb4ac7a16a52a", "guid": "bfdfe7dc352907fc980b868725387e98d424688f4e2b56eb182515b6fc2ea88b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817cb1b924b3079e77d1530681e7a313b", "guid": "bfdfe7dc352907fc980b868725387e98ccbcc4e57daf9e7f132fd15808fb173e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98225d69ea6e0e41198a623e74d822d436", "guid": "bfdfe7dc352907fc980b868725387e98ff4528fe0bced585be08e4ae763ce8ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829bf2cbbb7d360119aee4ce88074f01d", "guid": "bfdfe7dc352907fc980b868725387e986abde2d17f8b7522d5f9ce89b155ccdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a29ac7db481a69c26705801b6890293", "guid": "bfdfe7dc352907fc980b868725387e980b85037440b350d48ca82ebbe0689ce2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988aba8eb22b086c02e6eb5e4a601a0e33", "guid": "bfdfe7dc352907fc980b868725387e985b4fccbea6daf3584863ef6fdc9a9811"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f856298639b6fe4b21bbc3507c353fd2", "guid": "bfdfe7dc352907fc980b868725387e987620290a5784a1ec6e9e274041468712"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c6d2b977d337f3bf9540fd126434a20", "guid": "bfdfe7dc352907fc980b868725387e98bf28c3f733a30f55be78bd58408bdf21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbb11fd85e117826d2fac427cd002dac", "guid": "bfdfe7dc352907fc980b868725387e98f91000a0b2570b458e3ed5b9210c8073"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f3c46f850cbf61356d3e08b7cd9c791", "guid": "bfdfe7dc352907fc980b868725387e9819e9b2dadc9f134a90872a6d209dba6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837dc20ff2e477fe0bdabf88e71f0e5d0", "guid": "bfdfe7dc352907fc980b868725387e9801fe102b30abc2973ecbf88e5423c93a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889d4a4afe5456ac2541390320ce61010", "guid": "bfdfe7dc352907fc980b868725387e983e7b4a349b4c1d18b2ec978acdb5a203"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1254a7b5937fd20208656898a3c944b", "guid": "bfdfe7dc352907fc980b868725387e98e7d126ec2a88ad8de5e94e04f2b4e751"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e41bef880ba4a0bb1bf294c71351c456", "guid": "bfdfe7dc352907fc980b868725387e988ac7b5a2800a97f58d3e1f787ea18884"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e1547906f3648a25a59840592fc0699", "guid": "bfdfe7dc352907fc980b868725387e980c93409db0924e736599f3e0077e5c63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1fca5821188eda536d07d234a200177", "guid": "bfdfe7dc352907fc980b868725387e98cb6dcdce022506117f5c2bb482098062"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98179b46ccc49d1bcd6327cd02620fe620", "guid": "bfdfe7dc352907fc980b868725387e98e664ebc22a5393b643837932d48cb678"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98756b7621b8a3087b2d29d8f9225df080", "guid": "bfdfe7dc352907fc980b868725387e989633377dae7bfe3769db028a5d9faa42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983854f87d1b521abad76303c22e6bd299", "guid": "bfdfe7dc352907fc980b868725387e983b7ab232d434366d17b9f932677882c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984888747354988483fc7a30692166b781", "guid": "bfdfe7dc352907fc980b868725387e983be0fce2901f5c69bf842371e50abad7"}], "guid": "bfdfe7dc352907fc980b868725387e98686c44a57fc00cb3e9f4de175ac051f3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98d95dd86df03b3f498468f75af973fc77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f5979f0786f4a5ad9848f0349b1159", "guid": "bfdfe7dc352907fc980b868725387e9857ee4b048063e886eba7423c6ad6ab6d"}], "guid": "bfdfe7dc352907fc980b868725387e98476a0f3d3a4d81160464d44091ea5d45", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a9a2ef47e8c4063c7361c7d4d54f6fe4", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e9860177f8abff0dec77065ebbdf1eb424d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}