package vn.zenity.betacineplex.view.auth

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView

/**
 * Created by Zenity.
 */

interface ForgotPasswordContractor {
    interface View : IBaseView {
        fun showForgotSuccess(message: String)
        fun showError(message: String)
    }

    interface Presenter : IBasePresenter<View> {
        fun forgotPassword(email: String)
    }
}
