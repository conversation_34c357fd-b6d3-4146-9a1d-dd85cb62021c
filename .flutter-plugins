# This is a generated file; do not edit or check into version control.
audio_session=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\audio_session-0.1.23\\
device_info_plus=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\device_info_plus-10.1.2\\
file_selector_linux=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\file_selector_linux-0.9.3+2\\
file_selector_macos=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\file_selector_macos-0.9.4+2\\
file_selector_windows=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\file_selector_windows-0.9.3+3\\
firebase_core=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\firebase_core-3.3.0\\
firebase_core_web=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\firebase_core_web-2.17.5\\
firebase_messaging=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\firebase_messaging-15.0.4\\
firebase_messaging_web=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\firebase_messaging_web-3.8.12\\
fl_location=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\fl_location-4.4.2\\
fl_location_web=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\fl_location_web-4.2.0\\
flutter_contacts=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\flutter_contacts-1.1.9+2\\
flutter_inappwebview=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\flutter_inappwebview-6.0.0\\
flutter_inappwebview_android=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\
flutter_inappwebview_ios=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\
flutter_inappwebview_macos=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\
flutter_inappwebview_web=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\flutter_inappwebview_web-1.0.8\\
flutter_keyboard_visibility=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility-6.0.0\\
flutter_keyboard_visibility_linux=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_linux-1.0.0\\
flutter_keyboard_visibility_macos=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_macos-1.0.0\\
flutter_keyboard_visibility_web=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_web-2.0.0\\
flutter_keyboard_visibility_windows=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\flutter_keyboard_visibility_windows-1.0.0\\
flutter_plugin_android_lifecycle=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.24\\
geocoding=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\geocoding-2.2.2\\
geocoding_android=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\geocoding_android-3.3.1\\
geocoding_ios=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\geocoding_ios-2.3.0\\
google_maps_flutter=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\google_maps_flutter-2.10.0\\
google_maps_flutter_android=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_android-2.14.11\\
google_maps_flutter_ios=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_ios-2.13.2\\
google_maps_flutter_web=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_web-0.5.10\\
image_gallery_saver=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\image_gallery_saver-2.0.3\\
image_picker=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\image_picker-1.1.2\\
image_picker_android=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\image_picker_android-0.8.12+18\\
image_picker_for_web=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\image_picker_for_web-3.0.6\\
image_picker_ios=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\image_picker_ios-0.8.12+1\\
image_picker_linux=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\image_picker_linux-0.2.1+1\\
image_picker_macos=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\image_picker_macos-0.2.1+1\\
image_picker_windows=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\image_picker_windows-0.2.1+1\\
integration_test=D:\\flutter SDK\\flutter_windows_3.27.1-stable\\flutter\\packages\\integration_test\\
just_audio=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\just_audio-0.9.42\\
just_audio_web=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\just_audio_web-0.4.13\\
open_filex=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\open_filex-4.6.0\\
package_info_plus=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\package_info_plus-8.1.2\\
path_provider=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\
path_provider_android=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.15\\
path_provider_foundation=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\
path_provider_linux=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\
path_provider_windows=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\
permission_handler=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\permission_handler-11.3.1\\
permission_handler_android=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\permission_handler_android-12.0.13\\
permission_handler_apple=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\permission_handler_apple-9.4.5\\
permission_handler_html=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\
permission_handler_windows=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\
qr_code_scanner=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\qr_code_scanner-1.0.1\\
shared_preferences=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.3.4\\
shared_preferences_android=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.0\\
shared_preferences_foundation=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\
shared_preferences_linux=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\
shared_preferences_web=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\shared_preferences_web-2.4.2\\
shared_preferences_windows=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\
sqflite=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.1\\
sqflite_android=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\sqflite_android-2.4.0\\
sqflite_darwin=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\sqflite_darwin-2.4.1\\
syncfusion_flutter_pdfviewer=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\syncfusion_flutter_pdfviewer-26.2.14\\
syncfusion_pdfviewer_macos=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\syncfusion_pdfviewer_macos-26.2.14\\
syncfusion_pdfviewer_web=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\syncfusion_pdfviewer_web-26.2.14\\
syncfusion_pdfviewer_windows=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\syncfusion_pdfviewer_windows-26.2.14\\
url_launcher=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\
url_launcher_android=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.14\\
url_launcher_ios=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.2\\
url_launcher_linux=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\
url_launcher_macos=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\
url_launcher_web=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\url_launcher_web-2.3.3\\
url_launcher_windows=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.3\\
video_player=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\video_player-2.9.5\\
video_player_android=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\video_player_android-2.7.16\\
video_player_avfoundation=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\video_player_avfoundation-2.6.5\\
video_player_web=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\video_player_web-2.3.3\\
wakelock_plus=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\wakelock_plus-1.2.10\\
webview_flutter=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\webview_flutter-4.10.0\\
webview_flutter_android=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\webview_flutter_android-4.2.0\\
webview_flutter_wkwebview=D:\\flutter_pub\\Cache\\hosted\\pub.dev\\webview_flutter_wkwebview-3.20.0\\
