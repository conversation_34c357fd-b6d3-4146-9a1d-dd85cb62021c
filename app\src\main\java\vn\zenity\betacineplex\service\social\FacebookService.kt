package vn.zenity.betacineplex.service.social

import android.app.Activity
import android.app.AlertDialog
import android.content.Context
import com.facebook.AccessToken
import com.facebook.CallbackManager
import com.facebook.FacebookCallback
import com.facebook.Profile
import com.facebook.internal.FragmentWrapper
import com.facebook.login.*

class FacebookService {
    private var properties = FacebookProperties()
    private var parentFragment: FragmentWrapper? = null
    var activity: Activity? = null

    private fun getDefaultAudience(): DefaultAudience {
        return properties.defaultAudience
    }

    private fun getLoginBehavior(): LoginBehavior {
        return properties.loginBehavior
    }

    fun setFragment(fragment: androidx.fragment.app.Fragment) {
        parentFragment = FragmentWrapper(fragment)
    }

    fun setFragment(fragment: android.app.Fragment) {
        parentFragment = FragmentWrapper(fragment)
    }

    private fun getFragment(): androidx.fragment.app.Fragment? {
        return if (parentFragment != null) parentFragment?.supportFragment else null
    }

    private fun getNativeFragment(): android.app.Fragment? {
        return if (parentFragment != null) parentFragment?.nativeFragment else null
    }


    private fun getLoginManager(): LoginManager {
        val manager = LoginManager.getInstance()
        manager.defaultAudience = getDefaultAudience()
        manager.loginBehavior = getLoginBehavior()
        return manager
    }

    fun registerCallback(
        callbackManager: CallbackManager,
        callback: FacebookCallback<LoginResult>
    ) {
        getLoginManager().registerCallback(callbackManager, callback)
    }

    fun unregisterCallback(callbackManager: CallbackManager) {
        getLoginManager().unregisterCallback(callbackManager)
    }

    fun isLogin(): Boolean {
        val accessToken = accessToken()
        return accessToken != null && !accessToken.isExpired
    }

    fun accessToken(): AccessToken? {
        return AccessToken.getCurrentAccessToken()
    }

    fun performLogin() {
        val loginManager = getLoginManager()
        when {
            this.getFragment() != null -> {
                loginManager.logInWithReadPermissions(
                    this.getFragment(),
                    properties.permissions
                )
            }
            this.getNativeFragment() != null -> {
                loginManager.logInWithReadPermissions(
                    this.getNativeFragment(),
                    properties.permissions
                )
            }
            else -> {
                loginManager.logInWithReadPermissions(
                    this.activity,
                    properties.permissions
                )
            }
        }
    }

    fun performLogout(context: Context, confirmLogout: Boolean = false) {
        val loginManager = getLoginManager()
        if (confirmLogout) {
            // Create a confirmation dialog
            val logout = context.resources.getString(
                R.string.com_facebook_loginview_log_out_action
            )
            val cancel = context.resources.getString(
                R.string.com_facebook_loginview_cancel_action
            )
            val message: String
            val profile = Profile.getCurrentProfile()
            message = if (profile?.name != null) {
                String.format(
                    context.resources.getString(
                        R.string.com_facebook_loginview_logged_in_as
                    ),
                    profile.name
                )
            } else {
                context.resources.getString(
                    R.string.com_facebook_loginview_logged_in_using_facebook
                )
            }
            val builder = AlertDialog.Builder(context)
            builder.setMessage(message)
                .setCancelable(true)
                .setPositiveButton(logout) { _, _ -> loginManager.logOut() }
                .setNegativeButton(cancel, null)
            builder.create().show()
        } else {
            loginManager.logOut()
        }
    }

    internal class FacebookProperties {
        var defaultAudience = DefaultAudience.FRIENDS
        var permissions: List<String>? = listOf("public_profile, email")
            private set
        var loginBehavior = LoginBehavior.NATIVE_WITH_FALLBACK
    }
}