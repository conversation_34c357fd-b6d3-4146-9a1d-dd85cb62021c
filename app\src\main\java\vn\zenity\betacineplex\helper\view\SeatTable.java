package vn.zenity.betacineplex.helper.view;

import android.animation.Animator;
import android.animation.TypeEvaluator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.VectorDrawable;
import android.os.Build;
import android.os.Handler;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseBooleanArray;
import android.util.TypedValue;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;
import android.view.animation.DecelerateInterpolator;

import androidx.annotation.DrawableRes;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.vectordrawable.graphics.drawable.VectorDrawableCompat;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

import vn.zenity.betacineplex.R;

/**
 * Created by baoyunlong on 16/6/16.
 * Update by Vinhdn on 8/05/18
 */
public class SeatTable extends View {

    /**
     * 座位已售
     */
    private static final int SEAT_TYPE_SOLD = 1;

    /**
     * 座位已经选中
     */
    private static final int SEAT_TYPE_SELECTED = 2;

    /**
     * 座位可选
     */
    private static final int SEAT_TYPE_AVAILABLE = 3;

    /**
     * 座位不可用
     */
    private static final int SEAT_TYPE_NOT_AVAILABLE = 4;

    private static final int SEAT_TYPE_SELECTING = 0;

    private static final int SEAT_TYPE_USER_SELECTED = 5;

    private static final int SEAT_TYPE_REVERSE = 6;


    private final boolean DBG = false;

    Paint paint = new Paint();

    Paint overviewPaint = new Paint();

    Paint lineNumberPaint;

    float lineNumberTxtHeight;

    /**
     * 用来保存所有行号
     */
    ArrayList<String> lineNumbers = new ArrayList<>();

    Paint.FontMetrics lineNumberPaintFontMetrics;

    Matrix matrix = new Matrix();

    /**
     * 座位水平间距
     */
    int spacing;

    /**
     * 座位垂直间距
     */
    int verSpacing;

    /**
     * 行号宽度
     */
    int numberWidth;

    /**
     * 行数
     */
    int row;

    /**
     * 列数
     */
    int column;

    Bitmap seatNormalBitmap;

    Bitmap seatNormalSelectedBitmap;

    Bitmap seatNormalSoldBitmap;

    Bitmap seatNormalProcessingBitmap;

    Bitmap seatNormalReverseBitmap;

    Bitmap seatDoubleBitmap;

    Bitmap seatDoubleSelectedBitmap;

    Bitmap seatDoubleSoldBitmap;

    Bitmap seatDoubleProcessingBitmap;

    Bitmap seatDoubleReverseBitmap;

    Bitmap seatVIPBitmap;

    Bitmap seatVIPSelectedBitmap;

    Bitmap seatVIPSoldBitmap;

    Bitmap seatVIPProcessingBitmap;

    Bitmap seatVIPReverseBitmap;

    Bitmap overviewBitmap;

    int lastX;

    int lastY;

    /**
     * 整个座位图的宽度
     */
    int seatBitmapWidth;

    /**
     * 整个座位图的高度
     */
    int seatBitmapHeight;

    /**
     * 标识是否需要绘制座位图
     */
    boolean isNeedDrawSeatBitmap = true;

    /**
     * 概览图白色方块高度
     */
    float rectHeight;

    /**
     * 概览图白色方块的宽度
     */
    float rectWidth;

    /**
     * 概览图上方块的水平间距
     */
    float overviewSpacing;

    /**
     * 概览图上方块的垂直间距
     */
    float overviewVerSpacing;

    /**
     * 概览图的比例
     */
    float overviewScale = 4.8f;

    /**
     * 荧幕高度
     */
    float screenHeight;

    /**
     * 荧幕默认宽度与座位图的比例
     */
    float screenWidthScale = 0.5f;

    /**
     * 荧幕最小宽度
     */
    int defaultScreenWidth;

    /**
     * 标识是否正在缩放
     */
    boolean isScaling;

    float scaleX, scaleY;

    /**
     * 是否是第一次缩放
     */
    boolean firstScale = true;

    /**
     * 最多可以选择的座位数量
     */
    int maxSelected = Integer.MAX_VALUE;

    /**
     * 整个概览图的宽度
     */
    float rectW;

    /**
     * 整个概览图的高度
     */
    float rectH;

    Paint headPaint;

    Bitmap headBitmap;

    /**
     * 是否第一次执行onDraw
     */
    boolean isFirstDraw = true;

    /**
     * 标识是否需要绘制概览图
     */
    boolean isDrawOverview = false;

    /**
     * 标识是否需要更新概览图
     */
    boolean isDrawOverviewBitmap = true;

    int overview_checked;

    int overview_sold;

    int txt_color;

    int seatNormalResID;

    int seatNormalSelectedResID;

    int seatNormalSoldResID;

    int seatNormalProcessingResID;

    int seatNormalReverseResID;

    int seatVIPResID;

    int seatVIPSelectedResID;

    int seatVIPSoldResID;

    int seatVIPProcessingResID;

    int seatVIPReverseResID;

    int seatDoubleResID;

    int seatDoubleSelectedResID;

    int seatDoubleSoldResID;

    int seatDoubleProcessingResID;

    int seatDoubleReverseResID;

    boolean isOnClick;

    /**
     * 顶部高度,可选,已选,已售区域的高度
     */
    float headHeight;

    Paint pathPaint;

    RectF rectF;

    /**
     * 头部下面横线的高度
     */
    int borderHeight = 1;

    Paint redBorderPaint;

    float xScale1 = 1;

    float yScale1 = 1;

    Matrix tempMatrix = new Matrix();

    int bacColor = Color.parseColor("#7e000000");

    Handler handler = new Handler();

    float[] m = new float[9];

    ScaleGestureDetector scaleGestureDetector = new ScaleGestureDetector(getContext(),
            new ScaleGestureDetector.OnScaleGestureListener() {
                @Override
                public boolean onScale(ScaleGestureDetector detector) {
                    isScaling = true;
                    float scaleFactor = detector.getScaleFactor();
                    if (getMatrixScaleY() * scaleFactor > maxZoom) {
                        scaleFactor = maxZoom / getMatrixScaleY();
                    }
                    if (firstScale) {
                        scaleX = detector.getCurrentSpanX();
                        scaleY = detector.getCurrentSpanY();
                        firstScale = false;
                    }

                    if (getMatrixScaleY() * scaleFactor < minZoom) {
                        scaleFactor = minZoom / getMatrixScaleY();
                    }
                    matrix.postScale(scaleFactor, scaleFactor, scaleX, scaleY);
                    invalidate();
                    return true;
                }

                @Override
                public boolean onScaleBegin(ScaleGestureDetector detector) {
                    return true;
                }

                @Override
                public void onScaleEnd(ScaleGestureDetector detector) {
                    isScaling = false;
                    firstScale = true;
                }
            });

    private float minZoom = 0.8f;

    private float maxZoom = 2.0f;

    private SeatChecker seatChecker;

    /**
     * 荧幕名称
     */
    private String screenName = "";

    private int downX, downY;

    private boolean pointer;

    /**
     * 默认的座位图宽度,如果使用的自己的座位图片比这个尺寸大或者小,会缩放到这个大小
     */
    private float defaultImgW = 80;

    /**
     * 默认的座位图高度
     */
    private float defaultImgH = 70;

    /**
     * 座位图片的宽度
     */
    private int seatWidth;

    private int seatDoubleWidth;

    /**
     * 座位图片的高度
     */
    private int seatHeight;

    private int seatDoubleHeight;

    private Runnable hideOverviewRunnable = new Runnable() {
        @Override
        public void run() {
            isDrawOverview = false;
            invalidate();
        }
    };

    private ArrayList<Integer> selects = new ArrayList<>();

    private float zoom;

    private SparseBooleanArray checkIsDoubleMap = new SparseBooleanArray();

    GestureDetector gestureDetector = new GestureDetector(getContext(),
            new GestureDetector.SimpleOnGestureListener() {

                @Override
                public boolean onSingleTapUp(MotionEvent e) {
                    isOnClick = true;
                    int x = (int) e.getX();
                    int y = (int) e.getY();

                    for (int i = 0; i < row; i++) {
                        int mColumn = (seatChecker != null) ? seatChecker.getColumnOfRow(i) : column;
                        int notUsed = (seatChecker != null) ? seatChecker.getMarginLeftOfRow(i) : 0;
                        int marginLeft = 0;
                        int seatWidth = SeatTable.this.seatWidth;
                        if (notUsed != 0) {
                            marginLeft = (int) ((float) ((seatWidth + spacing) * notUsed) / 2f);
                        }
                        for (int j = 0; j < mColumn; j++) {
                            boolean isDouble = seatChecker != null && seatChecker.isDoubleSeat(i, j);
                            int tempX = (int) ((j * seatWidth + j * spacing - (isDouble && j > 0 ?
                                    spacing : 0) + marginLeft) * getMatrixScaleX() + getTranslateX());
                            int maxTemX = (int) (tempX + (isDouble && j > 0 ? spacing : 0) +
                                    (isDouble ? seatDoubleWidth : seatWidth) * getMatrixScaleX());

                            int tempY = (int) ((i * seatHeight + i * verSpacing) * getMatrixScaleY()
                                    + getTranslateY());
                            int maxTempY = (int) (tempY + seatHeight * getMatrixScaleY());

                            if (seatChecker != null
                                    && seatChecker.isValidSeat(i, j)
                                    && !seatChecker.isSold(i, j)
                                    && !seatChecker.isLaneExit(i, j)
                                    && !seatChecker.isReverse(i, j)
                                    && !seatChecker.isSelected(i, j)
                                    && (!seatChecker.isUserSelected(i, j) || isHave(getID(i, j)) >= 0)) {
                                if (x >= tempX && x <= maxTemX && y >= tempY && y <= maxTempY) {
                                    int id = getID(i, j);
                                    int index = isHave(id);
                                    if (index >= 0) {
                                        remove(index);
                                        if (seatChecker != null) {
                                            seatChecker.unCheck(i, j);
                                        }
                                    } else {
                                        if (getTotalSelected() >= maxSelected - (isDouble ? 1 : 0)) {
                                            if (seatChecker != null) {
                                                seatChecker.notifiMaxSelect();
                                            }
                                            return super.onSingleTapConfirmed(e);
                                        } else {
                                            addChooseSeat(i, j);
                                            if (seatChecker != null) {
                                                seatChecker.checked(i, j);
                                            }
                                        }
                                    }
                                    isNeedDrawSeatBitmap = true;
                                    isDrawOverviewBitmap = true;
                                    float currentScaleY = getMatrixScaleY();

                                    if (seatChecker != null && seatChecker
                                            .isEnableZoom() && currentScaleY < minZoom) {
                                        scaleX = x;
                                        scaleY = y;
                                        zoomAnimate(currentScaleY, minZoom/* + 0.2f*/);
                                    }

                                    invalidate();
                                    break;
                                }
                            }
                        }
                    }

                    return super.onSingleTapConfirmed(e);
                }
            });

    public SeatTable(Context context) {
        this(context, null);
    }

    public SeatTable(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
//        init();
    }

    public SeatTable(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    /**
     * 设置行号 默认显示 1,2,3....数字
     */
    public void setLineNumbers(ArrayList<String> lineNumbers) {
        this.lineNumbers = lineNumbers;
        invalidate();
    }

    public SeatChecker getSeatChecker() {
        return seatChecker;
    }

    public void setSeatChecker(SeatChecker seatChecker) {
        this.seatChecker = seatChecker;
        invalidate();
    }

    private void init(Context context, AttributeSet attrs) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.SeatTable);
        overview_checked = typedArray
                .getColor(R.styleable.SeatTable_overview_checked, Color.parseColor("#5A9E64"));
        overview_sold = typedArray.getColor(R.styleable.SeatTable_overview_sold, Color.RED);
        txt_color = typedArray.getColor(R.styleable.SeatTable_txt_color, Color.WHITE);

        seatNormalResID = typedArray
                .getResourceId(R.styleable.SeatTable_seat_normal, R.drawable.ic_chair_normal);
        seatNormalSelectedResID = typedArray
                .getResourceId(R.styleable.SeatTable_seat_normal_selected,
                        R.drawable.ic_chair_normal_selected);
        seatNormalSoldResID = typedArray
                .getResourceId(R.styleable.SeatTable_seat_normal_sold, R.drawable.ic_chair_normal_buy);
        seatNormalProcessingResID = typedArray
                .getResourceId(R.styleable.SeatTable_seat_normal_processing,
                        R.drawable.ic_chair_normal_processing);
        seatNormalReverseResID = typedArray.getResourceId(R.styleable.SeatTable_seat_normal_reverse,
                R.drawable.ic_chair_normal_reserve);

        seatVIPResID = typedArray
                .getResourceId(R.styleable.SeatTable_seat_VIP, R.drawable.ic_chair_vip);
        seatVIPSelectedResID = typedArray.getResourceId(R.styleable.SeatTable_seat_VIP_selected,
                R.drawable.ic_chair_vip_selected);
        seatVIPSoldResID = typedArray
                .getResourceId(R.styleable.SeatTable_seat_VIP_sold, R.drawable.ic_chair_vip_buy);
        seatVIPProcessingResID = typedArray.getResourceId(R.styleable.SeatTable_seat_VIP_processing,
                R.drawable.ic_chair_vip_processing);
        seatVIPReverseResID = typedArray
                .getResourceId(R.styleable.SeatTable_seat_VIP_reverse, R.drawable.ic_chair_vip_reserve);

        seatDoubleResID = typedArray
                .getResourceId(R.styleable.SeatTable_seat_double, R.drawable.ic_chair_double);
        seatDoubleSelectedResID = typedArray
                .getResourceId(R.styleable.SeatTable_seat_double_selected,
                        R.drawable.ic_chair_double_selected);
        seatDoubleSoldResID = typedArray
                .getResourceId(R.styleable.SeatTable_seat_double_sold, R.drawable.ic_chair_double_buy);
        seatDoubleProcessingResID = typedArray
                .getResourceId(R.styleable.SeatTable_seat_double_processing,
                        R.drawable.ic_chair_double_processing);
        seatDoubleReverseResID = typedArray.getResourceId(R.styleable.SeatTable_seat_double_reverse,
                R.drawable.ic_chair_double_reserve);
        typedArray.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int height = (int) (headHeight + screenHeight + borderHeight + verSpacing);
        for (int i = 0; i < row; i++) {
            height += defaultImgH + verSpacing;
        }
//        super.onMeasure(widthMeasureSpec, height);
        setMeasuredDimension(widthMeasureSpec,
                (int) (height * getMatrixScaleX()));
    }

    private void init() {
        spacing = (int) dip2Px(5);
        verSpacing = (int) dip2Px(10);
        defaultScreenWidth = (int) dip2Px(80);

        seatNormalBitmap = getBitmapFromDrawable(seatNormalResID);
        seatNormalSelectedBitmap = getBitmapFromDrawable(seatNormalSelectedResID);
        seatNormalSoldBitmap = getBitmapFromDrawable(seatNormalSoldResID);
        seatNormalProcessingBitmap = getBitmapFromDrawable(seatNormalProcessingResID);
        seatNormalReverseBitmap = getBitmapFromDrawable(seatNormalReverseResID);

        seatVIPBitmap = getBitmapFromDrawable(seatVIPResID);
        seatVIPSelectedBitmap = getBitmapFromDrawable(seatVIPSelectedResID);
        seatVIPSoldBitmap = getBitmapFromDrawable(seatVIPSoldResID);
        seatVIPProcessingBitmap = getBitmapFromDrawable(seatVIPProcessingResID);
        seatVIPReverseBitmap = getBitmapFromDrawable(seatVIPReverseResID);

        seatDoubleBitmap = getBitmapFromDrawable(seatDoubleResID);
        seatDoubleProcessingBitmap = getBitmapFromDrawable(seatDoubleProcessingResID);
        seatDoubleSelectedBitmap = getBitmapFromDrawable(seatDoubleSelectedResID);
        seatDoubleSoldBitmap = getBitmapFromDrawable(seatDoubleSoldResID);
        seatDoubleReverseBitmap = getBitmapFromDrawable(seatDoubleReverseResID);

        float scaleX = defaultImgW / seatNormalBitmap.getWidth();
        float scaleY = defaultImgH / seatNormalBitmap.getHeight();
        xScale1 = scaleX;
        yScale1 = scaleY;

        seatHeight = (int) (seatNormalBitmap.getHeight() * yScale1);
        seatWidth = (int) (seatNormalBitmap.getWidth() * xScale1);

        seatDoubleHeight = (int) (seatDoubleBitmap.getHeight() * yScale1);
        seatDoubleWidth = (int) (seatDoubleBitmap.getWidth() * xScale1);

        seatBitmapWidth = (int) (column * seatNormalBitmap
                .getWidth() * xScale1 + (column - 1) * spacing);
        seatBitmapHeight = (int) (row * seatNormalBitmap
                .getHeight() * yScale1 + (row - 1) * verSpacing);
        paint.setColor(Color.RED);
        numberWidth = (int) dip2Px(20);

        screenHeight = dip2Px(20);
        headHeight = dip2Px(0);

        headPaint = new Paint();
        headPaint.setStyle(Paint.Style.FILL);
        headPaint.setTextSize(24);
        headPaint.setColor(Color.WHITE);
        headPaint.setAntiAlias(true);

        pathPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        pathPaint.setStyle(Paint.Style.FILL);
        pathPaint.setColor(Color.parseColor("#e2e2e2"));

        redBorderPaint = new Paint();
        redBorderPaint.setAntiAlias(true);
        redBorderPaint.setColor(Color.RED);
        redBorderPaint.setStyle(Paint.Style.STROKE);
        redBorderPaint.setStrokeWidth(getResources().getDisplayMetrics().density * 1);

        rectF = new RectF();

        rectHeight = seatHeight / overviewScale;
        rectWidth = seatWidth / overviewScale;
        overviewSpacing = spacing / overviewScale;
        overviewVerSpacing = verSpacing / overviewScale;

        rectW = column * rectWidth + (column - 1) * overviewSpacing + overviewSpacing * 2;
        rectH = row * rectHeight + (row - 1) * overviewVerSpacing + overviewVerSpacing * 2;
        overviewBitmap = Bitmap.createBitmap((int) rectW, (int) rectH, Bitmap.Config.ARGB_4444);

        lineNumberPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        lineNumberPaint.setColor(bacColor);
        lineNumberPaint.setTextSize(getResources().getDisplayMetrics().density * 16);
        lineNumberTxtHeight = lineNumberPaint.measureText("4");
        lineNumberPaintFontMetrics = lineNumberPaint.getFontMetrics();
        lineNumberPaint.setTextAlign(Paint.Align.CENTER);

        if (lineNumbers == null) {
            lineNumbers = new ArrayList<>();
        }
        if (lineNumbers.size() <= 0) {
            lineNumbers = new ArrayList<>(Arrays
                    .asList("A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O",
                            "P", "Q", "R", "S", "T"));
        }

        matrix.postTranslate(numberWidth + spacing,
                headHeight + screenHeight + borderHeight + verSpacing);
//        matrix.postScale(1f, )
//        onMeasure();
        setHorizontalScrollBarEnabled(true);

    }

    private Bitmap getBitmapFromDrawable(@DrawableRes int drawableId) {
        Drawable drawable = AppCompatResources.getDrawable(getContext(), drawableId);

        if (drawable instanceof BitmapDrawable) {
            return ((BitmapDrawable) drawable).getBitmap();
        }

        if (drawable instanceof VectorDrawableCompat || (Build.VERSION.SDK_INT >= Build
                .VERSION_CODES.LOLLIPOP && drawable instanceof VectorDrawable)) {
            Bitmap bitmap = Bitmap
                    .createBitmap(drawable.getIntrinsicWidth() * 2, drawable.getIntrinsicHeight() * 2,
                            Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
            drawable.draw(canvas);
            return bitmap;
        } else {
            throw new IllegalArgumentException("unsupported drawable type");
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        long startTime = System.currentTimeMillis();
        if (row <= 0 || column == 0) {
            return;
        }
        int height = (int) (headHeight + screenHeight + borderHeight + verSpacing);
        for (int i = 0; i < row; i++) {
            height += defaultImgH + verSpacing;
        }
//        measure(getMeasuredWidth(), (int) (height * getMatrixScaleX()));
//        setMeasuredDimension(getMeasuredWidth(), (int) (height * getMatrixScaleX()));
        if (getLayoutParams() != null && getLayoutParams() instanceof ConstraintLayout
                .LayoutParams) {
            ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) getLayoutParams();
            lp.height = (int) (height * getMatrixScaleX());
            setLayoutParams(lp);
        }

        if (isFirstDraw && getMeasuredWidth() > 0) {
            isFirstDraw = false;
            minZoom = (float) getWidth() / (float) (column * seatWidth + spacing * (column + 1) +
                    2 * numberWidth);
//            minZoom = (float) getWidth() / (float) (getMeasuredWidth());
            maxZoom = 2f * minZoom;
            matrix.postScale(minZoom, minZoom);
        }

        drawSeat(canvas);
//        drawNumber(canvas);

//        if (headBitmap == null) {
//            headBitmap = drawHeadInfo();
//        }
//        canvas.drawBitmap(headBitmap, 0, 0, null);

        drawScreen(canvas);

//        if (isDrawOverview) {
//            long s = System.currentTimeMillis();
//            if (isDrawOverviewBitmap) {
//                drawOverview();
//            }
//            canvas.drawBitmap(overviewBitmap, 0, 0, null);
//            drawOverview(canvas);
//            Log.d("drawTime", "OverviewDrawTime:" + (System.currentTimeMillis() - s));
//        }
//        onMeasure(getMeasuredWidth(), getMeasuredHeight());
        if (DBG) {
            long drawTime = System.currentTimeMillis() - startTime;
            Log.d("drawTime", "totalDrawTime:" + drawTime);
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int y = (int) event.getY();
        int x = (int) event.getX();
        super.onTouchEvent(event);

        if (seatChecker != null && seatChecker.isEnableZoom())
            scaleGestureDetector.onTouchEvent(event);
        gestureDetector.onTouchEvent(event);
        int pointerCount = event.getPointerCount();
        if (pointerCount > 1) {
            pointer = true;
        }

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                pointer = false;
                downX = x;
                downY = y;
                isDrawOverview = true;
                handler.removeCallbacks(hideOverviewRunnable);
                invalidate();
                break;
            case MotionEvent.ACTION_MOVE:
                if (!isScaling && !isOnClick) {
                    int downDX = Math.abs(x - downX);
                    int downDY = Math.abs(y - downY);
                    if ((downDX > 10 || downDY > 10) && !pointer) {
                        int dx = x - lastX;
                        int dy = y - lastY;
                        if (seatChecker != null && !seatChecker.isMoveHorizontal()) {
                            dx = 0;
                        }

                        if (seatChecker != null && !seatChecker.isMoveVertical()) {
                            dy = 0;
                        }
                        matrix.postTranslate(dx, dy);
                        invalidate();
                    }
                }
                break;
            case MotionEvent.ACTION_UP:
                handler.postDelayed(hideOverviewRunnable, 1500);

                autoScale();
                int downDX = Math.abs(x - downX);
                int downDY = Math.abs(y - downY);
                if ((downDX > 10 || downDY > 10) /*&& !pointer*/) {
                    autoScroll();
                }

                break;
        }
        isOnClick = false;
        lastY = y;
        lastX = x;

        return true;
    }

    Bitmap drawHeadInfo() {
        String txt = "已售";
        float txtY = getBaseLine(headPaint, 0, headHeight);
        int txtWidth = (int) headPaint.measureText(txt);
        float spacing = dip2Px(10);
        float spacing1 = dip2Px(5);
        float y = (headHeight - seatNormalBitmap.getHeight()) / 2;

        float width = seatNormalBitmap
                .getWidth() + spacing1 + txtWidth + spacing + seatNormalSoldBitmap
                .getWidth() + txtWidth + spacing1 + spacing + seatNormalSelectedBitmap
                .getHeight() + spacing1 + txtWidth;
        Bitmap bitmap = Bitmap.createBitmap(getWidth(), (int) headHeight, Bitmap.Config.ARGB_8888);

        Canvas canvas = new Canvas(bitmap);

        //绘制背景
        canvas.drawRect(0, 0, getWidth(), headHeight, headPaint);
        headPaint.setColor(Color.BLACK);

        float startX = (getWidth() - width) / 2;
        tempMatrix.setScale(xScale1, yScale1);
        tempMatrix.postTranslate(startX, (headHeight - seatHeight) / 2);
        canvas.drawBitmap(seatNormalBitmap, tempMatrix, headPaint);
        canvas.drawText("可选", startX + seatWidth + spacing1, txtY, headPaint);

        float soldSeatBitmapY = startX + seatNormalBitmap
                .getWidth() + spacing1 + txtWidth + spacing;
        tempMatrix.setScale(xScale1, yScale1);
        tempMatrix.postTranslate(soldSeatBitmapY, (headHeight - seatHeight) / 2);
        canvas.drawBitmap(seatNormalSoldBitmap, tempMatrix, headPaint);
        canvas.drawText("已售", soldSeatBitmapY + seatWidth + spacing1, txtY, headPaint);

        float checkedSeatBitmapX = soldSeatBitmapY + seatNormalSoldBitmap
                .getWidth() + spacing1 + txtWidth + spacing;
        tempMatrix.setScale(xScale1, yScale1);
        tempMatrix.postTranslate(checkedSeatBitmapX, y);
        canvas.drawBitmap(seatNormalSelectedBitmap, tempMatrix, headPaint);
        canvas.drawText("已选", checkedSeatBitmapX + spacing1 + seatWidth, txtY, headPaint);

        //绘制分割线
        headPaint.setStrokeWidth(1);
        headPaint.setColor(Color.GRAY);
        canvas.drawLine(0, headHeight, getWidth(), headHeight, headPaint);
        return bitmap;

    }

    /**
     * 绘制中间屏幕
     */
    void drawScreen(Canvas canvas) {
        pathPaint.setStyle(Paint.Style.FILL);
        pathPaint.setColor(Color.parseColor("#e2e2e2"));
        float startY = headHeight + borderHeight + getTranslateY() - 1.5f * screenHeight * getMatrixScaleY();

        float centerX = seatBitmapWidth * getMatrixScaleX() / 2 + getTranslateX();
        float screenWidth = seatBitmapWidth * screenWidthScale * getMatrixScaleX();
        if (screenWidth < defaultScreenWidth) {
            screenWidth = defaultScreenWidth;
        }

        Path path = new Path();
        path.moveTo(centerX, startY);
        path.lineTo(centerX - screenWidth / 2, startY);
        path.lineTo(centerX - screenWidth / 2 + 20, screenHeight * getMatrixScaleY() + startY);
        path.lineTo(centerX + screenWidth / 2 - 20, screenHeight * getMatrixScaleY() + startY);
        path.lineTo(centerX + screenWidth / 2, startY);

//        canvas.drawPath(path, pathPaint);

        pathPaint.setColor(Color.BLACK);
        int textSize = (int) TypedValue
                .applyDimension(TypedValue.COMPLEX_UNIT_SP, 14, getResources().getDisplayMetrics());
        pathPaint.setTextSize(textSize * getMatrixScaleX());

        canvas.drawText(screenName, centerX - pathPaint.measureText(screenName) / 2,
                getBaseLine(pathPaint, startY, startY + screenHeight * getMatrixScaleY()), pathPaint);
    }

    void drawSeat(Canvas canvas) {
        zoom = getMatrixScaleX();
        long startTime = System.currentTimeMillis();
        float translateX = getTranslateX();
        float translateY = getTranslateY();
        float scaleX = zoom;
        float scaleY = zoom;

        for (int i = 0; i < row; i++) {
            float top = i * seatNormalBitmap
                    .getHeight() * yScale1 * scaleY + i * verSpacing * scaleY + translateY;

            float bottom = top + seatNormalBitmap.getHeight() * yScale1 * scaleY;
            if (bottom < 0 || top > getHeight()) {
                continue;
            }

            int mColumn = (seatChecker != null) ? seatChecker.getColumnOfRow(i) : column;
            int notUsed = (seatChecker != null) ? seatChecker.getMarginLeftOfRow(i) : 0;
            int marginLeft = 0;
            if (notUsed != 0) {
                marginLeft = (int) ((float) ((seatNormalBitmap
                        .getWidth() + spacing) * notUsed) / 2f);
            }
            for (int j = 0; j < mColumn; j++) {

                boolean isDouble = false;
                if (seatChecker != null && seatChecker.isDoubleSeat(i, j)) {
                    isDouble = true;
//                    if (j % 2 == 1) continue;
                }
                float left = j * seatNormalBitmap
                        .getWidth() * xScale1 * scaleX + j * (spacing) * scaleX + translateX +
                        (marginLeft) * scaleX * xScale1;

                float right = (left + seatNormalBitmap.getWidth() * xScale1 * scaleY);
                if (right < 0 || left > getWidth()) {
                    continue;
                }

                int seatType = getSeatType(i, j);
                tempMatrix.setTranslate(left, top);
                tempMatrix.postScale(xScale1, yScale1, left, top);
                tempMatrix.postScale(scaleX, scaleY, left, top);
                boolean isVIP = false;
                if (seatChecker != null && seatChecker.isVIP(i, j)) {
                    isVIP = true;
                }

                switch (seatType) {
                    case SEAT_TYPE_SELECTED:
                        canvas.drawBitmap(
                                isDouble ? seatDoubleProcessingBitmap : (isVIP ?
                                        seatVIPProcessingBitmap : seatNormalProcessingBitmap),
                                tempMatrix, paint);
                        break;
                    case SEAT_TYPE_SOLD:
                        canvas.drawBitmap(
                                isDouble ? seatDoubleSoldBitmap : (isVIP ? seatVIPSoldBitmap :
                                        seatNormalSoldBitmap),
                                tempMatrix, paint);
                        break;
                    case SEAT_TYPE_SELECTING:
                        canvas.drawBitmap(
                                isDouble ? seatDoubleProcessingBitmap : (isVIP ?
                                        seatVIPProcessingBitmap : seatNormalProcessingBitmap),
                                tempMatrix, paint);
                        break;
                    case SEAT_TYPE_USER_SELECTED:
                        canvas.drawBitmap(
                                isDouble ? seatDoubleSelectedBitmap : (isVIP ? seatVIPSelectedBitmap
                                        : seatNormalSelectedBitmap),
                                tempMatrix, paint);
                        break;
                    case SEAT_TYPE_REVERSE:
                        canvas.drawBitmap(
                                isDouble ? seatDoubleReverseBitmap : (isVIP ? seatVIPReverseBitmap :
                                        seatNormalReverseBitmap),
                                tempMatrix, paint);
                        break;
                    case SEAT_TYPE_AVAILABLE:
                        canvas.drawBitmap(
                                isDouble ? seatDoubleBitmap : (isVIP ? seatVIPBitmap :
                                        seatNormalBitmap),
                                tempMatrix, paint);
                        break;
                    case SEAT_TYPE_NOT_AVAILABLE:
                        break;
                }

                if (seatType != SEAT_TYPE_NOT_AVAILABLE) {
                    drawText(canvas, i, j, top, left);
                }
            }
        }

        if (DBG) {
            long drawTime = System.currentTimeMillis() - startTime;
            Log.d("drawTime", "seatDrawTime:" + drawTime);
        }
    }

    private int getSeatType(int row, int column) {
//        if(row == 0 && column == 0){
//            Log.d("hihi", "getSoldStatus: " + seatChecker.getSoldStatus(row,column));
//        }
        if (seatChecker != null) {
            if (!seatChecker.isValidSeat(row, column)) {
                return SEAT_TYPE_NOT_AVAILABLE;
            } else if (seatChecker.isSold(row, column)) {
                return SEAT_TYPE_SOLD;
            } else if (seatChecker.isSelecting(row, column)) {
                return SEAT_TYPE_SELECTING;
            } else if (seatChecker.isSelected(row, column)) {
                return SEAT_TYPE_SELECTED;
            } else if (seatChecker.isUserSelected(row, column)) {
                if (isHave(getID(row, column)) >= 0) {
                    return SEAT_TYPE_USER_SELECTED;
                } else {
                    return SEAT_TYPE_SELECTED;
                }
            } else if (seatChecker.isReverse(row, column)) {
                return SEAT_TYPE_REVERSE;
            }
        }

        if (isHave(getID(row, column)) >= 0) {
            return SEAT_TYPE_USER_SELECTED;
        }
        return SEAT_TYPE_AVAILABLE;
    }

    private int getID(int row, int column) {
        return row * this.column + column;
    }

    /**
     * 绘制选中座位的行号列号
     */
    private void drawText(Canvas canvas, int row, int column, float top, float left) {

        String txt = lineNumbers.get(row) + (column + 1) + "";
        String txt2 = lineNumbers.get(row) + (column + 1 + 1) + "";
        String txt1 = (column + 1) + "";

        boolean isDouble = false;
        if (seatChecker != null) {
            txt = seatChecker.getSeatName(row, column);
            isDouble = seatChecker.isDoubleSeat(row, column);
            if (isDouble) {
                txt2 = seatChecker.getSeatName(row, column + 1);
            }
            String[] strings = seatChecker.checkedSeatTxt(row, column);
            if (strings != null && strings.length > 0) {
                if (strings.length >= 2) {
                    txt = strings[0];
                    txt1 = strings[1];
                } else {
                    txt = strings[0];
                    txt1 = null;
                }
            }
        }
        txt1 = null;

        TextPaint txtPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
        txtPaint.setColor(txt_color);
        txtPaint.setTypeface(Typeface.DEFAULT_BOLD);
        float seatHeight = this.seatHeight * getMatrixScaleX();
        float seatWidth = this.seatWidth * getMatrixScaleX();
        txtPaint.setTextSize(seatHeight / 2.5f);

        //获取中间线
        float center = seatHeight / 2;
        float txtWidth = txtPaint.measureText(txt);
        float startX = left + seatWidth / 2 - txtWidth / 2;

        //只绘制一行文字
//        if(txt1==null){
        canvas.drawText(txt, startX, getBaseLine(txtPaint, top, top + seatHeight * 0.8f), txtPaint);
        if (isDouble) {
            canvas.drawText(txt2,
                    left + this.seatDoubleWidth * getMatrixScaleX() - seatWidth / 2 - txtWidth / 2,
                    getBaseLine(txtPaint, top, top + seatHeight * 0.8f), txtPaint);
        }
//        }else {
//            canvas.drawText(txt, startX, getBaseLine(txtPaint, top, top + center), txtPaint);
//            canvas.drawText(txt1, startX, getBaseLine(txtPaint, top + center, top + center +
// seatHeight / 2), txtPaint);
//        }

        if (DBG) {
            Log.d("drawTest:", "top:" + top);
        }
    }

    /**
     * 绘制行号
     */
    void drawNumber(Canvas canvas) {
        long startTime = System.currentTimeMillis();
        lineNumberPaint.setColor(bacColor);
        int translateY = (int) getTranslateY();
        float scaleY = getMatrixScaleY();

        rectF.top = translateY - lineNumberTxtHeight / 2;
        rectF.bottom = translateY + (seatBitmapHeight * scaleY) + lineNumberTxtHeight / 2;
        rectF.left = 0;
        rectF.right = numberWidth;
//        canvas.drawRoundRect(rectF, numberWidth / 2, numberWidth / 2, lineNumberPaint);

        lineNumberPaint.setColor(Color.BLACK);

        for (int i = 0; i < row; i++) {

            float top = (i * seatHeight + i * verSpacing) * scaleY + translateY;
            float bottom = (i * seatHeight + i * verSpacing + seatHeight) * scaleY + translateY;
            float baseline = (bottom + top - lineNumberPaintFontMetrics.bottom -
                    lineNumberPaintFontMetrics.top) / 2;

            canvas.drawText(lineNumbers.get(i), numberWidth / 2, baseline, lineNumberPaint);
        }

        if (DBG) {
            long drawTime = System.currentTimeMillis() - startTime;
            Log.d("drawTime", "drawNumberTime:" + drawTime);
        }
    }

    /**
     * 绘制概览图
     */
    void drawOverview(Canvas canvas) {

        //绘制红色框
        int left = (int) -getTranslateX();
        if (left < 0) {
            left = 0;
        }
        left /= overviewScale;
        left /= getMatrixScaleX();

        int currentWidth = (int) (getTranslateX() + (column * seatWidth + spacing * (column - 1))
                * getMatrixScaleX());
        if (currentWidth > getWidth()) {
            currentWidth = currentWidth - getWidth();
        } else {
            currentWidth = 0;
        }
        int right = (int) (rectW - currentWidth / overviewScale / getMatrixScaleX());

        float top = -getTranslateY() + headHeight;
        if (top < 0) {
            top = 0;
        }
        top /= overviewScale;
        top /= getMatrixScaleY();
        if (top > 0) {
            top += overviewVerSpacing;
        }

        int currentHeight = (int) (getTranslateY() + (row * seatHeight + verSpacing * (row - 1))
                * getMatrixScaleY());
        if (currentHeight > getHeight()) {
            currentHeight = currentHeight - getHeight();
        } else {
            currentHeight = 0;
        }
        int bottom = (int) (rectH - currentHeight / overviewScale / getMatrixScaleY());

        canvas.drawRect(left, top, right, bottom, redBorderPaint);
    }

    Bitmap drawOverview() {
        isDrawOverviewBitmap = false;

        int bac = Color.parseColor("#7e000000");
        overviewPaint.setColor(bac);
        overviewPaint.setAntiAlias(true);
        overviewPaint.setStyle(Paint.Style.FILL);
        overviewBitmap.eraseColor(Color.TRANSPARENT);
        Canvas canvas = new Canvas(overviewBitmap);
        //绘制透明灰色背景
        canvas.drawRect(0, 0, rectW, rectH, overviewPaint);

        overviewPaint.setColor(Color.WHITE);
        for (int i = 0; i < row; i++) {
            float top = i * rectHeight + i * overviewVerSpacing + overviewVerSpacing;
            for (int j = 0; j < column; j++) {

                int seatType = getSeatType(i, j);
                switch (seatType) {
                    case SEAT_TYPE_AVAILABLE:
                        overviewPaint.setColor(Color.WHITE);
                        break;
                    case SEAT_TYPE_NOT_AVAILABLE:
                        continue;
                    case SEAT_TYPE_SELECTED:
                        overviewPaint.setColor(overview_checked);
                        break;
                    case SEAT_TYPE_SOLD:
                        overviewPaint.setColor(overview_sold);
                        break;
                }

                float left;

                left = j * rectWidth + j * overviewSpacing + overviewSpacing;
                canvas.drawRect(left, top, left + rectWidth, top + rectHeight, overviewPaint);
            }
        }

        return overviewBitmap;
    }

    /**
     * 自动回弹
     * 整个大小不超过控件大小的时候:
     * 往左边滑动,自动回弹到行号右边
     * 往右边滑动,自动回弹到右边
     * 往上,下滑动,自动回弹到顶部
     * <p>
     * 整个大小超过控件大小的时候:
     * 往左侧滑动,回弹到最右边,往右侧滑回弹到最左边
     * 往上滑动,回弹到底部,往下滑动回弹到顶部
     */
    private void autoScroll() {
        float currentSeatBitmapWidth = seatBitmapWidth * getMatrixScaleX();
        float currentSeatBitmapHeight = seatBitmapHeight * getMatrixScaleY();
        float moveYLength = 0;
        float moveXLength = 0;

        //处理左右滑动的情况
        if (currentSeatBitmapWidth < getWidth()) {
            if (getTranslateX() < 0 || getMatrixScaleX() < numberWidth + spacing) {
                //计算要移动的距离

                if (getTranslateX() < 0) {
                    int ww = 0;
                    if (getMeasuredWidth() > getWidth()) {
                        ww = getMeasuredWidth() - getWidth();
                    }
                    if (getTranslateX() < -ww) {
                        moveXLength = (-getTranslateX()) /*+ numberWidth*/ + spacing - ww;
                    }
                } else {
                    moveXLength = numberWidth + spacing - getTranslateX();
                }

            }
        } else {

            if (getTranslateX() < 0 && getTranslateX() + currentSeatBitmapWidth > getWidth()) {

            } else {
                //往左侧滑动
                if (getTranslateX() + currentSeatBitmapWidth < getWidth()) {
                    moveXLength = getWidth() - (getTranslateX() + currentSeatBitmapWidth);
                } else {
                    //右侧滑动
                    moveXLength = -getTranslateX() + numberWidth + spacing;
                }
            }

        }

        float startYPosition = screenHeight * getMatrixScaleY() + verSpacing * getMatrixScaleY()
                + headHeight + borderHeight;

        //处理上下滑动
        if (currentSeatBitmapHeight + headHeight < getHeight()) {

            if (getTranslateY() < startYPosition) {
                moveYLength = startYPosition - getTranslateY();
            } else {
                moveYLength = -(getTranslateY() - (startYPosition));
            }

        } else {

            if (getTranslateY() < 0 && getTranslateY() + currentSeatBitmapHeight > getHeight()) {

            } else {
                //往上滑动
                if (getTranslateY() + currentSeatBitmapHeight < getHeight()) {
                    moveYLength = getHeight() - (getTranslateY() + currentSeatBitmapHeight);
                } else {
                    moveYLength = -(getTranslateY() - (startYPosition));
                }
            }
        }

        Point start = new Point();
        start.x = (int) getTranslateX();
        start.y = (int) getTranslateY();

        Point end = new Point();
        end.x = (int) (start.x + moveXLength);
        end.y = (int) (start.y + moveYLength);

        moveAnimate(start, end);

    }

    private void autoScale() {

        if (getMatrixScaleX() > maxZoom) {
            zoomAnimate(getMatrixScaleX(), maxZoom);
        } else if (getMatrixScaleX() < minZoom) {
            zoomAnimate(getMatrixScaleX(), minZoom);
        }
    }

    public void resetSelected() {
        selects = new ArrayList<>();
        checkIsDoubleMap = new SparseBooleanArray();
    }

    public ArrayList<String> getSelectedSeat() {
        ArrayList<String> results = new ArrayList<>();
        for (int i = 0; i < this.row; i++) {
            for (int j = 0; j < this.column; j++) {
                if (isHave(getID(i, j)) >= 0) {
                    results.add(i + "," + j);
                }
            }
        }
        return results;
    }

    private int isHave(Integer seat) {
        return Collections.binarySearch(selects, seat);
    }

    private void remove(int index) {
        selects.remove(index);
        checkIsDoubleMap.delete(index);
    }

    private float getTranslateX() {
        matrix.getValues(m);
        return m[2];
    }

    private float getTranslateY() {
        matrix.getValues(m);
        return m[5];
    }

    private float getMatrixScaleY() {
        matrix.getValues(m);
        return m[4];
    }

    public float getMatrixScaleX() {
        matrix.getValues(m);
        return m[Matrix.MSCALE_X];
    }

    private float dip2Px(float value) {
        return getResources().getDisplayMetrics().density * value;
    }

    private float getBaseLine(Paint p, float top, float bottom) {
        Paint.FontMetrics fontMetrics = p.getFontMetrics();
        int baseline = (int) ((bottom + top - fontMetrics.bottom - fontMetrics.top) / 2);
        return baseline;
    }

    private void moveAnimate(Point start, Point end) {
        ValueAnimator valueAnimator = ValueAnimator.ofObject(new MoveEvaluator(), start, end);
        valueAnimator.setInterpolator(new DecelerateInterpolator());
        MoveAnimation moveAnimation = new MoveAnimation();
        valueAnimator.addUpdateListener(moveAnimation);
        valueAnimator.setDuration(400);
        valueAnimator.start();
    }

    private void zoomAnimate(float cur, float tar) {
        ValueAnimator valueAnimator = ValueAnimator.ofFloat(cur, tar);
        valueAnimator.setInterpolator(new DecelerateInterpolator());
        ZoomAnimation zoomAnim = new ZoomAnimation();
        valueAnimator.addUpdateListener(zoomAnim);
        valueAnimator.addListener(zoomAnim);
        valueAnimator.setDuration(400);
        valueAnimator.start();
    }

    private void zoom(float zoom) {
        float z = zoom / getMatrixScaleX();
        matrix.postScale(z, z, scaleX, scaleY);
        invalidate();
    }

    private void move(Point p) {
        float x = p.x - getTranslateX();
        float y = p.y - getTranslateY();
        matrix.postTranslate(x, y);
        invalidate();
    }

    public void setData(int row, int column) {
        this.row = row;
        this.column = column;
        init();
        invalidate();
    }

    private int getTotalSelected() {
        int total = 0;
        for (Integer id : selects) {
            total += checkIsDoubleMap.get(id) ? 2 : 1;
        }
        return total;
    }

    private void addChooseSeat(int row, int column) {
        int id = getID(row, column);
        for (int i = 0; i < selects.size(); i++) {
            int item = selects.get(i);
            if (id < item) {
                selects.add(i, id);
                if (seatChecker != null) {
                    if (seatChecker.isDoubleSeat(row, column)) {
                        checkIsDoubleMap.append(id, true);
                    }
                }
                return;
            }
        }

        selects.add(id);
        if (seatChecker != null) {
            if (seatChecker.isDoubleSeat(row, column)) {
                checkIsDoubleMap.append(id, true);
            }
        }
    }

    public void setScreenName(String screenName) {
        this.screenName = screenName;
    }

    public void setMaxSelected(int maxSelected) {
        this.maxSelected = maxSelected;
    }

    private int getRowNumber(int row) {
        int result = row;
        if (seatChecker == null) {
            return -1;
        }

        for (int i = 0; i < row; i++) {
            for (int j = 0; j < column; j++) {
                if (seatChecker.isValidSeat(i, j)) {
                    break;
                }

                if (j == column - 1) {
                    if (i == row) {
                        return -1;
                    }
                    result--;
                }
            }
        }
        return result;
    }

    private int getColumnNumber(int row, int column) {
        int result = column;
        if (seatChecker == null) {
            return -1;
        }

        for (int i = row; i <= row; i++) {
            for (int j = 0; j < column; j++) {

                if (!seatChecker.isValidSeat(i, j)) {
                    if (j == column) {
                        return -1;
                    }
                    result--;
                }
            }
        }
        return result;
    }

    public interface SeatChecker {

        /**
         * 是否可用座位
         */
        boolean isValidSeat(int row, int column);

        /**
         * 是否已售
         */
        boolean isSold(int row, int column);

        boolean isSelecting(int row, int column);

        boolean isSelected(int row, int column);

        boolean isUserSelected(int row, int column);

        boolean isReverse(int row, int column);

        void checked(int row, int column);

        void unCheck(int row, int column);

        int getColumnOfRow(int row);

        int getMarginLeftOfRow(int row);

        int getMarginTopOfRow(int row);

        boolean isDoubleSeat(int row, int column);

        boolean isLaneExit(int row, int column);


        /**
         * 获取选中后座位上显示的文字
         *
         * @return 返回2个元素的数组, 第一个元素是第一行的文字, 第二个元素是第二行文字, 如果只返回一个元素则会绘制到座位图的中间位置
         */
        String[] checkedSeatTxt(int row, int column);

        boolean isVIP(int row, int column);

        boolean isEnableZoom();

        boolean isMoveVertical();

        boolean isMoveHorizontal();

        void notifiMaxSelect();

        String getSeatName(int row, int column);

        int getSoldStatus(int row, int column);
    }

    class MoveAnimation implements ValueAnimator.AnimatorUpdateListener {

        @Override
        public void onAnimationUpdate(ValueAnimator animation) {
            Point p = (Point) animation.getAnimatedValue();

            move(p);
        }
    }

    class MoveEvaluator implements TypeEvaluator {

        @Override
        public Object evaluate(float fraction, Object startValue, Object endValue) {
            Point startPoint = (Point) startValue;
            Point endPoint = (Point) endValue;
            int x = (int) (startPoint.x + fraction * (endPoint.x - startPoint.x));
            int y = (int) (startPoint.y + fraction * (endPoint.y - startPoint.y));
            return new Point(x, y);
        }
    }

    class ZoomAnimation implements ValueAnimator.AnimatorUpdateListener, Animator.AnimatorListener {

        @Override
        public void onAnimationUpdate(ValueAnimator animation) {
            zoom = (Float) animation.getAnimatedValue();
            zoom(zoom);

            if (DBG) {
                Log.d("zoomTest", "zoom:" + zoom);
            }
        }

        @Override
        public void onAnimationCancel(Animator animation) {
        }

        @Override
        public void onAnimationEnd(Animator animation) {
        }

        @Override
        public void onAnimationRepeat(Animator animation) {
        }

        @Override
        public void onAnimationStart(Animator animation) {
        }

    }

}
