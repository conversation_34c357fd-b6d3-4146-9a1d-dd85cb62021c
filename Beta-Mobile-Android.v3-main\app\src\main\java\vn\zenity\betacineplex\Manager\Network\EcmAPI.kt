package vn.zenity.betacineplex.Manager.Network

import io.reactivex.Observable
import io.reactivex.Single
import retrofit2.http.*
import vn.zenity.betacineplex.model.*

/**
 * Created by tinhvv on 4/8/18.
 */
interface EcmAPI {
    @GET("api/v1/ecm/categories/news-promotion/{lang}")// màn home, gọi api này để lấy id sau đó gọi thằng NewForCategory để lấy các bài
    fun getNewPromotion(@Path("lang") lang: String = ""): Observable<DDKCReponse<ArrayList<NewModel>>>

    @GET("api/v1/ecm/categories/news-events/{lang}")// tương tự promotion, nó cùng 1 màn hình
    fun getNewEvent(@Path("lang") lang: String = ""): Observable<DDKCReponse<ArrayList<NewModel?>>>

    @GET("api/v1/ecm/categories/notifications/{lang}")// sẽ get về nhiều id, xong cần chạy background để lấy đc các bài theo id rồi sort và hiển thị
    fun getNotification(@Path("lang") lang: String = ""): Observable<DDKCReponse<ArrayList<NewModel>>>

    @GET("api/v2/erp/notifications")
    fun getNotifications(): Observable<DDKCReponse<ArrayList<NewNotification>>>

    @GET("api/v1/erp/notifications/{id}/campaign-detail")
    fun getNotificationDetail(@Path("id") id: Int): Observable<DDKCReponse<NotificationDetail>>

    @GET("api/v1/erp/notifications/{accountId}/total-unread")
    fun getNumberUnread(@Path("accountId") accountId: String): Observable<DDKCReponse<NunberUnread>>

    @PUT("api/v1/erp/notifications/{id}/read-status")
    fun readNotification(@Path("id") id: Int): Observable<DDKCReponse<Boolean>>

    @PUT("api/v2/erp/notifications/read-status")
    fun readNotificationV2(@Body data: Map<String, String>): Observable<DDKCReponse<Boolean>>

    @GET("api/v1/ecm/tuyendung/{lang}")// sẽ get về nhiều id, xong cần chạy background để lấy đc các bài theo id rồi sort và hiển thị
    fun getRecruitment(@Path("lang") lang: String = "",
                       @Query("pageSize") pageSize: Int = 20,
                       @Query("pageNumber") pageNumber: Int = 1): Observable<DDKCReponse<ArrayList<NewsModel>>>


    @GET("api/v1/ecm/{id}/news")// gọi sau khi có id của 3 thằng trên
    fun getNewForCategory(@Path("id") id: String,
                          @Query("pageSize") pageSize: Int = 20,
                          @Query("pageNumber") pageNumber: Int = 1): Observable<DDKCReponse<ArrayList<NewsModel>>>

    @GET("api/v1/ecm/{id}/news")// gọi sau khi có id của 3 thằng trên
    fun getNewForCategoryNoParams(@Path("id") id: String): Observable<DDKCReponse<NewsModel>>

    @GET("api/v1/ecm/{id}/news")// gọi sau khi có id của bọn getTermId, Payment, security, companyinfo
    fun getNewWithId(@Path("id") id: String): Observable<DDKCReponse<NewsModel>>

    @GET("api/v1/ecm/topics/{lang}")// topic trong setting, get ra id rồi dùng thằng faqs để lấy các câu hỏi và trả lời
    fun getTopic(@Path("lang") lang: String = "vi"): Observable<DDKCReponse<ArrayList<TopicModel>>>

    @GET("api/v1/ecm/{id}/faqs")// lấy detail từ thằng trên
    fun getFAQ(@Path("id") id: String): Observable<DDKCReponse<ArrayList<TopicDetailModel>>>

    @GET("api/v1/ecm/parameter")// lấy ra id rồi dùng thằng getNewWithID để gọi tiếp
    fun getTermId(@Query("code") lang: String = "mobile:app:dieukhoan:vi"): Observable<DDKCReponse<PolicyModel>>

    @GET("api/v1/ecm/parameter") // lấy ra id rồi dùng thằng getNewWithID để gọi tiếp
    fun getPaymentPolicyId(@Query("code") lang: String = "mobile:app:dieukhoan-thanhtoan:vi"): Observable<DDKCReponse<PolicyModel>>

    @GET("api/v1/ecm/parameter") // lấy ra id rồi dùng thằng getNewWithID để gọi tiếp
    fun getSecurityId(@Query("code") lang: String = "mobile:app:dieukhoan-baomat:vi"): Observable<DDKCReponse<PolicyModel>>

    @GET("api/v1/ecm/parameter") // lấy ra id rồi dùng thằng getNewWithID để gọi tiếp
    fun getCompanyInfoId(@Query("code") lang: String = "mobile:app:thongtin-congty:vi"): Observable<DDKCReponse<PolicyModel>>

    @GET("api/v1/ecm/voucher-public/news")
    fun getPublicVouchers(@Query("pageNumber") pageNumber: Int = 1,
                          @Query("pageSize") pageSize: Int = 20): Observable<DDKCReponse<ArrayList<NewsModel>>>

    @POST("api/v2/erp/storyline/{storylineId}/voucher-code")
    fun getVoucherCode(@Path("storylineId") id: String): Observable<DDKCReponse<VoucherCodeModel>>
}