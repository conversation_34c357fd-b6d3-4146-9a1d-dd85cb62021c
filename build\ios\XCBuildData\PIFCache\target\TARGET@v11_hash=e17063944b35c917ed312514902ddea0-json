{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cd585f20ebac1c91a4c818b120de855e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9865835bce8c09454204387fb69daf2311", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e7f08b390a2127e4b127a39abcc1c83b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987f4ddca786b47d84b5cf66726bd1eb99", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e7f08b390a2127e4b127a39abcc1c83b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bfe51d5108a0dacf3d454ed8ac9b178b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eac1dc727db1e236cde84a91197370a0", "guid": "bfdfe7dc352907fc980b868725387e98360f7927b484f2fefec8d343726f09b0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f84169e4a9ee46216bcca3403846378c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fdb11bac1016f1458a27775d3712adba", "guid": "bfdfe7dc352907fc980b868725387e98084cf0d158b6e64211499e1f32a7e1b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983402d91a9e8a77b6b2be40609b20ee42", "guid": "bfdfe7dc352907fc980b868725387e981fa3c5c8bb23e823ad15e4691b703659"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fd66e846e4ae53e7eefa13bc451d63b", "guid": "bfdfe7dc352907fc980b868725387e98af47f771a8d8677cb4667034e9e1912c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98951e20831ab9532198eba8e4a1cd91e2", "guid": "bfdfe7dc352907fc980b868725387e9852859252a3eaae3611aa1cf8e58bd2b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849b4ca43b68aeba9cab0a201aca722cf", "guid": "bfdfe7dc352907fc980b868725387e98db3331f5968b25efbb55f52f420be0db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980056204fe939593eb99cb2e925c32cb0", "guid": "bfdfe7dc352907fc980b868725387e983a5f07afefb6ee4aa0e4f7c2aa614a2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806bfe8a758577b6f899d98b102157276", "guid": "bfdfe7dc352907fc980b868725387e987d98901ecb335dee20af027d21d2d4b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873ba8783b5fff94bb88d335695d8062b", "guid": "bfdfe7dc352907fc980b868725387e982761ebcd9bb8f4c5e89c152eb3ca2b07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98986422701859496245ff5f0114d2d19a", "guid": "bfdfe7dc352907fc980b868725387e984782ae64d63158f6b69be3988afcb888"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af9709a92a4c32c08b7335c913942681", "guid": "bfdfe7dc352907fc980b868725387e98f27a2789f3cd43741b65fb8d4148b9a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d555cabf390813675e39cf0a8f2d40b", "guid": "bfdfe7dc352907fc980b868725387e9878f8a1e5b2571571d6b6c38f4a29e766"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb64c7aecff1bf299692b3e13056f72e", "guid": "bfdfe7dc352907fc980b868725387e98148490c9e64a37df7df05209c79069df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa9c23c50feb7f6fa63cdb6e1bdab426", "guid": "bfdfe7dc352907fc980b868725387e989c944c6aa59940a1ab2b88fc61521c63"}], "guid": "bfdfe7dc352907fc980b868725387e9809d573b176279251e552161eefacb51e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98c4e4feb6fad3761a7d75cd5e272bbc37"}], "guid": "bfdfe7dc352907fc980b868725387e98203aadba07646b4dbab9ed1d382d9eb6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e02c3c39c6586a8fa13738483fb63aa1", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98de9d96ee2517293c16d320f88d42d619", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}