{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1af1075936557b7bf56bae837cb8465", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986bd8553a279bd2e6c1b5d96c7982fe70", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a2c3534e5bf52a9615feb2c693ab9301", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982c500a53fd1a038852469b3f430597a0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a2c3534e5bf52a9615feb2c693ab9301", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9866c9a9b3771dfb08c2b99386fd5370b0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c7751588fab72762309e0f4557cbfc2d", "guid": "bfdfe7dc352907fc980b868725387e98b82cea02682976743e02e038a6264716", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f07031905f99bf4521af0ccfe153aa8", "guid": "bfdfe7dc352907fc980b868725387e98fb01a9f5dcd0d3c89e0d87e24d5c18c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4ce7768eef52178d8197381813374e5", "guid": "bfdfe7dc352907fc980b868725387e989b093c6cbedc1ae7f03afaf0e7fa33c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888ad0c0b0ae3478ac6b848fae90ff885", "guid": "bfdfe7dc352907fc980b868725387e986feb2720604215e127306772cb19e38f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c620e17193159e05792ae26aa8bdc53e", "guid": "bfdfe7dc352907fc980b868725387e984ff70b60c4a1ff0740899cb42f5177b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816d94b901e096d153b8ce8f12a15899f", "guid": "bfdfe7dc352907fc980b868725387e9817af3096cf2078813f780b1b708ddda2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb48483d756534757d1b16a463e84676", "guid": "bfdfe7dc352907fc980b868725387e9884ce7b2b763c8f844bb05243fc3a3cc6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4f965979deea9d235309e1c58175010", "guid": "bfdfe7dc352907fc980b868725387e98c5cadeec7c7670ec3098d9204575616b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98352fff32d9b6046232ed90ec6ec01d7b", "guid": "bfdfe7dc352907fc980b868725387e980c6dd90304d5de7965922de33c3c5d08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b47b505ee48f0863225cc29bf3d3a560", "guid": "bfdfe7dc352907fc980b868725387e98d3bd2bd805b12cda061cd86cade68870", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873b22ee2358b47f01025a1b3620a04fa", "guid": "bfdfe7dc352907fc980b868725387e98cbbd3a58bcfe076ac460130b7418a6c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c95f6a0f211cf12869f680b6fb65aec", "guid": "bfdfe7dc352907fc980b868725387e98d7366706c78783abaf468a401ee9c92a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4b57203e9e820a2ec4b847539f85184", "guid": "bfdfe7dc352907fc980b868725387e98cbb1073de4e6103d10787ef4959a1394", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b990190bb8c81a666c81cc80529bcd4", "guid": "bfdfe7dc352907fc980b868725387e98a5843af0d567ec97ed04181e941c50a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f56052c9285ce6d54719711dd5c38c93", "guid": "bfdfe7dc352907fc980b868725387e98c462f82e77178eedf77f615a17748886", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad3c013f3094a2cd26e645d259ab4eeb", "guid": "bfdfe7dc352907fc980b868725387e9825c0853aa866346a381fa071a225fe9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8757e17525e68022a2e9864a2b38158", "guid": "bfdfe7dc352907fc980b868725387e98ad432facf67073808ee8c2493e62b568", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4009ff07be29eb45433dc6640afd2dd", "guid": "bfdfe7dc352907fc980b868725387e98123ea806c2104699bffea050fadb493f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcfec92f15b676577b36fd0ee7411d26", "guid": "bfdfe7dc352907fc980b868725387e98d590d86cd7d3e12dae611e73c376c2e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804428011ad85c5a87517758c80c470de", "guid": "bfdfe7dc352907fc980b868725387e98170d14bbb35eefd93a6c68731e4ae5fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ec7ef5468bc8add0cedc085c6fbf1da", "guid": "bfdfe7dc352907fc980b868725387e986a9dc6de4c773b0b85c40c56ed640ce4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989956192c659ca47df27a162d132c6077", "guid": "bfdfe7dc352907fc980b868725387e980f7ee4e55600698d93113ac4dd43b63f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5f1df79004b0e7610248e1474855a67", "guid": "bfdfe7dc352907fc980b868725387e98f68965f589a9a4f2ea10590393f3153f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6cdebc8af892432f3f93673a5c5fd84", "guid": "bfdfe7dc352907fc980b868725387e9883f45e9c7c69e02c84e8718f9d2bdaab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7f2d511b47d3c05ae50f2c11ee577c0", "guid": "bfdfe7dc352907fc980b868725387e982335d9788e790c9bed6b2e834763a097"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98823220d64924bf72f79bcae99dde345a", "guid": "bfdfe7dc352907fc980b868725387e987480064a4727bdde9a5cbece00c2839a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dce14dfe32764a58681bbe73fc0ace69", "guid": "bfdfe7dc352907fc980b868725387e98350f34e8c7fe55b8b1ac85a61a28dc7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffe0f10351ac7fa0fd9a19721f252d2c", "guid": "bfdfe7dc352907fc980b868725387e9869b8c46650a01c3b8d39ef27f88fc830", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adf1836da5e1e9679d442702819f3eaa", "guid": "bfdfe7dc352907fc980b868725387e98d88de7d1ca22d5fa5a1f1a0b8dd34242", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef79d03ea15b92a4de8099640459f622", "guid": "bfdfe7dc352907fc980b868725387e98b42121d380bcc4b2c001ca1d7b6ca923", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a10503fe7e9861aaaa68b7495c524aaa", "guid": "bfdfe7dc352907fc980b868725387e987e03fe07e347e5b815e10c1ae88780d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b7985fd72debbd408394c05b95df43e", "guid": "bfdfe7dc352907fc980b868725387e98de5db21f5fd7b40354ac07a84f4c42a7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985983809461f31759dae5b7bbacd7c2dd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9881e1ee73c57ae04a838931e3b65f50bb", "guid": "bfdfe7dc352907fc980b868725387e988013aab1c49d3d0379a4cc5404857c6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854b6552232393a50542491e6b1199be3", "guid": "bfdfe7dc352907fc980b868725387e98b07d3edb65e46cbb3a31b2aba58676d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980207f4eb12e9b02992a52c6426fa7542", "guid": "bfdfe7dc352907fc980b868725387e98c25ee29593d262b49d044e9f09432859"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98245e2e58dc8a97e3877ff7995c6d72de", "guid": "bfdfe7dc352907fc980b868725387e9888ac807877898d5bea2aef53fb7c050b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f266315f3ad33fad8c8f1d0329fb8374", "guid": "bfdfe7dc352907fc980b868725387e98300986d29c2067764b752b33e5aec92b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98356edd525478ddf21fae18b0454f3202", "guid": "bfdfe7dc352907fc980b868725387e98c743c84e989255ed81812075c95414ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6a2daea0e902287b2475a7a2f402a27", "guid": "bfdfe7dc352907fc980b868725387e9871fea6e981df1b65726c369769f3b75d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842287190b196b9eee961e9d118949b92", "guid": "bfdfe7dc352907fc980b868725387e984c3f9c107285197e7e1226d821e75bab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98825251c1f9803a822e5048b3ba20522d", "guid": "bfdfe7dc352907fc980b868725387e98de65a4e9bf75315bfa73babf11767633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848d4614ee4e300204bac491b57230169", "guid": "bfdfe7dc352907fc980b868725387e98b2befefcc440df06b9cbe00c9ea39a7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac4c89b5e03ef1bc878d84faeacd912e", "guid": "bfdfe7dc352907fc980b868725387e98e414e1c0bf6558594fffacdf5f6104d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ab0754879485b74a8d135f1a1c14f48", "guid": "bfdfe7dc352907fc980b868725387e987e377cf1f2ddd5f14c2bd550491eba98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858492ba46affcc2c51c7f9a621737fe9", "guid": "bfdfe7dc352907fc980b868725387e9804159e19c87c50598293535dd3f415d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e98fe517f7a01238f499b8b277d113a", "guid": "bfdfe7dc352907fc980b868725387e9822191e3d909039387ce1b159e8a904f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9a3570aee30f9b373f5fe3250f6741c", "guid": "bfdfe7dc352907fc980b868725387e9889748dd5771472f57e7e3e84a679369b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d32b32d28e4870151d5d822fd53adac", "guid": "bfdfe7dc352907fc980b868725387e98b8c138e085685db606a64599ac4f48f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984786212520d2daa853009175c470fbf5", "guid": "bfdfe7dc352907fc980b868725387e98be783091b561a469720ba5f27cae7edf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98000f78e03f4e17a460c438cbcf8fe96f", "guid": "bfdfe7dc352907fc980b868725387e98571ff3fb52c7238b809d3cb0c4f9ca5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815293801c80ecff55045f112bf0b8ab2", "guid": "bfdfe7dc352907fc980b868725387e98458fed62ac830c6b0eeae57c2fa4e98f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984289dca000343e95b753a2ff32c9bc51", "guid": "bfdfe7dc352907fc980b868725387e98f033ccbcc12cef71d54f95e0182ea12a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982df568f9f09e78df151c2407dafe56d2", "guid": "bfdfe7dc352907fc980b868725387e983c343bd0263492f1d92b7bd9d0dc2604"}], "guid": "bfdfe7dc352907fc980b868725387e98da116b3ba77c6d846e207c6ecf66ec45", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9893806850a1d9b23ec1b8b1c21c5832c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "guid": "bfdfe7dc352907fc980b868725387e98750f17080423f1294a2b5cbc5e90d7cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f5979f0786f4a5ad9848f0349b1159", "guid": "bfdfe7dc352907fc980b868725387e98d7e8803d32e4a7fb4e7ed7ab010f30c5"}], "guid": "bfdfe7dc352907fc980b868725387e9887ea2a50c4705c602a014b5e573081c2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985dc654a1931afaf08dce6f9b651743d4", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98dbdb2fe6360d658147ee677a36124e42", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}