<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/viewTimeRoot"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:paddingLeft="@dimen/margin_small"
    android:paddingRight="@dimen/margin_small"
    android:background="?attr/selectableItemBackground"
    xmlns:tools="http://schemas.android.com/tools">
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTimeBooking"
        android:layout_width="78dp"
        android:layout_height="30dp"
        tools:text="18:00"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:textColor="@color/textDark"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_bold"
        android:gravity="center"
        android:background="@drawable/shape_gray_rounded"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSeatBooking"
        android:layout_width="78dp"
        android:layout_height="20dp"
        app:layout_constraintLeft_toLeftOf="@+id/tvTimeBooking"
        app:layout_constraintRight_toRightOf="@+id/tvTimeBooking"
        tools:text="120 trống"
        android:textSize="14sp"
        android:textColor="@color/textBlack"
        app:fontFamily="@font/sanspro_regular"
        app:layout_constraintTop_toBottomOf="@+id/tvTimeBooking"
        android:gravity="center"/>
</androidx.constraintlayout.widget.ConstraintLayout>