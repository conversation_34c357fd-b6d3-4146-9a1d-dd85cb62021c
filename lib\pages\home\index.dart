import 'package:flutter/material.dart';
import 'package:flutter_app/pages/Movie_schedule/index.dart';
import 'package:flutter_app/pages/other_tab/index.dart';
import 'package:flutter_app/pages/promotion/index.dart';
import 'package:flutter_app/pages/voucher/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../constants/index.dart';
import '../../cubit/index.dart';
import '../../models/index.dart';
import '../Movie_schedule/model/Film_model.dart';
import '../cinema/index.dart';
import '../cinema/model/cinema_model.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages.elementAt(selectIndex),
      bottomNavigationBar: BottomNavigationBar(
        unselectedItemColor: CColor.black.shade400,
        type: BottomNavigationBarType.fixed,
        unselectedLabelStyle: const TextStyle(fontSize: CFontSize.sm),
        selectedLabelStyle: const TextStyle(fontSize: CFontSize.sm),
        selectedItemColor: CColor.blue,
        currentIndex: selectIndex,
        onTap: (value) {
          setState(() {
            selectIndex = value;
          });
        },
        items: [
          BottomNavigationBarItem(icon: imageIcon(0, 'selected', 'select'), label: "Lịch chiếu\ntheo phim"),
          BottomNavigationBarItem(icon: imageIcon(1, 'cinema', 'Cinema'), label: "Lịch chiếu\ntheo rạp"),
          BottomNavigationBarItem(icon: imageIcon(2, 'voucher', 'Voucher'), label: "Voucher\n "),
          BottomNavigationBarItem(icon: imageIcon(3, 'khuyenmai', 'Khuyenmai'), label: "Khuyến mãi\n "),
          BottomNavigationBarItem(icon: imageIcon(4, 'other', 'Other'), label: "Khác\n "),
        ],
      ),
    );
  }

  int selectIndex = 0;

  Widget imageIcon(int index, String name, String name1) {
    return Image.asset(
      'assets/icon/tabbar/${index == selectIndex ? name : 'un$name1'}@3x.png',
      width: 24,
      height: 24,
    );
  }

  final List<Widget> _pages = <Widget>[
    BlocProvider(create: (context) => BlocC<FilmModel>(), child: const MovieSchedule()),
    const ListFilmScreen(),

    // ChooseCinemaScreen(cinema: CinemaModel(
    //   CinemaId: '1',
    //   name: 'Beta Cinema',
    //   pictureURL: 'https://example.com/cinema.jpg',
    // ),),
    const MyVoucherScreen(),
    const PromotionPage(),
    const OtherScreen(),
  ];

  @override
  void initState() {
    // Role.initState(context);
    super.initState();
  }
}
