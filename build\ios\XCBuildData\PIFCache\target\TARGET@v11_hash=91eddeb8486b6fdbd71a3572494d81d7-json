{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982181e2253b9529da93f61a35bda8d9d4", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f3a185e42125cbebc3819e8b331525fe", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f310c5260a3d245b9e069547367d5b8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dca1441b75da8d73bc8871a565a06daf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f310c5260a3d245b9e069547367d5b8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984b2947a0e47c6783917482d0dcbfd807", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bbb8ffb7ee259fae5ff4f82c6774e992", "guid": "bfdfe7dc352907fc980b868725387e98fcc02261a83e42ee87b846de2079ec4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cac1e6cae5811662cbd825f7d67ae29a", "guid": "bfdfe7dc352907fc980b868725387e98bdbc0e2e743f0a07b66af364373bc252", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0db5ac2ca7897b5e0414f0684b3112a", "guid": "bfdfe7dc352907fc980b868725387e98335f9ed5a79d432791bbf5c92e5b3492", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8538fe40ebea5fe0b7f2805bc60d0ff", "guid": "bfdfe7dc352907fc980b868725387e988b097379285645448c9c4929cd4b20fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98003228d0bfe30898aafde3e035e2533c", "guid": "bfdfe7dc352907fc980b868725387e98fd05f30313980f00f867d5944fc74c91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c44c10f67d67d9b1d2939c61539e08", "guid": "bfdfe7dc352907fc980b868725387e989d69fc12afda128463b9a46bd665f27d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809853df253f7dabeb3c6ae1571ea3c9d", "guid": "bfdfe7dc352907fc980b868725387e989eba36a3226093fdfaa97fc0a50cec99", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f052c21d905e0eacbd62ab69776d90d6", "guid": "bfdfe7dc352907fc980b868725387e982167094fcc5dadeb3b1fa6090d492be2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98043ddc3ab229762585642369c813bb53", "guid": "bfdfe7dc352907fc980b868725387e9862d2aa3c2854a2040aba7392817120a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd130a0e448da1c1da1ad79cdbc88c03", "guid": "bfdfe7dc352907fc980b868725387e98de131408265e2f45da14d20962f8491b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9787e4b40cc93314c9d76daaa321065", "guid": "bfdfe7dc352907fc980b868725387e98291ac983a1b219c9415e19b8ee691726", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8f2d14b311a7e3cff87c080c387866f", "guid": "bfdfe7dc352907fc980b868725387e98057281909193b8835a1aa9bff19d0101", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989989a97023f28f1cf9dd92bb0900417b", "guid": "bfdfe7dc352907fc980b868725387e980a3cb1a18c47dceb76f0acea6e6ca6d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98744dcbf84ba8b46731eca663448deb72", "guid": "bfdfe7dc352907fc980b868725387e98c0fa431de1b2ffe289cd585bda1ab4ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981be5b098298d368dd9d884c38c031ab6", "guid": "bfdfe7dc352907fc980b868725387e982b815412fb526cdbfb2b887f2440528a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98144fbb3464b37f8d73703d1d6f0c05ba", "guid": "bfdfe7dc352907fc980b868725387e98eb8c79d8d8c808b8d67431dfa00ae42e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cec128c091f952fb0ccd41c07bd995b", "guid": "bfdfe7dc352907fc980b868725387e988c8272e87a243be8fff2853f12e51b90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b89fa9ac13abf4324f0b65c13befbf80", "guid": "bfdfe7dc352907fc980b868725387e98309da21ab41e11838f4d99e083ba4e40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989085bbc2d323cc46ee2bf8248416c39e", "guid": "bfdfe7dc352907fc980b868725387e981f40419b5266f085cd397531622cc6ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae58278ba069750c8b471c9903e1a7de", "guid": "bfdfe7dc352907fc980b868725387e9813251c088d1a95e02fcc6610e65d4420", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d10d044bc90bbf1fadb961a42c4a90b", "guid": "bfdfe7dc352907fc980b868725387e98d6627d3e20eea644fbebcdd8f1366b2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ce3cd5a664698e907f5d0777eb43970", "guid": "bfdfe7dc352907fc980b868725387e98dd0cc4f72f7afa88fcf0f3b5e4475bf4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9828ed39b58687b7b85f8587561af34c03", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986aa19529dc85c72ae892126656194b6f", "guid": "bfdfe7dc352907fc980b868725387e982f1b5d16e62dc88fd8271f6852d5630f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffd5bd5f21d2036158dbf173a58f1e1e", "guid": "bfdfe7dc352907fc980b868725387e981f6d2fed972a63a9e46253933199f190"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fbe130fcacf4174479ec8782b29b734", "guid": "bfdfe7dc352907fc980b868725387e981f280999fa04be1f17acb49162ac8d6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f081c121c71d082f0b05f222946a1bd7", "guid": "bfdfe7dc352907fc980b868725387e9814298ff6f5a0ca0461cfdc7871500e99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984efa8e5232f0704cd7ba609b175f07ec", "guid": "bfdfe7dc352907fc980b868725387e9871b32f71fd3572df342618439bd4ed5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98048ffd50b02f7e84a008cd642d9b149c", "guid": "bfdfe7dc352907fc980b868725387e98d26d5a811b42f91a69c17ffdc9a4b7c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987496816fe7364dbdb34640940d3f9c46", "guid": "bfdfe7dc352907fc980b868725387e98115a7f490aa09d7bbb45b4fcfa6c931c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c5677ab504ed2699abaf7c4c560639f", "guid": "bfdfe7dc352907fc980b868725387e9887bdb0c6514d6c5722c7e34fadf5074f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dc3f32ee61734cfc8eac62e3cdb52ab", "guid": "bfdfe7dc352907fc980b868725387e9859dec753378bf28c247bbdad250e04bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4f62dcb1fe12ccb52ec76f266d47130", "guid": "bfdfe7dc352907fc980b868725387e98d1bf802a2a37c754f2bc589f65437dca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb1e42e20ba7b3722f209b4c06a4e527", "guid": "bfdfe7dc352907fc980b868725387e98022581b443fdd2a29613169d903ccf5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b213c1e9a84ee389300604561a909610", "guid": "bfdfe7dc352907fc980b868725387e9886fd179c1f978861aa1d003b240979e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e484aeac19075889a31fc59e83538da9", "guid": "bfdfe7dc352907fc980b868725387e98ee3c7ef8c1ac0351bff4a85618be2217"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a401dded0d5fa17800abe2746184dea3", "guid": "bfdfe7dc352907fc980b868725387e98f28e85546d1ba0e48a4a8c81453105a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dac858b9aed71b68676e875be3bae31b", "guid": "bfdfe7dc352907fc980b868725387e988bd57fc76f61f52b1e6152b3dfdc488d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811707b5472da2d91df81ee1ee1f0314b", "guid": "bfdfe7dc352907fc980b868725387e9881eaafa78c5c3bba6c5e27e0463bd299"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b41d91b3333f8edcbd9051eab74659e4", "guid": "bfdfe7dc352907fc980b868725387e98a3140a0d935f5f37b386e6a61fcbb697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871a7e957b0c3d278407bf5b808c876fb", "guid": "bfdfe7dc352907fc980b868725387e98b3850570d634e59e0f6ca6ed65eb5082"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2e8045253b6085c6d5cab4a059fe334", "guid": "bfdfe7dc352907fc980b868725387e982984c410ba8d03254864abf5c6a5495c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98494dfdba4036d7fc1c7f0239c76c4ca9", "guid": "bfdfe7dc352907fc980b868725387e98b95572379e55815a34e42e3b98e58624"}], "guid": "bfdfe7dc352907fc980b868725387e980339c6b36c1660251d437a1ace8ab5da", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98f3c8536e3b54edae6b331fa69ec4b2ec"}], "guid": "bfdfe7dc352907fc980b868725387e98013c238e1e923a6002417e4f56142c2d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b1e72c52dd739b23a0e2d6cee4993e27", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9801520e3b7c64881c6d9d610b9885355e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}