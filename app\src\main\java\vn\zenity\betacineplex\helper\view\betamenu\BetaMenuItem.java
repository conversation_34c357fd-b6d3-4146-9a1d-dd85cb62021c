package vn.zenity.betacineplex.helper.view.betamenu;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.PorterDuff;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import androidx.vectordrawable.graphics.drawable.VectorDrawableCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.appcompat.widget.AppCompatTextView;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;

import vn.zenity.betacineplex.R;

public class BetaMenuItem extends AppCompatTextView{

    private int icon = -1;
    private int iconSelected = -1;
    private String title = "";
    private boolean isSelected = false;
    private Drawable leftDrawable = null;
    private String menuTag = "";
    private boolean isOverlay = true;

    public BetaMenuItem(Context context) {
        this(context, null);
    }

    public BetaMenuItem(Context context, int icon, String title, boolean isSelected) {
        this(context, null);
        this.icon = icon;
        this.title = title;
        this.isSelected = isSelected;
    }

    public BetaMenuItem(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BetaMenuItem(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        if (attrs != null) {
            TypedArray styledAttributes = getContext().obtainStyledAttributes(attrs, R.styleable.BetaMenuItem, 0, 0);
            icon = styledAttributes.getResourceId(R.styleable.BetaMenuItem_btMenuIcon, -1);
            iconSelected = styledAttributes.getResourceId(R.styleable.BetaMenuItem_btMenuIconSelected, -1);
            title = styledAttributes.getString(R.styleable.BetaMenuItem_btMenuTitle);
            if (title == null) title = "";
            menuTag = styledAttributes.getString(R.styleable.BetaMenuItem_btMenuTag);
            if (menuTag == null) menuTag = "";
            isSelected = styledAttributes.getBoolean(R.styleable.BetaMenuItem_btMenuIsSelected, false);
            isOverlay = styledAttributes.getBoolean(R.styleable.BetaMenuItem_btMenuIsOverlay, true);
            styledAttributes.recycle();
        }

        TypedValue outValue = new TypedValue();
        getContext().getTheme().resolveAttribute(android.R.attr.selectableItemBackground, outValue, true);
        setBackgroundResource(outValue.resourceId);
        setText(title);
        setTextColor(isSelected ? getResources().getColor(R.color.colorPrimaryDark) : getResources().getColor(R.color.textDark));

        if (!isInEditMode()) {
            setTypeface(ResourcesCompat.getFont(context, R.font.oswald_regular));
            setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.font_normal));
        }else {
            setTypeface(Typeface.DEFAULT_BOLD);
        }
        setGravity(Gravity.CENTER_VERTICAL);
        if (!isOverlay && isSelected && iconSelected >= 0) {
            leftDrawable = VectorDrawableCompat.create(context.getResources(), iconSelected, null);
        } else if(icon >= 0) {
            leftDrawable = VectorDrawableCompat.create(context.getResources(), icon, null);
        }
        if (leftDrawable != null && isOverlay) {
            leftDrawable.setColorFilter(isSelected ? getResources().getColor(R.color.colorPrimaryDark) : getResources().getColor(R.color.textDark), PorterDuff.Mode.SRC_ATOP);
            DrawableCompat.setTint(this.leftDrawable, isSelected ? getResources().getColor(R.color.colorPrimaryDark) : getResources().getColor(R.color.textDark));
            DrawableCompat.setTintMode(this.leftDrawable, PorterDuff.Mode.MULTIPLY);
        }
        setCompoundDrawablesRelativeWithIntrinsicBounds(leftDrawable, null, null, isSelected ? ResourcesCompat.getDrawable(context.getResources(), R.drawable.shape_primary_radius, null) : null);
        setCompoundDrawablePadding((int) (context.getResources().getDisplayMetrics().density * 8));
    }

    public void changeSelected(boolean isSelected) {
        this.isSelected = isSelected;
        if (!isOverlay && isSelected && iconSelected >= 0) {
            leftDrawable = VectorDrawableCompat.create(getResources(), iconSelected, null);
        } else if(icon >= 0) {
            leftDrawable = VectorDrawableCompat.create(getResources(), icon, null);
        }
        if (leftDrawable != null && isOverlay) {
            leftDrawable.setColorFilter(isSelected ? getResources().getColor(R.color.colorPrimaryDark) : getResources().getColor(R.color.textDark), PorterDuff.Mode.SRC_ATOP);
            DrawableCompat.setTint(this.leftDrawable, isSelected ? getResources().getColor(R.color.colorPrimaryDark) : getResources().getColor(R.color.textDark));
            DrawableCompat.setTintMode(this.leftDrawable, PorterDuff.Mode.MULTIPLY);
        }
        setTextColor(isSelected ? getResources().getColor(R.color.colorPrimaryDark) : getResources().getColor(R.color.textDark));
        setCompoundDrawablesRelativeWithIntrinsicBounds(leftDrawable, null, null, isSelected ? ResourcesCompat.getDrawable(getResources(), R.drawable.shape_primary_radius, null) : null);
    }

    public String getMenuTag() {
        return menuTag;
    }

    public void setMenuTag(String menuTag) {
        this.menuTag = menuTag;
    }

    public void changeIcon(int icon) {
        this.icon = icon;
        changeSelected(isSelected);
    }
}
