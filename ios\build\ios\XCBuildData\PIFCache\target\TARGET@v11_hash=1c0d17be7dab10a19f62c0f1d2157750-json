{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984fc5368535943567a6061e2317422930", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98870a66c11147ff67c9fb9061b1593fe6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833fbf11c00f7a2c7ec1616927f3b2831", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980526a3f36227eb54d5f7f6d033ec4774", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833fbf11c00f7a2c7ec1616927f3b2831", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a4365ae7738531572c54a0bca428c0dc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ae5381c860a00d46f99ea89dd71fe959", "guid": "bfdfe7dc352907fc980b868725387e982eb29d75e89509b82161aa9d456f5ea3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f83a8905b6056801a0de79309e994f2f", "guid": "bfdfe7dc352907fc980b868725387e988d68f8d5007bbe2e0adbbe4dc1bbe38d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bd9340ff69d5513fc8a3252a6e61d39", "guid": "bfdfe7dc352907fc980b868725387e9809da2434792e4a68062b8523e7af2ba1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fd883ccf1b66f619bbae5255c9cc822", "guid": "bfdfe7dc352907fc980b868725387e9823bc3ec8de7f1a723bd0fa74d49513a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d959adb455821d132d65ce35801f4d0c", "guid": "bfdfe7dc352907fc980b868725387e986869a84d7048bb4fc7289ec0410d9ac6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98939163e97c57ba9a746e8577269b8c97", "guid": "bfdfe7dc352907fc980b868725387e98bc0de72cbe1d8da29402aaf481fa7069"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870f316d77a0bfc96c57421d0de2ef81d", "guid": "bfdfe7dc352907fc980b868725387e9863709e61348de123a0a923711267677c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4050ced09e9ece2165716d25422438f", "guid": "bfdfe7dc352907fc980b868725387e98515e8dc1e709b532ecaf1f5868cd068c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a51fcb65e790c488a7f267a974c79a4", "guid": "bfdfe7dc352907fc980b868725387e980f60d3d4869f17721fa0a0e80fac2401"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c906486056a9bdcb134da19a3e546cd", "guid": "bfdfe7dc352907fc980b868725387e988974d542605f772d79119194ef7bd892"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98724c6b0f30b3c53662c58de02812d7fc", "guid": "bfdfe7dc352907fc980b868725387e98c1c9cc8e69cb526f725e75f745abdb6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dbb5d55dac4f6e66c592dabddf6cc3d", "guid": "bfdfe7dc352907fc980b868725387e988c5c4426776090aba51086129b65de20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d5e8c8a03068a76e30e3e9f49c1c5a0", "guid": "bfdfe7dc352907fc980b868725387e98e754e6dd1a73567a61e241a006b8eb53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1679736483668fe46353431ad028b17", "guid": "bfdfe7dc352907fc980b868725387e98e9e8f7b519ba916db7f4250a5341ad08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac731ab082d58c48b393635cc588406f", "guid": "bfdfe7dc352907fc980b868725387e9830d3f85517664648fbf31ac40f172711"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874985e409a2b596ed33564aae9013912", "guid": "bfdfe7dc352907fc980b868725387e9866ac0751990fea0411274c2881511283"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884d90c38ca08d272994717bb3f9df745", "guid": "bfdfe7dc352907fc980b868725387e9870fea080738fca3f378609d9a0268239"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9bb7927e193307526aa6842456ac73e", "guid": "bfdfe7dc352907fc980b868725387e98e8f366ea25cb67827b58fad3534c38c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980baedf3fdfc11c1fa9d01d84a8c78565", "guid": "bfdfe7dc352907fc980b868725387e98df4a936d134c5a67e6dad80fedece190"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd4e9abb16fcba0b10d05b1738b32cae", "guid": "bfdfe7dc352907fc980b868725387e988221218f5c56a1e53661fecd3ad361e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c1a2b4c87ece24f5085c421bf89a7c1", "guid": "bfdfe7dc352907fc980b868725387e9819d17212d41a703f677e53692b0d1327"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fe24cb5598b2e8066b508a44f80d559", "guid": "bfdfe7dc352907fc980b868725387e9817ddb93c10c64ab3fa0c27b25141ec18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d6c9acc64cf3c47e5582a9956746292", "guid": "bfdfe7dc352907fc980b868725387e982099f5f827c1ed9b0db050bff5d3b5d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e76ae7f241767d5552c3f1d0cb72277a", "guid": "bfdfe7dc352907fc980b868725387e98bba737b6af3d5cf7e3d2c0cd4767eb24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98600cc841574f8f111dd3bea2d29420ef", "guid": "bfdfe7dc352907fc980b868725387e98850e713acc07ebb89851abd513a06830"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afb24e8a10afe339715e7e1793025679", "guid": "bfdfe7dc352907fc980b868725387e9884898a7365ac8fd4bf180db4813de42e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989abb68f5423bac4bc235807bc9b6e5c3", "guid": "bfdfe7dc352907fc980b868725387e98f28a5a0d37bbe0c050e8bcb5b7387b92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859324900ad3b16a29597bd076718defc", "guid": "bfdfe7dc352907fc980b868725387e982d73811dd26aafaa9cc713fa24819822"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856eaefeb9333233a5fdce0e7f4e5de96", "guid": "bfdfe7dc352907fc980b868725387e985f0ea4a0df599102f74bab2525543be0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cac6b7f9578320a18eab668fbce90d02", "guid": "bfdfe7dc352907fc980b868725387e98454e8e878532fd92a2ef03cedb744f38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876998274434473abc2c6853f014e8cb8", "guid": "bfdfe7dc352907fc980b868725387e98e81a34116946f6be79e9f145c809c5fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98609cfd6aa724665b2ad62df15f56b5a9", "guid": "bfdfe7dc352907fc980b868725387e988aff07c30b77b47e3174745943ecf9b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fd7a35ea7dcfcd3e8ee715b0346cdd5", "guid": "bfdfe7dc352907fc980b868725387e9876b241a692d09b6485f7f8be3791ddef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aba8a2dfe7ce0ca58f69b4ac70f2d112", "guid": "bfdfe7dc352907fc980b868725387e98f5012b36f8a813c6b06a33fca4e66dfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b1e916b99d0de53236dee004ce38f40", "guid": "bfdfe7dc352907fc980b868725387e9815756a8bf2bb6a138ac251ea5af60d12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983de64f6dbab104a78552eb0b35a5cc76", "guid": "bfdfe7dc352907fc980b868725387e98f6f180babb3000a0b99a8f746d43f314"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5e7766291ba72ca5ee26d791d773ea5", "guid": "bfdfe7dc352907fc980b868725387e982b000bbd590059e6a89e2c36476762b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae8e3584d32cb7183ea507dbdc4128c5", "guid": "bfdfe7dc352907fc980b868725387e985d0280f4b10ed8422cfc3c41089f3d07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce37e39334dd55df6a0d1116d137e910", "guid": "bfdfe7dc352907fc980b868725387e98e7c710216d96e732c216551fe35cdbcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e890da22a1555f6fb8c0116c7ef2e93a", "guid": "bfdfe7dc352907fc980b868725387e986ab07d25459d4f2c48a2026beeb55a9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844ba5712e6c3f7b40dfacf924b47bb1b", "guid": "bfdfe7dc352907fc980b868725387e987ba05c226fb70a961acc261b2fbec104"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb9e99c30d5f6ea7522aeabe30a6e8a4", "guid": "bfdfe7dc352907fc980b868725387e9898ce2eb844db91ecca9f9c0a03822b3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fa09bc603af8bcc73dfc1d6aa65fbad", "guid": "bfdfe7dc352907fc980b868725387e9802590d9f8192ca60dae66ec33bf3374e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7a2c423cdba60c75d88a442d83ecc87", "guid": "bfdfe7dc352907fc980b868725387e985fa28673b8e187634e8513df17a65c48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f6f1aab6ea79158395fa998e54ccb8a", "guid": "bfdfe7dc352907fc980b868725387e98b639ace4abf686165d7c001bb6be9b3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c53ed063daa824d59bed4ffa7e4169", "guid": "bfdfe7dc352907fc980b868725387e98bd30e3a275ec2a5c50485ce4fe4f8117"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc3e5de3b16285637a18756dc300b616", "guid": "bfdfe7dc352907fc980b868725387e984b3adf49f49b4b43eb69391d144f096e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98853b80f4845fed00028b0a04c2adcad4", "guid": "bfdfe7dc352907fc980b868725387e9816f0186d888f2459d19d62ee38037731"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980aea58ba8309ac71b69d7b5321f3d1a7", "guid": "bfdfe7dc352907fc980b868725387e9854b40eb172cc724bc8dc536a57cff384"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a491f04d1b9ac87439e00cae2597c04", "guid": "bfdfe7dc352907fc980b868725387e98cf412c636bdc49bac9bb58d0b5c423e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b670b4e99f478f66b30cfccc8ae54ec", "guid": "bfdfe7dc352907fc980b868725387e984e31f2c7e6f494509035b23082a35b2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a25c68b139101dcfad0532fa80e8328", "guid": "bfdfe7dc352907fc980b868725387e98bdb0f58fdf4819a8218ab864f7615c76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837c54ddf3a431e0289d85f1d11d52716", "guid": "bfdfe7dc352907fc980b868725387e98c7deb9b0042f9131763a30d6edc5c47a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f1c2149ead91dc22641c66bf12ffd56", "guid": "bfdfe7dc352907fc980b868725387e98d668186786113415b6f1df15227b7b21"}], "guid": "bfdfe7dc352907fc980b868725387e98a712417da8d2ec29eefb09cc181fc847", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980b264493efea7d6c99ac4f8634ffc79b", "guid": "bfdfe7dc352907fc980b868725387e985ae2c4134b6a3458437ea1a48da483ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bd22206f177418794ebcd10fb93b6d6", "guid": "bfdfe7dc352907fc980b868725387e98b392982d399dad25b7e027822620f2e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f717a490d337d6bc1161c88f6c139b63", "guid": "bfdfe7dc352907fc980b868725387e985a48144c5f2cf248ce340002b806ebba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f168bad7e413f109d73c6174454dbadf", "guid": "bfdfe7dc352907fc980b868725387e98cb1676d7125538b362fb3ebb81fb69fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2dec47ca6b133e329486ba4d7297f47", "guid": "bfdfe7dc352907fc980b868725387e9848e443ab271bb6965b0e1ef542b490a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819ab0fa966c9798c89550fe672d0e8b8", "guid": "bfdfe7dc352907fc980b868725387e980915426f6e60fcaa61754088326168f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983708bbc45873983e57b473627fb2a2c2", "guid": "bfdfe7dc352907fc980b868725387e98dd025d8be5133b4b10b279038d605f46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fca0558fe09c071825b88c4ba8404460", "guid": "bfdfe7dc352907fc980b868725387e980a3e1a6cdcfb1642e0b1bfa459bab9b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d85e68d5372f1168a30c9057b66019b", "guid": "bfdfe7dc352907fc980b868725387e980bc0902bece3ab4d6bced3f77e5b884f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8e42109540c1a5106cfbf3a9d3cef91", "guid": "bfdfe7dc352907fc980b868725387e98e8a91e5fce19eaa9e408cd296bc5e2c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7397ef3c9f98d1c7c3fbeb4183aa876", "guid": "bfdfe7dc352907fc980b868725387e982b8f31b471fe7b20ec8c6dd003a68efb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7e95b57a9e08bbfb2537be106fef953", "guid": "bfdfe7dc352907fc980b868725387e98cb4ca229a6ba1e246cfef732f067ce03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981db551cb6525b1506b7fb4ac7a16a52a", "guid": "bfdfe7dc352907fc980b868725387e98f33d50a905ce311ccb9b14703185a428"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817cb1b924b3079e77d1530681e7a313b", "guid": "bfdfe7dc352907fc980b868725387e98dc9cf44dccd3e6c4afdd2cb217353f46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98225d69ea6e0e41198a623e74d822d436", "guid": "bfdfe7dc352907fc980b868725387e98083b8dad7c98ba87384562fac99a0957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829bf2cbbb7d360119aee4ce88074f01d", "guid": "bfdfe7dc352907fc980b868725387e98ef22d260907ddd1de8e136effa453148"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a29ac7db481a69c26705801b6890293", "guid": "bfdfe7dc352907fc980b868725387e98bba566f29ff68f4260f2ba6ef8c5cd7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988aba8eb22b086c02e6eb5e4a601a0e33", "guid": "bfdfe7dc352907fc980b868725387e9882bea0e2963e6558eb1c3c7fc9877a04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f856298639b6fe4b21bbc3507c353fd2", "guid": "bfdfe7dc352907fc980b868725387e98e4ad1e34faf0d972e80f0bc31f330027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c6d2b977d337f3bf9540fd126434a20", "guid": "bfdfe7dc352907fc980b868725387e9854f547c4d20fb498f57273e084608f8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbb11fd85e117826d2fac427cd002dac", "guid": "bfdfe7dc352907fc980b868725387e98d14195a465076f82fb27f849851a336b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f3c46f850cbf61356d3e08b7cd9c791", "guid": "bfdfe7dc352907fc980b868725387e98d390fed7fd27ffda24e0fadc22313f35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837dc20ff2e477fe0bdabf88e71f0e5d0", "guid": "bfdfe7dc352907fc980b868725387e98191d659baaa7dd902db7a3b4c9e735a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889d4a4afe5456ac2541390320ce61010", "guid": "bfdfe7dc352907fc980b868725387e98e3e795805afc888162c43a539ab49619"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1254a7b5937fd20208656898a3c944b", "guid": "bfdfe7dc352907fc980b868725387e981e1afb562119048c7b9f2de1f0574201"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e41bef880ba4a0bb1bf294c71351c456", "guid": "bfdfe7dc352907fc980b868725387e98f48f5a121fdaf5b84e12514e1b0dc905"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e1547906f3648a25a59840592fc0699", "guid": "bfdfe7dc352907fc980b868725387e98c4965b77f0c6444c875e224943f4e111"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1fca5821188eda536d07d234a200177", "guid": "bfdfe7dc352907fc980b868725387e980e986df1c7b83387b04c923b6b3f561b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98179b46ccc49d1bcd6327cd02620fe620", "guid": "bfdfe7dc352907fc980b868725387e986f0246ab52b0b24ffd2020ed6ced449d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98756b7621b8a3087b2d29d8f9225df080", "guid": "bfdfe7dc352907fc980b868725387e98c98634b3d288255ec5ec4a3a63bfda2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983854f87d1b521abad76303c22e6bd299", "guid": "bfdfe7dc352907fc980b868725387e9886f15a6d2aed414240d763564a321315"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984888747354988483fc7a30692166b781", "guid": "bfdfe7dc352907fc980b868725387e9802c07ea91802839007d1fcf1c91fe4a6"}], "guid": "bfdfe7dc352907fc980b868725387e98fd5a4d6aa0a80c2894873f04a55a7742", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98da30f0f5d0a359395356735d6bb13eb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f5979f0786f4a5ad9848f0349b1159", "guid": "bfdfe7dc352907fc980b868725387e98b33ba84778b6932152f6a889f5a39044"}], "guid": "bfdfe7dc352907fc980b868725387e9879d7e5b02afbfdd8051a8f8ae7bc7041", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989720b6f4361110cc3ad0bc7e21276efc", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98dab9029a808743f5f7ae1d9a201e6db4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}