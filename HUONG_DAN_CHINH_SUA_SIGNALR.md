# Hướng dẫn chỉnh sửa kết nối SignalR

## Vấn đề

Sau khi kiểm tra kỹ các file trong repo iOS và Flutter, tôi đã phát hiện một số khác biệt quan trọng trong cách kết nối SignalR giữa hai nền tảng. Những khác biệt này gây ra lỗi "There was a bad response from the server" khi cố gắng tham gia vào một nhóm.

## Các khác biệt chính

### 1. Tên phương thức

Trong repo iOS, các phương thức được gọi là:
- `sendMessage` (không phải `Send`)
- `JoinGroup` (không phải `Join`)
- `LeaveGroup` (không phải `Leave`)

### 2. URL kết nối

Trong repo iOS, URL kết nối được định nghĩa trong `Config.swift` là:
```swift
static let SignalRURL = "https://www.betacinemas.vn/signalr/hubs"
```

### 3. Tên hub

Trong repo iOS, tên hub là `chooseSeatHub`:
```swift
fileprivate lazy var hub = hubConnection.createHubProxy(hubName: "chooseSeatHub")
```

## Các thay đổi đã thực hiện

### 1. Cập nhật tên phương thức trong SignalRClient.swift

```swift
// Trước
switch method {
case "JoinGroup", "joinGroup":
    serverMethod = "Join"
case "LeaveGroup", "leaveGroup":
    serverMethod = "Leave"
case "sendMessage", "SendMessage":
    serverMethod = "Send"
default:
    serverMethod = method
}

// Sau
switch method {
case "JoinGroup", "joinGroup":
    // Trong repo iOS, phương thức được gọi là "JoinGroup" (không phải "Join")
    serverMethod = "JoinGroup"
case "LeaveGroup", "leaveGroup":
    serverMethod = "LeaveGroup"
case "sendMessage", "SendMessage":
    // Trong repo iOS, phương thức được gọi là "sendMessage" (không phải "Send")
    serverMethod = "sendMessage"
default:
    serverMethod = method
}
```

**Lý do:** Trong repo iOS, phương thức được gọi là `JoinGroup` và `sendMessage`, không phải `Join` và `Send`. Việc sử dụng tên phương thức không chính xác dẫn đến lỗi "There was a bad response from the server".

### 2. Cập nhật tên phương thức trong native_signalr_service.dart

```dart
// Trước
final String _broadcastMessageEvent = 'broadcastMessage';
final String _sendMessageEvent = 'Send';  // Đổi thành "Send" để phù hợp với máy chủ
final String _joinGroupEvent = 'Join';    // Đổi thành "Join" để phù hợp với máy chủ
final String _leaveGroupEvent = 'Leave';  // Đổi thành "Leave" để phù hợp với máy chủ

// Sau
final String _broadcastMessageEvent = 'broadcastMessage';
final String _sendMessageEvent = 'sendMessage';  // Phải khớp với iOS: "sendMessage"
final String _joinGroupEvent = 'JoinGroup';      // Phải khớp với iOS: "JoinGroup"
final String _leaveGroupEvent = 'LeaveGroup';    // Phải khớp với iOS: "LeaveGroup"
```

**Lý do:** Cần đảm bảo tên phương thức trong Flutter khớp với tên phương thức trong iOS để đảm bảo tính nhất quán.

### 3. Cập nhật URL trong seat.dart

```dart
// Trước
await _nativeSignalRService.startSignalR(
  customUrl: ApiService.signalRUrl /*'https://www.betacinemas.vn'*/
);

// Sau
// Sử dụng URL chính xác như trong iOS: Config.SignalRURL = "https://www.betacinemas.vn/signalr/hubs"
await _nativeSignalRService.startSignalR(
  customUrl: 'https://www.betacinemas.vn/signalr/hubs'
);
```

**Lý do:** Cần sử dụng URL chính xác như trong iOS để đảm bảo kết nối đến cùng một endpoint.

### 4. Cập nhật URL trong native_signalr_service.dart

```dart
// Trước
final baseUrl = customUrl ?? 'https://www.betacinemas.vn';
// ...
await _signalRService.connect(
  baseUrl,
  headers: {
    'Authorization': 'Bearer $token',
  },
  useDefaultUrl: true,
);

// Sau
final hubUrl = customUrl ?? 'https://www.betacinemas.vn/signalr/hubs';
// ...
await _signalRService.connect(
  hubUrl,
  headers: {
    'Authorization': 'Bearer $token',
  },
  useDefaultUrl: false, // Đặt false vì URL đã đầy đủ
);
```

**Lý do:** URL đã đầy đủ, không cần thêm "/signalr" vào cuối. Việc sử dụng `useDefaultUrl: true` sẽ thêm "/signalr" vào URL, dẫn đến URL không chính xác.

## Kết quả mong đợi

Sau khi thực hiện các thay đổi này, kết nối SignalR sẽ hoạt động chính xác:

1. Kết nối ban đầu đến máy chủ SignalR sẽ thành công
2. Tham gia vào nhóm sẽ thành công
3. Gửi và nhận cập nhật ghế sẽ hoạt động chính xác

## Kiểm tra

Để kiểm tra xem các thay đổi có hiệu quả không:

1. Chạy ứng dụng và mở màn hình chọn ghế
2. Kiểm tra log để xem liệu kết nối SignalR có thành công không
3. Kiểm tra xem việc tham gia vào nhóm có thành công không
4. Thử chọn một ghế và xem liệu tin nhắn có được gửi thành công không

## Lưu ý

- Đảm bảo rằng token xác thực hợp lệ
- Đảm bảo rằng URL kết nối chính xác
- Đảm bảo rằng tên phương thức chính xác
- Đảm bảo rằng tham số được truyền đúng định dạng
