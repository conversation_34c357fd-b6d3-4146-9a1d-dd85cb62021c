<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/grayBg"
    android:clickable="true"
    android:descendantFocusability="beforeDescendants"
    android:fitsSystemWindows="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:layout_marginTop="@dimen/statusBarHeight"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/payment_detail"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_small"
            app:srcCompat="@drawable/ic_menubuger_white" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingTop="@dimen/margin_normal"
                android:paddingBottom="@dimen/margin_normal">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/padding_small"
                    android:layout_marginRight="@dimen/padding_small"
                    android:background="@drawable/shape_white_radius"
                    android:paddingLeft="@dimen/padding_normal"
                    android:paddingTop="@dimen/padding_normal"
                    android:paddingRight="@dimen/padding_normal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvFilmName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_extra_large"
                        app:fontFamily="@font/oswald_regular"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Pacific Rim: Trỗi Dậy" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvTypeFile"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_normal"
                        app:fontFamily="@font/sanspro_regular"
                        app:layout_constraintTop_toBottomOf="@+id/tvFilmName"
                        tools:text="C16  |  2D - LT  |  Võ thuật |  135 phút " />

                    <View
                        android:id="@+id/line1"
                        android:layout_width="0dp"
                        android:layout_height="0.2dp"
                        android:layout_marginTop="@dimen/margin_normal"
                        android:background="@color/grayLine"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvTypeFile" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/title1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/margin_normal"
                        android:text="@string/show_cinema"
                        android:textColor="@color/textBlack"
                        android:textSize="@dimen/font_normal"
                        app:fontFamily="@font/sanspro_regular"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/line1" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/title2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/show_date"
                        android:textColor="@color/textBlack"
                        android:textSize="@dimen/font_normal"
                        app:fontFamily="@font/sanspro_regular"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvRapChieu" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/title3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/show_hour"
                        android:textColor="@color/textBlack"
                        android:textSize="@dimen/font_normal"
                        app:fontFamily="@font/sanspro_regular"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvNgayChieu" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/title4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/show_room"
                        android:textColor="@color/textBlack"
                        android:textSize="@dimen/font_normal"
                        app:fontFamily="@font/sanspro_regular"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvGioChieu" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvSeatTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanspro_regular"
                        android:text="@string/seats"
                        android:textColor="@color/textBlack"
                        android:textSize="16sp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvPhongChieu" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvComboTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/sanspro_regular"
                        android:text="@string/combo"
                        android:textColor="@color/textBlack"
                        android:textSize="16sp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvSeats" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvRapChieu"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:textColor="@color/textBlack"
                        android:textSize="@dimen/font_normal"
                        app:fontFamily="@font/sanspro_bold"
                        app:layout_constraintBottom_toBottomOf="@+id/title1"
                        app:layout_constraintLeft_toLeftOf="@+id/tvNgayChieu"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/title1"
                        tools:text="Beta Cineplex Mỹ Đình" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvNgayChieu"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="40dp"
                        android:textColor="@color/textBlack"
                        android:textSize="@dimen/font_normal"
                        app:fontFamily="@font/sanspro_bold"
                        app:layout_constraintBottom_toBottomOf="@+id/title2"
                        app:layout_constraintLeft_toRightOf="@+id/tvComboTitle"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/title2"
                        tools:text="15/04/2018" />


                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvGioChieu"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:textColor="@color/textBlack"
                        android:textSize="@dimen/font_normal"
                        app:fontFamily="@font/sanspro_bold"
                        app:layout_constraintLeft_toLeftOf="@+id/tvNgayChieu"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/title3"
                        tools:text="16:40" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvPhongChieu"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:textColor="@color/textBlack"
                        android:textSize="@dimen/font_normal"
                        app:fontFamily="@font/sanspro_bold"
                        app:layout_constraintLeft_toLeftOf="@+id/tvNgayChieu"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/title4"
                        tools:text="MD -SCREEN - 1" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvSeats"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:textColor="@color/textBlack"
                        android:textSize="@dimen/font_normal"
                        app:fontFamily="@font/sanspro_bold"
                        app:layout_constraintLeft_toLeftOf="@+id/tvPhongChieu"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/tvSeatTitle"
                        tools:text="MD -SCREEN - 1 MD -SCREEN - 1 MD -SCREEN - 1" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvCombo"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:textColor="@color/textBlack"
                        android:textSize="@dimen/font_normal"
                        app:fontFamily="@font/sanspro_bold"
                        app:layout_constraintLeft_toLeftOf="@+id/tvSeats"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/tvComboTitle"
                        tools:text="Pacific Rim: Trỗi Dậy Pacific Rim: Trỗi Dậy Pacific Rim: Trỗi Dậy" />


                    <View
                        android:id="@+id/line2"
                        android:layout_width="0dp"
                        android:layout_height="0.2dp"
                        android:layout_marginTop="@dimen/margin_normal"
                        android:background="@color/grayLine"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvCombo" />

                    <androidx.appcompat.widget.LinearLayoutCompat
                        android:id="@+id/llPayment"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/margin_normal"
                        android:paddingBottom="@dimen/margin_normal"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/line3"
                        app:layout_constraintTop_toBottomOf="@+id/line2">

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:id="@+id/llCash"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:text="@string/cash"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_regular" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvPrice"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_bold"
                                tools:text="100.000đ" />
                        </androidx.appcompat.widget.LinearLayoutCompat>

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:id="@+id/llVoucher"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:text="@string/voucher"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_regular" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvVoucher"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_bold"
                                tools:text="100.000đ" />
                        </androidx.appcompat.widget.LinearLayoutCompat>

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:id="@+id/llPoint"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:text="@string/point"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_regular" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvPoint"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_bold"
                                tools:text="100.000đ" />
                        </androidx.appcompat.widget.LinearLayoutCompat>

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:id="@+id/llCard"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:text="@string/card"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_regular" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvCard"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_bold"
                                tools:text="100.000đ" />
                        </androidx.appcompat.widget.LinearLayoutCompat>

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:id="@+id/llShopeePay"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:text="@string/shoppe_pay"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_regular" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvShopeePay"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_bold"
                                tools:text="100.000đ" />
                        </androidx.appcompat.widget.LinearLayoutCompat>

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:id="@+id/llMomo"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:text="@string/momo"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_regular" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvMomo"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_bold"
                                tools:text="100.000đ" />
                        </androidx.appcompat.widget.LinearLayoutCompat>

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:id="@+id/llZaloPay"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:text="@string/zalo_pay"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_regular" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvZaloPay"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="3"
                                android:textColor="@color/textBlack"
                                android:textSize="@dimen/font_normal"
                                app:fontFamily="@font/sanspro_bold"
                                tools:text="100.000đ" />
                        </androidx.appcompat.widget.LinearLayoutCompat>


                    </androidx.appcompat.widget.LinearLayoutCompat>

                    <View
                        android:id="@+id/line3"
                        android:layout_width="0.2dp"
                        android:layout_height="0dp"
                        android:layout_marginTop="@dimen/margin_normal"
                        android:layout_marginEnd="@dimen/margin_normal"
                        android:background="@color/grayLine"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/llTotal"
                        app:layout_constraintTop_toBottomOf="@+id/tvCombo" />

                    <androidx.appcompat.widget.LinearLayoutCompat
                        android:id="@+id/llTotal"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_margin="@dimen/margin_normal"
                        android:gravity="center"
                        android:orientation="vertical"
                        app:layout_constraintBottom_toBottomOf="@+id/llPayment"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/llPayment">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvTotalLb"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/sum_price"
                            android:textColor="@color/textBlack"
                            android:layout_marginTop="@dimen/margin_normal"
                            android:textSize="@dimen/font_normal"
                            app:fontFamily="@font/sanspro_regular" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvTotal"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:textColor="@color/textRed"
                            android:textSize="@dimen/font_medium"
                            app:fontFamily="@font/sanspro_bold"
                            tools:text="130.000đ" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/textView5"
                            style="@style/TextContent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="@string/acccumulated_points" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvDiemTichLuy"
                            style="@style/TextContent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/colorPrimaryDark"
                            android:textSize="@dimen/font_medium"
                            app:fontFamily="@font/sanspro_bold"
                            app:layout_constraintEnd_toEndOf="@+id/textView5"
                            app:layout_constraintStart_toStartOf="@+id/textView5"
                            app:layout_constraintTop_toBottomOf="@+id/textView5"
                            tools:text="108" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/textView6"
                            style="@style/TextContent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="@string/date_acccumulated_points" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvDateExpiredPoint"
                            style="@style/TextContent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/margin_normal"
                            android:text="10/10/2023"
                            android:textColor="@color/textff3377"
                            app:fontFamily="@font/sanspro_bold"
                            app:layout_constraintEnd_toEndOf="@+id/textView6"
                            app:layout_constraintStart_toStartOf="@+id/textView6"
                            app:layout_constraintTop_toBottomOf="@+id/textView6" />
                    </androidx.appcompat.widget.LinearLayoutCompat>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="25dp"
                    android:layout_marginLeft="@dimen/padding_small"
                    android:layout_marginTop="-5dp"
                    android:layout_marginRight="@dimen/padding_small"
                    android:background="@drawable/line_white_dash"
                    android:layerType="software" />
            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/padding_small"
                android:layout_marginTop="-25dp"
                android:layout_marginRight="@dimen/padding_small"
                android:background="@drawable/shape_white_radius"
                android:padding="@dimen/padding_normal">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivCode"
                    android:layout_width="match_parent"
                    android:layout_height="70dp"
                    android:background="@color/white"
                    android:scaleType="fitXY"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvCode"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/textDark"
                    android:textSize="@dimen/font_normal"
                    app:fontFamily="@font/sanspro_bold"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ivCode"
                    tools:text="12312321324" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="0dp"
                android:layout_marginTop="-15dp"
                android:layout_marginRight="0dp"
                android:padding="@dimen/padding_normal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvGuide"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_small"
                    android:text="@string/TransactionDetail.GuideText"
                    android:textColor="@color/textDark"
                    android:textSize="@dimen/font_normal"
                    app:fontFamily="@font/sanspro_regular"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvNote"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_normal"
                    android:text="@string/TransactionDetail.NoticeText"
                    android:textColor="@color/textDark"
                    android:textSize="@dimen/font_normal"
                    app:fontFamily="@font/sanspro_regular"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvGuide" />

            </androidx.constraintlayout.widget.ConstraintLayout>


        </LinearLayout>
    </ScrollView>
</LinearLayout>
