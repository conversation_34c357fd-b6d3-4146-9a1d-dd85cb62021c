class PromotionCategory {
  PromotionCategory({
    String? categoryId,
    String? parentCategoryId,
    String? order,
    String? subChild,
    int? level,
    String? termId,
    String? name,
    String? description,
  }) {
    _categoryId = categoryId;
    _parentCategoryId = parentCategoryId;
    _order = order;
    _subChild = subChild;
    _level = level;
    _termId = termId;
    _name = name;
    _description = description;
  }

  PromotionCategory.fromJson(dynamic json) {
    _categoryId = json['CategoryId'];
    _parentCategoryId = json['ParentCategoryId'];
    _order = json['Order'];
    _subChild = json['SubChild'];
    _level = json['Level'];
    _termId = json['TermId'];
    _name = json['Name'];
    _description = json['Description'];
  }

  String? _categoryId;
  String? _parentCategoryId;
  String? _order;
  String? _subChild;
  int? _level;
  String? _termId;
  String? _name;
  String? _description;

  PromotionCategory copyWith({
    String? categoryId,
    String? parentCategoryId,
    String? order,
    String? subChild,
    int? level,
    String? termId,
    String? name,
    String? description,
  }) =>
      PromotionCategory(
        categoryId: categoryId ?? _categoryId,
        parentCategoryId: parentCategoryId ?? _parentCategoryId,
        order: order ?? _order,
        subChild: subChild ?? _subChild,
        level: level ?? _level,
        termId: termId ?? _termId,
        name: name ?? _name,
        description: description ?? _description,
      );

  String? get categoryId => _categoryId;
  String? get parentCategoryId => _parentCategoryId;
  String? get order => _order;
  String? get subChild => _subChild;
  int? get level => _level;
  String? get termId => _termId;
  String get name => _name ?? '';
  String? get description => _description;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['CategoryId'] = _categoryId;
    map['ParentCategoryId'] = _parentCategoryId;
    map['Order'] = _order;
    map['SubChild'] = _subChild;
    map['Level'] = _level;
    map['TermId'] = _termId;
    map['Name'] = _name;
    map['Description'] = _description;
    return map;
  }
}

class PromotionItem {
  PromotionItem({
    String? storylineID,
    int? storylineType,
    String? tieu_de,
    String? tieu_de_ko_dau,
    String? tieu_de_phu,
    String? tieu_de_limit,
    String? tieu_de_phu_copy,
    int? truc_tiep,
    String? duong_dan_anh_dai_dien,
    String? tieu_de_anh,
    String? tieu_de_anh_ko_dau,
    String? tom_tat_noi_dung,
    String? tom_tat_noi_dung_phu,
    String? tom_tat_noi_dung_limit,
    String? tom_tat_anh_dai_dien,
    String? publishOnDate,
    String? breadcrumbs,
    String? newsURI,
    String? duong_dan_video_dai_dien,
    String? keyWords,
    List<Content>? noi_dung_chi_tiet,
    bool? isExistVoucherCode,
  }) {
    _storylineID = storylineID;
    _storylineType = storylineType;
    _tieu_de = tieu_de;
    _tieu_de_ko_dau = tieu_de_ko_dau;
    _tieu_de_phu = tieu_de_phu;
    _tieu_de_limit = tieu_de_limit;
    _tieu_de_phu_copy = tieu_de_phu_copy;
    _truc_tiep = truc_tiep;
    _duong_dan_anh_dai_dien = duong_dan_anh_dai_dien;
    _tieu_de_anh = tieu_de_anh;
    _tieu_de_anh_ko_dau = tieu_de_anh_ko_dau;
    _tom_tat_noi_dung = tom_tat_noi_dung;
    _tom_tat_noi_dung_phu = tom_tat_noi_dung_phu;
    _tom_tat_noi_dung_limit = tom_tat_noi_dung_limit;
    _tom_tat_anh_dai_dien = tom_tat_anh_dai_dien;
    _publishOnDate = publishOnDate;
    _breadcrumbs = breadcrumbs;
    _newsURI = newsURI;
    _duong_dan_video_dai_dien = duong_dan_video_dai_dien;
    _keyWords = keyWords;
    _noi_dung_chi_tiet = noi_dung_chi_tiet;
    _isExistVoucherCode = isExistVoucherCode;
  }

  PromotionItem.fromJson(dynamic json) {
    _storylineID = json['StorylineID'];
    _storylineType = json['StorylineType'];
    _tieu_de = json['Tieu_de'];
    _tieu_de_ko_dau = json['Tieu_de_ko_dau'];
    _tieu_de_phu = json['Tieu_de_phu'];
    _tieu_de_limit = json['Tieu_de_limit'];
    _tieu_de_phu_copy = json['Tieu_de_phu_copy'];
    _truc_tiep = json['Truc_tiep'];
    _duong_dan_anh_dai_dien = json['Duong_dan_anh_dai_dien'];
    _tieu_de_anh = json['Tieu_de_anh'];
    _tieu_de_anh_ko_dau = json['Tieu_de_anh_ko_dau'];
    _tom_tat_noi_dung = json['Tom_tat_noi_dung'];
    _tom_tat_noi_dung_phu = json['Tom_tat_noi_dung_phu'];
    _tom_tat_noi_dung_limit = json['Tom_tat_noi_dung_limit'];
    _tom_tat_anh_dai_dien = json['Tom_tat_anh_dai_dien'];
    _publishOnDate = json['PublishOnDate'];
    _breadcrumbs = json['Breadcrumbs'];
    _newsURI = json['NewsURI'];
    _duong_dan_video_dai_dien = json['Duong_dan_video_dai_dien'];
    _keyWords = json['KeyWords'];
    _isExistVoucherCode = json['IsExistVoucherCode'];
    if (json['Noi_dung_chi_tiet'] != null) {
      _noi_dung_chi_tiet = [];
      json['Noi_dung_chi_tiet'].forEach((v) {
        _noi_dung_chi_tiet?.add(Content.fromJson(v));
      });
    }
  }

  String? _storylineID;
  int? _storylineType;
  String? _tieu_de;
  String? _tieu_de_ko_dau;
  String? _tieu_de_phu;
  String? _tieu_de_limit;
  String? _tieu_de_phu_copy;
  int? _truc_tiep;
  String? _duong_dan_anh_dai_dien;
  String? _tieu_de_anh;
  String? _tieu_de_anh_ko_dau;
  String? _tom_tat_noi_dung;
  String? _tom_tat_noi_dung_phu;
  String? _tom_tat_noi_dung_limit;
  String? _tom_tat_anh_dai_dien;
  String? _publishOnDate;
  String? _breadcrumbs;
  String? _newsURI;
  String? _duong_dan_video_dai_dien;
  String? _keyWords;
  List<Content>? _noi_dung_chi_tiet;
  bool? _isExistVoucherCode;

  String? get storylineID => _storylineID;
  int? get storylineType => _storylineType;
  String get title => _tieu_de ?? '';
  String? get titleNoAccent => _tieu_de_ko_dau;
  String? get subTitle => _tieu_de_phu;
  String? get limitTitle => _tieu_de_limit;
  String? get subTitleCopy => _tieu_de_phu_copy;
  int? get isLive => _truc_tiep;
  String? get imageUrl => _duong_dan_anh_dai_dien;
  String? get imageTitle => _tieu_de_anh;
  String? get imageTitleNoAccent => _tieu_de_anh_ko_dau;
  String get summary => _tom_tat_noi_dung ?? '';
  String? get subSummary => _tom_tat_noi_dung_phu;
  String? get limitSummary => _tom_tat_noi_dung_limit;
  String? get thumbnailImage => _tom_tat_anh_dai_dien;
  String? get publishDate => _publishOnDate;
  String? get breadcrumbs => _breadcrumbs;
  String? get newsURI => _newsURI;
  String? get videoUrl => _duong_dan_video_dai_dien;
  String? get keywords => _keyWords;
  List<Content>? get content => _noi_dung_chi_tiet;
  bool? get isExistVoucherCode => _isExistVoucherCode;

  String get fullContent {
    return (_noi_dung_chi_tiet?.map((content) =>
      content.paragraphData?.paragraphContent ?? "").toList() ?? []).join('\n');
  }

  String getNewsUrl(String baseUrl) {
    return baseUrl + (_newsURI ?? '');
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['StorylineID'] = _storylineID;
    map['StorylineType'] = _storylineType;
    map['Tieu_de'] = _tieu_de;
    map['Tieu_de_ko_dau'] = _tieu_de_ko_dau;
    map['Tieu_de_phu'] = _tieu_de_phu;
    map['Tieu_de_limit'] = _tieu_de_limit;
    map['Tieu_de_phu_copy'] = _tieu_de_phu_copy;
    map['Truc_tiep'] = _truc_tiep;
    map['Duong_dan_anh_dai_dien'] = _duong_dan_anh_dai_dien;
    map['Tieu_de_anh'] = _tieu_de_anh;
    map['Tieu_de_anh_ko_dau'] = _tieu_de_anh_ko_dau;
    map['Tom_tat_noi_dung'] = _tom_tat_noi_dung;
    map['Tom_tat_noi_dung_phu'] = _tom_tat_noi_dung_phu;
    map['Tom_tat_noi_dung_limit'] = _tom_tat_noi_dung_limit;
    map['Tom_tat_anh_dai_dien'] = _tom_tat_anh_dai_dien;
    map['PublishOnDate'] = _publishOnDate;
    map['Breadcrumbs'] = _breadcrumbs;
    map['NewsURI'] = _newsURI;
    map['Duong_dan_video_dai_dien'] = _duong_dan_video_dai_dien;
    map['KeyWords'] = _keyWords;
    map['IsExistVoucherCode'] = _isExistVoucherCode;
    if (_noi_dung_chi_tiet != null) {
      map['Noi_dung_chi_tiet'] = _noi_dung_chi_tiet?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class Content {
  Content({
    ParagraphData? paragraphData,
  }) {
    _paragraphData = paragraphData;
  }

  Content.fromJson(dynamic json) {
    _paragraphData = json['ParagraphData'] != null ? ParagraphData.fromJson(json['ParagraphData']) : null;
  }

  ParagraphData? _paragraphData;

  ParagraphData? get paragraphData => _paragraphData;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_paragraphData != null) {
      map['ParagraphData'] = _paragraphData?.toJson();
    }
    return map;
  }
}

class ParagraphData {
  ParagraphData({
    String? paragraphContent,
  }) {
    _paragraphContent = paragraphContent;
  }

  ParagraphData.fromJson(dynamic json) {
    _paragraphContent = json['ParagraphContent'];
  }

  String? _paragraphContent;

  String? get paragraphContent => _paragraphContent;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['ParagraphContent'] = _paragraphContent;
    return map;
  }
}
