{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98869b51186bc821cb6ac8ff727347ee4d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a41615bcfff837b7fc069e4f0fcd0e35", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f362a276f4286a9d84b7a8df431f46c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988c503bcaa04b5c6f2a2a55219ae3d981", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f362a276f4286a9d84b7a8df431f46c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98346979eb4500b5c8fe4e00f058f32270", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984ece0fefacdd71790a3ba22d884bbcc8", "guid": "bfdfe7dc352907fc980b868725387e98900b1a7a6d862624c43567df34e89f80", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98184e7c998974b20412638e455023b2d1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981a07f362cfe2cfbe4b647ddb9859f00c", "guid": "bfdfe7dc352907fc980b868725387e98ec7026ea9b5db8b45a596baedf16335d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983526ea6185d8a3d8a2e906b98a1e1fff", "guid": "bfdfe7dc352907fc980b868725387e987bacae3f39b8ee0fe5e36aa2211605c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6374a40a1da37e4d5b9bb8f71bc2691", "guid": "bfdfe7dc352907fc980b868725387e983057c19e2c7d2973cbefd761483fc7a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c136bf542c09e76c2e92edc4dc8bd74", "guid": "bfdfe7dc352907fc980b868725387e98e7d8f6216e641fd538696ef07098c494"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841410bd7355521b5531af09a774d294f", "guid": "bfdfe7dc352907fc980b868725387e98167baa676600485ae85840024a3b5f75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98164149537905cea4387467a6b98162f1", "guid": "bfdfe7dc352907fc980b868725387e98e7b37874ea8166d56f4e7797f1631bc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871d479fa4f265a610a957d981a4d47a4", "guid": "bfdfe7dc352907fc980b868725387e98ba0c24af8e3adeb6f0378ee5ccdc3681"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f047dab61b5bc0b2edaac698c8c16947", "guid": "bfdfe7dc352907fc980b868725387e980ec92a6e972c224981f76d61f661af13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98257e23c93d2d753db87bc9cd46235e25", "guid": "bfdfe7dc352907fc980b868725387e9881dad2e586d802f2badd52026f2139e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855c6cc83357ac0056102f907dedf40e3", "guid": "bfdfe7dc352907fc980b868725387e981fae2a1dd5cf357ef3956edb326d51cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812262985f6665364b00d503ccdd045ed", "guid": "bfdfe7dc352907fc980b868725387e984525a0e75a50dab8d75e075eb747cfe9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884e4aa5970fc06008718b0e661e9f6ad", "guid": "bfdfe7dc352907fc980b868725387e988d1af413c8070d6e407343e37356ad0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb0fd5c433e4a25d8065d072f26ee7e8", "guid": "bfdfe7dc352907fc980b868725387e98967a18a93104df85aa7d7737e436ebcb"}], "guid": "bfdfe7dc352907fc980b868725387e984158300b2af8bb0c596a97544951108b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98259802434f9039465aa18194dd820203"}], "guid": "bfdfe7dc352907fc980b868725387e9862b824299c40f1ccecbd7eca2f9f35d1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ef14471fdbec1d257fb03f7c4deba468", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e986b7e994733f2682020cab7fb0d193f0b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}