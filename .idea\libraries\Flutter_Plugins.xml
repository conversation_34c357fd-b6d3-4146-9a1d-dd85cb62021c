<component name="libraryTable">
  <library name="Flutter Plugins" type="FlutterPluginsLibraryType">
    <CLASSES>
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_android-2.2.15" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sqflite_android-2.4.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_contacts-1.1.9+2" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/wakelock_plus-1.2.10" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/package_info_plus-8.1.2" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+3" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fl_location_web-4.2.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/webview_flutter_wkwebview-3.20.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/just_audio_web-0.4.13" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_web-0.5.10" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_android-6.3.14" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_pdfviewer_web-26.2.14" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter-2.10.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+2" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_core-3.3.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player_android-2.7.16" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+18" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider-2.1.5" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/webview_flutter_android-4.2.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_gallery_saver-2.0.3" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_android-2.14.11" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview-6.0.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geocoding-2.2.2" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/just_audio-0.9.42" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.2" />
      <root url="file://$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/integration_test" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.3" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player-2.9.5" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_core_web-2.17.5" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/device_info_plus-10.1.2" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/audio_session-0.1.23" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_web-2.3.3" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker-1.1.2" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences-2.3.4" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.2" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player_avfoundation-2.6.5" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/open_filex-4.6.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player_web-2.3.3" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/qr_code_scanner-1.0.1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fl_location-4.4.2" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler-11.3.1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_messaging-15.0.4" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geocoding_ios-2.3.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_pdfviewer-26.2.14" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_messaging_web-3.8.12" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_web-1.0.8" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geocoding_android-3.3.1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/webview_flutter-4.10.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sqflite-2.4.1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher-6.3.1" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.0" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>