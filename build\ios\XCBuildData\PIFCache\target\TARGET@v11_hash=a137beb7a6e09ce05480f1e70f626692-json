{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818ae423f5be4ba71868ac879856decb1", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980ae35d9f3f9aa3576697722b8ba9cef6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b89092029fe192fcb533f11f7e331cee", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980a4518036862d93f4f7fce3f00387620", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b89092029fe192fcb533f11f7e331cee", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ff64376f66123ddfec93e30e8aaa9caf", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c513ad862dd88760ffb389979c53a242", "guid": "bfdfe7dc352907fc980b868725387e9868252d4458e9e2905100bc66afdb7e65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e75625c831b14fa1b01632dfb2bc0032", "guid": "bfdfe7dc352907fc980b868725387e98173a551f7f2403476e94edcb7aca07fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850b64b1a735cdaeb73f096777b3cb188", "guid": "bfdfe7dc352907fc980b868725387e985ac08f8ffea7a2776f7f6d5057d1c90c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8899896bf22bb02ce258e6de0a5714f", "guid": "bfdfe7dc352907fc980b868725387e989f7056a1b7c5e2d718129b2ad25001e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e359e5a2956ae0a61285146565376a35", "guid": "bfdfe7dc352907fc980b868725387e984d7a5e34f3b0c703bb8fd4f2150e5cff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f9f770c00f38eae0c872d8a6de0c264", "guid": "bfdfe7dc352907fc980b868725387e9884f0ada733cc92b2956a01a945e1b25b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f0f2f4dcd7783f41c36be6061356b6a", "guid": "bfdfe7dc352907fc980b868725387e98be1b98443121e77017c0f323bea41db9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bf67499628d587dabed88e10550fd98", "guid": "bfdfe7dc352907fc980b868725387e98c360a9a29500bcd5c2f4d71b2059870a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feed315846a139b8a1f5ad3e50ca489b", "guid": "bfdfe7dc352907fc980b868725387e9809df143274dcd972fd85af8ff55c7862", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c1d5ca75a3c5bb9bfd901a7ba3101f5", "guid": "bfdfe7dc352907fc980b868725387e985146a1379b8f85edba9565e7ff2a5faf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ab5f26e4b360c2e5f983beb5aab2bd2", "guid": "bfdfe7dc352907fc980b868725387e98af9f3b0cb486c96b53f40fb3114c23e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d248798a3f1a9a0d06f59d1fa0fe0f8", "guid": "bfdfe7dc352907fc980b868725387e98bb0ae34b6756f62beeb01a329d0c999d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f23ec1f2b691c8da34b474be816d19aa", "guid": "bfdfe7dc352907fc980b868725387e9826016fe5226e3f7582c98efb1e788f1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a4f44e4caab22197b6793549d37105c", "guid": "bfdfe7dc352907fc980b868725387e9821a9fcfff1106c49808c9692f00246d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98847475e02eb923a5bff35508c44ff203", "guid": "bfdfe7dc352907fc980b868725387e9899185635aa917028bde45c7883a20c19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9235b560c9899404c68c649eab960df", "guid": "bfdfe7dc352907fc980b868725387e98272a92eec89961ddff41dcfde0ac20e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a497043625adccc7eca3bb2da5078b73", "guid": "bfdfe7dc352907fc980b868725387e98ff741a75a613a3a40cc66fa0e8c284f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3e1a4b811dc155e87f5d1395c1c5115", "guid": "bfdfe7dc352907fc980b868725387e9898ef9612cd49a591f4f76fccd16e1592"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b9f81f7a0e748b4bd23337a5a454dc7", "guid": "bfdfe7dc352907fc980b868725387e9886140867009fa62ae5151d8e1b20c25f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98377959ddfc89c6f03d8af35de9e7eae5", "guid": "bfdfe7dc352907fc980b868725387e9878230b8804759764e0fc8de94633d54c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c138c36e6cb5ca89abac3a7de97783ca", "guid": "bfdfe7dc352907fc980b868725387e9833a3904079ef1aa7496d3c46525fb1fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7a03bf35b068eb7a08e7ab969cd7453", "guid": "bfdfe7dc352907fc980b868725387e98258b99c8de996551433d9fc78765545f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821d272fe035430b8d8bdef4152f3977d", "guid": "bfdfe7dc352907fc980b868725387e98b80780411802728786e10046a131458b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c3b7a801aa09a1272f3044d2bab44da", "guid": "bfdfe7dc352907fc980b868725387e98962108f1379329e5467f49d4850899a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cf5882023f3232745fb2d8317193a9a", "guid": "bfdfe7dc352907fc980b868725387e980c3588ca25bfcf47b20d22b92e4f5290"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98058a838c5cdf6247dd6d0e6b5d2415ad", "guid": "bfdfe7dc352907fc980b868725387e98af8d3c078648c661a9e5ca1d3475f580", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa62a4483c073c34d83f08ea7d8ab5d2", "guid": "bfdfe7dc352907fc980b868725387e98119b53e6b39463e5338a7df035d186a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd18ca4057c4dfff2eb3ce66e589d151", "guid": "bfdfe7dc352907fc980b868725387e98d546ede8997e90d158d6649a47eb2601", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982718306fcfa4b2229fa60b373ae38e15", "guid": "bfdfe7dc352907fc980b868725387e985320243277eddeea8623385527758d14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd1aacfad3193b1e9ecfd346799ccec6", "guid": "bfdfe7dc352907fc980b868725387e9837c2e2b95b91150f840b170da46e5b21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98289d76764288b40ff76c52bdc7e1ede4", "guid": "bfdfe7dc352907fc980b868725387e988bb89f8542d66783acb00c85fa373c61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f58162d5bb4f9a14ed1e9c98f51d1f9", "guid": "bfdfe7dc352907fc980b868725387e982c61711cae6403273aeb2fd826a2dd7e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9873207c4f056526deddaa689355df2342", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fa4aa1649e3066561dce2956dac6fdf3", "guid": "bfdfe7dc352907fc980b868725387e98a70287441ee62d5d565361c1de24c1bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3c8a9b4deaab4bedacbb2825f13d37d", "guid": "bfdfe7dc352907fc980b868725387e98abd353b603eeeddf14445435dd12811a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98733e5760f5d7cece6fef2bfa2d8fcccb", "guid": "bfdfe7dc352907fc980b868725387e98ea76299bedfedee03a623fa60f24bb36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986296781f41371511f6db61923934c373", "guid": "bfdfe7dc352907fc980b868725387e98717d174ce8ce320bdd0c495fbc8f7ce8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98379b3a1fd6b144aa1e7556b35d9c4077", "guid": "bfdfe7dc352907fc980b868725387e98cf31fac2d79eb83b1eef7de22384d9a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b47a0b8c4a4eeba1d693f61b3c7dceeb", "guid": "bfdfe7dc352907fc980b868725387e986f7963ad2a55075bd31700b78dfc49ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e01c8c75352f0c87a9f631818c8db3eb", "guid": "bfdfe7dc352907fc980b868725387e981439b5b1b34b9f7ba8ce2fce90f84bc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98010485e2b58f989cf115c6cb19330b0f", "guid": "bfdfe7dc352907fc980b868725387e98881f4389407d3a809e34b55a9da47b1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98983e4c3c3d9cc8bd5c210a1048deefa0", "guid": "bfdfe7dc352907fc980b868725387e98d3580c5bb43f124c75dfd0281bb42088"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98879d77cd65e6c1eed862f4beb01459bb", "guid": "bfdfe7dc352907fc980b868725387e98540bb5c2789a1463ec0d1c53d3c61ffb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98375b908f2b8497f4483bb9ac49d8bf9f", "guid": "bfdfe7dc352907fc980b868725387e9894a268cd0e29e8afb775b746af6aebef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98277f62e86b7ed8e1b7de065292d1e799", "guid": "bfdfe7dc352907fc980b868725387e983f5692b9cd5e7b56db136b5806424daa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d367c55e6b4be584ca183908a2ffaa34", "guid": "bfdfe7dc352907fc980b868725387e987a8add710976c2e21a4f23bc1b9d0c5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac14fba17fbbc572457dbf7d2232afc6", "guid": "bfdfe7dc352907fc980b868725387e9829a0e277046017048f876c85c16ba004"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5d72b7de9c56c8e822e39e9c12148ba", "guid": "bfdfe7dc352907fc980b868725387e984b15796953802b7b32b57c5a945413f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819765831a8d03b36716870a81c336861", "guid": "bfdfe7dc352907fc980b868725387e9851bea07fe771dc22dae9f70c4cce1616"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a7b7db8905d918b38967cbc8afb2c41", "guid": "bfdfe7dc352907fc980b868725387e98556f962c91a698315b7109c3064a0d49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f9edacb4eb6ae6be3a1a5165c990cf0", "guid": "bfdfe7dc352907fc980b868725387e98021acef4d32d3a905b4e922c3db5b3ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2ad1992ea6879571bab29761744f5dd", "guid": "bfdfe7dc352907fc980b868725387e9897594afb4da999328359653f6fa63e6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8191b98a743b8711bb44e5559f6ac85", "guid": "bfdfe7dc352907fc980b868725387e989d868cbf6f3f92a74ea2315544067df2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0eec28b0fc4693b3527b6d9be9f66b2", "guid": "bfdfe7dc352907fc980b868725387e98d990e26004dceaeebc7ae74a7a83677e"}], "guid": "bfdfe7dc352907fc980b868725387e986518e0d1ab27cba1b6e51a8438d22d62", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98d4431fbb74acdfea333645aa98572a54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "guid": "bfdfe7dc352907fc980b868725387e983ba997057a57d77ef2e51cf343d27850"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f5979f0786f4a5ad9848f0349b1159", "guid": "bfdfe7dc352907fc980b868725387e981f6b22c537e069372c0d5cb4c3a2694a"}], "guid": "bfdfe7dc352907fc980b868725387e987783e07cb3e6d4ef9ef7700aa4e6ae15", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988f7c85b69136d14cc8798bd968e69a6b", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98199bbac6a581223bc2694079b54affc9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}