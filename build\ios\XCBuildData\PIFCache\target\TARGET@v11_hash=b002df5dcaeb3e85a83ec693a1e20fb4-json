{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986474995720f3b5ace7c481115838b586", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_contacts/flutter_contacts-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_contacts/flutter_contacts-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_contacts/flutter_contacts.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_contacts", "PRODUCT_NAME": "flutter_contacts", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8ffff8c4608b48a02b8e6dff7150e62", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd623bf358b0eaaea04e2849705936d4", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_contacts/flutter_contacts-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_contacts/flutter_contacts-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_contacts/flutter_contacts.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_contacts", "PRODUCT_NAME": "flutter_contacts", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ef1fc6d25699796b1f97b56600d50813", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd623bf358b0eaaea04e2849705936d4", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_contacts/flutter_contacts-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_contacts/flutter_contacts-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_contacts/flutter_contacts.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_contacts", "PRODUCT_NAME": "flutter_contacts", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b5afe3ea1143ea5558fd7a311b1ef47e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982a6af109bf9028bddf6329a07b16b269", "guid": "bfdfe7dc352907fc980b868725387e98d6a9faf101b28e0cb6b0f6b28fd8ed44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98967451d05482e7616d48c9b287cbe248", "guid": "bfdfe7dc352907fc980b868725387e981f92bf70f178b27d9a3efa8ed140403f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982f000430adb083ec7cc5dc23620bf9cb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989f5292d02f604ab7b3bd7907c2441404", "guid": "bfdfe7dc352907fc980b868725387e98793caf8167e7011ce094e87f31b30b03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da31a69c9291600b7db161f9faab39e3", "guid": "bfdfe7dc352907fc980b868725387e98263009124ff76943b278466d51a2be88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4266d4cce456b95ba9277bdb8fcacec", "guid": "bfdfe7dc352907fc980b868725387e98d9ad7a1fd83ab08a997b7463fbfa9a98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863681c076692fd4a2a0b77c2c4454f3a", "guid": "bfdfe7dc352907fc980b868725387e98f4add278f5b9add1fb3a1d53f911946f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f27f8282eacc9c8475f72fe420b249d2", "guid": "bfdfe7dc352907fc980b868725387e98a6ee27c2d92bb828c22d4d5428d618c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc58b899971f3c29dd0d0153a26a47c6", "guid": "bfdfe7dc352907fc980b868725387e9830c714ca63519cd2b6fd4c3889cce027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c8e924079b0e2949dabed288cea4dab", "guid": "bfdfe7dc352907fc980b868725387e985ccb9cdf975d0a3aae85f5ed4a2331d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98390702bd189d14594aa7f46c0f1757e2", "guid": "bfdfe7dc352907fc980b868725387e98e3e174dea831267a68d2d3fb9317e890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa71f98386dde38f8076971cb995acc2", "guid": "bfdfe7dc352907fc980b868725387e981edb9baa65b84c3d649b2eb6d67ecc52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7e6c0912ba0d67686aab8a464e87767", "guid": "bfdfe7dc352907fc980b868725387e989c9d8fd3ffbfaf3eea40189dc2343b9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836c864889634b26b19d610c11a713586", "guid": "bfdfe7dc352907fc980b868725387e98e06f8ad587c8c6787c1a696d75887b60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820279ec1e90ac1a5874f6e00553c99fb", "guid": "bfdfe7dc352907fc980b868725387e98020f73c02cd7305c22ba22735f6269f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d564e49cceefa11d9801207d2dbd69a", "guid": "bfdfe7dc352907fc980b868725387e98aab974d220566b743a1495cfadc2e83d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a35f0dffd41aedbd2dfe9e32e7421406", "guid": "bfdfe7dc352907fc980b868725387e9830d2019e22015de92bc026367a34b33a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f99bd5410253b42bb70f452bbec60853", "guid": "bfdfe7dc352907fc980b868725387e98065b2048d2e476a0abfdbd7d52ed8f3a"}], "guid": "bfdfe7dc352907fc980b868725387e987c966a3b129a240cdc79c574c521c055", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98660c51cf828305bc3a5ec4c52aa8b13a"}], "guid": "bfdfe7dc352907fc980b868725387e98fa71aa902bf434f3999bc057159ce99f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e988a175a6448755246720f963908648c4a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e983dbb0d5d79b94fc9af349ed668188ddd", "name": "flutter_contacts", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9844de8c75da5c0dc8b59260788428245e", "name": "flutter_contacts.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}