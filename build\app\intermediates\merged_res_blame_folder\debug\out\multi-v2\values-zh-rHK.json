{"logs": [{"outputFile": "com.flutter.flutter.petro.app-mergeDebugResources-55:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\ea3b19e143447a44a4f630074d7ef7d3\\transformed\\appcompat-1.6.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,6416", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,6490"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\66324e78beec671b05773d52d84e9d9a\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "29,30,31,32,33,34,35,72", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2666,2758,2857,2951,3045,3138,3231,6495", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "2753,2852,2946,3040,3133,3226,3322,6591"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\b65ab1a894c6b8dbf614ad54010261ad\\transformed\\jetified-play-services-base-18.3.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3327,3428,3556,3671,3773,3880,3996,4098,4299,4409,4510,4639,4754,4861,4969,5024,5081", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "3423,3551,3666,3768,3875,3991,4093,4186,4404,4505,4634,4749,4856,4964,5019,5076,5148"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\5d18fffccc249c12ecbab6b8a87e0427\\transformed\\preference-1.2.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,441,609,688", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "166,247,317,436,604,683,759"}, "to": {"startLines": "54,65,69,70,73,74,75", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5153,5860,6227,6297,6596,6764,6843", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "5214,5936,6292,6411,6759,6838,6914"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\10bd4d9e9b2285a923396ec591922feb\\transformed\\jetified-exoplayer-core-2.18.7\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,478,554", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "106,162,220,273,345,399,473,549,608"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5302,5358,5414,5472,5525,5597,5651,5725,5801", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "5353,5409,5467,5520,5592,5646,5720,5796,5855"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\fd8f94385d16413adb0e8f5b1a36f410\\transformed\\jetified-zxing-android-embedded-4.3.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,104,151,244", "endColumns": "48,46,92,69", "endOffsets": "99,146,239,309"}, "to": {"startLines": "76,77,78,79", "startColumns": "4,4,4,4", "startOffsets": "6919,6968,7015,7108", "endColumns": "48,46,92,69", "endOffsets": "6963,7010,7103,7173"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\b3a78d3d4f57d7607051978acffd5ad0\\transformed\\browser-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "55,66,67,68", "startColumns": "4,4,4,4", "startOffsets": "5219,5941,6033,6134", "endColumns": "82,91,100,92", "endOffsets": "5297,6028,6129,6222"}}, {"source": "D:\\android studio\\android-gradle\\caches\\transforms-3\\d552f6a7ccd94a500b0ef15a133e6620\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4191", "endColumns": "107", "endOffsets": "4294"}}]}]}