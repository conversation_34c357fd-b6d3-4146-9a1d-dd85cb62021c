package vn.zenity.betacineplex.fcm

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.PendingIntent.FLAG_IMMUTABLE
import android.app.PendingIntent.FLAG_UPDATE_CURRENT
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationCompat.*
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.google.gson.Gson
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.DeviceHelper
import vn.zenity.betacineplex.helper.extension.PreferencesHelper
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.logD
import vn.zenity.betacineplex.view.MainActivity
import kotlin.random.Random


/**
 * Created by vinh on 1/2/18.
 */
class BetaFirebaseMessagingService : FirebaseMessagingService() {

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        val deviceId = DeviceHelper.shared.deviceId()
        if (token.isNotEmpty()) {
            if (!Global.share().isLogin && PreferencesHelper.shared.getBooleanValue(Constant.Key.isRegisterToken, false)) {
                val dis = APIClient.shared.accountAPI.unregisterFCMToken(mapOf("DeviceId" to deviceId,
                    "AccountId" to (Global.share().user?.AccountId ?: ""),
                    "DeviceToken" to token,
                    "DeviceType" to "android")).applyOn().subscribe({
                    PreferencesHelper.shared.putValue(Constant.Key.isRegisterToken, false)
                }, {
                })
            }
            logD("GCM TOKEN $token")
            val dis = APIClient.shared.accountAPI.registerFCMToken(mapOf("DeviceId" to deviceId,
                "AccountId" to (Global.share().user?.AccountId ?: ""),
                "DeviceToken" to token,
                "DeviceType" to "android")).applyOn().subscribe({
                if (it.isSuccess) {
                    PreferencesHelper.shared.putValue(Constant.Key.isRegisterToken, true)
                }
            }, {

            })
        }
    }

    override fun handleIntent(intent: Intent) {
        super.handleIntent(intent);
    }

    override fun onMessageReceived(message: RemoteMessage) {
        if (!Global.share().isLogin) {
            return
        }
        message.notification?.body?.let {
            val data = Gson().toJson(message.data)
            sendNotification(message.notification?.title
                    ?: App.shared().getString(R.string.app_name), it, data)
        }
    }

    private fun sendNotification(title: String = App.shared().getString(R.string.app_name), messageBody: String, data: String?) {
        val intent = Intent(baseContext, MainActivity::class.java)
        data?.let {
            intent.putExtra("data", data)
        }
        val pendingIntent =  PendingIntent.getActivity(this, 0 /* Request code */, intent,
            FLAG_IMMUTABLE or  FLAG_UPDATE_CURRENT )

        val channelId = getString(R.string.default_notification_channel_id)
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        val icon = BitmapFactory.decodeResource(resources, R.mipmap.ic_launcher)

        val notificationBuilder = NotificationCompat.Builder(this, channelId)
                .setLargeIcon(icon)
                .setSmallIcon(R.drawable.ic_beta)
                .setContentTitle(title)
                .setContentText(messageBody)
                .setContentIntent(pendingIntent)
                .setBadgeIconType(BADGE_ICON_LARGE)
                .setAutoCancel(false)
                .setSound(defaultSoundUri)
                .setWhen(System.currentTimeMillis())
                .setDefaults(DEFAULT_ALL)
                .setPriority( PRIORITY_HIGH)

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        if (Build.VERSION.SDK_INT >= 26) {
            val channelName = getString(R.string.default_notification_channel_id)
            val chanel = NotificationChannel(channelId,
                    channelName, NotificationManager.IMPORTANCE_HIGH)
            chanel.description = messageBody
            chanel.enableVibration(true)
            chanel.enableLights(true)
            notificationManager.createNotificationChannel(chanel)
        }

        val id = System.currentTimeMillis() % 500000 + Random(0).nextInt(10)
        notificationManager.notify(id.toInt(), notificationBuilder.build())
    }

}