package vn.zenity.betacineplex.view.film

import android.content.Context
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.*
import java.util.*

/**
 * Created by Zenity.
 */

interface BookByCinemaContractor {
    interface View : IBaseView {
        fun showListFilm(films: List<FilmModel>)
        fun showShowDates(dates: List<Calendar>)
        fun showCinemaDetail(cinema: CinemaModel)
        fun getViewContext(): Context?
    }

    interface Presenter : IBasePresenter<View> {
        fun getListFilm(date: Calendar = Calendar.getInstance(), cinemaId: String)
        fun getShowDate(cinemaId: String)
        fun getCinemaDetail(cinemaId: String)
    }
}
