import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants/index.dart';
import '../utils/index.dart';

/// Unified Notification Service for Flutter
///
/// ✅ CROSS-PLATFORM APPROACH - No native code required!
///
/// Handles both Firebase Cloud Messaging (FCM) and Local Notifications
/// across iOS and Android platforms using pure Flutter code.
///
/// Features:
/// - Cross-platform push notifications
/// - Local notifications
/// - Token registration/unregistration
/// - Notification permissions
/// - Background/foreground message handling
/// - Topic subscriptions
/// - Deep linking support
///
/// Replaces native implementations:
/// - iOS: UNUserNotificationCenter + MessagingDelegate
/// - Android: FirebaseMessagingService + NotificationManager
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  static NotificationService get instance => _instance;

  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  String? _fcmToken;
  String? _apnsToken;
  bool _isInitialized = false;

  // Notification channel constants - tương tự iOS
  static const String _channelId = 'beta_cinemas_notifications';
  static const String _channelName = 'Beta Cinemas';
  static const String _channelDescription = 'Notifications from Beta Cinemas';

  /// Initialize notification service - tương ứng với initNotification() trong iOS
  Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('🔔 Initializing NotificationService...');

    try {
      // Initialize local notifications
      await _initializeLocalNotifications();

      // Initialize Firebase messaging
      await _initializeFirebaseMessaging();

      // Request permissions
      await requestPermissions();

      // Register FCM token
      await registerFCMToken();

      _isInitialized = true;
      debugPrint('✅ NotificationService initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing NotificationService: $e');
    }
  }

  /// Initialize local notifications - tương ứng với iOS UNUserNotificationCenter setup
  Future<void> _initializeLocalNotifications() async {
    debugPrint('🔔 Initializing local notifications...');

    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channel for Android
    if (Platform.isAndroid) {
      await _createNotificationChannel();
    }
  }

  /// Create notification channel for Android - tương tự iOS notification categories
  Future<void> _createNotificationChannel() async {
    const androidChannel = AndroidNotificationChannel(
      _channelId,
      _channelName,
      description: _channelDescription,
      importance: Importance.high,
      enableVibration: true,
      enableLights: true,
      playSound: true,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);
  }

  /// Initialize Firebase messaging - tương ứng với Firebase setup trong iOS
  Future<void> _initializeFirebaseMessaging() async {
    debugPrint('🔔 Initializing Firebase messaging...');

    // Set foreground notification presentation options - tương tự iOS
    await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background message taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

    // Handle app launch from notification
    final initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      _handleMessageOpenedApp(initialMessage);
    }

    // Handle token refresh
    FirebaseMessaging.instance.onTokenRefresh.listen(_onTokenRefresh);
  }

  /// Request notification permissions - tương ứng với registerForPushNotifications() trong iOS
  Future<bool> requestPermissions() async {
    debugPrint('🔔 Requesting notification permissions...');

    try {
      final settings = await FirebaseMessaging.instance.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
        announcement: false,
        carPlay: false,
        criticalAlert: false,
      );

      final isGranted = settings.authorizationStatus == AuthorizationStatus.authorized ||
                      settings.authorizationStatus == AuthorizationStatus.provisional;

      debugPrint('🔔 Notification permission status: ${settings.authorizationStatus}');
      debugPrint('🔔 Notification permissions granted: $isGranted');

      return isGranted;
    } catch (e) {
      debugPrint('❌ Error requesting notification permissions: $e');
      return false;
    }
  }

  /// Get FCM token - tương ứng với iOS APNs token
  Future<String?> getFCMToken() async {
    try {
      _fcmToken = await FirebaseMessaging.instance.getToken();
      debugPrint('🔔 FCM Token: $_fcmToken');
      return _fcmToken;
    } catch (e) {
      debugPrint('❌ Error getting FCM token: $e');
      return null;
    }
  }

  /// Get APNs token (iOS only) - tương ứng với iOS APNs token
  Future<String?> getAPNsToken() async {
    if (!Platform.isIOS) return null;

    try {
      _apnsToken = await FirebaseMessaging.instance.getAPNSToken();
      debugPrint('🔔 APNs Token: $_apnsToken');
      return _apnsToken;
    } catch (e) {
      debugPrint('❌ Error getting APNs token: $e');
      return null;
    }
  }

  /// Register FCM token with server - tương ứng với iOS token registration
  Future<void> registerFCMToken() async {
    try {
      final token = await getFCMToken();
      if (token == null || token.isEmpty) {
        debugPrint('❌ No FCM token available for registration');
        return;
      }

      final deviceId = await _getDeviceId();
      final prefs = await SharedPreferences.getInstance();
      final accountId = prefs.getString(CPref.accountId) ?? '';

      debugPrint('🔔 Registering FCM token...');
      debugPrint('🔔 Device ID: $deviceId');
      debugPrint('🔔 Account ID: $accountId');

      // Call API to register token - implement based on your API
      final api = Api();
      final response = await api.auth.registerFCMToken(
        deviceId: deviceId,
        accountId: accountId,
        deviceToken: token,
        deviceType: Platform.isIOS ? 'ios' : 'android',
      );

      if (response?.isSuccess == true) {
        await prefs.setBool(CPref.isTokenRegistered, true);
        debugPrint('✅ FCM token registered successfully');
      } else {
        debugPrint('❌ Failed to register FCM token: ${response?.message}');
      }
    } catch (e) {
      debugPrint('❌ Error registering FCM token: $e');
    }
  }

  /// Unregister FCM token - tương ứng với iOS token unregistration
  Future<void> unregisterFCMToken() async {
    try {
      final token = _fcmToken ?? await getFCMToken();
      if (token == null || token.isEmpty) {
        debugPrint('❌ No FCM token available for unregistration');
        return;
      }

      final deviceId = await _getDeviceId();
      final prefs = await SharedPreferences.getInstance();
      final accountId = prefs.getString(CPref.accountId) ?? '';

      debugPrint('🔔 Unregistering FCM token...');

      // Call API to unregister token - implement based on your API
      final api = Api();
      final response = await api.auth.unregisterFCMToken(
        deviceId: deviceId,
        accountId: accountId,
        deviceToken: token,
        deviceType: Platform.isIOS ? 'ios' : 'android',
      );

      if (response?.isSuccess == true) {
        await prefs.setBool(CPref.isTokenRegistered, false);
        debugPrint('✅ FCM token unregistered successfully');
      } else {
        debugPrint('❌ Failed to unregister FCM token: ${response?.message}');
      }
    } catch (e) {
      debugPrint('❌ Error unregistering FCM token: $e');
    }
  }

  /// Handle foreground messages - tương ứng với iOS foreground notification handling
  void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('🔔 Received foreground message: ${message.messageId}');
    debugPrint('🔔 Title: ${message.notification?.title}');
    debugPrint('🔔 Body: ${message.notification?.body}');
    debugPrint('🔔 Data: ${message.data}');

    // Show local notification for foreground messages
    _showLocalNotification(message);
  }

  /// Handle message opened app - tương ứng với iOS notification tap handling
  void _handleMessageOpenedApp(RemoteMessage message) {
    debugPrint('🔔 Message opened app: ${message.messageId}');
    debugPrint('🔔 Data: ${message.data}');

    // Handle navigation based on notification data
    _handleNotificationNavigation(message.data);
  }

  /// Handle notification tap - tương ứng với iOS notification response handling
  void _onNotificationTapped(NotificationResponse response) {
    debugPrint('🔔 Notification tapped: ${response.id}');
    debugPrint('🔔 Payload: ${response.payload}');

    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!);
        _handleNotificationNavigation(data);
      } catch (e) {
        debugPrint('❌ Error parsing notification payload: $e');
      }
    }
  }

  /// Handle notification navigation - tương ứng với iOS deep linking
  void _handleNotificationNavigation(Map<String, dynamic> data) {
    debugPrint('🔔 Handling notification navigation with data: $data');

    // Implement navigation logic based on notification data
    // Example: navigate to specific screen based on notification type
    final screenCode = data['ScreenCode'];
    final refId = data['RefId'];

    if (screenCode != null) {
      // Navigate to specific screen
      debugPrint('🔔 Navigating to screen: $screenCode with refId: $refId');
      // Implement navigation logic here
    }
  }

  /// Show local notification - tương ứng với iOS local notification display
  Future<void> _showLocalNotification(RemoteMessage message) async {
    final notification = message.notification;
    if (notification == null) return;

    const androidDetails = AndroidNotificationDetails(
      _channelId,
      _channelName,
      channelDescription: _channelDescription,
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: true,
      enableLights: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      notification.title,
      notification.body,
      details,
      payload: jsonEncode(message.data),
    );
  }

  /// Handle token refresh - tương ứng với iOS token refresh
  void _onTokenRefresh(String token) {
    debugPrint('🔔 FCM token refreshed: $token');
    _fcmToken = token;
    registerFCMToken();
  }

  /// Get device ID - tương ứng với iOS device identifier
  Future<String> _getDeviceId() async {
    final deviceInfo = DeviceInfoPlugin();

    if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor ?? 'unknown_ios_device';
    } else if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.id;
    }

    return 'unknown_device';
  }

  /// Subscribe to topic - tương ứng với iOS topic subscription
  Future<void> subscribeToTopic(String topic) async {
    try {
      await FirebaseMessaging.instance.subscribeToTopic(topic);
      debugPrint('✅ Subscribed to topic: $topic');
    } catch (e) {
      debugPrint('❌ Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from topic - tương ứng với iOS topic unsubscription
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await FirebaseMessaging.instance.unsubscribeFromTopic(topic);
      debugPrint('✅ Unsubscribed from topic: $topic');
    } catch (e) {
      debugPrint('❌ Error unsubscribing from topic $topic: $e');
    }
  }

  /// Get current FCM token
  String? get currentFCMToken => _fcmToken;

  /// Get current APNs token
  String? get currentAPNsToken => _apnsToken;

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;
}
