package vn.zenity.betacineplex.view.film

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_filmdetail.*
import kotlinx.android.synthetic.main.item_event_home.view.*
import kotlinx.android.synthetic.main.item_header_film_detail.view.*
import load
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.helper.support.TrailerPlayActivity
import vn.zenity.betacineplex.model.FilmModel
import vn.zenity.betacineplex.model.NewsModel
import vn.zenity.betacineplex.view.event.EventDetailFragment
import vn.zenity.betacineplex.view.event.EventFragment

/**
 * Created by Zenity.
 */

class FilmDetailFragment : BaseFragment(), FilmDetailContractor.View {

    companion object {
        fun getInstance(film: FilmModel): FilmDetailFragment {
            val frag = FilmDetailFragment()
            frag.film = film
            return frag
        }

        fun getInstance(filmId: String, checkShowBuy: Boolean = false): FilmDetailFragment {
            val frag = FilmDetailFragment()
            frag.filmId = filmId
            frag.checkShowBuy = checkShowBuy
            return frag
        }
    }

    private var film: FilmModel? = null
    private var filmId: String? = null
    private val presenter = FilmDetailPresenter()
    private var listEvents = listOf<NewsModel>()
    private var checkShowBuy = false
    private lateinit var adapter: Adapter

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_filmdetail
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        btnShare?.setOnClickListener {
            var uri = film?.FilmGroupId ?: (film?.FilmId ?: return@setOnClickListener)
            uri = "${BuildConfig.SHARE_DOMAIN}/chi-tiet-phim.htm?gf=$uri"
            uri.share(R.string.share.getString(), activity ?: return@setOnClickListener)
        }
        btnBuy?.click {
            Tracking.share().selectMovie(context,film?.FilmId,film?.Name)
            openFragment(BookByFilmFragment.getInstance(film?.FilmGroupId ?: (film?.FilmId ?: return@click), false))
        }
        btnShare?.gone()
        btnBuy?.gone()
        if (film == null) {
            filmId?.let {
                presenter.getFilmDetail(it)
            }
            return
        }
        presenter.getListEventOfFilm(film!!)
        showData(true)
    }

    private fun showData(needLoadData: Boolean = false) {
        btnBuy?.visible(checkShowBuy && film?.HasShow == true)
        btnShare?.visible(!checkShowBuy || film?.HasShow != true)
        if (recyclerView.adapter == null) {
            adapter = Adapter()
            recyclerView.layoutManager = LinearLayoutManager(this.context)
            recyclerView.adapter = adapter
            btnBack.setOnClickListener {
                back()
            }
            if (needLoadData) {
                film?.FilmId?.let {
                    presenter.getFilmDetail(it)
                }
            }
        } else {
            recyclerView.adapter?.notifyItemChanged(0)
        }
    }

    override fun showListEvent(listEvents: List<NewsModel>) {
        this.listEvents = listEvents
        activity?.runOnUiThread {
            adapter.notifyDataSetChanged()
        }
    }

    override fun showFilmDetail(film: FilmModel) {
        this.film = film
        activity?.runOnUiThread {
            showData()
        }
        presenter.getListEventOfFilm(film)
    }

    private inner class Adapter : RecyclerView.Adapter<Holder>() {

        private val TYPE_EVENT = 1
        private val TYPE_HEADER = 0

        override fun getItemCount(): Int {
            return 1 + if (listEvents.size > 3) 3 else listEvents.size
        }

        override fun getItemViewType(position: Int): Int {
            if (position == 0) return TYPE_HEADER
            return TYPE_EVENT
        }

        @SuppressLint("SetTextI18n")
        override fun onBindViewHolder(holder: Holder, position: Int) {
            if (getItemViewType(position) == TYPE_HEADER) {
                holder.itemView.apply {
                    ivPoster.load(film?.MainPosterUrl?.toImageUrl())
                    ivBanner.load(film?.filmPosterUrl?.toImageUrl())
                    btnPlay.setOnClickListener {
                        val intent = Intent(activity, TrailerPlayActivity::class.java)
                        intent.putExtra(Constant.Key.trailerId, film?.TrailerURL)
                        activity?.startActivity(intent)
                    }
                    tvFilmTitle.text = film?.Name
                    if (TextUtils.isEmpty(film?.FilmFormatName)) {
                        tvFilmTypeSub.gone()
                    } else {
                        tvFilmTypeSub.visible()
                        tvFilmTypeSub.text = "(${film?.FilmFormatName})"
                    }
                    if (film?.FilmRestrictAgeName?.isNotEmpty() == true && film?.FilmRestrictAgeName != "P") {
                        tvNotive.visible()
                        tvNotive.text = getString(R.string.age_notice, film?.FilmRestrictAgeName?.replace("C", ""))
                    } else {
                        tvNotive.gone()
                    }
                    tvCast.text = film?.Actors
                    tvDirectors.text = film?.Director
                    tvLang.text = film?.MainLanguage
                    tvDescription.text = film?.Description ?: film?.ShortDescription
                    tvDuration.text = "${film?.Duration
                            ?: 0} ${R.string.minute.getString()}"
                    tvKind.text = film?.FilmGenreName
                    tvDateStart.text = film?.OpeningDate?.dateConvertFormat(showFormat = Constant.DateFormat.dateSavis)
                            ?: R.string.coming_soon.getString()
                    viewAllPromotion?.setOnClickListener {
                        openFragment(EventFragment())
                    }
                }
            } else {
                holder.itemView.apply {
                    val event = listEvents[position - 1]
                    ivEvent.load(event.Duong_dan_anh_dai_dien)
                    tvEventTitle.text = event.Tieu_de
                    setOnClickListener {
                        openFragment(EventDetailFragment.getInstance(event))
                    }
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            return if (viewType == TYPE_HEADER) {
                val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_header_film_detail, parent, false)
                Holder(itemView)
            } else {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_event_home, parent, false)
                Holder(item)
            }
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)
}
