<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/OrderedSet-Swift.h</key>
		<data>
		LaH2noL29GN1kK2LMBnNNYi5aCw=
		</data>
		<key>Headers/OrderedSet-umbrella.h</key>
		<data>
		LuRIiEZwFUIXNX3Fal0pZgS9xA8=
		</data>
		<key>Info.plist</key>
		<data>
		RGzv7YKgoPVDw6Kx7CN4HM/EY6E=
		</data>
		<key>Modules/OrderedSet.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		04YkTQwei4ZRH5o6llW78Gtl9+g=
		</data>
		<key>Modules/OrderedSet.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		ehPKiTjmbHcWYcFALUuA+FSutfQ=
		</data>
		<key>Modules/OrderedSet.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/OrderedSet.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		pK25j7dkJrBtLhrnMuX6Uhpn5nU=
		</data>
		<key>Modules/OrderedSet.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		YhJC96iPgNZ1OHJZvcdjRd4Jr64=
		</data>
		<key>Modules/OrderedSet.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/OrderedSet.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		qzffFxuVtUss+j46ry0/ZpRWgCU=
		</data>
		<key>Modules/OrderedSet.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		hmyRlrYjazuyO0jyp3yB6tobde4=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		pf393VMI5oEVuyJ0Wjuda7g8zFg=
		</data>
		<key>OrderedSet_privacy.bundle/Info.plist</key>
		<data>
		iZsc/fPEE9eGbskmJED5rbSzSYU=
		</data>
		<key>OrderedSet_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		AL1dh5ctObXBjoBiabSJ86M3HQs=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/OrderedSet-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JYTtNq8rTOfq0A5LD6gYxx9INTeMIx0EOeUmaYbFnuk=
			</data>
		</dict>
		<key>Headers/OrderedSet-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			goWqpzIPN2JjrJCMojA9BmsCcuzT8ZTX+EtbqByia0s=
			</data>
		</dict>
		<key>Modules/OrderedSet.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			+M4iBc/fqS1pnvyHEgb35W01rLlSit76IDBBiEmwSqI=
			</data>
		</dict>
		<key>Modules/OrderedSet.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			PSQfkoHB/8hvolOdSY3vpZgIcOHHNpKZQPwMHEekVzA=
			</data>
		</dict>
		<key>Modules/OrderedSet.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/OrderedSet.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			nKa/fkrwdlYAwou6/AAfyOGBTTZe2jJBRswIEK4WMRA=
			</data>
		</dict>
		<key>Modules/OrderedSet.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			ui5b+4dS8q01NJkehm1FT0Ceh2XnezK+DEunfD2ZzCU=
			</data>
		</dict>
		<key>Modules/OrderedSet.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/OrderedSet.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			rV4Tg9OhICR19YCrAvjEn4k63Pujc75KEeUnUVMaPPY=
			</data>
		</dict>
		<key>Modules/OrderedSet.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			aeqtO0J6/cVKtxuFhmsYXiCC5CfCCG7IDzxymmO/yuE=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			oZADS0GLa+XsHlua2QhC8tt90PtaHOdojoeQMCcDkIg=
			</data>
		</dict>
		<key>OrderedSet_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			0Gxu+ubl1hpLEXpfpERiKmkhhnGVBCsfNKFwYjDBaWc=
			</data>
		</dict>
		<key>OrderedSet_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			WpuPwM3bECAbtHzCgEs/AExyUUdmItJb/E61TtRuEIQ=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
