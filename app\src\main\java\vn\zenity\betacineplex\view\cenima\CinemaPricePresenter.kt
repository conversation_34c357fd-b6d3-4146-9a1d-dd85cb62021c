package vn.zenity.betacineplex.view.cenima

import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class CinemaPricePresenter : CinemaPriceContractor.Presenter {
    override fun getPrivacyPolicy(newId: String?) {
        val lang = App.shared().getCurrentLang()
        view?.get()?.showLoading()
        if (newId == null) {
            APIClient.shared.ecmAPI.getSecurityId("mobile:app:giave:$lang").applyOn()
                    .subscribe({
                        if (it.isSuccess) {
                            it.Data?.ParameterValue?.let {
                                getPrice(it)
                            }
                        }
                    }, {
                        view?.get()?.hideLoading()
                    })
        } else {
            getPrice(newId)
        }
    }

    private fun getPrice(newId: String) {
        APIClient.shared.ecmAPI.getNewWithId(newId).applyOn()
                .subscribe({
                    view?.get()?.hideLoading()
                    if (it.Data != null) {
                        view?.get()?.showPrivacyPolicy(it.Data!!)
                    }
                }, {
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<CinemaPriceContractor.View?>? = null
    override fun attachView(view: CinemaPriceContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.view?.clear()
        this.view = null
    }
}
