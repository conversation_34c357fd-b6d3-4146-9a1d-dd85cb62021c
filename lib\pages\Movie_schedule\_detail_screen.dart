import 'package:flutter/material.dart';
import 'package:flutter_app/pages/Movie_schedule/_film_choose_time.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/pages/youtube_play.dart';
import 'package:flutter_app/utils/src/convert_data.dart';

class FilmDetailScreen extends StatefulWidget {
  final FilmModel? film;
  final String? filmId; // Used if film object is not passed directly
  final bool fromBooking;

  const FilmDetailScreen({
    super.key,
    this.film,
    this.filmId,
    this.fromBooking = false,
  });

  @override
  State<FilmDetailScreen> createState() => _FilmDetailScreenState();
}

class _FilmDetailScreenState extends State<FilmDetailScreen> {
  FilmModel? _filmDetail;
  final List<dynamic> _newsData = []; // News data for the film
  bool _isLoading = true;
  String? _error;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _filmDetail = widget.film; // Use passed film initially
    if (_filmDetail == null && widget.filmId != null) {
      _fetchFilmDetails();
    } else {
      setState(() {
        _isLoading = false; // Already have film data
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _fetchFilmDetails() async {
    // TODO: Implement API call FilmApi.getFilmDetail
    setState(() {
      _isLoading = true;
      _error = null;
    });
    try {
      // final detail = await filmApi.getFilmDetail(id: widget.filmId!);
      // setState(() {
      //   _filmDetail = detail;
      //   _isLoading = false;
      // });
      // TODO: Fetch related news if needed
      // --- Placeholder ---
      await Future.delayed(const Duration(seconds: 1));
      setState(() {
        // _filmDetail = fetchedFilmData; // Assign fetched data
        _isLoading = false;
      });
      // --- End Placeholder ---
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = "Failed to load film details: $e";
      });
    }
  }

  void _playTrailer() {
    // Play the film trailer
    final String? trailerUrl = _filmDetail?.TrailerURL;
    if (trailerUrl != null && trailerUrl.isNotEmpty) {
      Uri uri = Uri.parse(trailerUrl);
      final idsUrl = uri.queryParameters['v'] ?? '';
      Navigator.push(context, MaterialPageRoute(builder: (context) => YoutubePlay(url: idsUrl,),)) ;
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Không có URL trailer')),
      );
    }
  }

  void _shareFilm() {
    // Share film information
    final String filmUrl = _filmDetail?.TrailerURL ?? "";
    final String filmName = _filmDetail?.getName() ?? "";

    // Show a snackbar for now (would use share_plus package in production)
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Sharing film: $filmName - $filmUrl')),
    );
  }

  void _navigateToBookingOrShare() {
    if (_filmDetail?.HasShow == true && !widget.fromBooking) {
      // Navigate to FilmChooseTimeScreen
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => FilmChooseTimeScreen(
            film: _filmDetail!,
            fromHome: false,
          ),
        ),
      );
    } else {
      _shareFilm();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: appBar(title: 'Đặt vé'),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text(_error!))
              : _filmDetail == null
                  ? const Center(child: Text('Không có dữ liệu phim')) // TODO: Localize
                  : CustomScrollView(
                      controller: _scrollController,
                      slivers: [
                        _buildSliverAppBar(),
                        SliverList(
                          delegate: SliverChildListDelegate(
                            [
                              _buildFilmInfoSection(),
                              _buildDescriptionSection(),
                              _buildNewsSection(),
                              const SizedBox(height: 80), // Space for the bottom button
                            ],
                          ),
                        ),
                      ],
                    ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: _isLoading || _filmDetail == null
          ? null
          : Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                width: double.infinity,
                child: FloatingActionButton.extended(
                  onPressed: _navigateToBookingOrShare,
                  // TODO: Localize text based on condition
                  label: Text(_filmDetail?.HasShow == true && !widget.fromBooking ? 'Đặt vé ngay' : 'Chia sẻ'),
                  icon: Icon(_filmDetail?.HasShow == true && !widget.fromBooking ? Icons.theaters : Icons.share),
                ),
              ),
            ),
    );
  }

  Widget _buildSliverAppBar() {
    // Get film details for the app bar
    final String logoUrl = _filmDetail?.MainPosterUrl ?? "";

    return SliverAppBar(
      expandedHeight: 250.0,
      pinned: true,
      stretch: true,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      leadingWidth: 40,
      // Giảm khoảng cách mặc định
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: const Text(
        'Chi tiết phim',
        style: TextStyle(color: Colors.white),
      ),
      centerTitle: false,
      flexibleSpace: FlexibleSpaceBar(
        stretchModes: const [StretchMode.zoomBackground],
        background: Stack(
          fit: StackFit.expand,
          children: [
            // Banner Image with Mask
            ClipPath(
              clipper: BottomRoundedClipper(), // Custom clipper for rounded bottom
              // TODO: Use CachedNetworkImage
              child: logoUrl.isNotEmpty
                  ? Image.network('${ApiService.baseUrlImage}/${Uri.decodeComponent(logoUrl)}', fit: BoxFit.cover)
                  : Container(color: Colors.grey),
            ),
            // Gradient overlay for text visibility might be needed depending on image
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.black54, Colors.transparent],
                  end: Alignment.bottomCenter,
                  begin: Alignment.topCenter,
                ),
              ),
            ),
            // Play Button
            Center(
              child: IconButton(
                icon: const Icon(Icons.play_circle_fill, color: Colors.white, size: 60),
                onPressed: _playTrailer,
              ),
            ),
            // Film Logo positioned at the bottom
            Positioned(
              bottom: 10,
              left: 16,
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  // TODO: Use CachedNetworkImage
                  child: logoUrl.isNotEmpty
                      ? Image.network('${ApiService.baseUrlImage}/${Uri.decodeComponent(logoUrl)}',
                          height: 100, width: 70, fit: BoxFit.cover)
                      : Container(height: 100, width: 70, color: Colors.grey[300]),
                ),
              ),
            ),
          ],
        ),
      ),
      // Title area in collapsed AppBar (optional)
      // title: Text(filmName, style: TextStyle(color: Colors.black)), // Appears when collapsed
    );
  }

  Widget _buildFilmInfoSection() {
    final String filmName = _filmDetail?.getName() ?? '';
    // final String ageRestrictionText = _filmDetail?.getAgeRestrictionText() ?? ''; // TODO: Implement
    // final Color ageRestrictionColor = _filmDetail?.getAgeRestrictionColor() ?? Colors.grey; // TODO: Implement

    // TODO: Get other details: Director, Actor, Type, Duration, Language, DateShow
    final String director = _filmDetail?.Director ?? '';
    final String actors = _filmDetail?.Actors ?? '';
    final String genre = _filmDetail?.getFilmGenre() ?? ''; // TODO: Implement
    final String duration = '${_filmDetail?.Duration ?? 0} phút'; // TODO: Localize 'minute'
    final String language = _filmDetail?.MainLanguage ?? ''; // TODO: Implement
    final String openingDate = Convert.date(_filmDetail?.OpeningDate ?? ""); // TODO: Implement

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  filmName,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                ),
              ),
              // if (ageRestrictionText.isNotEmpty)
              //   Container(
              //     padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              //     decoration: BoxDecoration(
              //       color: ageRestrictionColor,
              //       borderRadius: BorderRadius.circular(12),
              //     ),
              //     child: Text(
              //       ageRestrictionText,
              //       style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
              //     ),
              //   ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow('Đạo diễn', director), // TODO: Localize
          _buildInfoRow('Diễn viên', actors),
          _buildInfoRow('Thể loại', genre),
          _buildInfoRow('Thời lượng', duration),
          _buildInfoRow('Ngôn ngữ', language),
          _buildInfoRow('Ngày khởi chiếu', openingDate),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String title, String content) {
    if (content.isEmpty) return const SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100, // Adjust width as needed
            child: Text(
              title.toUpperCase(),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(content, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection() {
    // TODO: Get description from _filmDetail
    final String description = _filmDetail?.ShortDescription ?? '';
    if (description.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Nội dung phim'.toUpperCase(), // TODO: Localize
            style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          // TODO: Implement expandable text if needed
          Text(description, style: Theme.of(context).textTheme.bodyMedium),
        ],
      ),
    );
  }

  Widget _buildNewsSection() {
    // TODO: Implement News/Deals section based on _newsData
    if (_newsData.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tin tức & Ưu đãi'.toUpperCase(), // TODO: Localize
            style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          // TODO: Replace with ListView or Column of NewsAndDealsCell equivalent widgets
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _newsData.length,
            itemBuilder: (context, index) {
              final newsItem = _newsData[index];
              // return NewsAndDealsCard(news: newsItem);
              return ListTile(title: Text('News Item ${index + 1}')); // Placeholder
            },
          ),
        ],
      ),
    );
  }
}

// Custom Clipper for rounded bottom edge
class BottomRoundedClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(0, size.height - 20); // Start line slightly above bottom-left
    path.quadraticBezierTo(0, size.height, 20, size.height); // Curve at bottom-left
    path.lineTo(size.width - 20, size.height); // Line to near bottom-right
    path.quadraticBezierTo(size.width, size.height, size.width, size.height - 20); // Curve at bottom-right
    path.lineTo(size.width, 0); // Line to top-right
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
