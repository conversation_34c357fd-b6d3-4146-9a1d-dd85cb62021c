import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// Model class for seat response from SignalR
class SeatSignalrResponse {
  final String connectionId;
  final String showId;
  final int seatIndex;
  final int seatStatus;

  SeatSignalrResponse({
    required this.connectionId,
    required this.showId,
    required this.seatIndex,
    required this.seatStatus,
  });

  factory SeatSignalrResponse.fromMap(Map<String, dynamic> map) {
    return SeatSignalrResponse(
      connectionId: map['connectionId'] ?? '',
      showId: map['showId'] ?? '',
      seatIndex: int.tryParse(map['seatIndex']?.toString() ?? '0') ?? 0,
      seatStatus: int.tryParse(map['seatStatus']?.toString() ?? '0') ?? 0,
    );
  }
}

/// Enum for SignalR connection states
enum SignalRConnectionState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error,
}

/// Typedef for connection state listener
typedef ConnectionStateListener = void Function(bool isConnected);

/// Typedef for data listener
typedef DataListener = void Function(SeatSignalrResponse data);

/// SignalR service that uses platform channels to communicate with native implementations
class SignalRClassicService {
  // Singleton pattern
  static final SignalRClassicService _instance =
      SignalRClassicService._internal();
  factory SignalRClassicService() => _instance;
  SignalRClassicService._internal();

  // Method channel
  final MethodChannel _methodChannel =
      const MethodChannel('com.betacineplex/signalr');
  final EventChannel _eventChannel =
      const EventChannel('com.betacineplex/signalr/data');

  // Stream controllers
  final StreamController<bool> _connectionStateController =
      StreamController<bool>.broadcast();
  final StreamController<SeatSignalrResponse> _dataController =
      StreamController<SeatSignalrResponse>.broadcast();

  // Listeners
  final List<ConnectionStateListener> _connectionListeners = [];
  final List<DataListener> _dataListeners = [];

  // Connection state
  bool _isConnected = false;
  bool get isConnected => _isConnected;

  // Connection ID
  String _connectionId = '';
  String get connectionId => _connectionId;

  // Event subscription
  StreamSubscription? _eventSubscription;

  /// Initialize the SignalR service
  Future<void> initialize() async {
    // Set up event channel subscription
    _eventSubscription =
        _eventChannel.receiveBroadcastStream().listen(_handleEvent);

    debugPrint('SignalR Classic service initialized');
  }

  /// Connect to the SignalR hub
  Future<bool> connect(String url,
      {String hubName = 'chooseSeatHub', String? token}) async {
    try {
      // Clean up URL - remove /signalr/hubs and /chooseSeatHub if present
      String cleanUrl = url;

      // Remove /chooseSeatHub at the end
      if (cleanUrl.endsWith('/chooseSeatHub')) {
        cleanUrl =
            cleanUrl.substring(0, cleanUrl.length - '/chooseSeatHub'.length);
        debugPrint('Removed /chooseSeatHub from URL: $cleanUrl');
      }

      // Remove /signalr/hubs at the end since SignalR client will add /signalr automatically
      if (cleanUrl.endsWith('/signalr/hubs')) {
        cleanUrl =
            cleanUrl.substring(0, cleanUrl.length - '/signalr/hubs'.length);
        debugPrint('Removed /signalr/hubs from URL: $cleanUrl');
      }

      // Remove /signalr at the end since SignalR client will add it automatically
      if (cleanUrl.endsWith('/signalr')) {
        cleanUrl = cleanUrl.substring(0, cleanUrl.length - '/signalr'.length);
        debugPrint('Removed /signalr from URL: $cleanUrl');
      }

      debugPrint(
          'Connecting to SignalR hub at $cleanUrl with hubName: $hubName');

      // First try with 'connect' method
      try {
        final connectParams = <String, dynamic>{
          'url': cleanUrl,
          'hubName': hubName,
        };

        // Add token if provided
        if (token != null && token.isNotEmpty()) {
          connectParams['token'] = token;
          debugPrint('Connecting with authentication token');
        }

        final result = await _methodChannel.invokeMethod<dynamic>(
          'connect',
          connectParams,
        );

        if (result is Map) {
          _connectionId = result['connectionId']?.toString() ?? '';
          _isConnected = true;
          _notifyConnectionListeners(true);

          debugPrint('SignalR connected with ID: $_connectionId');

          // Register for the broadcastMessage method
          try {
            await _methodChannel.invokeMethod<void>(
              'on',
              {'method': 'broadcastMessage'},
            );
          } catch (e) {
            debugPrint('Error registering for broadcastMessage: $e');
            // Continue even if this fails
          }

          return true;
        } else if (result is bool && result) {
          _isConnected = true;
          _notifyConnectionListeners(true);

          debugPrint('SignalR connected (no connection ID)');

          // Try to register for the broadcastMessage method
          try {
            await _methodChannel.invokeMethod<void>(
              'on',
              {'method': 'broadcastMessage'},
            );
          } catch (e) {
            debugPrint('Error registering for broadcastMessage: $e');
            // Continue even if this fails
          }

          return true;
        }
      } catch (e) {
        debugPrint('Error with connect method: $e');
        // Fall through to try 'start' method
      }

      // Fall back to 'start' method
      final result = await _methodChannel.invokeMethod<dynamic>(
        'start',
        {'url': url},
      );

      if (result is bool && result) {
        _isConnected = true;
        _notifyConnectionListeners(true);

        debugPrint('SignalR connected using start method');
        return true;
      }

      debugPrint('SignalR connection failed: No result or invalid result');
      _isConnected = false;
      _connectionId = '';
      _notifyConnectionListeners(false);
      return false;
    } on PlatformException catch (e) {
      debugPrint('SignalR connection failed: ${e.message}');
      _isConnected = false;
      _connectionId = '';
      _notifyConnectionListeners(false);
      return false;
    } catch (e) {
      debugPrint('SignalR connection failed with unexpected error: $e');
      _isConnected = false;
      _connectionId = '';
      _notifyConnectionListeners(false);
      return false;
    }
  }

  /// Reconnect to the SignalR hub
  Future<bool> reconnect() async {
    try {
      debugPrint('Attempting to reconnect to SignalR hub');

      // First try with 'reconnect' method
      try {
        await _methodChannel.invokeMethod<void>('reconnect');
        debugPrint('Reconnect method called successfully');
        return true;
      } catch (e) {
        debugPrint('Error with reconnect method: $e');
        // Fall through to manual reconnect
      }

      // Manual reconnect: disconnect then connect
      await disconnect();

      // Wait a bit before reconnecting
      await Future.delayed(const Duration(seconds: 1));

      // Note: We can't automatically reconnect without the original parameters
      // The caller should call connect() again with the original parameters
      debugPrint('Manual reconnect requires calling connect() again');
      return false;
    } catch (e) {
      debugPrint('Error during reconnect: $e');
      return false;
    }
  }

  /// Disconnect from the SignalR hub
  Future<void> disconnect() async {
    try {
      // First try with 'disconnect' method
      try {
        await _methodChannel.invokeMethod<void>('disconnect');
      } catch (e) {
        debugPrint('Error with disconnect method: $e');
        // Fall through to try 'stop' method
        try {
          await _methodChannel.invokeMethod<void>('stop');
        } catch (e) {
          debugPrint('Error with stop method: $e');
          // Continue even if this fails
        }
      }

      _isConnected = false;
      _connectionId = '';
      _notifyConnectionListeners(false);
    } catch (e) {
      debugPrint('Error disconnecting from SignalR: $e');
    }
  }

  /// Join a SignalR group
  Future<bool> joinGroup(String groupName) async {
    if (!_isConnected) {
      debugPrint('Cannot join group: Not connected');
      return false;
    }

    try {
      debugPrint('Joining SignalR group: $groupName');

      await _methodChannel.invokeMethod<void>(
        'invoke',
        {
          'method': 'JoinGroup',
          'arguments': [groupName],
        },
      );

      debugPrint('Successfully joined group: $groupName');
      return true;
    } catch (e) {
      debugPrint('Error joining group $groupName: $e');
      return false;
    }
  }

  /// Leave a SignalR group
  Future<bool> leaveGroup(String groupName) async {
    if (!_isConnected) {
      debugPrint('Cannot leave group: Not connected');
      return false;
    }

    try {
      debugPrint('Leaving SignalR group: $groupName');

      await _methodChannel.invokeMethod<void>(
        'invoke',
        {
          'method': 'LeaveGroup',
          'arguments': [groupName],
        },
      );

      debugPrint('Successfully left group: $groupName');
      return true;
    } catch (e) {
      debugPrint('Error leaving group $groupName: $e');
      return false;
    }
  }

  /// Invoke a generic method on the SignalR hub
  Future<bool> invoke(String method, List<dynamic> arguments) async {
    if (!_isConnected) {
      debugPrint('Cannot invoke method: Not connected');
      return false;
    }

    try {
      debugPrint('Invoking SignalR method: $method with arguments: $arguments');

      await _methodChannel.invokeMethod<void>(
        'invoke',
        {
          'method': method,
          'arguments': arguments,
        },
      );

      debugPrint('Successfully invoked method: $method');
      return true;
    } catch (e) {
      debugPrint('Error invoking method $method: $e');
      return false;
    }
  }

  /// Send a seat update
  Future<bool> sendSeat(String showId, int seatIndex, int status) async {
    if (!_isConnected) {
      debugPrint('Cannot send seat update: Not connected');
      return false;
    }

    try {
      debugPrint(
          'Sending seat update: showId=$showId, seatIndex=$seatIndex, status=$status');

      // First try with 'invoke' method
      try {
        final args = [
          _connectionId,
          showId,
          seatIndex.toString(),
          status.toString()
        ];

        await _methodChannel.invokeMethod<void>(
          'invoke',
          {
            'method': 'sendMessage',
            'arguments': args,
          },
        );

        debugPrint('Seat update sent successfully using invoke method');
        return true;
      } catch (e) {
        debugPrint('Error with invoke method: $e');
        // Fall through to try 'sendSeat' method
      }

      // Fall back to 'sendSeat' method
      try {
        await _methodChannel.invokeMethod<void>(
          'sendSeat',
          {
            'showId': showId,
            'seatIndex': seatIndex,
            'status': status,
          },
        );

        debugPrint('Seat update sent successfully using sendSeat method');
        return true;
      } catch (e) {
        debugPrint('Error with sendSeat method: $e');
        rethrow; // Re-throw to be caught by outer catch
      }
    } catch (e) {
      debugPrint('Error sending seat update: $e');
      return false;
    }
  }

  /// Add a connection listener
  void addConnectionListener(ConnectionStateListener listener) {
    _connectionListeners.add(listener);
    listener(_isConnected);
  }

  /// Remove a connection listener
  void removeConnectionListener(ConnectionStateListener listener) {
    _connectionListeners.remove(listener);
  }

  /// Add a data listener
  void addDataListener(DataListener listener) {
    _dataListeners.add(listener);
  }

  /// Remove a data listener
  void removeDataListener(DataListener listener) {
    _dataListeners.remove(listener);
  }

  /// Handle events from the native side
  void _handleEvent(dynamic event) {
    debugPrint('Received event from native: $event');

    if (event is! Map) {
      debugPrint('Event is not a map: $event');
      return;
    }

    try {
      // Handle direct data format from Android
      if (event.containsKey('connectionId') &&
          event.containsKey('showId') &&
          event.containsKey('seatIndex') &&
          event.containsKey('seatStatus')) {
        final connectionId = event['connectionId'].toString();
        final showId = event['showId'].toString();
        final seatIndex = event['seatIndex'] is int
            ? event['seatIndex'] as int
            : int.tryParse(event['seatIndex'].toString()) ?? 0;
        final seatStatus = event['seatStatus'] is int
            ? event['seatStatus'] as int
            : int.tryParse(event['seatStatus'].toString()) ?? 0;

        final data = SeatSignalrResponse(
          connectionId: connectionId,
          showId: showId,
          seatIndex: seatIndex,
          seatStatus: seatStatus,
        );

        debugPrint(
            'Parsed seat data: $connectionId, $showId, $seatIndex, $seatStatus');

        _notifyDataListeners(data);
        _dataController.add(data);
        return;
      }

      // Handle connection state format
      if (event.containsKey('state')) {
        final stateIndex = event['state'] as int;
        final connectionId = event['connectionId']?.toString() ?? '';

        // State: 0 = Disconnected, 2 = Connected
        final connected = stateIndex == 2;

        _isConnected = connected;
        _connectionId = connectionId;

        debugPrint('Connection state changed: $connected, ID: $connectionId');

        _notifyConnectionListeners(connected);
        _connectionStateController.add(connected);
        return;
      }

      // Handle event format
      final eventName = event['event'] as String?;

      if (eventName == 'connectionState') {
        final connected = event['connected'] as bool? ?? false;
        final connectionId = event['connectionId'] as String?;

        _isConnected = connected;
        _connectionId = connectionId ?? '';

        _notifyConnectionListeners(connected);
        _connectionStateController.add(connected);
      } else if (eventName == 'hubMethod') {
        final method = event['method'] as String?;
        final arguments = event['arguments'] as List<dynamic>?;

        if (method == 'broadcastMessage' &&
            arguments != null &&
            arguments.length >= 4) {
          try {
            final data = SeatSignalrResponse(
              connectionId: arguments[0].toString(),
              showId: arguments[1].toString(),
              seatIndex: int.parse(arguments[2].toString()),
              seatStatus: int.parse(arguments[3].toString()),
            );

            _notifyDataListeners(data);
            _dataController.add(data);
          } catch (e) {
            debugPrint('Error parsing seat data: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('Error handling event: $e');
    }
  }

  /// Notify all connection listeners
  void _notifyConnectionListeners(bool connected) {
    for (final listener in _connectionListeners) {
      listener(connected);
    }
  }

  /// Notify all data listeners
  void _notifyDataListeners(SeatSignalrResponse data) {
    for (final listener in _dataListeners) {
      listener(data);
    }
  }

  /// Get a stream of connection state changes
  Stream<bool> get connectionStateStream => _connectionStateController.stream;

  /// Get a stream of data
  Stream<SeatSignalrResponse> get dataStream => _dataController.stream;

  /// Dispose the service
  void dispose() {
    _eventSubscription?.cancel();
    _connectionStateController.close();
    _dataController.close();
  }
}
