{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9870c2cd99459d116ddcf41e6be3bf77df", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98808e84b67c3e893857b172183b8ca008", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d3e014b32d0d93a7829ffae774e408a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9826f6050f8841890ba85f0395bba6ec74", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d3e014b32d0d93a7829ffae774e408a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981959713487bb9320278c245c17160cda", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e6b9e4676b077eeb70029206e332f04", "guid": "bfdfe7dc352907fc980b868725387e9870b9186ac373b576f865a20d85aea052"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea0d42e3cfccb7fd7366d4875e16e26c", "guid": "bfdfe7dc352907fc980b868725387e98649e8c6db19e688f760ffc39b560c01f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5b1ec92ea87b4aeab197029866df42a", "guid": "bfdfe7dc352907fc980b868725387e9843a467db44c47806f3468309f79bcb6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a08b0ce4023616214ebe193287c4587b", "guid": "bfdfe7dc352907fc980b868725387e980c2807be3341e585c7dad81e5cda5d70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883a33fe7910e912e972e06e13e3d107a", "guid": "bfdfe7dc352907fc980b868725387e987e7968c75626a2eee4c6286eb8370bae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8638a36f21afbbdaa0246cdb23b873d", "guid": "bfdfe7dc352907fc980b868725387e98deeb44fa00781a5a65a5ff9ba72cdd44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d94c2f4d3f812e419319621dc8beb9c", "guid": "bfdfe7dc352907fc980b868725387e982f333a49824f32f48be1f56aa25a0d4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98420271e715a7579dd9301aa77e207400", "guid": "bfdfe7dc352907fc980b868725387e984b2c8420cb189468fc81446f4f89ac5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826c2a606cae2ca6bca2aa67a62c85ebd", "guid": "bfdfe7dc352907fc980b868725387e98d0a1b832829380d993db5448054e88a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874e1ffae7c1c6e561a1b9d7d741c1468", "guid": "bfdfe7dc352907fc980b868725387e980b346506c524bd3c8c92db5573bd1fbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896d4dfc271390f155765a7f2787d2503", "guid": "bfdfe7dc352907fc980b868725387e98aca638d0a511a18a2e917c38e89775b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986803630e59b43270cb83d14c25ec5a1b", "guid": "bfdfe7dc352907fc980b868725387e987418aeef1ad58b157573857a2dd043b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f3441619ca2e31337ff1ef214da9e55", "guid": "bfdfe7dc352907fc980b868725387e98f9abb657ce0ed5a369cf944296646454", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98054629e759eabe673b873012d320c7a2", "guid": "bfdfe7dc352907fc980b868725387e988e3c36d3e6ebcc2c629b21017cb92943"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a55fb8dccd584294aaeb256e535488f5", "guid": "bfdfe7dc352907fc980b868725387e980002af385b914e8171c5e8c8e37cc369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caa05c3f0fbc2697c19db8ed97ade6ca", "guid": "bfdfe7dc352907fc980b868725387e987119032266625586c626225c1e16ed9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98633f021f4817e7bade40fa63fe4c134a", "guid": "bfdfe7dc352907fc980b868725387e9813a8b33fafb6e9be21f0c402be005a65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5fc96cbce57f8b94fb3d613af7135d0", "guid": "bfdfe7dc352907fc980b868725387e9862943e2763729aba95757f09d8c38275"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b83fa3ec0757495a9b6db6e72317d8ad", "guid": "bfdfe7dc352907fc980b868725387e98a152cd2aa5f724b1f7b4bf2cee918f91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baefeee8e5fd3222ac7acaac3f0e1979", "guid": "bfdfe7dc352907fc980b868725387e988f9bbdc4b243f023a84a0add6bf37532", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fc4f606173a3fe611a4e56c7acaa492", "guid": "bfdfe7dc352907fc980b868725387e98ab6e71779f8b9ff23a5784c71e377cc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb420d74d39650a683c9431695559ac7", "guid": "bfdfe7dc352907fc980b868725387e986bf63bcfdfc592994a61d63287b949bb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988e06ab4112253c282a7df56464f1920a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a70d80bf55c3493afdd3c54a83295f39", "guid": "bfdfe7dc352907fc980b868725387e98930adcd9c1fe4d23468ae2eb23a01617"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ae7765516e630a000ffdff04ecaf636", "guid": "bfdfe7dc352907fc980b868725387e988a71d1852995381ff1085b2f8d969ed0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e77aeedd233c24fb785df7d244a59d0", "guid": "bfdfe7dc352907fc980b868725387e98d50ad077fe7b61ec3a1e3d04734f0d53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d49a73bc96aa8e6354e54b730ad4d39b", "guid": "bfdfe7dc352907fc980b868725387e98654cb81d02945110913acc828a92d3b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dff319c0c8efa3a8f24486907547992b", "guid": "bfdfe7dc352907fc980b868725387e98a3ab356c7839edd6cc8fc285b5d02d1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821d4c7c0d57ca77ddd19b0dd8e123691", "guid": "bfdfe7dc352907fc980b868725387e98f679f643fb698a5ad26c95185050a03e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98315e94e30dfcc5c81eb822d4be94a7d5", "guid": "bfdfe7dc352907fc980b868725387e983522839a6f89f002e886dfd997ef62c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98751436bae0c19f316bc3eb63302b9fb8", "guid": "bfdfe7dc352907fc980b868725387e98eda650b58e943e906e6f7ee02f79cb98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbf30e4ab52643f25ec3135adcdba1a2", "guid": "bfdfe7dc352907fc980b868725387e989024feae416e9d6b81f3d5f258bb5ce7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cc8a22b16a4357fb466fefa762b6816", "guid": "bfdfe7dc352907fc980b868725387e9810748f2255070fc67079dcf254be40c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f51e303250eda4ceb16ec87949c0445f", "guid": "bfdfe7dc352907fc980b868725387e98dc69d3d09db89e75f30c3da97354d2c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986249874d310b2ee5e4c3c8124c589ccb", "guid": "bfdfe7dc352907fc980b868725387e98f87a92b1e90ad810ae3ae5bac08707b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98352cca711167969012475612fda500b0", "guid": "bfdfe7dc352907fc980b868725387e9829526114d612602aa72a16e0798d19c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983568e4e79f0adeb16a8d23291aa32a78", "guid": "bfdfe7dc352907fc980b868725387e9826f2a17a9f3a598b9fee2f0d128ea59e"}], "guid": "bfdfe7dc352907fc980b868725387e984b29edff518803cab91a7841115e63b1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e980fda40e07697f575dabbcadaa7e29ae9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d391d713992f1d4e99af11b30a3d8fcd", "guid": "bfdfe7dc352907fc980b868725387e98ff64c1ba0990323d354e9d1a8425277d"}], "guid": "bfdfe7dc352907fc980b868725387e986f1557b9bdcdc6cbc921ea7bb330bc24", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98aecee1f0849d84119bf62dd1109b247c", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e980f99232a9f848422c7096a81a2b3e2d4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}