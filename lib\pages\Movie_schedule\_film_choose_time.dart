import 'package:flutter/material.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
// import 'package:flutter_app/pages/Movie_schedule/widget/calendar_header.dart';
import 'package:flutter_app/pages/Movie_schedule/widget/cinema_film_time_list.dart';
import 'package:flutter_app/pages/cinema/choose/seat.dart';
import 'package:flutter_app/pages/cinema/model/cinema_model.dart';
import 'package:flutter_app/pages/cinema/widget/calendar_header.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/service/location_service.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fl_location/fl_location.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';

import '_detail_screen.dart';

class FilmChooseTimeScreen extends StatefulWidget {
  final FilmModel film;
  final bool fromHome;

  const FilmChooseTimeScreen({super.key, required this.film, this.fromHome = false});

  @override
  State<FilmChooseTimeScreen> createState() => _FilmChooseTimeScreenState();
}

class _FilmChooseTimeScreenState extends State<FilmChooseTimeScreen> {
  DateTime? _selectedDate;
  String? _selectedRegion;
  List<ShowCinemaModel> _showCinemaList = [];
  List<ShowCinemaModel> _allCinemas = []; // Store all cinemas before filtering
  List<DateTime> _calendarDates = [];
  bool _isLoading = true;
  bool _isLoadingDates = true;
  bool _isLoadingLocation = false;
  String? _error;
  Location? _userPosition;
  bool _locationEnabled = false;

  // Services
  late final _filmService = RepositoryProvider.of<Api>(context).film;
  final _locationService = LocationService();

  @override
  void initState() {
    super.initState();
    _fetchFilmShowDates();
    _checkLocationAndGetNearestCinemas();
  }

  Future<void> _checkLocationAndGetNearestCinemas() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      // Check if location services are enabled
      bool serviceEnabled = await FlLocation.isLocationServicesEnabled;
      if (!serviceEnabled) {
        _showLocationServicesDisabledDialog();
        setState(() {
          _isLoadingLocation = false;
          _locationEnabled = false;
        });
        return;
      }

      // Check location permission
      LocationPermission permission = await FlLocation.checkLocationPermission();
      if (permission == LocationPermission.denied) {
        permission = await FlLocation.requestLocationPermission();
        if (permission == LocationPermission.denied) {
          _showLocationPermissionDeniedDialog();
          setState(() {
            _isLoadingLocation = false;
            _locationEnabled = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showLocationPermissionPermanentlyDeniedDialog();
        setState(() {
          _isLoadingLocation = false;
          _locationEnabled = false;
        });
        return;
      }

      // Get current position
      _userPosition = await _locationService.determinePosition(
        showError: false,
        context: context,
      );

      setState(() {
        _locationEnabled = _userPosition != null;
        _isLoadingLocation = false;
      });

      // If we already have cinemas, update their distances
      if (_showCinemaList.isNotEmpty) {
        _updateCinemaDistances();
      }
    } catch (e) {
      setState(() {
        _isLoadingLocation = false;
        _locationEnabled = false;
      });
      print('Error getting location: $e');
    }
  }

  Future<void> _fetchFilmShowDates() async {
    setState(() {
      _isLoadingDates = true;
      _error = null;
      _showCinemaList = [];
    });

    try {
      final response = await _filmService.getFilmShowDate(id: widget.film.FilmId ?? '');

      if (response != null ) {
        final List<dynamic> dateStrings = response.data['content'] ?? [];

        // Parse date strings to DateTime objects
        final List<DateTime> dates = dateStrings
            .map((dateStr) => DateTime.tryParse(dateStr.toString()))
            .where((date) => date != null)
            .cast<DateTime>()
            .toList();

        // Sort dates
        dates.sort((a, b) => a.compareTo(b));

        setState(() {
          _calendarDates = dates;
          _isLoadingDates = false;

          if (_calendarDates.isNotEmpty) {
            _selectedDate = _calendarDates.first;
            _fetchFilmShowsForDate(_selectedDate!);
          } else {
            _isLoading = false;
          }
        });
      } else {
        throw Exception('Failed to load show dates');
      }
    } catch (e) {
      setState(() {
        _isLoadingDates = false;
        _isLoading = false;
        _error = "Failed to load show dates: $e";
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load show dates: $e')),
      );
    }
  }

  void _updateCinemaDistances() {
    if (_userPosition == null || _showCinemaList.isEmpty) return;

    for (var cinema in _showCinemaList) {
      if (cinema.latitude != null && cinema.longitude != null) {
        double? lat = double.tryParse(cinema.latitude!);
        double? lng = double.tryParse(cinema.longitude!);

        if (lat != null && lng != null) {
          cinema.distance = _locationService.calculateDistance(
            _userPosition!.latitude,
            _userPosition!.longitude,
            lat,
            lng,
          );
        }
      }
    }

    // Sort cinemas by distance
    _showCinemaList.sort((a, b) {
      double distA = a.distance ?? double.infinity;
      double distB = b.distance ?? double.infinity;
      return distA.compareTo(distB);
    });

    setState(() {});
  }

  Future<void> _fetchFilmShowsForDate(DateTime date) async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    final dateString = DateFormat('yyyy-MM-dd').format(date);

    try {
      final response = await _filmService.getFilmShow(
        id: widget.film.FilmId ?? '',
        dateShow: dateString,
      );

      if (response != null ) {
        final List<dynamic> cinemaData = response.data['content'] ?? [];
        final List<ShowCinemaModel> cinemas = cinemaData
            .map((item) => ShowCinemaModel.fromJson(item))
            .toList();

        // Store all cinemas
        _allCinemas = List.from(cinemas);

        // Update distances if location is available
        if (_userPosition != null) {
          for (var cinema in cinemas) {
            if (cinema.latitude != null && cinema.longitude != null) {
              double? lat = double.tryParse(cinema.latitude!);
              double? lng = double.tryParse(cinema.longitude!);

              if (lat != null && lng != null) {
                cinema.distance = _locationService.calculateDistance(
                  _userPosition!.latitude,
                  _userPosition!.longitude,
                  lat,
                  lng,
                );
              }
            }
          }

          // Sort cinemas by distance
          cinemas.sort((a, b) {
            double distA = a.distance ?? double.infinity;
            double distB = b.distance ?? double.infinity;
            return distA.compareTo(distB);
          });
        }

        setState(() {
          _showCinemaList = cinemas;
          _isLoading = false;
        });
      } else {
        setState(() {
          _showCinemaList = [];
          _allCinemas = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = "Failed to load shows for $dateString: $e";
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load shows: $e')),
      );
    }
  }

  void _showLocationServicesDisabledDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Dịch vụ vị trí bị tắt'),
          content: const Text('Vui lòng bật dịch vụ vị trí để xem khoảng cách đến rạp chiếu phim.'),
          actions: <Widget>[
            TextButton(
              child: const Text('Hủy'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Mở cài đặt'),
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
            ),
          ],
        );
      },
    );
  }

  void _showLocationPermissionDeniedDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Quyền truy cập vị trí bị từ chối'),
          content: const Text('Vui lòng cấp quyền truy cập vị trí để xem khoảng cách đến rạp chiếu phim.'),
          actions: <Widget>[
            TextButton(
              child: const Text('Đóng'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _showLocationPermissionPermanentlyDeniedDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Quyền truy cập vị trí bị từ chối vĩnh viễn'),
          content: const Text('Vui lòng cấp quyền truy cập vị trí trong cài đặt ứng dụng để xem khoảng cách đến rạp chiếu phim.'),
          actions: <Widget>[
            TextButton(
              child: const Text('Hủy'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Mở cài đặt'),
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
            ),
          ],
        );
      },
    );
  }

  void _onDateSelected(DateTime date) {
    setState(() {
      _selectedDate = date;
    });
    _fetchFilmShowsForDate(date);
  }

  // Region selection is not implemented yet

  void _onShowTimeSelected(ShowModel show, Cinema cinema) {
    // Check if user is logged in
    _checkLoginAndProceed(show, cinema);
  }

  Future<void> _checkLoginAndProceed(ShowModel show, Cinema cinema) async {
    // This would check if the user is logged in
    // For now, we always show the login dialog
    _showLoginDialog(show, cinema);
  }

  void _showLoginDialog(ShowModel show, Cinema cinema) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Đăng nhập'),
        content: const Text('Bạn cần đăng nhập để đặt vé.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to login screen
              // After login, navigate to seat selection
              // For now, just navigate to seat selection
              _navigateToSeatSelection(show, cinema);
            },
            child: const Text('Đăng nhập'),
          ),
        ],
      ),
    );
  }

  void _navigateToSeatSelection(ShowModel show, Cinema cinema) {
    // Navigate to seat selection screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChooseSeatScreen(
          cinemaId: cinema.cinemaId,
          cinemaName: cinema.name,
          showTime: show,
          film: widget.film,
        ),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    final String filmName = widget.film.getName() ?? 'Film Name';
    final String filmType = widget.film.getHalfOptions();
    final String bannerUrl = widget.film.MainPosterUrl ?? "";
    final String regionText = _selectedRegion ?? 'Tất cả';

    return Scaffold(
      appBar: appBar(
        title: 'Đặt vé theo phim',
        titleColor: Colors.white,
      ),
      body: ListView(
        children: [
          // Header Section with film banner and info
          _buildHeader(bannerUrl, filmName, filmType),

          // Calendar for date selection
          _isLoadingDates
              ? const SizedBox(
                  height: 80,
                  child: Center(child: CircularProgressIndicator()),
                )
              : _calendarDates.isEmpty
                  ? const SizedBox(
                      height: 80,
                      child: Center(child: Text('Không có ngày chiếu')),
                    )
                  : CalendarHeaderView(
                      dates: _calendarDates,
                      onDateSelected: _onDateSelected,
                      selectedDate: _selectedDate,
                    ),

          // Region selector with location status
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Text(
                        'Khu vực: $regionText',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (_userPosition != null)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green.shade50,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.location_on, size: 14, color: Colors.green.shade700),
                              const SizedBox(width: 4),
                              Text(
                                'Đã bật vị trí',
                                style: TextStyle(
                                  color: Colors.green.shade700,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
                TextButton.icon(
                  onPressed: () {
                    if (_userPosition == null) {
                      _checkLocationAndGetNearestCinemas();
                    } else {
                      // Show region picker or toggle between all cinemas and nearby cinemas
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Đang hiển thị rạp gần nhất')),
                      );
                    }
                  },
                  icon: Icon(_userPosition != null ? Icons.refresh_rounded : Icons.location_on),
                  label: Text(_userPosition != null ? 'Làm mới' : 'Bật vị trí'),
                ),
              ],
            ),
          ),

          // Show list by cinema
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _error != null
                  ? Center(child: Text(_error!))
                  : _showCinemaList.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.movie_outlined, size: 64, color: Colors.grey.shade300),
                              const SizedBox(height: 16),
                              Text(
                                'Không có suất chiếu cho ngày này',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        )
                      : CinemaFilmTimeList(
                          cinemas: _showCinemaList,
                          film: widget.film,
                          onShowSelected: _onShowTimeSelected,
                        ),

        ],
      ),
    );
  }

  Widget _buildHeader(String bannerUrl, String filmName, String filmType) {
    // Replicates the header structure with banner, gradient, name, type
    return Stack(
      alignment: Alignment.center,
      children: [
        // Banner image
        Container(
          height: 180,
          color: Colors.grey.shade200,
          child: bannerUrl.isNotEmpty
              ? Image.network(
                  '${ApiService.baseUrlImage}/$bannerUrl',
                  fit: BoxFit.cover,
                  width: double.infinity,
                  errorBuilder: (context, error, stackTrace) {
                    return const Center(
                      child: Icon(Icons.movie, size: 50, color: Colors.grey),
                    );
                  },
                )
              : const Center(
                  child: Icon(Icons.movie, size: 50, color: Colors.grey),
                ),
        ),
        // Gradient overlay
        Container(
          height: 180,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [ Colors.white70,Colors.white60, Colors.black54],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(filmName,
                  style: Theme.of(context)
                      .textTheme
                      .titleLarge
                      ?.copyWith(color: Colors.black, fontWeight: FontWeight.bold,),textAlign: TextAlign.center,),
              const SizedBox(height: 4),
              Text(filmType, style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.black,fontSize: 16),textAlign: TextAlign.center,),
              const SizedBox(height: 4),
              InkWell(
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => FilmDetailScreen(
                                  film: widget.film,
                                )));
                  },
                  child: Container(
                      padding: const EdgeInsets.all(CSpace.base),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(25), border: Border.all(color: Colors.indigo)),
                      child: const Text('Chi tiết phim', style: TextStyle(color: Colors.indigo,fontWeight: FontWeight.bold,fontSize: 16),)))
            ],
          ),
        ),
      ],
    );
  }
}
