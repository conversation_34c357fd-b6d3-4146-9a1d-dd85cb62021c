{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983853cab31b02729d992be8d84fa41925", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/fl_location/fl_location-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/fl_location/fl_location-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/fl_location/fl_location.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "fl_location", "PRODUCT_NAME": "fl_location", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9853a795819367ab11de7cb7137618f463", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9872ff5455193961695a848344dc28f8cb", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/fl_location/fl_location-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/fl_location/fl_location-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/fl_location/fl_location.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "fl_location", "PRODUCT_NAME": "fl_location", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9852039e1f36aee8865897eeec2e954517", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9872ff5455193961695a848344dc28f8cb", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/fl_location/fl_location-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/fl_location/fl_location-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/fl_location/fl_location.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "fl_location", "PRODUCT_NAME": "fl_location", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98049e4eb9eb935db6c7bbaf9de97542a7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d4dfc79117bceb2c978fffe449ceb691", "guid": "bfdfe7dc352907fc980b868725387e981618ca6dc052d290ff508e78f887781f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833d764ba2ed2fc087975b09e7f62661b", "guid": "bfdfe7dc352907fc980b868725387e982fab8c14f1e30b11294fbfac74d9c988", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e5d1c4a04f6895946100545597008a6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f5e80a58a56c3130151e11acd378b219", "guid": "bfdfe7dc352907fc980b868725387e98df68e79ca14095c8e1009b63638097fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c43b6d98b09c4658d190ecf5d05e7c00", "guid": "bfdfe7dc352907fc980b868725387e98bb392d8ce1c55578ea5fa0c6a0b40739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f4dadf674ca2eeb2eba6ee29fac891b", "guid": "bfdfe7dc352907fc980b868725387e983d9656af3b20bd98df92abb4f4c40275"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6aabe5ebf28e57ccda9e584c305ac5d", "guid": "bfdfe7dc352907fc980b868725387e9822c2b5a9ae3587786a7d10428b97a73b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887d14377dc22da7cfba60c53f3080c0c", "guid": "bfdfe7dc352907fc980b868725387e9883657d2cd539e3a6da34afc326526bcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc7dbe9ec10a3388304a43a23aa4f35d", "guid": "bfdfe7dc352907fc980b868725387e98ab3b455e3bb16a98f0573d8c32172500"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7d4bc13c69050bc38da07d974cc2ade", "guid": "bfdfe7dc352907fc980b868725387e9885e9cdc6e0eee46e311453451b4d8a9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df95971764e647da426522c256a4bb22", "guid": "bfdfe7dc352907fc980b868725387e987978ec833461b8ca74996afc7af7c92e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98489c37ea3df3848a1ee37e6b83dad862", "guid": "bfdfe7dc352907fc980b868725387e9893fbd2c220d6037fcec152ac33b7a2dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98849fd332503f50dde3d53d79212b091d", "guid": "bfdfe7dc352907fc980b868725387e9823fafb86be954f37b195eb2ad54abcba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f49cc5ade820fa75fd880a2ee049453c", "guid": "bfdfe7dc352907fc980b868725387e98fc5038c80a4dd9370f38d9efcab46d15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880f00156b99811054a6cc9a52f07cb8b", "guid": "bfdfe7dc352907fc980b868725387e98703a48024386e62e07456716e3e0417d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e8ea5720f1f88cf87c9fcf17031f03e", "guid": "bfdfe7dc352907fc980b868725387e9819736a1ea01e39c25809147b1f8dd140"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833b93cf871b84dd4c9c0592d08582fb7", "guid": "bfdfe7dc352907fc980b868725387e98aa490ba9d926f62143e668fd9fc873d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa39f97ed4c6831af38b09e5c0a45d37", "guid": "bfdfe7dc352907fc980b868725387e98c3e22d9759aa29f62071836b4d2a2844"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad44af46fb88ada05bff7442744083e2", "guid": "bfdfe7dc352907fc980b868725387e984b3e38d6f6845db30bb4f4380f35b66b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5d58f638ace87e0ad7954296594d057", "guid": "bfdfe7dc352907fc980b868725387e9827023ce14b1db9ae8e6c7decbb78e26f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ef1251b5ed8db2da4ff074326b88805", "guid": "bfdfe7dc352907fc980b868725387e9880652c8a326c040ce9fd242563e378f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aad9595043d584e701b01ac714ed07f", "guid": "bfdfe7dc352907fc980b868725387e98e212d0f62a0cd5956442676b694b6cbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857d74b951c46ad03e443574bf7956cba", "guid": "bfdfe7dc352907fc980b868725387e9823260413b39efbabcca44891a7c084b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cb712c49ad41247691f73957d87969b", "guid": "bfdfe7dc352907fc980b868725387e98a8eb699af7ca23dadf34d3f1290e4643"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2c501e940fbc0e02392fd3d28269bef", "guid": "bfdfe7dc352907fc980b868725387e984d4cd57545e32463b8cb031cace65ad2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adc45adeeed3d7737abf5a6c706519d6", "guid": "bfdfe7dc352907fc980b868725387e98bd721e2bf0037e1fd77c4d6895c7d608"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98543f86eb8d3f49c6283450f52cc9f91f", "guid": "bfdfe7dc352907fc980b868725387e98a9fa7dd7c5ecabb034cccf501b5ff15c"}], "guid": "bfdfe7dc352907fc980b868725387e988e8cb925a1372ecc105bc6ece611008a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9801df0a884b25a75112fd1df7c415ae3b"}], "guid": "bfdfe7dc352907fc980b868725387e9867acf46bcb77b2d550d31027f44213ae", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c1af8939c63004397c0e5b77f9e87459", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98830ca7dc8459bd0ce9ae77cfdffd2a7a", "name": "fl_location", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e989afda0e086f17d634e9b7bf38f00c90c", "name": "fl_location.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}