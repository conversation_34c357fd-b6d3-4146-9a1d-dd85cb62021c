{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d433a38d3227976ffead6e70d6ecc4e2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f1f8693f7c908f5b0affe1f241e6c220", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9820a61412090dbf59265a99a700ec71ef", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b731d65cc4406511fff066edd613cb3e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9820a61412090dbf59265a99a700ec71ef", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986d26b1adc32d733906901b1009d9fe42", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cc2112df94b0b0c0340bd89af8163695", "guid": "bfdfe7dc352907fc980b868725387e9896f12159b024320a005ded0cea3d9d83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d796881e37e7835b66c9e807a2862be", "guid": "bfdfe7dc352907fc980b868725387e98e43631fad6506776abad51f12862ffbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8938f7524114941cebc3c8e235f312f", "guid": "bfdfe7dc352907fc980b868725387e98db3278b756a4d9b02999eaefa831e29c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821240eb7863f5dd51a5907f4c2e2e6ce", "guid": "bfdfe7dc352907fc980b868725387e98f0850e9be1a16e613b75f72d64f521a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98549f985e3ca19708e137fa438179dfb3", "guid": "bfdfe7dc352907fc980b868725387e9877cabb98d23ecc35a485dc8c2e280484", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9e6b4eca3cbceba6623a63818f7237f", "guid": "bfdfe7dc352907fc980b868725387e98b8e17c2a5f891e9cc1572908a7618af1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04bfbb290966ffc59926d3c5f4fbfaa", "guid": "bfdfe7dc352907fc980b868725387e984703b7dcab508f7cc1f2804ca0b9c908", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848ae2fdaddeeb818022a836b5f9e5a25", "guid": "bfdfe7dc352907fc980b868725387e98a199371f1a346b671b89f22c82e24a60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98171da214e08915b8b24ed77c6d87f9bc", "guid": "bfdfe7dc352907fc980b868725387e9879d6fee1fc0de55f71dd3027fbe6aaa6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982586b64cd46cdbea0b46392c391a0ebe", "guid": "bfdfe7dc352907fc980b868725387e988f9b07eb7c2a1bc2cf83402ab942ef34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98722b36c1e16b8904f5ef15016a851627", "guid": "bfdfe7dc352907fc980b868725387e9830f0f928678b3a15df75654129fd8571", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4f4636101885bd5f6bfeaa170add781", "guid": "bfdfe7dc352907fc980b868725387e98a1b7234155e530a31b94c2ae62000859", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9f3fbee83bf24ab6c597bb385b58e6e", "guid": "bfdfe7dc352907fc980b868725387e98a4c52247a6a8341335e241fb41c4ca05", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cdd1cb59789d12fc9a9d459ecf81c1c", "guid": "bfdfe7dc352907fc980b868725387e987b9000704bfee22762f2a73f4e7cae26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837a1e03dad63dc6c7bb5951f79cfb8dd", "guid": "bfdfe7dc352907fc980b868725387e98d003cfaca71f92ff4b9f0c3d8ae7ef7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845dd7e8e27b78c69e3c20ef440305d1d", "guid": "bfdfe7dc352907fc980b868725387e98103be823e3e1a715ec8145593f0c3210", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9891b6c87882cff834a6d4722792d27722", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980b229eaf12fc31ea880005e88c7efcc0", "guid": "bfdfe7dc352907fc980b868725387e986d33fbe1b8b953dfd72e90e4328c206f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852adf8b4cf7adc4bcc211334122e9cd6", "guid": "bfdfe7dc352907fc980b868725387e987cfede425176636679afe620b6e65872"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817567c8d35e4e217adec9c4c3848f76c", "guid": "bfdfe7dc352907fc980b868725387e986cdb4c8c5d03d6dec6bbcb7369576213"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98302a4b1dde06e56a39b84e6a3bd59b9e", "guid": "bfdfe7dc352907fc980b868725387e986c4fba26ab9aec315429c21a90ae439f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebb090518bfc4491f4047a583d375eca", "guid": "bfdfe7dc352907fc980b868725387e983e0a9363aed82112dc21c827b16dcdeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b5815b7c3d46a7c20ddc19e189a45df", "guid": "bfdfe7dc352907fc980b868725387e98a582498800b10e42dff8c6853f58914e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ab968f3010d535d48cb6d6f73c5dad7", "guid": "bfdfe7dc352907fc980b868725387e9876e2fcde8e9bf49d940acd65e1dee028"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3deca73411b27eb476e089aaa01402a", "guid": "bfdfe7dc352907fc980b868725387e98cc14715b5f033db4d0789e8693e70d05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98820c7aac90e5701fc1d1041e0b648cc3", "guid": "bfdfe7dc352907fc980b868725387e9868a2a35993f2fcf5087d40396f426b3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cccec4790c388d458b8d79d6dccb243", "guid": "bfdfe7dc352907fc980b868725387e987567d6a6245f15e108298d6be9a43613"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a75475b18066c6838f8d0089e7ad6ce", "guid": "bfdfe7dc352907fc980b868725387e98f1a0992b778d57663aaa15add653d22f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980574a6d14be600da07a99d294cfa82c0", "guid": "bfdfe7dc352907fc980b868725387e98375343f53ecceeeb9bbcd2035b3354d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983714d0b0ca6a2971e8e1ae4bef95eeae", "guid": "bfdfe7dc352907fc980b868725387e98c22ce526dc185ca6a2dfc1c3953f397c"}], "guid": "bfdfe7dc352907fc980b868725387e98448c8389dfc199dac83239b7c4d4a87d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98f30217f2638041bc05fbf64d6676681c"}], "guid": "bfdfe7dc352907fc980b868725387e9837666ca8f131a9306dd950b0c4ffecf0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ceec10663e715758ba9294abee964c51", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}