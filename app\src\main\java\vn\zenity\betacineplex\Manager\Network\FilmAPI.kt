package vn.zenity.betacineplex.Manager.Network

import io.reactivex.Observable
import io.reactivex.Single
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.http.*
import vn.zenity.betacineplex.model.*
import vn.zenity.betacineplex.model.RequestModel.CreateBookingModel

/**
 * Created by tinhvv on 4/8/18.
 */
interface FilmAPI {
    @GET("api/v2/erp/films")// màn home, isShowing là đang chiếu, false là sắp chiếu
    fun getListFilm(@Query("isShowing") isShowing: Boolean): Observable<DDKCReponse<ArrayList<FilmModel>>>

    @GET("api/v2/erp/films/{filmId}")//màn film detai
    fun filmDetail(@Path("filmId") id: String): Observable<DDKCReponse<FilmModel>>

    @GET("api/v2/erp/shows/films")// màn home, suất đặc biệt
    fun showFilm(@Query("sneakShow") sneakShow: Boolean): Observable<DDKCReponse<ArrayList<ShowFilmModel>>>

    @GET("api/v1/erp/shows/{timeId}")
    fun getSeat(@Path("timeId") timeId: String): Observable<DDKCReponse<ShowTimeModel>>

    @GET("api/v1/erp/films/{filmId}/show-dates") //Lấy ngày đang mở bán vé
    fun getShowDates(@Path("filmId") filmId: String): Observable<DDKCReponse<List<String>>>

    @GET("api/v2/erp/films/{filmId}/show-dates") //Lấy ngày đang mở bán vé
    fun getShowDatesV2(@Path("filmId") filmId: String): Observable<DDKCReponse<List<String>>>

    @GET("api/v1/erp/films/{filmId}/shows") //Lấy danh sách rạp đang bán vé phim
    fun getShows(
        @Path("filmId") filmId: String,
        @Query("dateShow") dateShow: String
    ): Observable<DDKCReponse<ArrayList<FilmBooking>>>

    @GET("api/v2/erp/films/{filmId}/shows") //Lấy danh sách rạp đang bán vé phim
    fun getShowsV2(
        @Path("filmId") filmId: String,
        @Query("dateShow") dateShow: String
    ): Observable<DDKCReponse<ArrayList<GroupFilmBooking>>>

    @GET("api/v2/erp/films/{filmId}/shows-by-location") //Lấy danh sách rạp đang bán vé phim
    fun getShowsV2WithLocation(
        @Path("filmId") filmId: String,
        @Query("dateShow") dateShow: String,
        @Query("latitude") latitude: Double,
        @Query("longitude") longitude: Double,
        @Query("page") page: Int,
        @Query("size") size: Int,
        ): Observable<DDKCReponse<GroupFilmBookingEntity>>

    @POST("Booking")
    fun createBooking(@Body bookingModel: RequestBody): Observable<ResponseBody>
}