package vn.zenity.betacineplex.view.user

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_voucher.*
import kotlinx.android.synthetic.main.item_my_voucher.view.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.model.VoucherModel
import vn.zenity.betacineplex.view.user.point.GivePointFragment
import vn.zenity.betacineplex.view.user.voucher.AddVoucherFragment
import vn.zenity.betacineplex.view.user.voucher.UseVoucherFragment
import vn.zenity.betacineplex.view.user.voucher.VoucherHistoryFragment
import vn.zenity.betacineplex.view.voucher.VoucherFreeDetailFragment
import vn.zenity.betacineplex.view.voucher.VoucherFreeFragment

/**
 * Created by Zenity.
 */

class VoucherFragment : BaseFragment(), VoucherContractor.View {
    override fun showListVoucher(vouchers: List<VoucherModel>) {
        this.vouchers = vouchers.sortedByDescending { it.EndDate?.toDate(Constant.DateFormat.default) }
        activity?.runOnUiThread {
            (recyclerView?.adapter as? Adapter)?.notifyDataSetChanged()
        }
        llEmpty.visible(this.vouchers?.isEmpty() == true)
    }

    private val presenter = VoucherPresenter()
    private var vouchers: List<VoucherModel>? = null

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_voucher
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        tvActionBarTitle?.text = R.string.voucher_of_you.getString()
        recyclerView?.layoutManager = LinearLayoutManager(context)
        recyclerView?.adapter = Adapter()
        btnAddVoucher.click {
            openFragment(AddVoucherFragment.getInstance {
                if (it)
                loadVoucher()
            })
        }
        btnVoucherHistories.click {
            openFragment(VoucherHistoryFragment())
        }
        btnVoucherFree.click {
            openFragment(VoucherFreeFragment())
        }
        loadVoucher()
    }

    override fun registerVoucherSuccess() {
        activity?.runOnUiThread {
            toast(getString(R.string.add_new_success))
        }
        loadVoucher()
    }

    private fun loadVoucher() {
        presenter.getListVoucher(Global.share().user?.AccountId ?: return, "Voucher")
    }

    private inner class Adapter : RecyclerView.Adapter<Holder>() {

        private val typeEvent = 1

        override fun getItemCount(): Int {
            return vouchers?.size ?: 0
        }

        override fun getItemViewType(position: Int): Int {
            return typeEvent
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            if(vouchers?.isEmpty() == true) {
                return
            }
            if(vouchers == null) return
            val voucher = vouchers!![position]
            holder.itemView.apply {
                tvVoucherId.text = voucher.VoucherCode
                tvVoucherStatus.text = getString(if (voucher.IsSell == true) R.string.buy else R.string.free)
                tvVoucherStatus.setTextColor((if (voucher.IsSell == true) R.color.colorfd7c02 else R.color.text03599d).getColor())
                tvDescription.text = voucher.VoucherPackageName
                tvDeadline.text = "${getString(R.string.exp)}: ${voucher.ExpirationDate?.dateConvertFormat(Constant.DateFormat.requestServer, showFormat = Constant.DateFormat.dateSavis)}"
                tvContent.text = voucher.Description

                tvContent.visible(!voucher.Description.isNullOrEmpty())

                listOf(this, tvVoucherId).click {
                    voucher.Storyline?.StorylineID?.let {
                        openFragment(VoucherFreeDetailFragment.getInstance(it, false))
                    }
                }

                btnUse.click {
                    voucher.VoucherCode?.let {
                        openFragment(UseVoucherFragment.getInstance(it))
                    }
                }
                btnGive.click {
                    openFragment(GivePointFragment.getInstance(voucher.VoucherId ?: "", voucher.VoucherPackageName ?: "") {
                        if (it) {
                            presenter.getListVoucher(Global.share().user?.AccountId
                                    ?: return@getInstance, "Voucher")
                        }
                    })
                }
                btnGive.isEnabled = voucher.IsAvaiableForGift == true
                if (voucher.IsAvaiableForGift == true) {
                    btnGive.setTextColor(R.color.colorPrimaryDark.getColor())
                    btnGive.setBackgroundResource(R.drawable.border_colorapp_radius)
                } else {
                    btnGive.setTextColor(R.color.textGray.getColor())
                    btnGive.setBackgroundResource(R.drawable.border_gray_radius)
                }
            }

        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_my_voucher, parent, false)
            return Holder(itemView)
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)
}
