import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_app/service/native_signalr_service.dart';

/// Example widget demonstrating the use of the native SignalR service
class NativeSignalRExample extends StatefulWidget {
  final String hubUrl;
  final String groupName;
  final String userId;

  const NativeSignalRExample({
    Key? key,
    required this.hubUrl,
    required this.groupName,
    required this.userId,
  }) : super(key: key);

  @override
  State<NativeSignalRExample> createState() => _NativeSignalRExampleState();
}

class _NativeSignalRExampleState extends State<NativeSignalRExample> {
  final NativeSignalRService _signalRService = NativeSignalRService();
  final List<String> _messages = [];
  bool _isConnected = false;
  String? _connectionId;
  String? _errorMessage;
  StreamSubscription<SignalREvent>? _eventSubscription;

  @override
  void initState() {
    super.initState();
    
    if (Platform.isIOS) {
      _setupSignalR();
    }
  }

  @override
  void dispose() {
    _eventSubscription?.cancel();
    _signalRService.disconnect();
    super.dispose();
  }

  Future<void> _setupSignalR() async {
    // Listen for SignalR events
    _eventSubscription = _signalRService.events.listen((event) {
      if (event is SignalRConnectionEvent) {
        setState(() {
          _isConnected = event.connected;
          _connectionId = event.connectionId;
          _errorMessage = null;
        });
        
        if (event.connected) {
          _addMessage('Connected with ID: ${event.connectionId}');
          _joinGroup();
        } else {
          _addMessage('Disconnected');
        }
      } else if (event is SignalRErrorEvent) {
        setState(() {
          _errorMessage = '${event.errorCode}: ${event.errorMessage}';
        });
        _addMessage('Error: ${event.errorMessage}');
      } else if (event is SignalRMethodEvent) {
        _addMessage('Method ${event.method} called with arguments: ${event.arguments}');
      }
    });

    // Register for the ReceiveMessage method
    await _signalRService.on('ReceiveMessage', (arguments) {
      if (arguments.isNotEmpty) {
        final message = arguments[0] as String?;
        if (message != null) {
          _addMessage('Received: $message');
        }
      }
    });

    // Register for the ReceiveSeatUpdate method
    await _signalRService.on('ReceiveSeatUpdate', (arguments) {
      if (arguments.length >= 2) {
        final seatId = arguments[0] as String?;
        final status = arguments[1] as String?;
        if (seatId != null && status != null) {
          _addMessage('Seat $seatId updated to $status');
        }
      }
    });

    // Connect to the SignalR hub
    try {
      await _signalRService.connect(
        widget.hubUrl,
        headers: {
          'Authorization': 'Bearer YOUR_AUTH_TOKEN', // Replace with your actual token
        },
      );
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
      _addMessage('Connection error: $e');
    }
  }

  Future<void> _joinGroup() async {
    if (!_isConnected) return;

    try {
      await _signalRService.invoke(
        'JoinGroup',
        arguments: [widget.groupName],
      );
      _addMessage('Joined group: ${widget.groupName}');
    } catch (e) {
      _addMessage('Error joining group: $e');
    }
  }

  Future<void> _leaveGroup() async {
    if (!_isConnected) return;

    try {
      await _signalRService.invoke(
        'LeaveGroup',
        arguments: [widget.groupName],
      );
      _addMessage('Left group: ${widget.groupName}');
    } catch (e) {
      _addMessage('Error leaving group: $e');
    }
  }

  Future<void> _sendMessage() async {
    if (!_isConnected) return;

    try {
      await _signalRService.invoke(
        'SendMessageToGroup',
        arguments: [
          widget.groupName,
          widget.userId,
          'Hello from Flutter!',
        ],
      );
      _addMessage('Sent message to group');
    } catch (e) {
      _addMessage('Error sending message: $e');
    }
  }

  Future<void> _updateSeat(String seatId, String status) async {
    if (!_isConnected) return;

    try {
      await _signalRService.invoke(
        'UpdateSeat',
        arguments: [
          widget.groupName,
          seatId,
          status,
          widget.userId,
        ],
      );
      _addMessage('Updated seat $seatId to $status');
    } catch (e) {
      _addMessage('Error updating seat: $e');
    }
  }

  void _addMessage(String message) {
    setState(() {
      _messages.add('${DateTime.now().toIso8601String()}: $message');
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!Platform.isIOS) {
      return const Center(
        child: Text('Native SignalR is only supported on iOS'),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Native SignalR Example'),
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            color: _isConnected ? Colors.green.shade100 : Colors.red.shade100,
            child: Row(
              children: [
                Icon(
                  _isConnected ? Icons.check_circle : Icons.error,
                  color: _isConnected ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _isConnected
                        ? 'Connected (ID: $_connectionId)'
                        : _errorMessage ?? 'Disconnected',
                    style: TextStyle(
                      color: _isConnected ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _isConnected ? _joinGroup : null,
                  child: const Text('Join Group'),
                ),
                ElevatedButton(
                  onPressed: _isConnected ? _leaveGroup : null,
                  child: const Text('Leave Group'),
                ),
                ElevatedButton(
                  onPressed: _isConnected ? _sendMessage : null,
                  child: const Text('Send Message'),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _isConnected
                      ? () => _updateSeat('A1', 'selected')
                      : null,
                  child: const Text('Select A1'),
                ),
                ElevatedButton(
                  onPressed: _isConnected
                      ? () => _updateSeat('A1', 'available')
                      : null,
                  child: const Text('Release A1'),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Text(_messages[_messages.length - 1 - index]),
                );
              },
              reverse: true,
            ),
          ),
        ],
      ),
    );
  }
}
