<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    app:cardCornerRadius="5dp"
    app:cardUseCompatPadding="true"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:layout_marginTop="10dp"
    android:layout_marginBottom="5dp"
    app:cardBackgroundColor="@color/white"
    app:cardElevation="0dp"
    android:background="?attr/selectableItemBackground"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivBanner"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintDimensionRatio="h, 4:3"
            app:layout_constraintRight_toRightOf="parent"/>
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivBanner"
            android:padding="14dp"
            app:fontFamily="@font/sanspro_bold"
            android:textSize="16sp"
            android:textColor="@color/text1e1f28"/>
        <RelativeLayout
            android:id="@+id/rlOutOfVoucher"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="@+id/ivBanner"
            app:layout_constraintBottom_toBottomOf="@+id/ivBanner"
            app:layout_constraintLeft_toLeftOf="@+id/ivBanner"
            app:layout_constraintRight_toRightOf="@+id/ivBanner"
            android:visibility="gone"
            tools:visibility="visible"
            android:background="#BF000000">
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/white"
                android:text="@string/out_of_voucher"
                app:textAllCaps="true"
                app:fontFamily="@font/oswald_bold"
                android:textSize="28sp"
                android:gravity="center"/>
        </RelativeLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>