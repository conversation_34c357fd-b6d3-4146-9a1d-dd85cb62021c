package vn.zenity.betacineplex.view.auth

import android.content.Context
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.CityModel
import vn.zenity.betacineplex.model.RequestModel.RegisterModel

/**
 * Created by Zenity.
 */

interface RegisterContractor {
    interface View : IBaseView {
        fun showLogin()
        fun showRegisterSuccess(message: String, cardNumber: String)
        fun showListCity(cities: ArrayList<CityModel>)
        fun showDistrict(cityId: String, cities: ArrayList<CityModel>)
        fun getViewContext(): Context?
    }

    interface Presenter : IBasePresenter<View> {
        fun register(registerModel: RegisterModel)
        fun getCity()
        fun getDistrict(cityId: String)
    }
}
