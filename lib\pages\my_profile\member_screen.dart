import 'package:flutter/material.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/pages/my_profile/member_card_screen.dart';
import 'package:flutter_app/pages/my_profile/reward_points_screen.dart';
import 'package:flutter_app/pages/my_profile/transaction_history_screen.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:barcode/barcode.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/index.dart';

class MemberScreen extends StatefulWidget {
  const MemberScreen({super.key});

  @override
  State<MemberScreen> createState() => _MemberScreenState();
}

class _MemberScreenState extends State<MemberScreen> {
  final List<MenuItemData> _menuItems = [];
  bool _isLoading = true;
  MUser? _user;
  String? _cardClass;
  double _progress = 0.0;
  double _totalBill = 0;
  int _totalPoint = 0;
  int _remainingPoint = 0;
  String _remainingPointDate = '';
  String? _barcode;

  @override
  void initState() {
    super.initState();
    _loadData();
    _initMenuItems();
  }

  void _initMenuItems() {
    _menuItems.clear();
    _menuItems.addAll([
      // Exact iOS order và titles - tương tự iOS initMenu()
      // pointItem - "Member.BetaPoint".localized
      MenuItemData(
        title: 'Beta Point',
        icon: Icons.stars_outlined,
        onTap: () => _navigateToRewardPoints(),
      ),
      // introItem - "Member.Intro".localized
      MenuItemData(
        title: 'Giới thiệu bạn bè',
        icon: Icons.share_outlined,
        onTap: () => _navigateToIntroFriends(),
      ),
      // historyItem - "Member.TransactionHistory".localized
      MenuItemData(
        title: 'Lịch sử giao dịch',
        icon: Icons.history_outlined,
        onTap: () => _navigateToTransactionHistory(),
      ),
      // cardItem - "Member.MemberCard".localized
      MenuItemData(
        title: 'Thẻ thành viên',
        icon: Icons.credit_card_outlined,
        onTap: () => _navigateToMemberCard(),
      ),
      // accountItem - "Member.AccountInfo".localized
      MenuItemData(
        title: 'Thông tin tài khoản',
        icon: Icons.person_outline,
        onTap: () => context.pushNamed(CRoute.myAccountInfo),
      ),
      // changePassItem - "Member.ChangePass".localized
      MenuItemData(
        title: 'Đổi mật khẩu',
        icon: Icons.lock_outline,
        onTap: () => context.pushNamed(CRoute.myAccountPass),
      ),
      // deleteItem - "Member.DeleteAccount".localized
      MenuItemData(
        title: 'Xóa tài khoản',
        icon: Icons.delete_outline,
        onTap: () => _showDeleteAccountConfirmation(),
      ),
      // Note: logoutItem được handle riêng trong _buildLogoutButton()
    ]);
  }

  Future<void> _loadData() async {
    await context.read<AuthC>().check(context: context);

    setState(() {
      _isLoading = true;
    });

    try {
      // Get user data
      final authState = context.read<AuthC>().state;
      if (authState.user != null) {
        _user = authState.user;

        // Get card class
        final api = RepositoryProvider.of<Api>(context);
        final cardClassResponse = await api.auth.getCardClass();

        if (cardClassResponse != null && cardClassResponse.isSuccess) {
          final cardClasses = cardClassResponse.listObject ?? [];

          if (cardClasses.isNotEmpty) {
            // Find current card class
            final currentCard = cardClasses.firstWhere(
              (card) => card.classId == _user?.classId,
              orElse: () => cardClasses.first,
            );

            // Find top card class
            final topCard = cardClasses.last;

            setState(() {
              _cardClass = currentCard.code;
              _totalBill = _user?.totalBillPayment ?? 0;
              _totalPoint = _user?.totalPoint ?? 0;
              _remainingPoint = _user?.almostExpiredPoint ?? 0;
              _remainingPointDate = _user?.almostExpiredPointDate ?? '';

              // Calculate progress
              final vipMoney = topCard.totalPaymentCondition ?? 3000000;
              if (_cardClass != null && _cardClass!.contains('VIP')) {
                _progress = 1.0;
              } else {
                _progress = _totalBill / vipMoney;
              }

              // Generate barcode
              if (_user?.cardNumber != null && _user!.cardNumber!.isNotEmpty) {
                _barcode = _user!.cardNumber;
              }
            });
          }
        }
      }
    } catch (e) {
      print('Error loading member data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _uploadAvatar(XFile imageFile) async {
    // try {
    //   setState(() {
    //     _isLoading = true;
    //   });
    //
    //   final api = RepositoryProvider.of<Api>(context);
    //   final MUpload? data = await api.postUploadPhysicalBlob(
    //     file: imageFile,
    //     prefix: 'user-avatar',
    //     obj: {
    //       'docType': 'user-avatar',
    //     },
    //   );
    //
    //   if (data != null) {
    //     final result = await api.auth.updateAvatar(
    //       body: {
    //         'avatarUrl': data.fileUrl,
    //         'userId': _user?.id,
    //       },
    //     );
    //
    //     if (result != null && result.isSuccess) {
    //       // Refresh user data
    //       context.read<AuthC>().check(context: context);
    //       _loadData();
    //     }
    //   }
    // } catch (e) {
    //   print('Error uploading avatar: $e');
    // } finally {
    //   setState(() {
    //     _isLoading = false;
    //   });
    // }
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Wrap(
        children: [
          ListTile(
            leading: const Icon(Icons.photo_library),
            title: const Text('Chọn ảnh từ thư viện'),
            onTap: () {
              Navigator.pop(context);
              _pickImage(ImageSource.gallery);
            },
          ),
          ListTile(
            leading: const Icon(Icons.camera_alt),
            title: const Text('Chụp ảnh mới'),
            onTap: () {
              Navigator.pop(context);
              _pickImage(ImageSource.camera);
            },
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: source);

    if (image != null) {
      await _uploadAvatar(image);
    }
  }

  void _navigateToRewardPoints() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const RewardPointsScreen(),
      ),
    );
  }

  void _navigateToIntroFriends() {
    // TODO: Implement intro friends screen
    UDialog().showError(
      title: 'Thông báo',
      text: 'Tính năng đang được phát triển',
    );
  }

  void _navigateToTransactionHistory() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const TransactionHistoryScreen(),
      ),
    );
  }

  void _navigateToMemberCard() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider(create: (context) => BlocC(), child: const MemberCardScreen()),
      ),
    );
  }

  void _showDeleteAccountConfirmation() {
    UDialog().showConfirm(
      title: 'Xóa tài khoản',
      body: const Text(
        'Bạn có chắc chắn muốn xóa tài khoản? Hành động này không thể hoàn tác.',
        style: TextStyle(fontSize: CFontSize.sm),
        textAlign: TextAlign.center,
      ),
      btnOkText: 'Xóa tài khoản',
      btnOkOnPress: () async {
        // Save delete account request
        final preferences = await SharedPreferences.getInstance();
        await preferences.setString('delete_account', _user?.id ?? '');

        // Logout
        _logout();
      },
    );
  }

  void _logout() {
    // Unregister device token
    // TODO: Implement unregister device token

    // Logout
    context.read<AuthC>().logout();
    context.pop();
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    // New color scheme: Blue header, white background, black text
    const Color primaryBlue = Color(0xFF2196F3); // Material Blue

    return Scaffold(
      backgroundColor: Colors.white, // White background
      appBar: appBar(
        title: 'Thành viên BETA',
        backgroundColor: primaryBlue, // Blue header
        titleColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _user == null
              ? const Center(child: Text('Vui lòng đăng nhập để xem thông tin thành viên'))
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildMemberHeader(),
                      _buildMenuItems(),
                      const SizedBox(height: 24),
                      _buildLogoutButton(),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
    );
  }

  Widget _buildMemberHeader() {
    // New color scheme: Blue header, white background, black text
    const Color primaryBlue = Color(0xFF2196F3); // Material Blue
    const Color darkBlue = Color(0xFF1976D2); // Darker blue for gradient

    return Column(
      children: [
        // Banner section - Responsive height based on screen size
        SizedBox(
          height: CSpace.height * 0.15, // 15% of screen height
          child: Stack(
            children: [
              // Background banner - Responsive height
              Container(
                height: CSpace.height * 0.15,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                      begin: Alignment.topCenter, end: Alignment.bottomCenter, colors: [primaryBlue, darkBlue]),
                ),
                child: _user?.avatarUrl != null && _user!.avatarUrl.isNotEmpty
                    ? Container(
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: NetworkImage(_user!.avatarUrl),
                            fit: BoxFit.cover,
                            colorFilter: ColorFilter.mode(
                              primaryBlue.withValues(alpha: 0.6),
                              BlendMode.overlay,
                            ),
                          ),
                        ),
                      )
                    : null,
              ),

              // Gradient overlay - Responsive height
              Container(
                height: CSpace.height * 0.15,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      primaryBlue.withValues(alpha: 0.6),
                      darkBlue.withValues(alpha: 0.8),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        // White card container - Responsive overlap
        Transform.translate(
            offset: Offset(0, -CSpace.height * 0.04), // Responsive overlap (8% of screen height)
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 8),
              // padding: const EdgeInsets.only(top: 20, left: 20, right: 20, bottom: 20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Content overlay - Responsive padding
                  Padding(
                    padding: EdgeInsets.all(CSpace.height * 0.04), // 4% of screen width
                    child: Column(
                      children: [
                        // Avatar and name
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            GestureDetector(
                              onTap: _showImagePickerOptions,
                              child: Stack(
                                children: [
                                  Container(
                                    width: CSpace.width * 0.2, // 20% of screen width
                                    height: CSpace.width * 0.2, // Keep it square
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(color: darkBlue, width: 2),
                                      image: _user?.avatarUrl != null && _user!.avatarUrl.isNotEmpty
                                          ? DecorationImage(
                                              image: NetworkImage(_user!.avatarUrl),
                                              fit: BoxFit.cover,
                                            )
                                          : null,
                                      color: Colors.white,
                                    ),
                                    child: _user?.avatarUrl == null || _user!.avatarUrl.isEmpty
                                        ? Center(
                                            child: Text(
                                              _user?.name.isNotEmpty == true
                                                  ? _user!.name.substring(0, 1).toUpperCase()
                                                  : '',
                                              style: const TextStyle(
                                                  fontSize: 30, color: darkBlue, fontWeight: FontWeight.bold),
                                            ),
                                          )
                                        : null,
                                  ),
                                  Positioned(
                                    right: 0,
                                    bottom: 0,
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        shape: BoxShape.circle,
                                        border: Border.all(color: primaryBlue, width: 2),
                                      ),
                                      child: const Icon(Icons.camera_alt, size: 16, color: primaryBlue),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      _user?.name ?? '',
                                      style: const TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    // VIP icon - tương tự Android ivIcVip
                                    if (_cardClass != null && _cardClass!.contains('VIP'))
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                        decoration: BoxDecoration(
                                          color: Colors.amber,
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: const Text(
                                          'VIP',
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                                // const SizedBox(height: 4),
                                // Text(
                                //   _user?.cardNumber ?? '',
                                //   style: const TextStyle(
                                //     fontSize: 16,
                                //     color: Colors.white70,
                                //   ),
                                // ),
                              ],
                            ),
                          ],
                        ),

                        SizedBox(height: CSpace.height * 0.02), // Responsive spacing

                        // Barcode
                        if (_user?.cardNumber != null)
                          Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  const Text('Thẻ thành viên', style: TextStyle(fontSize: 16)),
                                  Text(
                                    _user!.cardNumber!,
                                    style:
                                        const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, letterSpacing: 1.2),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 80,
                                child: FutureBuilder<String>(
                                  future: _generateBarcode(_user!.cardNumber!),
                                  builder: (context, snapshot) {
                                    if (snapshot.hasData) {
                                      return SvgPicture.string(
                                        snapshot.data!,
                                        height: 80,
                                        width: double.infinity,
                                      );
                                    }
                                    return const SizedBox(
                                      height: 80,
                                      child: Center(child: CircularProgressIndicator()),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),

                        SizedBox(height: CSpace.height * 0.02), // Responsive spacing

                        // Stats section - tương tự Android tvTotalSpend và tvRewardPoints
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 8),
                          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.15),
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: Colors.black, width: 0.5),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: _buildStatItem(
                                    title: 'Tổng chi tiêu', value: '${_formatCurrency(_totalBill.toInt())} đ'),
                              ),
                              Container(
                                width: 0.5,
                                height: 50,
                                color: Colors.black,
                              ),
                              Expanded(
                                child: _buildStatItem(title: 'Điểm thưởng', value: '$_totalPoint điểm'),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Progress bar - tương tự Android progressBarVip và noticeUpgradeVIP
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 0),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.15),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(color: Colors.white.withValues(alpha: 0.3), width: 1),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: RichText(
                                      textAlign: TextAlign.center,
                                      text: TextSpan(
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Colors.black,
                                          fontWeight: FontWeight.w500,
                                        ),

                                        children: [
                                          TextSpan(
                                            text: _progress >= 1.0 ? 'Bạn đã đạt hạng VIP cao nhất' : 'Bạn cần tích luỹ thêm ',
                                          ),
                                          if (_progress < 1.0)
                                            TextSpan(
                                              text: '${_formatCurrency((3000000 * (1 - _progress)).toInt())} đ',
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Colors.red,
                                              ),
                                            ),
                                          if (_progress < 1.0) const TextSpan(text: ' để thăng hạng VIP'),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              // Progress bar với styling giống Android
                              ClipRRect(
                                borderRadius: BorderRadius.circular(6),
                                child: LinearProgressIndicator(
                                  value: _progress,
                                  backgroundColor: Colors.grey,
                                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.amber),
                                  minHeight: 8,
                                ),
                              ),
                              const SizedBox(height: 8),
                              // Progress labels - tương tự Android tvTotalSpendProgress
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '${_formatCurrency(_totalBill.toInt())} đ',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.black,
                                    ),
                                  ),
                                  Text(
                                    '${_formatCurrency(3000000)} đ',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.black,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        // Remaining points
                        if (_remainingPoint > 0)
                          Padding(
                            padding: const EdgeInsets.only(top: 24, left: 8, right: 8),
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                              decoration: BoxDecoration(
                                color: Colors.amber.shade50,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: Colors.amber.shade200, width: 1),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.warning_amber_rounded, color: Colors.orange, size: 24),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Text(
                                      'Bạn có $_remainingPoint điểm sẽ hết hạn vào ngày ${_formatDate(_remainingPointDate)}',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.black87,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }

  Widget _buildStatItem({
    required String title,
    required String value,
    IconData? icon,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (icon != null) const SizedBox(height: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 13,
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 6),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF1976D2),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMenuItems() {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          ...List.generate(
            _menuItems.length,
            (index) => _buildMenuItem(_menuItems[index]),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(MenuItemData item) {
    // New color scheme: Blue icons, black text
    const Color primaryBlue = Color(0xFF2196F3); // Material Blue

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: item.onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 4),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Color(0xFFE0E0E0),
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(item.icon, color: primaryBlue, size: 20),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  item.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF333333),
                  ),
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                size: 14,
                color: Color(0xFF999999),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    // New color scheme: Blue logout button
    const Color primaryBlue = Color(0xFF2196F3); // Material Blue

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: primaryBlue.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: () {
            UDialog().showConfirm(
              title: 'Đăng xuất',
              text: 'Bạn có chắc chắn muốn đăng xuất?',
              btnOkText: 'Đăng xuất',
              btnOkColor: primaryBlue,
              btnOkOnPress: _logout,
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: primaryBlue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 18),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 0,
          ),
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.logout, size: 20),
              SizedBox(width: 8),
              Text(
                'Đăng xuất',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<String> _generateBarcode(String data) async {
    final bc = Barcode.code128();
    return bc.toSvg(
      data,
      width: 300,
      height: 80,
      fontHeight: 0,
    );
  }

  String _formatCurrency(int value) {
    return value.toString().replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]}.',
        );
  }

  String _formatDate(String dateString) {
    if (dateString.isEmpty) return '';

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }
}

class MenuItemData {
  final String title;
  final IconData icon;
  final VoidCallback onTap;

  MenuItemData({
    required this.title,
    required this.icon,
    required this.onTap,
  });
}
