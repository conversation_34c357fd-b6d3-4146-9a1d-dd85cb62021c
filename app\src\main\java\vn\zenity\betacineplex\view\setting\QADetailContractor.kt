package vn.zenity.betacineplex.view.setting

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.TopicDetailModel

/**
 * Created by Zenity.
 */

interface QADetailContractor {
    interface View : IBaseView {
        fun showListQADetail(listQA: List<TopicDetailModel>)
    }

    interface Presenter : IBasePresenter<View> {
        fun getQADetail(qaId: String)
    }
}
