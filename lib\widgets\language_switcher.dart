import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../service/language_service.dart';

/// A widget that allows the user to switch between languages
class LanguageSwitcher extends StatefulWidget {
  final bool showLabel;
  final double iconSize;
  final Color? iconColor;
  final VoidCallback? onLanguageChanged;

  const LanguageSwitcher({
    Key? key,
    this.showLabel = true,
    this.iconSize = 24.0,
    this.iconColor,
    this.onLanguageChanged,
  }) : super(key: key);

  @override
  State<LanguageSwitcher> createState() => _LanguageSwitcherState();
}

class _LanguageSwitcherState extends State<LanguageSwitcher> {
  final LanguageService _languageService = LanguageService();
  bool _isEnglish = false;

  @override
  void initState() {
    super.initState();
    _checkCurrentLanguage();
  }

  Future<void> _checkCurrentLanguage() async {
    final isEnglish = await _languageService.isEnglish();
    if (mounted) {
      setState(() {
        _isEnglish = isEnglish;
      });
    }
  }

  Future<void> _toggleLanguage() async {
    final newLanguage = _isEnglish ? 'vi' : 'en';
    await _languageService.setLanguage(context, newLanguage);
    
    if (mounted) {
      setState(() {
        _isEnglish = !_isEnglish;
      });
    }
    
    // Call the callback if provided
    if (widget.onLanguageChanged != null) {
      widget.onLanguageChanged!();
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _toggleLanguage,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.language,
              size: widget.iconSize,
              color: widget.iconColor ?? Theme.of(context).iconTheme.color,
            ),
            if (widget.showLabel) ...[
              const SizedBox(width: 8),
              Text(
                _isEnglish ? 'English' : 'Tiếng Việt',
                style: TextStyle(
                  color: widget.iconColor ?? Theme.of(context).textTheme.bodyLarge?.color,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// A dropdown menu for language selection
class LanguageDropdown extends StatefulWidget {
  final ValueChanged<String>? onChanged;
  final Color? textColor;

  const LanguageDropdown({
    Key? key,
    this.onChanged,
    this.textColor,
  }) : super(key: key);

  @override
  State<LanguageDropdown> createState() => _LanguageDropdownState();
}

class _LanguageDropdownState extends State<LanguageDropdown> {
  final LanguageService _languageService = LanguageService();
  String _currentLanguage = 'vi';

  @override
  void initState() {
    super.initState();
    _loadCurrentLanguage();
  }

  Future<void> _loadCurrentLanguage() async {
    final language = await _languageService.getCurrentLanguage();
    if (mounted) {
      setState(() {
        _currentLanguage = language;
      });
    }
  }

  Future<void> _changeLanguage(String? language) async {
    if (language != null && language != _currentLanguage) {
      await _languageService.setLanguage(context, language);
      
      if (mounted) {
        setState(() {
          _currentLanguage = language;
        });
      }
      
      if (widget.onChanged != null) {
        widget.onChanged!(language);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return DropdownButton<String>(
      value: _currentLanguage,
      icon: const Icon(Icons.arrow_drop_down),
      elevation: 16,
      style: TextStyle(
        color: widget.textColor ?? Theme.of(context).textTheme.bodyLarge?.color,
      ),
      underline: Container(
        height: 2,
        color: Theme.of(context).primaryColor,
      ),
      onChanged: _changeLanguage,
      items: [
        DropdownMenuItem<String>(
          value: 'vi',
          child: Row(
            children: [
              Image.asset('assets/images/flag_vi.png', width: 24, height: 24),
              const SizedBox(width: 8),
              const Text('Tiếng Việt'),
            ],
          ),
        ),
        DropdownMenuItem<String>(
          value: 'en',
          child: Row(
            children: [
              Image.asset('assets/images/flag_en.png', width: 24, height: 24),
              const SizedBox(width: 8),
              const Text('English'),
            ],
          ),
        ),
      ],
    );
  }
}
