{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a28309da1a8acb06bc64a001ab53bc1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985821556c6da781aa460eb101b03b1f36", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29fca69aeeb124f2c6ac314a1a39ba5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987638c05703202e27837461429efcb206", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f29fca69aeeb124f2c6ac314a1a39ba5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bf711aca17f59d136ef2e476dc7e599b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98631782c2685c7862de60809270208d62", "guid": "bfdfe7dc352907fc980b868725387e980ec18e7aa3a7e25ef055a68444d5e7b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984027195107d43c1f071f9926545ac60f", "guid": "bfdfe7dc352907fc980b868725387e98c70c5f191b81a6d17a67b5fee0b2f00f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd8f13df474218c88ec3926a7801153f", "guid": "bfdfe7dc352907fc980b868725387e986cd2ab9ee0f8e000923dafce6c72ebf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b85c93da6106c4e5efa6a08876e63b5", "guid": "bfdfe7dc352907fc980b868725387e98e61811ac6d02158ac76e262181d4c865"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98910e0ab3e8c0216881b6916491477742", "guid": "bfdfe7dc352907fc980b868725387e98b613d228bc3c17ae04ce55ab21853af8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac772056a5e808a24e79bd2471090756", "guid": "bfdfe7dc352907fc980b868725387e98e21f87a7cf065049a55afedb05ee0008"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea4726a42c24d20602c746f0408601ce", "guid": "bfdfe7dc352907fc980b868725387e983a3d8d725e8406939a97a2a58eae03e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e71b9fb191e566f9e7648dd4f03d5200", "guid": "bfdfe7dc352907fc980b868725387e98b156df85e668ca78399b799065eeb596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821340b9002c1e290fa16b029d0684118", "guid": "bfdfe7dc352907fc980b868725387e9861aa8644c26918c34316b8b8f0c98b76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833dfe2589fd24db59d87ebf7dd6a175d", "guid": "bfdfe7dc352907fc980b868725387e983e43ee0b9488536c76753e729282ed94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3f8a3234a22b50c631af3633175a080", "guid": "bfdfe7dc352907fc980b868725387e986646fd325babd8dfda4520cc2292c09f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a65cfe2883f278571a61623a6233d3f", "guid": "bfdfe7dc352907fc980b868725387e98fdf175b36dd475917f9458a527403639", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a644b004340aee478a4b7b06be52982", "guid": "bfdfe7dc352907fc980b868725387e98d4eda053bdff5ed5f8a2e9a86299c368", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4b8cc806a24654d2003ce1679318754", "guid": "bfdfe7dc352907fc980b868725387e98a6eb61f456706ee85d765c46c6e9411e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f95a722e98dfa311d872b8907571a678", "guid": "bfdfe7dc352907fc980b868725387e98576bd8afd36072a22a3de25560692b4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d761f456882e46db71fe2666a258e2b3", "guid": "bfdfe7dc352907fc980b868725387e98990eb9d2091bea0247c22ab544344e1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862c36e4f06c614894c4b0448debd7a1f", "guid": "bfdfe7dc352907fc980b868725387e980206b8fd9a79cf6e95a01dacd4cdf314"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1259085296b9533943c06ce82cd88b8", "guid": "bfdfe7dc352907fc980b868725387e980a1171225457b84a69f71cbba6e25968"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b10deaa2af629cf571a4cddc3d476d67", "guid": "bfdfe7dc352907fc980b868725387e98d2dd6f092cd2b568c5859d147e356d03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7345491ba85e9a568e3770d8ea9694c", "guid": "bfdfe7dc352907fc980b868725387e98fc40c9d84a60d450865f0742845f1285", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe5707f24ecf36a9cc1f901066296997", "guid": "bfdfe7dc352907fc980b868725387e983fad4862ea078e0f2f176c0335437ca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98173e8e6b49334e4b5adbf6ea881f9f54", "guid": "bfdfe7dc352907fc980b868725387e9815a05150851582bc76a44d212e768eed", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982e0c687ea1401bded76b376b5bea9cbf", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9846e5cafe601eafc4be23851cd01b3ace", "guid": "bfdfe7dc352907fc980b868725387e98c046e67e8d04a189ce22aeebcf9f0c42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f0bb3d160be76547ed33bd4802f83cf", "guid": "bfdfe7dc352907fc980b868725387e98b298776937ee83029baf3b89538bacf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980deb5c6b9d61d9bc69053e002710e566", "guid": "bfdfe7dc352907fc980b868725387e98c6d7b6864cd8093b4d7280471c506246"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98825b2f5a9c9346f70459019feb7e4e41", "guid": "bfdfe7dc352907fc980b868725387e987ef5bf15c696a6a6fcd3b6a8c20ae139"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf3908c334d74c661bb3050bdcaff238", "guid": "bfdfe7dc352907fc980b868725387e98db312bb81c634efe76c17cb8843a262f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7f9a33aa127eeec096e445888477476", "guid": "bfdfe7dc352907fc980b868725387e9857892908ed8304631bbddeb1bd6aa5b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fa3985877ba9b63b645e05649aaef60", "guid": "bfdfe7dc352907fc980b868725387e9822f5c365abe5928d4b954625651349f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a378d20e89a371eaf844ab615b93e01", "guid": "bfdfe7dc352907fc980b868725387e98c4327705a4aa2d7c5d2d86877a860bee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f768c9d8000167e02e7b4b57bbcb8b36", "guid": "bfdfe7dc352907fc980b868725387e985fb7f04c03b97f4d00537a92eb3121d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e42aad3556214fa29403152e733f444d", "guid": "bfdfe7dc352907fc980b868725387e983ee89043d0906a1394e7b9f6b362b2cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980274403461e17c58fb91aee732e2ce9a", "guid": "bfdfe7dc352907fc980b868725387e98c853490864f69fbf23b3c656c0a2b774"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cccf834debc9f1c37b31327e609914c6", "guid": "bfdfe7dc352907fc980b868725387e9833f073411d2f6dd71e701e19a6987018"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851a9dc6f361fac56038e05f07561e29b", "guid": "bfdfe7dc352907fc980b868725387e983d930a9a4655f6883baecb69940cb5f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dba6997aa51055a6b44d568f572f3353", "guid": "bfdfe7dc352907fc980b868725387e980dc99d5be38d00b414b749161c965b37"}], "guid": "bfdfe7dc352907fc980b868725387e98bdb52a28753d4023e4837e875c526935", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e980f639b8d3a686c403cff15cf07a87254"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d391d713992f1d4e99af11b30a3d8fcd", "guid": "bfdfe7dc352907fc980b868725387e988df13c1ad6b60ae70957be3c6b231280"}], "guid": "bfdfe7dc352907fc980b868725387e98aa8074edf3f6f358b01ef651afcc18cb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988800b029fd2588c1e605d798cdd1f40d", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9819d6b50841d0a1dec65d26747c1d26f7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}