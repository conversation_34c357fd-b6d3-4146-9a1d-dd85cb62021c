{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c329620c51892527db69ac984ef9321b", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e986eaba3bbf34fffc52894406988f981b0", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9804db47a3ceef83edd118018eb43bf272", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98310322e41fe9abeff0f8d565206fe0a7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/ios/Classes/AudioSessionPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982e4d505355f1aba6e6ec4199f33a2fc5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/ios/Classes/AudioSessionPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98279810924491a311c29bc9b98e468252", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/ios/Classes/DarwinAudioSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815e83ab0b62337f043483986d98d5157", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/ios/Classes/DarwinAudioSession.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98865bb9adf348bb52cfa3c55f5241a01e", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd0cb9084e42e6763b0ac238f835b107", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f72769b275a3b5185f98d788cf90a687", "name": "audio_session", "path": "audio_session", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea76b23d5edea2a828f130cc28be83db", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a80f5807d48504388a53d83ab1b73186", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891084f1263efadfae928f9802258d193", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7eda2180a4912cf5e2fbd0c749cd7ff", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c88b1fd595221d3a5b2f4a8ca8ead9d", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9805e8989555b7387341dfd4148bccf515", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7579ab2213c28648233f6b04584ac63", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e5a101987dd0772617b849a927cbcb29", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b90f06a9f858853d12715cbfd6a9a7ff", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f949deb7f278d10102728d30a405e40d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce8b0e0bd0013d374a6a924851749789", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986fd163258b4989fe2cc73c790961c522", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983490b190a14ae503fac64e90b6a810a8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/ios/audio_session.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9865f1702bf3145c423b89acc0eb32c9b4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98177fde167dc31f25b330835903c17557", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b63f84fa4bcffd306ee1f56d4817fc4e", "path": "audio_session.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f31223c683192d4eb9ec5e7396002166", "path": "audio_session-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bf4319067968851bd0d8084b5d49ba2a", "path": "audio_session-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9801646fb42f5a6c49de45d22e3a1bff4b", "path": "audio_session-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980dc652d26198440a23fc0ae0d4d94f52", "path": "audio_session-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98653f0cc772a45932944e4732747c2986", "path": "audio_session.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98087290e2c756fcceabbefe118d2cf6d4", "path": "audio_session.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ef96e28946bf4ccb258818e4542992de", "name": "Support Files", "path": "../../../../Pods/Target Support Files/audio_session", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b578604344a7a677ad1216846bea494b", "name": "audio_session", "path": "../.symlinks/plugins/audio_session/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b32ab9432b1209a0619e35fab4c5a59a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios/Classes/FPPDeviceInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fbe7b1efe6b37a112b4b141d59e1d81b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios/Classes/FPPDeviceInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d254402552063efce9507d0dab1ede52", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db408360a61cc8c6cd227681f10f4160", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fdda8459bc0500123d4eca4fc69046b1", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9d146daa7beb0581925875c1a68bf04", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985830cfc6b066755ca7b335433c13cc60", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982bd74624bb14fb806ac041e711cde265", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869c1e833507bd88472b09ca042adb8c3", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e95c0d9fcf9bf72e5c67cca2dc1ce10c", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb8ae558483c86160c540e8eaef024ce", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eddbdf646d6eccba5c84ced0739ad6b3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98652f86f34c87c10d6862c9cc318888d1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981308566ad865cc36857ce4ad0baa1e95", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ff712d1557eed21f68270d93ae1d494", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebdbe0362724505ceb0b1e9b5ffdb15f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f755e9d5cb8da6e843c0bd5eb31e12d8", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e38199b4a61b4373dd72909135676055", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ca9f7e335bb5fcc2ca1a23b096ac35b", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a58e33ea4af69ee98c69cf63701513c", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f2eecb371e9691a8f3368d6b7b1b7df", "name": "Code", "path": "../Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f23667d603f2e21161c60665b7e388a2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98994c2c7ba924337b71cb26f3b576c27b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98604a37e3f845bf959d4c30628514f4da", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f18eec238bc64a8e48e979a108095c1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981371fe5734e19ba44e0cf6906bcee82f", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9876958fda1b5fd943f05cc40dbb0c684c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios/device_info_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c436939b6e6b037f4097c758f4504b9d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981b4efad6e52f70665093e1ba766b6447", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9875ee968d0f8a66d37d2d7c21a1e82bce", "path": "device_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e550b7ae9161d7c166a71acd9986dfb0", "path": "device_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98945ad0f9f23d8838ad01f35c982f43b0", "path": "device_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f1eac095564fdb1a81a3fc23d510fc6f", "path": "device_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98494539c4e1d94f0937361eccc8be986d", "path": "device_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9869286b977c4c8843403d9443db9b157f", "path": "device_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98604a773509288246237f6111c62be0e8", "path": "device_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9818ebe3256aa0f7929f3bd305f758f938", "path": "ResourceBundle-device_info_plus_privacy-device_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c0f244806533be07075f944c3b5f46bd", "name": "Support Files", "path": "../../../../Pods/Target Support Files/device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c777790535ee0f14cd03a1fe02cb22bf", "name": "device_info_plus", "path": "../.symlinks/plugins/device_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c529488b33970c28913ed5a1486d4eeb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.3.0/ios/Classes/FLTFirebaseCorePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981b78eec440619d6b62d739780ddde9fe", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.3.0/ios/Classes/FLTFirebaseCorePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e619e2404ce12a715390bc12e40b5527", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.3.0/ios/Classes/FLTFirebasePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989f732d91a1bde230e1dd8c58878aa77e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.3.0/ios/Classes/FLTFirebasePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872a6e0a7879323516077ad3e27f4ced3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.3.0/ios/Classes/FLTFirebasePluginRegistry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e12852712c1180cb72f76046180d1a9e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.3.0/ios/Classes/FLTFirebasePluginRegistry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98412d61b19b88c2731e3242cdd09941ee", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.3.0/ios/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984601274d39aa7b5a58728bc5e9d7cabf", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.3.0/ios/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fdf0bf1c37a8d508096df83d94495142", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f56170f040d8648c86fa74fdd6fb2d49", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ac1b8bceebd4a297a169f7dedf32539", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df62a563532b4b3c9ba5ad18ba420bd5", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984cb264142697d48bc07cdb79ba256e9c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da0bf283fea1be010e765564deae8c25", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2297d370c926c576874ed475f86bd8c", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a6cb31eb545b2f7e7e524ecb97004b7", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7836aba112bdee9b2e139567b4fa2f2", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9865c79870a74e94ee548c8883cfe8cfe1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e78e80d6241c1586fa4a6f65feebf544", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98691c5cda526ce8283100514c1a349033", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b61730025a1803dbe8870671b385aac3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db96e8a0a2e9c3532354177faeddcdd9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c64f4f08f7005b04f5b54430d1ee2d2c", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.3.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983c5f4c63596cbd839ef4953a9d31bc70", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.3.0/ios/firebase_core.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fb4b2fd203baaefe2391bcd4f36169b6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.3.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989a4bf6ad749f620713ecbae899eed58e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f9fa864d3f11d57715d010657fe6b76a", "path": "firebase_core.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984d31fb9188de4fdc6ac4391c265128d1", "path": "firebase_core-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983f4c8c17955ba6b8f90b5912b1dab63a", "path": "firebase_core-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a0a53252e36e7e5d89b311f6be7f8d8", "path": "firebase_core-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c2554e90667183a2bd2455a446e2636c", "path": "firebase_core-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c4901da40b3fccd6a260e9fa896411f7", "path": "firebase_core.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986213056a3186fea11d86da5d55e5f579", "path": "firebase_core.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98038802a75077649d3209d3cffec075ce", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe00690fa04cd68c038c91fbe246588a", "name": "firebase_core", "path": "../.symlinks/plugins/firebase_core/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805f1a248ab5a6813493e478d16e3e2c0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.0.4/ios/Classes/FLTFirebaseMessagingPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca99d3654de49d332b2732c546287349", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.0.4/ios/Classes/FLTFirebaseMessagingPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986d7a06b686ab3c7ae823381c7047fc7b", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98b6afca1e09d2bd1e3e395047f7430421", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.0.4/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9865bc4073a26a4faf0e7c6fed5ebe899b", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989060935ad6c2c5b4718d15b095e256f7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985be71dc2c1398f2aa95eaeb1f33dc8cd", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a84bac9343441325946b16bfb1db2326", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982fe63d7ef2e42bf95df47a190c7700f9", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98753622db7808d3a95b7ac31d858c3e9d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a6afde9e7d069d8249c5e68b446a46e", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f77d4611b72be30ec5e8d4ef773ee59", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a10f543da42e8aa2982907b194f7acc3", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804d597da4b614095c006c2d5c0e93f34", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ece6deaed1719ad0288157b3a31d217d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f307dd6f44e79e2c35c2e64726ec520", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868345fb9d87b19433008950f3d579045", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5f419c15a37b15af96b3485fe486195", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846b88b967fe619f67fe24f48535d996a", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.0.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e984ec8a83b3e6b60e44e8606c9bb0d2465", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.0.4/ios/firebase_messaging.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fdbd983d21917bc900242f5e1c6b6ce5", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.0.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989efbcf8d954e5dc6643bfb7b27d43918", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9801ddc4550a1589ddbb7f72188183c549", "path": "firebase_messaging.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9883be761a09cf7c5336b1c819da2d7f38", "path": "firebase_messaging-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b1f9cfaa6634eea0dfa53a024d78b919", "path": "firebase_messaging-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a285e01a566437a6aa0883c9941460b8", "path": "firebase_messaging-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897ef5192ef6a3f685a07052681b1580a", "path": "firebase_messaging-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b5bc79653b238a0856cdfa8d28625121", "path": "firebase_messaging.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c8d2cacb84eeb8dc0388c46400b7ddd3", "path": "firebase_messaging.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988c99f437fa8b33d5929d2f052acb5f49", "path": "ResourceBundle-firebase_messaging_Privacy-firebase_messaging-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e38c103553968ba196e1f5de02a31e62", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c65613411c0044b43bbedff54b465e12", "name": "firebase_messaging", "path": "../.symlinks/plugins/firebase_messaging/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d39de172c14848aaa0b7d2255d42b6a8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/FlLocationPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988bca8eecf49aa8da153da86d57fa3968", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/FlLocationPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984cb53363c57244cac41bdb3ee5f67407", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/LocationServicesStatusStreamHandlerImpl.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982136a4cb8a4afe1eda4bb1fec6490797", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/LocationStreamHandlerImpl.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98332c82c6f50535a15d1998b8abfe785e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/MethodCallHandlerImpl.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9821a72cfa4ab644c1065cb31933eee825", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/SwiftFlLocationPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e60ebea36be081f5054caa4d871bb0ec", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/errors/ErrorCodes.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982cbedc5368a222bf775aecaf7a867633", "name": "errors", "path": "errors", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9854d45385321a1dc4dcd30675ba2a10dd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/models/LocationAccuracy.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983fd96a44a1fe525c972ac2ca3269fb2d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/models/LocationData.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bf2854869c097675abecd4a266e42cf0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/models/LocationPermission.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98216accf2fec70fbc1249dbe08efc2554", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/models/LocationServicesStatus.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9842edf9dcbaba48b22e53686623d8bc8d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/models/LocationSettings.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9868ecce796357bc6854305b50e4ecf78b", "name": "models", "path": "models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984de65680af570bf56eaaa37f98f4e90d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/service/LocationDataHandler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9815e99025221da9f74adc4ad30e26451d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/service/LocationDataHandlerImpl.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aa16ca10a561e69a49622d39fae02c20", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/service/LocationDataHandlerImplForManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982494d12ee3d5e7da650087a51af956e0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/service/LocationDataProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9878fe4770fb5c3c58b62f45d276ce7e0d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/service/LocationDataProviderManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986de0a6bed2b57d276594384e3638c5fc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/service/LocationPermissionHandler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986a682fe0faac1d20b14520ea53bda23a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/service/LocationPermissionHandlerImpl.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fc2c6c8699f53f55ed6d61f280a91bc3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/service/LocationPermissionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989ad0820029fc538b7c912021c5385477", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/service/LocationServicesStatusWatcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ef6440e313354f6d8a6df222b16cce21", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/service/ServiceProvider.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cb334c9b58513042172fd08b42b6da6f", "name": "service", "path": "service", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f66a60c579d721909489ee9cb0e0c509", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/utils/ErrorHandleUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a153a755580b42f0db6bfbe4e098a844", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/Classes/utils/LocationServicesUtils.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983f759ea199696a71490b4fb33c138aec", "name": "utils", "path": "utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f69e1725d5caaef05ba25dcf5836f988", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988273ded5e2eb94303f9924e50238c649", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983efce6330872c35e9ff138d56e2ffa0b", "name": "fl_location", "path": "fl_location", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d68f1f7ee485bebb1e9bf42ad259fc5f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98abc4aeff8c80348ccf1690774d225ff7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98567d3a2e7bd8f39cd53a12749594ed7d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d185f2cf8ed9ce7127bb0682d1b97030", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c43bca09458288d7ae6de786db611be6", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856c1beea8ac142b7744b555de7fa53b2", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c0117f2b7fd9726ada2fe4b5bfb2c08", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd3e6578b8e4ee20df61aaccf11c52f9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869dd21233cbc505a0f34eda0d9e841d3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c07ad9d32a1702b0e085a41abbd0a3f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981df221f5241c5b458933a9dbb062bb76", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f07da29342899a97f61f2409b1d1ba33", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f99ef09c29e9b3b224880993c7ff137a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/ios/fl_location.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f58adf08e08a4275e9754228a08025a1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fl_location-4.4.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981bc1290f8b43db1c4ed2850b20d2ec2b", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98fc0b5f880fee5e111aa79500cf1efc20", "path": "fl_location.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982641a5266b88ff991d63267941bed994", "path": "fl_location-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b7c1d4ec5ae61dbdd5a3eee2e71e0ff5", "path": "fl_location-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9d3b32480b4ce4860213564493dc55f", "path": "fl_location-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986dd536103f1462fdffe7391a1ef3be36", "path": "fl_location-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ddef766af7107370946b39aaae17c0b0", "path": "fl_location.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98adceb30fc842f5f41aece21e9e2c8a5a", "path": "fl_location.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ddae9ece96d2c49aec76daff18249fa7", "name": "Support Files", "path": "../../../../Pods/Target Support Files/fl_location", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98929940c8efccbd9258fcfdaa26d6d44e", "name": "fl_location", "path": "../.symlinks/plugins/fl_location/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98ad1f628057212f50508d68a7094356bb", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dc38b228b3606361b249801c0d1a6f0c", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98eb0d3a0bd0c69379f5fe926e552c06d9", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986735ff1c261ac678a6b0187030bbcacc", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9814b217313e12c252fbf5a0a7e27ccdf7", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc62a2c0e215a05d721e973194b9904c", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e98f29e71cc97d7eecb9c51165d578e2326", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_barcode_scanner-2.0.0/ios/Assets/ic_flash_off.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e98145df5dc4c4239ad0272cd36db513d4b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_barcode_scanner-2.0.0/ios/Assets/ic_flash_on.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e98a0e24be25c5bedb469c5d9fcf744abb0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_barcode_scanner-2.0.0/ios/Assets/ic_switch_camera.png", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98da50f59eb4266f741406b255b2749c00", "name": "Assets", "path": "Assets", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98463010520cff2b853ed06d77619c2ae8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_barcode_scanner-2.0.0/ios/Classes/FlutterBarcodeScannerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989e25699b4c1e38ce5c1616cec3419e5b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_barcode_scanner-2.0.0/ios/Classes/FlutterBarcodeScannerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9878dec62eeacf9bce38bb3097c85cfb74", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_barcode_scanner-2.0.0/ios/Classes/SwiftFlutterBarcodeScannerPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987ba9a4cbb4720cb08f21c75b3b2c3949", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5363d4e56cdc40ee166a925c0c3c0fc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854d92294909a6a7db8bddbbe572868b0", "name": "flutter_barcode_scanner", "path": "flutter_barcode_scanner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af0d0bf6f96f3a2edffee8ba9d286998", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e928a703a40b4b4374221816bafcc45e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5f0c51ee434f851c2b7adcc5bedb33f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98460da1196b8072cd0bb19422e788c993", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a821c54012435246175985b19ee3d314", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7075a275ed625be995b2abc728bccc1", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982bbc6d22dbbaa8cbf956fb73a038ac43", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98412f194f15b18a19f119659236d67065", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985a053d36189afd10ac9a996503df0ea8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814678c925ba81b5405bea2619c03678a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880c3c37b4ba5dc6a86fcb3839e9008b6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892471fb394bc3758b67c4afe846d55ef", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_barcode_scanner-2.0.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e989cef90f677e272c45d6289662720850d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_barcode_scanner-2.0.0/ios/flutter_barcode_scanner.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9804209aa637aee954acbb460a4a69156d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_barcode_scanner-2.0.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98721ccb93ee7d96ca80e7d69e998593a4", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f44e3baa832bfdabb29be0aaaac848ab", "path": "flutter_barcode_scanner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9805cb111959be64b3bd3957af12c3c0c2", "path": "flutter_barcode_scanner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bc0fcd9e0156882287d1ba8980a21591", "path": "flutter_barcode_scanner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e38336eabc93c1f2616f4335600845df", "path": "flutter_barcode_scanner-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989352c7ac24c89693e7fc0b4636c887d8", "path": "flutter_barcode_scanner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98317c3a206ac90906af966891731d34e8", "path": "flutter_barcode_scanner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bf6c815f083b2872b95994a16efcf478", "path": "flutter_barcode_scanner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98059bef5cc94adb95cf27c2138cebbadb", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_barcode_scanner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ccd9c6a24c30a4662fd346a3b6105a5", "name": "flutter_barcode_scanner", "path": "../.symlinks/plugins/flutter_barcode_scanner/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b7f7cef15e313cd2ffdcb3ed69c1d4a8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/Contact.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f712877c9a4d472a26a46ae0fcc34bc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/FlutterContactsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989947343001644157f7f6dd796c91547f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/FlutterContactsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f53916ca0cbd52a703c968843f9ce99d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/SwiftFlutterContactsPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d681c2d42316240e74a979d3f5d24eaf", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/properties/Account.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b1ecf7f96d2827f8dc6aaa3b606f20d2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/properties/Address.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d9342b21f72d310c1fda268044367964", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/properties/Email.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e7ac8e28293c7226678b701317f586ec", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/properties/Event.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98668c6f88df39d24f19dfab4e54256b8f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/properties/Group.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9859272abc33e5e50e2dc6b3d98e2fc7e0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/properties/Name.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98834b410ebcac4c5b90346a3fbe395096", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/properties/Note.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ee25e4524a3eda33f4d404a05186fb6b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/properties/Organization.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98df83804e6f13769542d3a700f0b1b176", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/properties/Phone.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9812e3a70a32681415486b18f07690f1d9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/properties/SocialMedia.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9867805a64d564c3af80f15ed894daeffb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/Classes/properties/Website.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98de30d2ea8fcda16a89f2eb91e5925cfd", "name": "properties", "path": "properties", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985a3dab50620d885cd5de2dc0952a2147", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebeac91d968463f908d03c7d73e71392", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca4ea6065de9eeff060fcdf939789144", "name": "flutter_contacts", "path": "flutter_contacts", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f55de3943ec692d7bb9217fc13984682", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98884311595db5fa1e9c27be120c784c92", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab4a8b80e823a8c2f02992a75669a178", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801ec5397d01bf884737e3e783af85647", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879659de873702d88ab7ccb559486e776", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e70cbb7c4cb0fd443c8cbbf62120d94", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98612ebfa110c6e8c8d4b1b903e3ce6908", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829e68dc9d77d5fbb92e498cfdf1e9fdb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be3f5c9056a1c6ade4f91fcfc89293aa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987db5ae4676dd5a88a017595c5b5b0f8e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98318e84202744c35621afe7ebc9d44528", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9b13b486ba6b14a3719232975517c93", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f7061eb03496f2f6e2b6f05311032144", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/ios/flutter_contacts.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f5ca25518e045f8961a486ace5e797bd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98af610790e03032cb014e6022f1e423f0", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9856faf7c9c0d44d8ad9ccfe2d1fb5131a", "path": "flutter_contacts.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98310033d17d0e3149da5293b7542ab984", "path": "flutter_contacts-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986228527e644c8d3cb5f2c0f48451edc3", "path": "flutter_contacts-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa85f68fafc403fa396f66a1d19c52fe", "path": "flutter_contacts-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dae766fe93eab9aad6429574f2543965", "path": "flutter_contacts-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989caaebefeccc6299cc9f3a6f16fd2a55", "path": "flutter_contacts.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982c4637e71dcbb7cc983922a0da635fe2", "path": "flutter_contacts.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988440bd0e3a976fbdab41a6c55fcf36ef", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_contacts", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985942a12d9c53510d7560a26bca2a858a", "name": "flutter_contacts", "path": "../.symlinks/plugins/flutter_contacts/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849a72db32a6b4b9838ba58312441bbb0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/ios/Classes/FlutterKeyboardVisibilityPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ba34525882f5c8f24443ab240e082368", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/ios/Classes/FlutterKeyboardVisibilityPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dabbdb5caff1327f7f2d7c55b73777b9", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984dc65ccec39ce8d1d129c4aebff482d0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98081475bccac0159639c1515f25fdbda0", "name": "flutter_keyboard_visibility", "path": "flutter_keyboard_visibility", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8973f34b7741364d03d3d68264f6772", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980095d4bcbf60a2cc66b2424d2e14de1e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9850117c3333adaf41f59d134d7fdf61a7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e4d2d8830b5009e252f184c19f4bef1", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e52822691ab5b3a5e90f9871aa8ebb93", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989dad38d19521ce8233b123b1a9865a7a", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98febdd576bd812102825471b94fe18e7b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b565d82a594b8f3a318dd61d2b0b29ff", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d03317cc95107e90e01bc321fc369304", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9850de8b13eb3ec36116c82a745d171fb9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb1284b1862587d8126d67b1f86fd065", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98408ad453fc54a7fa65f7509fef2ed9a0", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f0b9ea6c173596cb1589553c66048717", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/ios/flutter_keyboard_visibility.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a0593d2bbccbed0c919ad3e261f48949", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c37fcd374773892078bb3fbde2d51947", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985f351814bdcf93eed1f203418ca063a7", "path": "flutter_keyboard_visibility.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981bd52d95091f3d99009196c8e175a302", "path": "flutter_keyboard_visibility-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984be233bab66bdc4fcd0c6aab06d247fd", "path": "flutter_keyboard_visibility-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c4c4bdb319742acd9aa18ca8456d8d84", "path": "flutter_keyboard_visibility-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98489c2c35b13969f7d3ae9676fb33a92c", "path": "flutter_keyboard_visibility-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98462ffc8d2e2a8768385a471fc1ddc5ba", "path": "flutter_keyboard_visibility.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ad1093c84784c141378405876e52521d", "path": "flutter_keyboard_visibility.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989696676d64aae64d329ddacd6e0ff702", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_keyboard_visibility", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cce442fd6abc6445f2faa0524d0b9374", "name": "flutter_keyboard_visibility", "path": "../.symlinks/plugins/flutter_keyboard_visibility/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ff1e15b9f130ad9e6b696543949aec95", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/GeocodingHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f079b2f3a8b8109b8d3a6fa09b2b3727", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/GeocodingHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cadc8425f3dbcf11363ba8aab1ac459d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/GeocodingPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989fbf44e8d47abca1351f7e7fe34956eb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/GeocodingPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98038d6ccfe0f425cee0019634d422254e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/Extensions/CLPlacemarkExtensions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d033388a6a9415514fa51ab2bd9adc20", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/Classes/Extensions/CLPlacemarkExtensions.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9880fb2b845222c1c9880d92ba1a6867c7", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b7dffbe88f195ab0065cf7354dbbf32", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829d2125fb94c1722c5a471302a5e79c6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7c60fd34eeb2865ffed2bb616463e56", "name": "geocoding_ios", "path": "geocoding_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b421bfb5886706c14814579bed634d6c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b3b668e37480de0d5dee39217e5137d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0c7738f2869be227802ab64d48a7599", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98248bad15d7901f8b6f79c24a14e976bf", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecca0d61993527eb55ee36079b7950dd", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de211fb064f5b727c835298afdb1d331", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd8e9539bba59138093eb0417a425fb4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883f19d8dcd8dbed237d28aed5b4b2c3c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982976bc3c4b7b328ccbc0da3df110c1a9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d94ef93c54dcd8694b80c80401da4cbf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cfd4a0f98791bad0b9cbd902d3c1ab97", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9abff158bf09c811ae03eac8b6052cd", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a0e90f4f49f2b734fa48c95c9518e40d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/ios/geocoding_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e989b438fbdf36f5bea644ec5ef469ad417", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-2.3.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984b673968217db4c69334e1116f0269f1", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98982d8bc218f372f4aafe7f2a385731a5", "path": "geocoding_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9801016df446cc41e786b1933a4b511006", "path": "geocoding_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9814a81e7c38a75162b9a519e9fe8c1462", "path": "geocoding_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988610475aa5a5a7d9312d1beaf6ebe8ca", "path": "geocoding_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851c7f595894c62537f543f1cff448c7f", "path": "geocoding_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98cd12dd44f59207b61991e50c5b8ce156", "path": "geocoding_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98303f3d8f5c37dcd1ca8d18a8c772f5b8", "path": "geocoding_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981538c681906c57240689cb190bf0766f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/geocoding_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a004506ff5afe3340f4169de9ea038f", "name": "geocoding_ios", "path": "../.symlinks/plugins/geocoding_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc2112df94b0b0c0340bd89af8163695", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FGMClusterManagersController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980b229eaf12fc31ea880005e88c7efcc0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FGMClusterManagersController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d796881e37e7835b66c9e807a2862be", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FGMMarkerUserData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9852adf8b4cf7adc4bcc211334122e9cd6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FGMMarkerUserData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b8938f7524114941cebc3c8e235f312f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapHeatmapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9817567c8d35e4e217adec9c4c3848f76c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapHeatmapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821240eb7863f5dd51a5907f4c2e2e6ce", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapJSONConversions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98302a4b1dde06e56a39b84e6a3bd59b9e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapJSONConversions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98549f985e3ca19708e137fa438179dfb3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ebb090518bfc4491f4047a583d375eca", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b9e6b4eca3cbceba6623a63818f7237f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapTileOverlayController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989b5815b7c3d46a7c20ddc19e189a45df", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapTileOverlayController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f04bfbb290966ffc59926d3c5f4fbfaa", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/google_maps_flutter_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848ae2fdaddeeb818022a836b5f9e5a25", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapCircleController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3deca73411b27eb476e089aaa01402a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapCircleController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98171da214e08915b8b24ed77c6d87f9bc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98820c7aac90e5701fc1d1041e0b648cc3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982586b64cd46cdbea0b46392c391a0ebe", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98722b36c1e16b8904f5ef15016a851627", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapMarkerController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985cccec4790c388d458b8d79d6dccb243", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapMarkerController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b4f4636101885bd5f6bfeaa170add781", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapMarkerController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9f3fbee83bf24ab6c597bb385b58e6e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolygonController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985a75475b18066c6838f8d0089e7ad6ce", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolygonController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980cdd1cb59789d12fc9a9d459ecf81c1c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolylineController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980574a6d14be600da07a99d294cfa82c0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolylineController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837a1e03dad63dc6c7bb5951f79cfb8dd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolylineController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9845dd7e8e27b78c69e3c20ef440305d1d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983714d0b0ca6a2971e8e1ae4bef95eeae", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9853cdaac020da84a8f8d157fc459de915", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98727cc62ba5d96271513b124547e90914", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f2bbc9c7243edd33805cb86a619ee468", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d999bda4624c9a1e045a8eb426eb54f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989586e25294140d88f019fda467c657c7", "name": "google_maps_flutter_ios", "path": "google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9859dccde4a506ac0f281dad57e56a6248", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856ba52e70e6cb78ef443b49ab649c82f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983fe22cecc9f26c6004211f8ce379623c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b294c7c8ebc24e53824fdaaf84603235", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb29248d2b4c7fdde1db0f08f25b7bd6", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc476590f1a34d11ac1ded94cc416a38", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812e9b655af7e0f0492a5a7a4cc240721", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9841946ca1ef9a6a46e0bd4dcb06d975e9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98942a338abcff3c662d27387e62fc9299", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983cc5ccb80d5827830172114ed7d73ed5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98897b4f49372091406b76e7687d13bf00", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b71f7f3d716876791ecbccee0186b399", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9879621140ee7c07900cb5d36b6b345d34", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987546ab913fd5769d48fdfb7537c31668", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/google_maps_flutter_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e987f2e266851b6e0372b815940082e3163", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b96ae41900c450090d5dc78934f57a7e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98698f2123ae1e785fcb06c655c33dcb1d", "path": "google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989ab968f3010d535d48cb6d6f73c5dad7", "path": "google_maps_flutter_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981bae920799e4f679f531f51aaba377f8", "path": "google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98beb13a5c6d2b2f63cc3825f0388e5a8f", "path": "google_maps_flutter_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d433a38d3227976ffead6e70d6ecc4e2", "path": "google_maps_flutter_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9820a61412090dbf59265a99a700ec71ef", "path": "google_maps_flutter_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982c6bc786bca3a0efb375fdfa4425abc3", "path": "ResourceBundle-google_maps_flutter_ios_privacy-google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e4cfdfdd0e62cbf50a66040e0d59d001", "name": "Support Files", "path": "../../../../Pods/Target Support Files/google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be9fa0210844d685e3e5e6c7f0399b43", "name": "google_maps_flutter_ios", "path": "../.symlinks/plugins/google_maps_flutter_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895b15440d0d38e94b8eef4d3672539db", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/ImageGallerySaverPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ebf24153c815aa246156dcaecd53716d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/ImageGallerySaverPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98105734053bbfa87aa28f039339bcc5b4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/Classes/SwiftImageGallerySaverPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983469c143789a83fbdaa4d28a3db672d2", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c7c5e0e984c88e4409468c341f3f247", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea033998f0cbb66649656ec89460834e", "name": "image_gallery_saver", "path": "image_gallery_saver", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f11bf8949dc3ce6845787aef7c4fff1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839e202eb9c202d8c08c5469f396c3f0a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897822e47c7c9d3ef7ae7a91b04303774", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988471791ee6dee478188348fd6a42917a", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984103171f953dad7bb1572847f7da56b2", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848ca5b865fe223a83e00ccfd6d758cae", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891c6f001b214124a06973e428c193a71", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2e4a56fe0632cf409416888329388b8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896936fd0d935b0995af26f789ca845ea", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbf27ff313c09583f003027de1115f91", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b07ffb7bbb719a6cd58ed8c82961ccdd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea45f5d1d4557a6abd820b38d0719ce2", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988909f4a2316443c901319b58d2839e91", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/ios/image_gallery_saver.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c3dcca0a9cd59af3e349d97b94e3b400", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98625e8d1b8358b02d52932962ef4ae742", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980ddfd4c453f145c60339504772b320d4", "path": "image_gallery_saver.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9897b044639040fb7ca576f34bce9d8be9", "path": "image_gallery_saver-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fd11f58caa0114fafbaa95572a7a5b14", "path": "image_gallery_saver-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dde4de5f0b03476025e553a4741f839d", "path": "image_gallery_saver-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ce532c61fe1299e3df6849804713279", "path": "image_gallery_saver-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984754ce45c72440a98a05073a40de3406", "path": "image_gallery_saver.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9840bbeb85e31b8ee4130fe27aaf0832b6", "path": "image_gallery_saver.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98305b3ff0eea8aeb8bff181834872ed94", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_gallery_saver", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e83817392b7007876805571f01e53298", "name": "image_gallery_saver", "path": "../.symlinks/plugins/image_gallery_saver/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9817fe0251558412b6bb4ce530735c314a", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989c104af98fd22277e3e2c3b157c0be15", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806544ed4e0948e0b4a8e24f92135fe44", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985587dc62c71e5fb5da3e9b8136483443", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982baea020da990aff3a6344f48ad8ba61", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad831a8f9dcd9168e2134ed73338a81c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9aa21018904471a68840b8fe66d3267", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fbb5b9a64546b45e3c7903a21577b8bd", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833f394a7d97d9e157dd7f5bd80aea2c8", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a0a294cd03df93003a430a1188b8c2f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de60eeb06ea0b47af70488ff78b3e793", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807f2dd3f9ea673db44128b836e885949", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a05606ff9c4d9acf5107d3e08719b10", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987cb6f469e62b5ec70c8e48bd0a8e49bc", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98be7fa557aec116f4725b14081012b377", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a991d2234a9ed824ecc014ce4167eba", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eaaad17d20d6de82ac9bffe1602a8ffc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ecd89e31f3208bc306ff0098ea54fcb", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c4f995c1488a29868d06a353d1bc1d1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9892186ade8f49f67d7f70aea71087d193", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b09f868402eb66e7bc40aa655bdc0413", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d078decf1e492c3d7dd3cdb6bdaa558", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b2227ff21a2ed5a31f18e7d6da239cec", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981e09b9e0f28c826bb30cafa1a1454951", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb30c5c108ddeb7b044348f59916d5de", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd9d33791bd8d1d161a3898e2b356426", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f4b8c2ebc8b1d15e946919da45818fff", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fadfa3dbec29d504235e6b11e687e398", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c9b557464423b65375ed5cd29aaa2a5c", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d743a3eb065d008178525a65da41df5", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9cc22892371c453c02ebb44e5c77755", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98563ae0298d091d6cf099d0741d2c6df3", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3d3fb4f487934c3bb2b96038a229a74", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98759d54b96821b10d5c9143760839e0e7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7948c5ebf4c569dd8c8af0f971768b1", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d8b4f045d83a3b6bb418f53093f2c04", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98507e05f0659d70cba159c166de92eeee", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac10c8de7e45fd220bc0e9bdc3a615ce", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98163d22a0390b6369f55a9a81684d26d2", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7b77273444a700295326fae903cd870", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986429bf477dae9cd50647731d62b9725d", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989af5ab264aaaaaf2475bc0ddd9328bf8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801b63ccc469027f867f87ed01f23cf03", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985afa2f8548ba658a518abaa2daee364d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880398dcad15de0e120a4138069e38a45", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983840d25a63ba5a1d9ff5b010f9038cc5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98823aadf7f3521557955a5500c7d32aa3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f8f9707fce7663d1a2911998def8150", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb7ad78c60cce203b7bcf8516501ac12", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9876fac6ce816f953d466f2db950dab28c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98349941c9c904ac946a72e8fdf96ab068", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f92cd5bd67734aa398f7c79928092e08", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986c31d6f065eba3cab412ec7481fdf67f", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e847c909d27825b7115e7841c500fcd9", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98acacd76343ccfcee0ac5f83b9b07e99d", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987f5b13c391d52909dd5374cc7111047e", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fff5adf67974805090ca27b203aebc78", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b61f6f9262048fc3a41c1357329a73af", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a97c39b966a3a7cae36e18207fb5a0e2", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e80a5176ac692d49eec28bdf1f8b5561", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98611e69fdbd5c6081bb38dbd2a1f91717", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fec72e3dcbd8dfb41aa192f5cd180b4b", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c38dd9c816d12e79a0f7246c1c70ecbb", "path": "../../../../../../../../../../../fvm/versions/3.22.3/packages/integration_test/ios/integration_test/Sources/integration_test/FLTIntegrationTestRunner.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b5f9e2d31402a3d3b53a81bb28ade9ca", "path": "../../../../../../../../../../../fvm/versions/3.22.3/packages/integration_test/ios/integration_test/Sources/integration_test/IntegrationTestIosTest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989f319658e5f49c339a199c5346cfb32c", "path": "../../../../../../../../../../../fvm/versions/3.22.3/packages/integration_test/ios/integration_test/Sources/integration_test/IntegrationTestPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838f5d66ec4ef50eca81be2a26e9eb72f", "path": "../../../../../../../../../../../../fvm/versions/3.22.3/packages/integration_test/ios/integration_test/Sources/integration_test/include/FLTIntegrationTestRunner.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982cc1af60d197f7be1d6ea8c1b9aa1664", "path": "../../../../../../../../../../../../fvm/versions/3.22.3/packages/integration_test/ios/integration_test/Sources/integration_test/include/IntegrationTestIosTest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f69b7086ce284cc5fdf0dd3ab0e40d7", "path": "../../../../../../../../../../../../fvm/versions/3.22.3/packages/integration_test/ios/integration_test/Sources/integration_test/include/IntegrationTestPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988a4077f9ab85894a86e45e005bcbcd88", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989dc9ce95558c2630df946ffa49473a25", "name": "integration_test", "path": "integration_test", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898bd4c6b8f5104b816cd9d9d82d7669e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f4627071c53e0119c4f8b01f777bfc3", "name": "integration_test", "path": "integration_test", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9888880da368a71418fc68fe8507e8e05d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a04a6078523d617e65495719a59dea66", "name": "integration_test", "path": "integration_test", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae611fbe6fe09bd44b3d52a81be1abc0", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c171a5db04bbe4bc4891285a253089d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98465d25aaa8ede69460d3ff43c4c4a7ea", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989dfda7b89d5f29bc57ff6f9514fbb1ff", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ffc46f3a773390337e7d24fd1e6e1bad", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98099864af9def65b97a1a22e5e09f53ca", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d77f89789410bf4bf2f4fd64636366d8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982dccef7b66ac14efa792f9595d166748", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbb8c46acb224574c2ae4582aa62aa14", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa2a808ab47f0ad8a93a1ec52f1effc4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98705cd05b233b13c2baa6a71ff5f66949", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c465509605ee12f0dc002c90fde89a9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e7678a80efe3b219c1302d846955e50", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5ea443ef77cdac2efb5cbecea548e50", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ce9c2c77ba0fd3b21d763a5cfbd012d", "name": "..", "path": "../../../../../../../../fvm/versions/3.22.3/packages/integration_test/ios/integration_test/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98382995898c63c1c792035c964b6f2c4e", "path": "../../../../../../../../fvm/versions/3.22.3/packages/integration_test/ios/integration_test.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9827e029bad1ac616f86402f882f77efbc", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e1eb121652e1724ec6155e774c1ca61e", "path": "integration_test.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98712e79490c5a622b45e0f1869236d247", "path": "integration_test-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e820b041597a865e26ead358f6852cde", "path": "integration_test-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980bcecfa6589957a3af1f3666d8343aaa", "path": "integration_test-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c6b6996a156190550cb1ddea474d2631", "path": "integration_test-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983c5487a920386634de027809f7304c55", "path": "integration_test.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98494431b15a76acc0bfd6093568ef147f", "path": "integration_test.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9842988e80313ab448d526f1cfeb6463ec", "name": "Support Files", "path": "../../../../Pods/Target Support Files/integration_test", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98589e36144d120ebcf37cd715e7ed9aff", "name": "integration_test", "path": "../.symlinks/plugins/integration_test/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982c13b008425192b7ecfb00a469aa9967", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/AudioPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f99666d017e71f12a7beb99fa5fe0af0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/AudioPlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc01657ae28e78aad19b791ad1d0262d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/AudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a264099beac54dbbd66e77020f8a794d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/AudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c2c6674b8a51434df77c24faf14a751", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/BetterEventChannel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9870acf6a8e946a9d42736b8a2488e0b9c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/BetterEventChannel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3fff3a5a352c6ec3edee83c615a91fe", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/ClippingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980bef45e2d6f107cb8cedeba8a3ba2285", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/ClippingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a569375d9c1491cd9ca9cd8324d526b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/ConcatenatingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c288af17a2aa2e1776a65f82616c4d3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/ConcatenatingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98045fd4970fbbbedd40fd29909119a743", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/IndexedAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988963509a6a0a9025ce87f6af89c1b10b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/IndexedAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eaf9a9833c0cfbcbbbad0199da52e5ca", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/IndexedPlayerItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ea066953f4dbadb1e1f0ae550faaa758", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/IndexedPlayerItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98529138e9e94f379db064d8c51b3240ed", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/JustAudioPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986124906cd3ff85652807e40fd012e034", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/JustAudioPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e79a963e3060896b5040cc4e59cadc6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/LoadControl.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98447858091dd739969116859099b86c79", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/LoadControl.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982bbdcb4dfbd00e7074e951baf9a51dbd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/LoopingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989be07fd480532cd637a915b2cf043443", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/LoopingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9869a91b18789600d1c78ed1e9dcc5441f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/UriAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98804adb7023a1b2a58f1e02ec48e43dd7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/Classes/UriAudioSource.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980e303f20f9b2ecfe53a82c4f5d286482", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fff2f92b3a9f35429343be8e5aaddf72", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c229b3e61bb44c0803a4f23f135ac73a", "name": "just_audio", "path": "just_audio", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ed7ce7d598c1d4f65bfddff7a3d17c2", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842a4a1daf79539f9187b73bdd2165c20", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e8fb412ed85e78f76de9ecdb98f2681", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d72e8fd50e5bea65d65617ddb931d0df", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98080a5b9b99384121d3c7cc663077c988", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c13f5d928a6791dcddb695109677791", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0a985817c6a3b496e1a87b833707bad", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe47d341a6cfa5f81caa578225f3b2de", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b51bd07f552db47b95398eaa983b2913", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980aa7a5fca1e14d1b1fc6c95eafb13d17", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867843f5788210e2e6804f02c9ea16eae", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986af23ca8254b49da1b1850f701ee8149", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9895cc3a42b8cfedc4f69d860e3001026c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/ios/just_audio.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e983368bdd995a879172f91cd0eebe039f5", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.43/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98173d7f16a6a42e92d62cca841ecb501a", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b507157e89928f97198d9abee587a489", "path": "just_audio.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e3ed8d1aa864f2e093e2fe355414a689", "path": "just_audio-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a4686d081c9d7fdd766b920898497bcd", "path": "just_audio-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f151a22e2516e9140b10a6de35128a99", "path": "just_audio-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e33ae8541c14ea9145cf990fb2c944f", "path": "just_audio-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f01052db69729086837946776b677573", "path": "just_audio.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987173d5e1a890485807e71e6395272bb0", "path": "just_audio.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98970a9351d42d6588ba7f4df8ef2baea0", "name": "Support Files", "path": "../../../../Pods/Target Support Files/just_audio", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980cd06a75041893012a31882b5d2f01e5", "name": "just_audio", "path": "../.symlinks/plugins/just_audio/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a327c47ec429d9eb77ef05fe501381b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/open_filex-4.6.0/ios/Classes/OpenFilePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f11c6246fe860be9ac561a51f2fef3a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/open_filex-4.6.0/ios/Classes/OpenFilePlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9896c867cb74b81aa5c571d5cc78022afa", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f819854dbbbd95c261807e44db58b53", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b77feb6453fe7fc7eb0f861d3494d71", "name": "open_filex", "path": "open_filex", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bad880eb3458094a0c94772584e9e8e9", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d152fc30c431fd8e846671e0c2029ea6", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98751410fc6da37ce17f29e854f1ad8ac3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818519aa0417f32482049f476bc8c0124", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c4539ea119e968d5ba4ac36d5ad5f19b", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98242eb0eb757136e0887106f94af12190", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98303b55d81e333abd30a5c75622f1f602", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aecf281995b7bb6358a9bf2b195b7588", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed0421106c46c79e8081c29f2226d3f0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987853d494f0a560e755d3c6e08f96a615", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a3872856721c72ccefe69a14a2baa083", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98026090bfda7002847ed0bcfac8040a49", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/open_filex-4.6.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98aa4727ca60f74926bc9dd6460882ca55", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/open_filex-4.6.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e981e13839a8a9df1eed7d5b0fb9292fe0f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/open_filex-4.6.0/ios/open_filex.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c03bbb0e67a41cebc1cba303364db32f", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98894ccebcf9c420071f8b59dbb32f7759", "path": "open_filex.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9890ab346c2daa83a6fe8d9e6aa7ac5d43", "path": "open_filex-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9891b18886ad6a152652a8c4ef33be6df6", "path": "open_filex-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f7f31b08f8706bfb18d1749987fd0273", "path": "open_filex-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838c570db711ab55bdbc2eae4623343ab", "path": "open_filex-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984f3fc541f0ceeb23660d7e35a9619139", "path": "open_filex.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98242ff7df72b9eac29f9c9f924b692000", "path": "open_filex.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ff46297890c98883bf6ffc82dd18763c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/open_filex", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e67a490f24058383182cffd04c47a5", "name": "open_filex", "path": "../.symlinks/plugins/open_filex/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987660362b1a9c88865bf000167a526545", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.3/ios/package_info_plus/Sources/package_info_plus/FPPPackageInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e987dc398cfd10511c0b6205fbdb1c23489", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.3/ios/package_info_plus/Sources/package_info_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c316d12ad43ebb67f9c02aa6af372c4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.3/ios/package_info_plus/Sources/package_info_plus/include/package_info_plus/FPPPackageInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c92f37c6b50beb57bced8aac755d0ebf", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c6a663aabbd1c90591431ce2972bd0f", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f1d6eb01099dd440094ca6698082b02", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2f4216d43f7172ad25863b6e7a545d5", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f12dbe531588e0b1cef5724b74cfb78", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861b15228b74b2576c0258cfae003aaea", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98500ecaacb05a49ef18e477ca67d867a5", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d7b9691f3782dad9373c6da81e742701", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981659debd537e36895b1448fb575c0b01", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5662bf244f57d98614be6db28569525", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa22f8858785d779ba1b03fa7835adb7", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da6301d0e26a26adce6f92aae5c1ec91", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829cd88bb6b4167ddeec28bb005198473", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860e0ab225ef16e2a499d4295e0979757", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ecb970e5bfd11918cacb327531044d1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c33e49a95a32e410e46f3673684aea2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8c26b5de3fc0e478909b7035a6fd2ea", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868446901514030fc344fc3a5033a917e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98239d422a36060acadd4e2b1edbc7d3eb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d1d4e4886a95f7c6223b8388996f2ad9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98804a4dd4e83446c1b28dd1fa7564ac3e", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.3/ios/package_info_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e57570ee5a977fe35203eac757a4a179", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f44f015e8122a079e0aaf7a50a982d04", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.3/ios/package_info_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98323009573b75856a476aaa2c0825ded9", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988959d754d92919a0af5c7c07ee79b77e", "path": "package_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98183f0738cbeb2ac337e67794c39c67c5", "path": "package_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9810b1b41624e8b997ca8bec646db29e27", "path": "package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed817e5dc8f202591e017eef4bf6e947", "path": "package_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98752ca48d28d49a3cca382a4006fa8816", "path": "package_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b1cb70db966ab7593928794badfef88c", "path": "package_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f51756731b788d412aa810d279569006", "path": "package_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986822ed3a83f17d6ba791c633fd952731", "path": "ResourceBundle-package_info_plus_privacy-package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fa6236f8a7236444e55fea1affd31d4e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860414f665c61bf39a435b892a5566646", "name": "package_info_plus", "path": "../.symlinks/plugins/package_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98772a419c9f719ed73d70d2e4583f1d09", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985e189a2305c7baf15da45fb9ac2c5023", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d4e37a27d00ffb315d17cc48794495f", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9890d6fd8ed1d4a6e2bd06bee26242ce6b", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e30dd99d625fa373668e725cfae31cef", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f65fcf3c98b5b06cbf3ed5adbe439328", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982117de05c2864bde535976c6a73fd19a", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b95fb98d3f46aa9afdd5ec2c7a61cfa", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b4bad1aee2540806568e1b3e3612358", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9850fb99afd2dbe8ebd3b7420cdb1c8568", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c1516d9583bb19c18df2fd19bfac53f5", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814fe63978a8e06087be7dd1601707d86", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98345f349c420f95ab50e9096c8039a25b", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa29216670d89534cc5584fcc2b96f79", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987fe2f4ad3ee3a392155751c88700b917", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a877aaeb4678eba2a32bebc26e9cd021", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981f323de462e3d375b550f6f95ef65f62", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c3e550bb3c3b7a6592ca9386993dfdc", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b50eac4c7265b3ac0bb98f29e3dae9b", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987731c56041b2f593767eb3b8c7b855b8", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98882214a34024206209d408b744cac432", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f310272a62eb68f59aadce2f982cc68d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980920f493f8c3376177a97136af7c8b4e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828b60ab6d0238b329d54fcc88aba9536", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af75c9c6f1fb4f7993b373581b53daee", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983c8a6708de8f52074ac7de7f42faaa9a", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e2b35b0092b69f5ffbc4db7cf4cf635d", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f447eb711ec0391393c6b398db6d0f7d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985052e0d993cb0ae34d469511ad878077", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d4a59215d0b44b5fa413c14c0edd444", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f193073b15c7002d6a3ce4b8fd181dd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f021d014c5e72059c656514d1de0db3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b76259a63e75ac336a19206f425a0313", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982423cfebeec81f02a27aca0884e45c46", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833b10e3d294561e58403926d0c692bcf", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98160ad1d05e355b9e80ff5e8eeeefba17", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98bed88a65256c455625ce1bd486350945", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bbe5e391ddd18094785ce2c57435d3d6", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98fb94f4d5b70155124cf215531c9e0e15", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9837ab0e8a000de12f0613d58d35a7012e", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98099f75eec134707a8cb4a30ceca8a8a1", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e562c9152ad749d25ca61f16289fe489", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988214c1c61dc1d8581dc6e29241a87556", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b7dbb29eac6e55053e499a0226d50b81", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b14c7521d47f40f216178c76b5bdc230", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a30e7d6a4f308e1285dcbfaa8ce074cd", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c3903b889e950977f886ae2b8bc17b39", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98abddc5af28ddadb53ae74437b99d2b05", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98978cde07d82391ad34578b6336672299", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9846e9f14f352016a89d4e4a1c4adab81b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9814ee9968c949d5de07ed6fd58c3cc177", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b2b52597303fc2288ac05fc077fd04cc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ff8008e65649656e63baa9e00c633c45", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd009cf07d60e72ee41d28c1c3ee7b31", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987a88c4ea7d328f5c317791f2908bb7aa", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855f84fce298aa02ef5b39a8f7011130d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AssistantPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc5e062318fb59346c8411cd7c9a9f4d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AssistantPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d044ccd63cd9201c21d8977d2c508574", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7e386a9601096156372faeb8d006456", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882485793748789bfec463b985f530a24", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BackgroundRefreshStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986e17a02413d0401bace3058b5429e256", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BackgroundRefreshStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98900a07b13a0a4c54863bbb108d438d23", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bba726e9134f56f989e6d8a07113adc9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa1eaaee0b26d0fa176745f128ca03ef", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aaf9271c7feadca09ec4f85c513b444c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840e713c445a96e6c83e37cef702cebd2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ee1494649fd00e3f2d3f2fb16eba72b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9814e9e53691689979d84d8ba75c64916d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cd8b709ffa0c7486429b1bcabd763f44", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983489354e2f59b442910d39b17af17d1d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e474d5a5e2400e3c29bcbb3f9f09627a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d7f5c1e1ce91579c163f4ec1270c6242", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983f542e1ee134e9c5cd87e51986e834c8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f3713ec396f14e324dcf10b62c5bc5fa", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9895bbcd49251ce53b50a49f15f420a99f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984747502ab2a06bad7ad67af4b26ce3d4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b4e98fc3e1f5d1fe8f531c32063d6ae", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98713e7f3cf50f4e6180efe3c7bae1d42d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888ddf50fac4637215dddf973df20519f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e00a7aa5d8e2202d5c6beb9a16938382", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5d1ee9ef24d86a7032082dc2dba098a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bf64de45af00373f3cbc8970f5c1b51c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f79eac11d78ade8f7086c8083e7e0d4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3a72b208a0d801f94d98bdf0fcc4553", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872a6459086e83c09ffe9b73838179aa0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985d221fd642bdb429e40fc6f02679b054", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9882ec2c9baaffcaaae6d818eef5156", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980fd67d914650ed6cd14426971d0fa828", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982005e19a330316de690195ce90cd1b58", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e7644471b00197e7201ee8876bc5606", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aaf0bfd509fc66bde769c66c59f993f0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988b544159239933888ea34e97cb65c0c7", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834f83f812b359f76bcea5397a426b331", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9815ed3fa435887fdea11f37f029c2e957", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988d60b9de967f7dc115f7b2f6b8b89b8c", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b106883f278458437789220aba0e215a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987dcaf1f4a11a5a0c2041914395c0a80d", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9882e7fbdcd855df6951a33ce8eb9301df", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0855bd9729e1411b4fc028e52c4ea06", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898dc082d254b3cd962f2319302788e52", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecc6d845e6b86c840b4e319afe0a723c", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897e004df0f01c2832a949fbb484ddc3b", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98633ad1d7777b420380339410093e8f0d", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897ee186cee517b1b49827c031e5e64f6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985294fd8703d155befd48ec9f27fb6752", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9abd311d1bc986cf44f4977aab61e70", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e06b389522b259eaf95810d57a6efff4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827afcf6460ba4b76126cb92f56102c4c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ad61b3dce1816a5bc0d5e7928450b8a", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e4fd3aa5e6a918b8d9d32d8f20c91abe", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e989520efdd950815bb2d596f8530cc32cb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d8ca5a4850365e57d1eed5b5f9deccf9", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984fc3b58b8abe29cd53f9905095f377f2", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9829c712574663447cc8eab57965b5f850", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ab227ca5446ff18a8463fbbcc34e4775", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9864f0fc52bc1966bf4fcecff4550ecf6e", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd41d9eb99a3303d38d2eab4835ed59f", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987ab0aa00eee5f1eff22edbde6ae9ad5e", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985744c3854c1acc5e17ab38a228cbd5f3", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b29ca0bd7ef7769c22b92be352351a39", "path": "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e565612cd2b6b20c6319cd726efd0068", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ed2b74d9cf326445779b1e9de64dd6f", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989543394fbc99e1e6ea5cdc00c813a0a5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/FlutterQrPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98675cdf084be38635083a55998c4eaf00", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/FlutterQrPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d23e018429cb3d7a84857b4145b7eeb1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/QRView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a882cb611739d3b13f9568dccd786c4a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/QRViewFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98639542a307f0064cc05beec6d9e9e7e5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/Classes/SwiftFlutterQrPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987974cf6b592e7cade892a417fd654b75", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980606a1e43c77584229948cd119c49e50", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809100702580e44a39efb88a6a314ddc8", "name": "qr_code_scanner", "path": "qr_code_scanner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a44069454918350165802982f3dab04c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc5fb09eb73dd201ce7eabe42f6b67ed", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd8df658bffde4cd9187e1f91c618a91", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98096457403fc116f2690c0a264edaaffe", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a33486e5d6ecdf9aab4e9edffae3a65c", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828488c6025eb374e890043b2fdb1dda8", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983514832f318f7ea30f33e2511b7f7db3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816571f108582656d060087433b66e5ce", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3ddbb12f6950731f99eba6486e4916a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98771ddb5865682e4ef201926ec78ae83c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98654814163d3d6139ee7eb71bca57dd5c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce4084d5dcc23ef66755e5ff2e2b2f81", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9830f52e75f36b2aeaae459e45c175e6cd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9833adf1c27ded2db929113e372667b985", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/ios/qr_code_scanner.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98aa36777ff4ee6dcf0ea6ded8cbc1d8fc", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9813480206a1a23cca1117dc6de61ef9d9", "path": "qr_code_scanner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b7f2baaa87787857f59c45ced7881643", "path": "qr_code_scanner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983506107b0da9fc2f08bf97356b76000e", "path": "qr_code_scanner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98985e5bd65bc99eb9be54666513287407", "path": "qr_code_scanner-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd704ee3168dfc67821de6e6aaa6ab79", "path": "qr_code_scanner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989b3a14e04b4dc755ed408eab81beefd8", "path": "qr_code_scanner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98968560c44a63f348a1bfe3b225238ef3", "path": "qr_code_scanner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98639f3ef3729d4282fd4698cf2de7fdf0", "name": "Support Files", "path": "../../../../Pods/Target Support Files/qr_code_scanner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd12332e953a18adcd60446296877267", "name": "qr_code_scanner", "path": "../.symlinks/plugins/qr_code_scanner/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98580ec327520117e5ecd0fc16794db445", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9842115708ba8c7c15b61eda51854ea63f", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e3892d7cb04b3b6315ff0c6913096a1", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f22f52ea9cc3ba17352d9e13585268b", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98911da4e5d55623d35eef65cd9b6b85f9", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98436982a61455948c004166b122540eea", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9819436ab8d74eee04aba46b5e3ad77178", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983220d6bf45bbbd50eb3e06c108f7802a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd7caff85d5a6a69685644a058870be1", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3013106e4e8cf532fb23d60deb5f4be", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bade2f726a0f257e77d1e8bbbcd783a6", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c6e56eab1aeae882b637230caab6530f", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899c795952d747740cac1ad34168274cc", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b612973d2cc029199dc45812da6f7ecc", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98754ef9ebe5eaf34713a3455015575d7e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9896c0cf0f680443a6f70ad577d4a9353d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988d92dff9a94036b6a6eebcec535b8484", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98695b203317d730dc0230c9f45fa50594", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0e2c518e7c5bd7c33515e86c116b669", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c9090556d6169e3b02ea1f3c0429004", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880b3689299428d39c2619482a7e9de59", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c28f207655cab4de67fcd2de36d5972", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881e5512618693ea22aebbafc025897ec", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98969c2b24b2e09b1fd19969391a9970c6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ceb98f895b676b9b03b8810584fbc3b7", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9887e28f364747e9e5942b5300ebc476b1", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985364d9cc50e9ac60460a9f4efecb82ca", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9876655e2f9a025866a991ffb58d3a79a2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e5dfb789f89f3e3f3a4b6c9bcfacbc3a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a6dd3a50379df3ce3ff1e49dd9ba1ab", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a7088e15f5019b6bf333ba1c0f21884b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a5778b1a731a1b61ff75f3195a97b78", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98933ff9bb3cc2d038912dab324d5d022d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2dcedd69dae2dea967c6a190438b75c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98374f1b06b78464e7cbc51b04920467a5", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9832a5c6880cc21133a92a8b3ac26ee6e8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e986c203559774c7276e2adc5d820e8961b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b4918ff91ec00985960bcb6ab0f66c7b", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983daf917978a625c94a5c48a2fd7f219d", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98fef9e2530243cd35d88d8b4886c5705a", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cad59549091606cd102ee24e35d719fe", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f0e68a5d7ad4de3c3a7ba2ecace5112b", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d5170d0308951ae2cf3581397f2e613b", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9841d5983de77cae556742f3af425b831a", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981447e28a98eb7ef29280ab3f06b0f6b2", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989952a1d9a40055abfdccad1ca0744cdf", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ebcc8507e7d4b0d6dda5e76350ede7dc", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9fcb46d8f1933976cdb7968ebcb425a", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98739da9cbaba46a50ad55d29a64632952", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98382ab5a40ea8d536ca55bc6210856042", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98faf3e48379699d6f6620b1e6447b0502", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883574fd07cf181a1f9b5911995c3b056", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e80b6c3b28903ea110ab6bb6bc5feb7b", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851a3747d4e44e418ae57a3a8bdc02a7d", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d338985793cd5c17b6a7015a8f2cc13", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b3f133e0807c21f2f082e14d90f6073", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e46b6dd56b772a430fbb63bbfe6dc6db", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ecd72c91c62d358a19128f5d32671f9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871b6ce4fefcb7f402c9d38fbad6ebbd7", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899f842ca034a072be10c8ccc89d359e8", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d1afc274b3a187190fc79a1944a0c139", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98825c512cbaa1be672fb2bb4f288e26b6", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980978c69400a0ed4509e2c2846a070b67", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4d5ce9dab772bdf837e4be43fa08708", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d4d598acbaa2d225f5da0d18cb587862", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b73985aaf64b70f8539a07480b61daf6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9850b9ac15d1b421b39495f0b9c3ed0d66", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e0eca36fada053d23c4b0092aa1c4f73", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a2c0d2d75e5b518a4206892f9c863c6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a0668f52435946b723ac7f9dc0a442cb", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984157580d70a890a225e7d3245579ad86", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d0094943841fa0e068b90e8ce2faf958", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e622bf4f141cff6ba8bf9383e998aaa", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985a3f2e9c6919960e4db23a9bde7572e3", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98728a27d1a8db2ca6a8eabf910837da4d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989a033361d2a9a12c6a4833867cf48a45", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984269a3f4c8ac013d24ac254de4df6b39", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982e5da627db56c18222f2df67a2d68cfc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98049428696addb68b4294c92e1a481447", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c2513a13232a4c3e78278bd08f6e5bf", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b82aaa85202abd0899618061cfc13e84", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988cb339e374cbe143f08b81c7a4509bad", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989c76c692f03a4bb45660eb8552d7a306", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982e995522604d6a658d6dd4c0d0179b9e", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986761a4cc9c76f755af73e38491eac541", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98385b49c92a43f37368cfcc9d4d24a2b4", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3f00fce61030f0ea39fc3f517e78925", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf5b66bc5f40416bf3b7c496840ca430", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4d0410223bf2c4c89b00a43256be3bd", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831943970c79aa921ff3af6e696a62e27", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fcafe9289be6e58cfc856e96cda84d09", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98621b352b88d1086ac2257574dfa1ff73", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875f8b77082b4363f7fa6df99f4c598a3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98761d8f31fb44427dbb924cf3df4e1086", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9beaf011f56d092417f0de433f9cd85", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd89abaddfc0c9ed3cd1680843fabba4", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e2527e6749905e1dca58384f239a9ba", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aec3b2e8bf24c5f559b1c91b427b594b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b923b1160ea48b18207f26c5a570196c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808d589c9f9b64cbbeaaeea13d270f46f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b384f639b45a55fd5c08a5a409248c1e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98320ac2cd9b3fe0f3ac51e8d5c67f2a0a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9d8d28fa8e3d04c5666586e0303e413", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2d5cf55bb52551f48c29f7b61eadd81", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f11eb3ea70339c83c2faafe65e8e3a13", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e9824a6846e6e38e8356df5ab681c7f5bf6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e980a20bcd84218ee66835144413823ff0a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9805286240074e8d2c071e33627b408e15", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a26802dd9c0d0edd033a5128bd365e3e", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e982d684a4434ec59c62829b46883af1bf6", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982ff48b571ca94e083d61a1f171ab0193", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980aae19279f8252e6b3269bc90c30d417", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b2a278d85abb50cd2e04d109617a5c63", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d50d00893ff48dc0aa532b398a4cc3e", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9842fba444bb5c6e4ee9512a30bbdfa715", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b08b3b14bde7458a1ead420ae720c031", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cd76b58bd041851c33c14074d79cd286", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983dc5b324dc155055359142f1e29591a9", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a3e6750c0039e1d0931e6ffdcc6556b2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/syncfusion_flutter_pdfviewer-26.2.14/ios/Classes/SwiftSyncfusionFlutterPdfViewerPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98242c38626f349dd57f8b6f3049b844e4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/syncfusion_flutter_pdfviewer-26.2.14/ios/Classes/SyncfusionFlutterPdfViewerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986bc2ac02a5e377c5ff562ffab0037f95", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/syncfusion_flutter_pdfviewer-26.2.14/ios/Classes/SyncfusionFlutterPdfViewerPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980c3db115894554ea4a292fad48a22ab2", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c444b05e93e74891bd61d0389f148d1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873707b23bf340977702f282c3fea6995", "name": "syncfusion_flutter_pdfviewer", "path": "syncfusion_flutter_pdfviewer", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98867620dc4a01d76c615d0d03787de704", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98afe8ddaa6e2df19069d9da8031159649", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd130b0141a37c7fe9d9688d69cac26a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9887ee62f0d9d482e1efdec1a172922e72", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860ceea4bb619bf4eec14652c4ecf6708", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868ddc60f722b07093041582aa9ccef3c", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98422c156c2f68c475b4e4b3bc4f5a0361", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5af1f0cc13a193f92f4a2248d4a8a6d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814395a9ac1e6a1700aeb2bf569b0f283", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889eabe48e24b2b50b5f4329a40f2de1e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816b3091aca441c6ae581cb14359d106e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832d701652a04337026e37209157f431f", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/syncfusion_flutter_pdfviewer-26.2.14/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c1d09f5ab6240ae7fcd821879fe29800", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/syncfusion_flutter_pdfviewer-26.2.14/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9845a045e87a1bc4cda724ab4d90915d58", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/syncfusion_flutter_pdfviewer-26.2.14/ios/syncfusion_flutter_pdfviewer.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986860035da3631e83fb1a9443da1e47b1", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f81b895376b976439b5242b38521c302", "path": "syncfusion_flutter_pdfviewer.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d35c4a2fb90fc39328e0bd5f98e1ddbc", "path": "syncfusion_flutter_pdfviewer-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98da62837efb56f5b84eb79403572db6b1", "path": "syncfusion_flutter_pdfviewer-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f80d3ced150c9c36d42c093a4207cb46", "path": "syncfusion_flutter_pdfviewer-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860d61a6f292afd77c82e81b8b42340b8", "path": "syncfusion_flutter_pdfviewer-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98eeb394227802cad0273c762a28bbdd82", "path": "syncfusion_flutter_pdfviewer.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9893fd539876dac96d6b85d8eab86dfed9", "path": "syncfusion_flutter_pdfviewer.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ae49464c74caec9fd7b7374c37a144c7", "name": "Support Files", "path": "../../../../Pods/Target Support Files/syncfusion_flutter_pdfviewer", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c74c569d2dcfa7342bfd53ed6ef5e579", "name": "syncfusion_flutter_pdfviewer", "path": "../.symlinks/plugins/syncfusion_flutter_pdfviewer/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98ea23d0b824469cdb8861fe9a35398c2c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9876f8ba94b5b861dadccb3c2ea22802d4", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852204cb92b7988c4e335465bea37523a", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828f26a990a58791d82f5b7df88de6379", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9859d50885c8eb64e7b804dbc4cabed79a", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985494fa0be99d96464d5de49cc078759e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e31dd843d7f9a4b34781082ba22b6b67", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8c13180e592ef1e8b452bdaacd9ec80", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855dbf8ddbc831a5b1911ec1f05139e41", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce1329cc3cc8b1309f2cdb5df68a7a4c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836ac1455c7cc26ab238d140a9f460aaa", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982164eb77ff79735d9fedbbae2358f3dd", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899148af588a0574ca8decf3d9e808af3", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987cd64aa7bb90e9becb29a39296c4413f", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983a3ff93485d7468aa9bed43bbc0597c0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e82442c8e7724a8436e0c4920d92c936", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980258694da9444adff738c1baaf344a42", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980588a7bc8e1d389b20e4d2a1ebf1e1ae", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b70064d60f08863e5932e62c1ca3decb", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e2fe964c9548bd75966a10214b47eeb", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2e6db956ef8d2735ee7d22e8a256ab9", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bfb1ec9b0b057e47599f8bba1b7f0acd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e8c24b3cd29ad9666aa08a96ce6259e", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98946120a2ae76a42f25a549f0a71aa175", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c922e108aa052a1213b9aedc0d6af564", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5ef9c1fc3bcd16a24b5b0d18c17717e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eab6b1ef7294ca08b139089067d69e36", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e0a1f3ac02d92575ac40852d0819e3db", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f6c5d8057ab7a301d2c5c0d78cae1db", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c0e0d08390fb7e18c49960eae503667", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877d510eba1ec94c6a7bbdf9908bb04d7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e424b8708c551fafece9e7538744e0a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f86150591b34672811a820feec70d2f7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815a87830b098fb2cb605609961cc0b88", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9fb55456e21ecbbde79b5f10187cc5e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98256890fef6b8ac641881f2fbc7a0c80f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853ebe6f5afe3ac16f2bf333be8a2cff8", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9836d627fd1c10eef8be880a40eeb7d397", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a47b23c61f1a3e556ead9d94d1f97d72", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fb515e745bdcccc44fe9ea5a28f67965", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985fb3eab6911805ce89581971d14f743e", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9870b53d6c527f78ec6e983898dc013dff", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98202baf6fa1e88630096a43d4a0470e4d", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985c04aa1ad6550f7a4ce134634be5ee90", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9896baaf4c88520a4f37cb5d7cf27e0543", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989ea77c1efa7b256721194a1b57c1e134", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98911ba3d18ce5b604524914925785ec99", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bddb2a8cffb0726cdea7d7f5a45e1e4b", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985da16c9707d396f2f84e2c3fdfd1e729", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d99d1b69389411da0a78fdf46e3d68b", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98b413b5d0735aca675b9947ebf1dc5bb7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9802467f30c3e51119326a8a9323c01c29", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e96a3ff776d29f729a9e97fcd31156b7", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a243e08d5d408731fa2f59c246b2b251", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d86af30467d3d8d4a6cf9bba7ebbc05", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa24d4b6757ccb6691812d90eb487c52", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980cb7fdcb0c3b847847618549926ee52a", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c01aa4d0a8f5d64e359783f08be926be", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df4d92c4e0cd25ecede481af9e57de0e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a98e5302cfb6b24c1d3ddc35595839b1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987650091ed67a48a877335b6106ba57a1", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98861d2dbd4086026da67e88c1a3762357", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98187ab3730f112560ae51839eccae8f54", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d80711396a16926e377c0004e125bf1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cace87a0d581a8f96d70cdd14e139fe0", "name": "..", "path": ".pub-cache", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c62faaa671cdc91d182ab651599f79cc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/AVAssetTrackUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98470cc17ba254f4776cea8bfcf3686832", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPAVFactory.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98360d96bf587129a13df76d5bc08862cd", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPFrameUpdater.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98589b20794bea193bc5c6539bfc31196f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981edc44847e1e2d3eb300965318243c95", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983164930713a3bb0add0ca549117b8660", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980e6e1ccb2bd3dc1238e3b6c2f9f55936", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/AVAssetTrackUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d3e594074e8923edf4c3fedc9dd6e04", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPAVFactory.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9886323e6a822585b219b53b25c20ece3d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897db8159e6b01646a7e88dc96c88bcc0", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPFrameUpdater.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989698d9943e0f300c364946232786f528", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872b166ec7a662dc3db768509087fdc40", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ad625734fec8708a8658588bd3b5a97", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980e08b3c6eb14171adc06be71dc9b2d5b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804298ab7ff32bd59d738b884dbcd4bdd", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985c8cb0012923f99dfcc92822a5cf52fd", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ada29ffdcee1c599aec7db897303d589", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851007036f1123951a1448ae26be7b1e4", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981a5cc099aa53a43c67798aec7a4b8289", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation/Sources/video_player_avfoundation_ios/FVPDisplayLink.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98639e6f545e1b8a31f44821b057f790a6", "name": "video_player_avfoundation_ios", "path": "video_player_avfoundation_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c993cfd71d04490ebe59a49ba8f39d78", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824d27810919bd43b307354a339ddd73d", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98960ad59bc48921af182f4d3ffb737468", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c75d02aeb7f7bb85dee04d2a94e432c", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986198151f0852c544a95f3346eb567666", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987da0ebe1487c04ce1256607a6f7395eb", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98743f2cc819a9aeb9804daecb8709da3f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98123d46973bd98a92441a5060d24977ca", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986738c93b006ae0d77e38da3c8fa14b40", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5fd640a53d1e58fb0d3f3d17bb3cd70", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98206fe18a030794349c9886c13a5365c0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5cf95b507d3a87da521f87b677d2812", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9810b590b87c27970c05ef2271cbf1851a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b66e7383c602b9a4cf181593895c5a7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880121b684eeda99c1dcfd71c450179ab", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2274632d6e729fd2bc375d75a4b27b3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98475ea79e70184d5ac850d9bde85a0d57", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985a55c28f3c5935ae4eb25401e159e23e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b60fbffc43231476bef93c0640ff99a3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.7/darwin/video_player_avfoundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e1f7f17e9bcb978ec7cecfe97ac8db35", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9818dcb0fc843584b31bcdf7b6236d2cbe", "path": "ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9877603dacc5d7e15a812119adf9ec3b9d", "path": "video_player_avfoundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985cb794ce9101842b5187168a0e345a66", "path": "video_player_avfoundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98565fb6a91c6981ea06560464e2211822", "path": "video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7c8d168c009f273a60d5fc8aa0df555", "path": "video_player_avfoundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bbfbb88d726c3e5adade71059a9a0629", "path": "video_player_avfoundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a7d5a84800a049f10f27f4b3f64fdc72", "path": "video_player_avfoundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989b5e13208ab5e87128a39d56773ec610", "path": "video_player_avfoundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981c048b4d862d6aac42d567c6148c1b6f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d05c3bb99643c3ef6ccdc972ad158ab3", "name": "video_player_avfoundation", "path": "../.symlinks/plugins/video_player_avfoundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e608a4ac2f9123c7f7725e2550634de", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b39cecbee7000bd93c87b5a19c90b19b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a835cd704a4bb21a8e5b928d89353cb0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Classes/UIApplication+idleTimerLock.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9857883e12f785b688b401c5bc48b3e097", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Classes/UIApplication+idleTimerLock.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98304f8fd32548df45b43f6045420bf921", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Classes/WakelockPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7cf206dbb2543884a7c3a703e078865", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Classes/WakelockPlusPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987316e8ba5178ef840853bb045a4351c7", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98aeb32df8469187c84c557a872a4f5e51", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b497e1800a5de9a13879862fb6a5ab34", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98761e6f822da66f8510680c7501e68eed", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982694a13fefb295ee3bce3718210e2ee8", "name": "wakelock_plus", "path": "wakelock_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987889ba494a69dff7f187b59e2c45b128", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824a5f8341aa850f8e1330d0c6dcdcb7b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c235a4e508cadcb67374e48c2b013b2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c33157daec224304215685a0d3d77d21", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98981bf09c20fc245da307662718bd5419", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988aa1a4589913ecd89b592f6021347f59", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce3f2c28904d6040ba1d47117ff0f452", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984438c0de0d0604e5864e53b16128930e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98358a3034720a5002f9077ee14fa7eb38", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988388cfae6649044a70701e437e971e3c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830f3e2c006dfaf5c25eef76ed19505c6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd1efb71447544b32b27f08ad71f8f93", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9870da3bbb5885583166b49ee81f8354f0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a9358aca9afd2ddca4452becb4bd65d6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/wakelock_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981d466c8232e17eec6659c84a660ac5d8", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9862b0367890822abf1ff375e43c6ef170", "path": "ResourceBundle-wakelock_plus_privacy-wakelock_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98be721a4bc430c46fb07cfe8518d1e690", "path": "wakelock_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f3a68bbe8f19dbc5e81f34b32cd7d96e", "path": "wakelock_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984954d0ee1387a9586d71383b81aab51e", "path": "wakelock_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98656cb382ea6ef8f44c46691fbd664f9a", "path": "wakelock_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98636e90449e853c4a18b0367199b0ee00", "path": "wakelock_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f9aa3a5ad856ee82f7d77abf542e84f8", "path": "wakelock_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980c1ac7bcdd028d5dbe3c052d938ddadb", "path": "wakelock_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fd48bba26b50791f91ba33e8119e94a3", "name": "Support Files", "path": "../../../../Pods/Target Support Files/wakelock_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ab23bae69ccfe0166302cdfe708b502", "name": "wakelock_plus", "path": "../.symlinks/plugins/wakelock_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d182e4b689b1f984151d29a43d0562ad", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cfdffb1569ea241641979da24cd439cb", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986a7de9c79c009b3f5661c77d23aa9feb", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835ee54a5eb9a71724ba45fc38459b1b9", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2047a107220ce637efe3f4fc65a9c65", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d41e509d944ac46dd0c17650d746f75d", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9941c45ea622a228c7fe42369f6b2bf", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984565a1a3711f4a6a555a7aa79ccf24d3", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848cf83523f1fd643deabfd8bab3c2496", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbe8457cd08569c8cd620bb02f90bc75", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983415c0e729bf76d0aa85dd195fd48e74", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986699118d1498b23c3cf9d8c2a6b016b9", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c367ea6f13d83787040b99564f51985f", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987fcddad36507961a5560d2a91bca855b", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987cc17d1e2539a8e1db94580c769f7caa", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FLTWebViewFlutterPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884cfea2c42013a4e63832bd694c478af", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFDataConverters.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d6426e5f6d6fc444d031f366de0312f4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFGeneratedWebKitApis.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9825493f4f717caea9fa669d17d62b0f42", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFHTTPCookieStoreHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981ccd4ba6e2160bc3a766f1ecd0656780", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFInstanceManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9813a722cb58d104e57f1bf8ceb82baf06", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFNavigationDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986a69cfa3fed0162b66b07ee3ea331cd9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFObjectHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9864d983f5c5aa9264a72c1d2b3d089b16", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFPreferencesHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981dc2966ceebd4c61aed99421e7f41fe4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScriptMessageHandlerHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984f2f50e11b5d856f8c038a3fde226918", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScrollViewDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98168220c49441c65c7678cc911892b73e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScrollViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9861e0c1580874df57c638b40acbd07c62", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUIDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9824cfe925a1265804fc713e06b77a93aa", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUIViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f8298df76f2966adfe0905f2611601a8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLAuthenticationChallengeHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98425101112663167cf613d03753ee122d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLCredentialHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984cea8425531ea8eb9c4b67263702384d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98520d1ca0792c38d2c719fbc287b91f77", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLProtectionSpaceHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98de78ba9bc7a9b0dd3227cb3cbb93933c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUserContentControllerHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98744422921dff312ed2055db32a419429", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebsiteDataStoreHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980d2a23eea40affa0e023e2fc08b14a57", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewConfigurationHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e29bf50fef2a8a315686dd4f440d5720", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewFlutterWKWebViewExternalAPI.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ceb70e1404b04bcdfabf5a5633724172", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9844d0ee3bcf0227a44bd1e72187fed8c0", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872d49e86baf12109c741085eb851c37f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FLTWebViewFlutterPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f26b819b8c2ecabbb204fa0268b56b0f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFDataConverters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833e3320dc0681cfdb827b986d18fd8c1", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFGeneratedWebKitApis.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb53959138015be13cec033d36f8066f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFHTTPCookieStoreHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98611ff547d7cfa702aaa50837ab226e74", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFInstanceManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982bd84680700c0977fc6547badc24c20c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFInstanceManager_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a94b4c82ff779bc56843d4ec362f7c6c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFNavigationDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98781dc1e0aea564d02a7d2c5e1c7cf12f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFObjectHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989acdb258b1b282cf76a2d3c294cffbbc", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFPreferencesHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985017be0569016c8e620bfde2d8091cf4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScriptMessageHandlerHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e409ce32f820e7265f6ef19fea1c96d4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScrollViewDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d7cdd7ebc1c68f18b3f8e492f7d1225", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScrollViewHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983102938a64493f060fbe99af3259ed21", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUIDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc7b0126333dc8a78d2a28a0d6c2ecf9", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUIViewHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d31dae03c3c0fc97d4a0c5346bbd092e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLAuthenticationChallengeHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd26d7a4e8384866227806bc07e879d3", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLCredentialHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9876f97045859ee37709a4fe7c9c299585", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd3b93554e71e51225f5e26a4617ad11", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLProtectionSpaceHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d0c6f0cb4bf191838ed4fd6bbf04d62", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUserContentControllerHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983762507ea667e37a5805a10e18f14187", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebsiteDataStoreHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98537e6e8af938311ac018d943f154cf44", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewConfigurationHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988973a0ddf76a8dd72278087c298e29ab", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewFlutterWKWebViewExternalAPI.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98015fcb5b41c9b6f01497fc57494c0392", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewHostApi.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f402e134ca036326881054a8614c591b", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f5099cc65353eb9d9d25e1ad79daba4", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889cde31ed7c282006ad33f273dda5d85", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856300cb8be043a99cf3417572f658c15", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851428c618f99f41a92b4fdbfc14fcd72", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9840b6223f4983ef1aeae9695baae747a1", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98869d0c2a3b7ef5b7b0146d00fda0a3b7", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98339364ec06f9872d0388a412f00bd9cf", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a9a011bae9cbb39cfbd5f88c8bfd363", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a304d225d3f6659fffacb7feee4bb51d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812b5b91cd43d79e2c3bcc591cd705445", "name": "sale-management-mobile", "path": "sale-management-mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a5c44c57230af1563dc4aa4dccb4ded", "name": "Android", "path": "Android", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b72ab23ba53e816626567b4194d602e", "name": "Code", "path": "Code", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b6ab2d9fbfdfdce1309518c6fe04d9c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c541504845dac8c99500ea0d9c9b36a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982457b038b2c90e4589c360e2bae3dcf7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98949b39d856e1bef832415e5b5cccd9d6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837f8766a61ba43a8abc73b287465bf91", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb535b402e58ba5fc52bb2394e4c186a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a8cc6d2df79f448da043f8fe6269c56", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982bae886bb11cd70b817cddb82da4b734", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a376c5697074b8808dc8cfb6d0d0beaf", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/FlutterWebView.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c2e16c92a6fb2971811699a7aaf17db6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9874fb3bfe3995d6961055a4c9d5b11d80", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0/darwin/webview_flutter_wkwebview.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c0307eddd2ad8017e826e582ecbe18a5", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98090559801bf3c8e410a42dddc145d08d", "path": "ResourceBundle-webview_flutter_wkwebview_privacy-webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98815663ec6ea94a91217222b54efebfd9", "path": "webview_flutter_wkwebview.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98774dbeffd0de51b4f89b73013ddc1b85", "path": "webview_flutter_wkwebview-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985fe37cfceeacc8a5e2bb1ee7a7b7a734", "path": "webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9898dc5510ed7d67046c7ddff573cd45c6", "path": "webview_flutter_wkwebview-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981778913df8cb916e52b8d22bc6b2dc9c", "path": "webview_flutter_wkwebview.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987238c73f0264a839debdffd36cf8227c", "path": "webview_flutter_wkwebview.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e03a8aa1ff9ff0f7634ed7e937ff6710", "name": "Support Files", "path": "../../../../Pods/Target Support Files/webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98924aa88ec13288e51f09ff9e527de68e", "name": "webview_flutter_wkwebview", "path": "../.symlinks/plugins/webview_flutter_wkwebview/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987cab0b30800b6e5caddcd7ee2e0c450c", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e989ab1b98dcaecdab67652de081536af71", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVFoundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98e2b71cf372e6e9ae47666e20b9325b75", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreTelephony.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98fea329deb62d97ea5f5a28100303f484", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/QuartzCore.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Security.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98b5f5979f0786f4a5ad9848f0349b1159", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/SystemConfiguration.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98d391d713992f1d4e99af11b30a3d8fcd", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fd8003707fea3bda0b0e61281546e941", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818a9377e250b9d121296a9203d9c1d90", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98619b6f580a9c99ff9fbafa8810cb9f96", "path": "CoreOnly/Sources/Firebase.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98091a3398aa02ad065e6465525acd0c16", "name": "CoreOnly", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985e93d1a0483a6fa96022fa1d410bb94f", "path": "Firebase.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98557ad16a3724c87f3efbed9d7bcd3df2", "path": "Firebase.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98943b2cdaf64add3c843a54c22e88ca3c", "name": "Support Files", "path": "../Target Support Files/Firebase", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa5934324acf5bfc0f8d1e9af9f1d7e3", "name": "Firebase", "path": "Firebase", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98631782c2685c7862de60809270208d62", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9846e5cafe601eafc4be23851cd01b3ace", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984027195107d43c1f071f9926545ac60f", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRApp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f0bb3d160be76547ed33bd4802f83cf", "path": "FirebaseCore/Sources/FIRApp.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd8f13df474218c88ec3926a7801153f", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b85c93da6106c4e5efa6a08876e63b5", "path": "FirebaseCore/Sources/FIRBundleUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980deb5c6b9d61d9bc69053e002710e566", "path": "FirebaseCore/Sources/FIRBundleUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98910e0ab3e8c0216881b6916491477742", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98825b2f5a9c9346f70459019feb7e4e41", "path": "FirebaseCore/Sources/FIRComponent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ac772056a5e808a24e79bd2471090756", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bf3908c334d74c661bb3050bdcaff238", "path": "FirebaseCore/Sources/FIRComponentContainer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea4726a42c24d20602c746f0408601ce", "path": "FirebaseCore/Sources/FIRComponentContainerInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e71b9fb191e566f9e7648dd4f03d5200", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7f9a33aa127eeec096e445888477476", "path": "FirebaseCore/Sources/FIRComponentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821340b9002c1e290fa16b029d0684118", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987fa3985877ba9b63b645e05649aaef60", "path": "FirebaseCore/Sources/FIRConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833dfe2589fd24db59d87ebf7dd6a175d", "path": "FirebaseCore/Sources/FIRConfigurationInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3f8a3234a22b50c631af3633175a080", "path": "FirebaseCore/Extension/FIRDependency.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988a378d20e89a371eaf844ab615b93e01", "path": "FirebaseCore/Sources/FIRDependency.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a65cfe2883f278571a61623a6233d3f", "path": "FirebaseCore/Sources/Public/FirebaseCore/FirebaseCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4b8cc806a24654d2003ce1679318754", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f95a722e98dfa311d872b8907571a678", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e42aad3556214fa29403152e733f444d", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d761f456882e46db71fe2666a258e2b3", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980274403461e17c58fb91aee732e2ce9a", "path": "FirebaseCore/Sources/FIRHeartbeatLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9862c36e4f06c614894c4b0448debd7a1f", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1259085296b9533943c06ce82cd88b8", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cccf834debc9f1c37b31327e609914c6", "path": "FirebaseCore/Sources/FIRLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b10deaa2af629cf571a4cddc3d476d67", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRLoggerLevel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c7345491ba85e9a568e3770d8ea9694c", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIROptions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9851a9dc6f361fac56038e05f07561e29b", "path": "FirebaseCore/Sources/FIROptions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe5707f24ecf36a9cc1f901066296997", "path": "FirebaseCore/Extension/FIROptionsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98173e8e6b49334e4b5adbf6ea881f9f54", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRVersion.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dba6997aa51055a6b44d568f572f3353", "path": "FirebaseCore/Sources/FIRVersion.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98225950272f7482cb04e12c77f40dc740", "path": "FirebaseCore/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9802f1f20a35ffe7745eb574f155831404", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98dae514e99dca7a1acfecb0a3f9cc1bcc", "path": "FirebaseCore.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f768c9d8000167e02e7b4b57bbcb8b36", "path": "FirebaseCore-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a806c96e217bf5d178d83385236fb069", "path": "FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a644b004340aee478a4b7b06be52982", "path": "FirebaseCore-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984a28309da1a8acb06bc64a001ab53bc1", "path": "FirebaseCore.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f29fca69aeeb124f2c6ac314a1a39ba5", "path": "FirebaseCore.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ee1826bed9b4a47f4dbc049e4761306f", "path": "ResourceBundle-FirebaseCore_Privacy-FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98270f4534a28172b2acb85fefd8939eca", "name": "Support Files", "path": "../Target Support Files/FirebaseCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea48a39cc566fa1ddb41bdb1a5d98db0", "name": "FirebaseCore", "path": "FirebaseCore", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981a07f362cfe2cfbe4b647ddb9859f00c", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983526ea6185d8a3d8a2e906b98a1e1fff", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989c136bf542c09e76c2e92edc4dc8bd74", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Heartbeat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9841410bd7355521b5531af09a774d294f", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98164149537905cea4387467a6b98162f1", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatLoggingTestUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9871d479fa4f265a610a957d981a4d47a4", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsBundle.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f047dab61b5bc0b2edaac698c8c16947", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98257e23c93d2d753db87bc9cd46235e25", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9855c6cc83357ac0056102f907dedf40e3", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/RingBuffer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9812262985f6665364b00d503ccdd045ed", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Storage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9884e4aa5970fc06008718b0e661e9f6ad", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/StorageFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fb0fd5c433e4a25d8065d072f26ee7e8", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/WeakContainer.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c4d6db300760a068d613fa52fd6f370d", "path": "FirebaseCore/Internal/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e45303ecfe00eb0fa1362fb75db13539", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a5c7f2b3cb02244aaa264ec8b8804a51", "path": "FirebaseCoreInternal.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c6374a40a1da37e4d5b9bb8f71bc2691", "path": "FirebaseCoreInternal-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ad746f7ea521c8fd3cb99b4fbe40a44e", "path": "FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a35b93ce543f2d7c97219ddf8eedffd", "path": "FirebaseCoreInternal-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ece0fefacdd71790a3ba22d884bbcc8", "path": "FirebaseCoreInternal-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98869b51186bc821cb6ac8ff727347ee4d", "path": "FirebaseCoreInternal.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980f362a276f4286a9d84b7a8df431f46c", "path": "FirebaseCoreInternal.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986e1393acce59b24d95488c2a7875af8e", "path": "ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985355b4b8ef2e0a19897aa6e472ed6477", "name": "Support Files", "path": "../Target Support Files/FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aaa2a395f3727a81bfd1c190431a84f2", "name": "FirebaseCoreInternal", "path": "FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989dcd9ef1f7ca17e341ed497b8690e671", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec1d6d5daf7d4c211cf680d0f4f58f54", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9f1c0c9254a2d0a21c07338d09ad423", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9812baf503e8cc0dceb71988bed761aae5", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9852825f934b8b078e03958fce4e48c3dc", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f3e48b51aaee67331cc0afec17993d6", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856b36060a40eec7ae1df313219442e13", "path": "FirebaseCore/Extension/FIRDependency.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef216bf4cb151e4df2fcf5591cea668c", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98acda295cb0ca3a24826926ffba6e7760", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FirebaseInstallations.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cb1be11f6cb0ceed71f7d02d99fc30fa", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f60b5bf858a503b42f8a0ad63a82bf7", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d996935b57df21ecda36778ebd04ebc", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallations.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d322201c40105521ddec10903dc527a0", "path": "FirebaseInstallations/Source/Library/FIRInstallations.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f01fc497de050bf7ff1cff7864e241b8", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e9466b5691d16bfd7c405daf9128ac36", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9818169e901e3ecd7111bd42f00d0689b3", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallationsAuthTokenResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d658686f809a6b6210746c5ccd52dc59", "path": "FirebaseInstallations/Source/Library/FIRInstallationsAuthTokenResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e9c1e9ff28f85ae979f1333b4d3f231", "path": "FirebaseInstallations/Source/Library/FIRInstallationsAuthTokenResultInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980288204af30b3a42d74b28cc9d033f8f", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsBackoffController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982a0d320decf712f026cb9fd3e479e1dc", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsBackoffController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1e4193456e4c867eb4aa0a0b49ef55e", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallationsErrors.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988edcf8c533938259f1c995cf89c5b36d", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsErrorUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98455331d0cdfe8b1df2e0459370d276c0", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsErrorUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c0bdc2099aa6a096ef8f3861f78fab6", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsHTTPError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987833507aedc23f1bad88944da037712e", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsHTTPError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98becf15ea8bcbb0dc794a62a220b49135", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsIDController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98473cbff1ccbadd61a51ee41bc0ebe27b", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsIDController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98639ef74c59c329047255764c8ab5627d", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987d7a0b537d7bb3246308b4911ba42bde", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a463ea5328f33b634a489c13bed7e086", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDTokenStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9895770072cee554a02c8f3b6a1be408f0", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDTokenStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9e2e7d4948e9b776b5725c9b7650435", "path": "FirebaseInstallations/Source/Library/FIRInstallationsItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832ab913b2f2096ae94c8b27abd447acc", "path": "FirebaseInstallations/Source/Library/FIRInstallationsItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6995843c0652854d73794f9cecbf78f", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsItem+RegisterInstallationAPI.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982eea806a021fffd0e0092a70c4e818e4", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsItem+RegisterInstallationAPI.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a6db0c6cb93c423dca46d2f320ee804", "path": "FirebaseInstallations/Source/Library/FIRInstallationsLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ae3f3602a65fc31052c6768a09a8ea7a", "path": "FirebaseInstallations/Source/Library/FIRInstallationsLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981040b13dcaa5125fdf0e4201b4cdada9", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsSingleOperationPromiseCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f8b90ef478cb64643d53e0a2c3825e97", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsSingleOperationPromiseCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4e2c6da99128a674224504b924acfa2", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsStatus.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e9cb0eec1686fb1b0b14cba23590271", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9813b6f81cac1c2f5b179aaf9a87acb9f6", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989d2b43597c5e775f1ee62a872a4c1af2", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredAuthToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9885683d2164def3b6325e6a18246f5f7f", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredAuthToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a0679c6eba29810d10ba20a0db35675f", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9816204c83d663b0e51f01f07dc6d10857", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d7584780407fa23f73d865859bcd55d4", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982addef68152165195072300fc602bd7a", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893c9141319efd42c7d0689a6a00a60c7", "path": "FirebaseCore/Extension/FIROptionsInternal.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e983787891f51e9ebc4ed7973d3d19f76a6", "path": "FirebaseInstallations/Source/Library/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9812649523fbc3d38cc90f2e64750e45f1", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9861b199f82b8b96d4e1a87a4f74c61080", "path": "FirebaseInstallations.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988a7451fc3fbd567288a6cb5a81950b9d", "path": "FirebaseInstallations-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989c049a07bfd93033878833db6096694c", "path": "FirebaseInstallations-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb14ff3da0c8f0ddb1e36d53c43b0516", "path": "FirebaseInstallations-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bef1ff8f621f6797583fd7a266cabaa9", "path": "FirebaseInstallations.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9860ac90c6fca353627e6619ba88f48e41", "path": "FirebaseInstallations.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9864dd6c0e62ee8bf022fba324524daf83", "path": "ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e9a35d35e628aa6ed1a41da7e7d24da6", "name": "Support Files", "path": "../Target Support Files/FirebaseInstallations", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988073713dd0df4d27a727b2062325cded", "name": "FirebaseInstallations", "path": "FirebaseInstallations", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe5bddc423ee9534b6a6c7c3661f6301", "path": "Interop/Analytics/Public/FIRAnalyticsInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987055ce46f1d1286bf25198c30d750236", "path": "Interop/Analytics/Public/FIRAnalyticsInteropListener.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988eda7b0b8b637173fd00614a39f85a2d", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9839b7dd5abf71c5ab5f161b9083af1e7e", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de51ffa78ec45d7efeb6728c98dd5f7e", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98624af64bbd1fd9ce6482ad543ec52328", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6f8f4e05ff7df7f628cb2af26b16b0d", "path": "FirebaseCore/Extension/FIRDependency.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987872cdaff08c122c1d69e73e5ef5b967", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982170c4dd3ec297e05e5930e65010a277", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1f8cf48f1edf442840921e76165ec9b", "path": "FirebaseMessaging/Sources/FirebaseMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee3421d29c3c4d5a863c3c03593af525", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FirebaseMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988cf6c899a8b3819f6b9f59c5341834b3", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9813ebfde6922d8bed336968b1aea0392d", "path": "Interop/Analytics/Public/FIRInteropEventNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980dafc746c80282d8740168d0a40c87b9", "path": "Interop/Analytics/Public/FIRInteropParameterNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e80e3b3cfe9c6a845dbc97657c870fed", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c597c915bff1fb9021ab9d9ddf70f6f5", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d779da698ae61b0bfb51544831e40e6f", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98388b3dc7d9cf708f26546481af8c93a6", "path": "FirebaseMessaging/Sources/FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b53e0e0f8a8af9008e67680cd208bd5", "path": "FirebaseMessaging/Sources/FIRMessaging_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ba9c9e95e9bec7c923642e49402265c", "path": "FirebaseMessaging/Sources/FIRMessagingAnalytics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98483fbe6c0f01dc37b81d066f695388a9", "path": "FirebaseMessaging/Sources/FIRMessagingAnalytics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985616bdaad4f2629e3694aec4770f6e18", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAPNSInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820d5deb02fc30a7f354e25b44479678b", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAPNSInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae1b798b0b93a8be09619a29b1fd5dda", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthKeychain.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dae1cb3d762831c0777fcfef1defd571", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthKeychain.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985109c4e07e01cc74329b6563e82934f3", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bab71710997b1e0af9395b616ad47805", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec29884dc0c613a4018e01bb9748f37a", "path": "FirebaseMessaging/Sources/Token/FIRMessagingBackupExcludedPlist.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9807ab112fbe424f267c7c88f25e93743a", "path": "FirebaseMessaging/Sources/Token/FIRMessagingBackupExcludedPlist.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d5943f774874ea5ad8da9dc8e37a44c8", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinPreferences.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b09d117792ce2b2483b31db437f660a2", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinPreferences.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bc3fd3a569ad974cdec11a9cf7c70d7c", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3addc9ab8b5d1a9c55718437de1e043", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9822b24d36ff0e814ad681cc941ec8f588", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9889b2f94453ac3934d1068d4586432301", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d27911c4e867e09cf32dd69048faa7c6", "path": "FirebaseMessaging/Sources/FIRMessagingCode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9832b38e4b5ac0ed6ccf5a6c6fb26e0ad2", "path": "FirebaseMessaging/Sources/FIRMessagingConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987830fc20cce0c661a98a934d8724ef64", "path": "FirebaseMessaging/Sources/FIRMessagingConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985bf0df905b5b9d014ad2a18c441938a2", "path": "FirebaseMessaging/Sources/FIRMessagingContextManagerService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c1d7975dc46b28876b5debac04ff675d", "path": "FirebaseMessaging/Sources/FIRMessagingContextManagerService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af9886221714421a6047cad21d0533e6", "path": "FirebaseMessaging/Sources/FIRMessagingDefines.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98661f3d3a7de36bb539913df5c49b78b8", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessagingExtensionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d1d86f418e7541024e504617c01be1df", "path": "FirebaseMessaging/Sources/FIRMessagingExtensionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98813d6542e08439ba5ba84ab84f0a9a59", "path": "FirebaseMessaging/Interop/FIRMessagingInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6f9de9418e92dfb616450337da795b2", "path": "FirebaseMessaging/Sources/Token/FIRMessagingKeychain.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b39ec0384cd79aee6c723b29340a1755", "path": "FirebaseMessaging/Sources/Token/FIRMessagingKeychain.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e0058c84f29c1ebff72a9a02eb626bc", "path": "FirebaseMessaging/Sources/FIRMessagingLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a0eb0d855218e9a8d348570870aa9cea", "path": "FirebaseMessaging/Sources/FIRMessagingLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9852c27066414e139680f3c9736d4707be", "path": "FirebaseMessaging/Sources/FIRMessagingPendingTopicsList.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fc43dca1b0dcc3bc86ff0509d27428b8", "path": "FirebaseMessaging/Sources/FIRMessagingPendingTopicsList.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988230664110fb5eff250bf609dd4e97bf", "path": "FirebaseMessaging/Sources/FIRMessagingPersistentSyncMessage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984fe6ece16ec3f915aa2af632c180ba30", "path": "FirebaseMessaging/Sources/FIRMessagingPersistentSyncMessage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98694f6f65b53787f986cbaeb5b163ec85", "path": "FirebaseMessaging/Sources/FIRMessagingPubSub.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fff31a4820aa9bfd6dd8fc375ddfd032", "path": "FirebaseMessaging/Sources/FIRMessagingPubSub.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c20b656a56cc92279d9beee4d4d925f0", "path": "FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ea54fde367d5dd6db74deedd8cff415c", "path": "FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988d609f54306e8d8bad3b193c342b54c2", "path": "FirebaseMessaging/Sources/FIRMessagingRmqManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c69f56854cc1f3566ea180ec89fe39b3", "path": "FirebaseMessaging/Sources/FIRMessagingRmqManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aecc4b6e085f5ddeab2f8c9f2a04c594", "path": "FirebaseMessaging/Sources/FIRMessagingSyncMessageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a7ef36c542b394f028ebbd6a19e51959", "path": "FirebaseMessaging/Sources/FIRMessagingSyncMessageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b7d0c0b3b96cef8898340bf776ea4b5d", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenDeleteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d8a32b991fc33c03da5da509aff419d4", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenDeleteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98906a5f38951e5147674b9a7e2833ee15", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenFetchOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb1190759729e8ab762e9125c1df8411", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenFetchOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984366985146e0ca21fef2a0948190c659", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb012bce79be418b1c5f2552831344cd", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f767bcee28cb4f1a41cdfcdd2f3f8252", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98580fa90b5f3305752ce2e35a5d0e8e8e", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825a0a135c572d20e39577c07cea399b6", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985efae14fd67fda1755c5b1f2cc284f1e", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a657dfb46aa004e14a38734dac601c73", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cf0ad6aaed66e46d971d2a0e5fec84f0", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809fa6ca8e59ef82daeb36d3b5994aa08", "path": "FirebaseMessaging/Sources/FIRMessagingTopicOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9811b69d377364bb630f2944c6278f686a", "path": "FirebaseMessaging/Sources/FIRMessagingTopicOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c757a2809139e75ecd1c8570134d849d", "path": "FirebaseMessaging/Sources/FIRMessagingTopicsCommon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ecc7e05968d275689b51f7036aeed24d", "path": "FirebaseMessaging/Sources/FIRMessagingUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9862f0456d65a237488355fcf83ad00826", "path": "FirebaseMessaging/Sources/FIRMessagingUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d0ff973ebce25750196c92251f09f1e7", "path": "FirebaseCore/Extension/FIROptionsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c78901cae404bfea5210214f9e88a634", "path": "FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9864ab88f164e5e36eddaa737214d1fcc3", "path": "FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f527b85e24aa80f3229bb89a218d3fe", "path": "FirebaseMessaging/Sources/NSDictionary+FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9836f9c24097bb54ee0353117af389028a", "path": "FirebaseMessaging/Sources/NSDictionary+FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984bcfdd8adf34b9a31889b8351db062e0", "path": "FirebaseMessaging/Sources/NSError+FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983153449e579f1948db1b9e14677180a2", "path": "FirebaseMessaging/Sources/NSError+FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e981247bf447d6678d0ec5195d0bc214453", "path": "FirebaseMessaging/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ad9b06d9a5db87b0ecf2e1c4a40ce1c1", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9809d5cf7e5a3d24c2fb855ddd8a1a602a", "path": "FirebaseMessaging.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980ee00532d79df158f0d142e2afc9813b", "path": "FirebaseMessaging-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983de90289874d68c4c7223b3ca759964b", "path": "FirebaseMessaging-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985cf2e12792e20c1dfc962e7fb408add6", "path": "FirebaseMessaging-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984f18b1e230b0195ed69ce22594bef844", "path": "FirebaseMessaging.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9895e428256ec1499087342c635a62fc7d", "path": "FirebaseMessaging.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9830f3b6b2c4748ca80cdc78069cc61906", "path": "ResourceBundle-FirebaseMessaging_Privacy-FirebaseMessaging-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9870c11d6c7bc7d92b2d2bae8f48c9f12c", "name": "Support Files", "path": "../Target Support Files/FirebaseMessaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d5eeda40aba2b487b9fd86e1f94aefe", "name": "FirebaseMessaging", "path": "FirebaseMessaging", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98543455e680ba4fbccd14c6fd15507f81", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/CartesianPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980c9904a73c75b77dd23675ba263c2334", "path": "Sources/GoogleMapsUtils/GeometryUtils/CLLocationCoordinate2D+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987fd63a2117bb8c6cf12a69d73b6428ff", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986f2a64de1d5201b68c4412d6584e8a32", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9823374335f01386794ff357f24be60c5f", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPath+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98df8467fe1d86a786a36f2154d3f8538e", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolygon+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dfd3252049220b4bc3bcc2cc8fa1d984", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolyline+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98924a04f2bec9d3a7d431badbfae413d3", "path": "Sources/GoogleMapsUtilsObjC/include/GMUCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874bb072ef741e89582ba677fbc24d10a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837047b9489899330799d37258b8cdf54", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9885ce6eb4356d662937b1410a80c19f23", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9514482e838d46400d33b64d3c8f7c3", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c8151c7f04c99e2132e55df16cc552cc", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987eb129e98d6c5b5a8e92e2d7c526ecdb", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b5e8ca506b29cd2253bb9ae10e99e3b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1983e208d738b0486dc50917831de60", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c252e0c8e6fea2155aa5724d4b89cca3", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c5c3aced12b64ba7de31ba93a821688d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e0e9ae932b1aa6285c3606fefa2e73b9", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9889917b8a2398bf825231f385fbe550bf", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98145425595d45abb50db77569b04df3ff", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6e0402095d621d84cefa6a3ae3b8d71", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98940e880d6a656f7824ba268b58b23cbd", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989af13f282d0bac9ba964ed20ad929e74", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fa8f33a0d82516d72828fc2ed7567dcb", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eebace50c44d02faa45b0dfe6b441f87", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd5f6b5f17783c056309c96656c6ade5", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c59fd1b1fb76dfcf2ee4074e998d7dd", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98461edcc6c018e1b1f95fb6db559257b8", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb4b54b99a2659bfb3254e7e9c4dea71", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984c5dde746bdd3c1148b7a778c9dc4cc1", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982b861bc82de647280725454d4a1eb884", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea22490d95ee0bd32df1db7da5731115", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98488e1816739f9f3a9ab2615158c416cb", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a4be5da2485940856a5a37f698983c9", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98772bc254b04cb57607e5e44063f7b4f9", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983bb4b401036c95314d8ce560246f7147", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9845490fca01a20a0194073a36ced38ed7", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98babdb077d5dc6394b9e3bfc2e3b2eaa9", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9858a70b3cd1797cf5a32158aca09c7ad9", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9831e965d8f8be4f1a6e432583a957dca3", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987180fa2e708506f70131771e656fdaa7", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9886ec19688befb478fc3ba42f8122d683", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980de6e25a456de0ba2b8f8de2816cf2d3", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b5b272e3d90fdb23191fd548f560d70", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d34abe5b66e30ce01e802b60e77e86f0", "path": "Sources/GoogleMapsUtilsObjC/include/GMUMarkerClustering.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f2a2e9b20d40230a37ba3e8126292aa", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f3ae7dcdf42c414a61824321bbac705", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9890eff781e73a0526d8f7e62abf15875a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f83d22572da24a49fd89b6d61123d544", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98290ecf9ed7b30bf8353c5312e3409fce", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98524578b66f238b32c62e5ed5487247f4", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a6a4a474c085ab036f535cc91e4aef1c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986a61c354934ea0cbfdb5d33078396fcc", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ceb192af5804215ce6361e948eba3373", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3afa1eec867093e6bdf47a91554d3f6", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed3889cbf5ada33dd0591626ba860b6a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981b5bbdd227c5ffb6e8e604ab5139389d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d2f418534bf6b5088435d1ad26c7a3ce", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987c0c6f2880f60b8e048a78bb358ea508", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a74cebe977009cc0352196bf7c2dc4c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984c939f4e004f3ce62849e4701bcefb70", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d5205ef810aa25b0ab2180473c68be5c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984e864966d6c7633d5aea62fc8fd1bd77", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856f458a3469f21a23a8a609e11b2ce00", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f23270499cd9f1bbfec0bb461ac81b37", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834619dd0763209d06c2d59349d134a9a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e10e590fd48133d7840af4bfe9d61abf", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838dff27683a507004906e7b85a3bb0d9", "path": "Sources/GoogleMapsUtilsObjC/include/GoogleMapsUtils-Bridging-Header.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0fb2aed0d84f8207077666453926626", "path": "Sources/GoogleMapsUtilsObjC/include/GQTBounds.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a49b8069b7e685641a42d37e19943bbc", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981cb5badccf55342711aa7b112ad661fa", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a0d40e13fad95463ef0b850aa8981fbd", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b2722dcb70550e18e43b6f4ef2d5ac8", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989b30d7ad8dcfbf26485acf3784577f76", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b78360b6846b2c826d92136c425c1efc", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987b12522221d30f950b4a9a0aaebc8d64", "path": "Sources/GoogleMapsUtils/Heatmap/HeatmapInterpolationPoints.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987a4576dabc5f4f3d43db56798cdde3bf", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/LatLngRadians.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e0954ef76fba037df4aa7a07f1ec0710", "path": "Sources/GoogleMapsUtils/GeometryUtils/MapPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c29a037b0e22003f33f5512b120b6cef", "path": "Sources/GoogleMapsUtils/GeometryUtils/Math.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98536d245223c3fce5819cd35607ce2296", "path": "Sources/GoogleMapsUtils/Helper/MockMapView.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e982f2b51657469d1e529f7b71eb1de6ad5", "path": "Google-Maps-iOS-Utils.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98249aec636ca402456f4f77589e1c98a9", "path": "Google-Maps-iOS-Utils-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d349db594a6ffbd2fba20a7770fcc39e", "path": "Google-Maps-iOS-Utils-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985976a84d45d4ce3760cda40ea01c9f09", "path": "Google-Maps-iOS-Utils-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cbb19208e815303fbd403eabaecd6a69", "path": "Google-Maps-iOS-Utils-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98794c39fb86a289985589c9d25a614413", "path": "Google-Maps-iOS-Utils.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9821bd096f90d4573664d72eaa7eaa4b3d", "path": "Google-Maps-iOS-Utils.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9802a837d483a3b08ea894ef2215b7a2ac", "name": "Support Files", "path": "../Target Support Files/Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d7d1030bde6db5802f5663dc0ce5f400", "name": "Google-Maps-iOS-Utils", "path": "Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9835f2f7c56fc18801d769c62d5ef25836", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fbfcefef94362e5cbbe9abed634918c8", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9822d6626736ab6afea67d13292f8fe263", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cbd223958942c9a234fe42c2d6f9f6f9", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a39bdc6ba0938f3fa7ea211a7c975d8c", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a57d3ff5f59f6d4f97a2518ee9041753", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988cb3b090f1932c7e6e063dd616925c7c", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981076a71f21c4b830a467b3669d8e11cb", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a21edfd4da8e5b0242e0707686d1f950", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989835db8ffc5fcc140ed4af4b0575128b", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883870f3f9a66fbeab8629ae266c2c79f", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTCompressionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981725c743cc1f1daef620c7eaa93dfdaa", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTCompressionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f5a699597a6913c2664ad85c5c74df3f", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTNanopbHelpers.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9898c10d45fcc1febbad4e3da17a10788a", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTNanopbHelpers.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987deaa28bbf81fe28a6623a4f93fac204", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980d1a1548c815a5cbbfbe078058083774", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894203b844dabf7dad36dbd44c47ee38b", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploadOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986218912d52940334c1059f96608e41eb", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploadOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa45beb3c4a78608e7595e9a2c0157ed", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORAssert.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e64d8e5c3db05796f11d853af85aece7", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORAssert.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c08b8ea1480e112a20c3dbd804956df4", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORClock.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98215d714fbb01aee32957d7d5529f4e57", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORClock.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983900d5b56a513006733015b3f76f607c", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORConsoleLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98299c2446d6d54210ac1e00437e63caab", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORConsoleLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9846c8654cfb117054512012de07c6a3b5", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORDirectorySizeTracker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f91142bba39a96b272ac35aa9422742b", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORDirectorySizeTracker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd35b78e8291893986d1b7f4aa8a456c", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREndpoints.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d9f268f33b47cbcc13dbe75a2d866989", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREndpoints.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9846ba7317a281981aae4841e16a5bb12f", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREndpoints_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1da57897ae5f3f2cceaa92235078a90", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREvent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9830da2a9ac5ca56b5ad38100ab0911ef3", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREvent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984363bdee7bff3e33e99a53e1192b9d41", "path": "GoogleDataTransport/GDTCCTLibrary/Public/GDTCOREvent+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9846d6796e0db97975ed1b4677810a6555", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b42ebb982399de34037671a192bf5ba6", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCOREvent+GDTMetricsSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b25911574a0f58ad691b4084b844dda4", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTMetricsSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982cd1e99d2cf711aecbbaf7f716c26fa0", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREvent_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d69e8307cfdcec586fc3a398c00b3805", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventDataObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ba790dc9fc5999d4ab59203fa810266", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCOREventDropReason.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f2e5d2e12c6ff198e3feee2570c7c4b", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9899afa68faa70db1f65bd775e1b93138c", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c51c1c3b9f01737c9c8ca16cbfd36ae3", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984f7f97975b989c717a7e4331f121138a", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage+Promises.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98733c5535735e502ee5f25d05f124af9c", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage+Promises.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a45e2b0f647602f995f5fcca3141f5b0", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORLifecycle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989d304d675b77afdfca2a9a51f2312357", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLifecycle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9807d28fb6f28705a6006a1ebeaab206ca", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORLogSourceMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98910b07c17a34362f5ec492a976efa88d", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLogSourceMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae5be066c2c4bfaa25e5fbabd37431ae", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9860286e1ae31eef039cc0a09217eb4d8f", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eb94acc9908149a8d614ac6058f44d82", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCORMetrics+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9812ad391fad877f6a385effda329661d3", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCORMetrics+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb6c4d79b460de9b695ef9982fce6648", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4b60e465d397bff39a9131fb99c46e1", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ebb045cceb1a869002a45dd334adc8c", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORMetricsControllerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa5ad6c70dfafc271876d54bdc2c3891", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9869be0a5a64337aba0dbc8a40bee37727", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed0bdd7b5e54c1fc38b8d5172c8604fc", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORPlatform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c796225b8cd622a93c00c3d0dcbafed9", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORPlatform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987780e2cc946cf5a92330104bfe69bcef", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORProductData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9844c7c00c7ea42b22e83e74b8283a26cc", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORProductData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ae2c2d1409e109b02436bb7c58661a9", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORReachability.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981aa8170d491ea2e3b283fb7b354f7df9", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORReachability.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fcc7c2103275cb2726c562799c709af7", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORReachability_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805a8c25ca48f58d822e8759fedb49cf0", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORRegistrar.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984713bf6b3aacc313e9e84ad668688a4b", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORRegistrar.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981462a81f7a948fb1b52c6e8a4e0bd6ad", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORRegistrar_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851e767d60df2a58e4858d7225392ffa1", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageEventSelector.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832965f7e08bc5ef5e656ef87e63b8082", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageEventSelector.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834ba3483ab7ba71478537837f47c5e0c", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORStorageMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cbd56675169247f6bc2a07eba8f12b19", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9841164a85934366b14eb47b36786e51f1", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980337f6c85ff836b8cf0177f8eb4afbe3", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageSizeBytes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980637c52e3d15dddce3bcd7c1459d01e3", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTargets.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bdfdcfc0ba83735a4f71cb2b92ef39e5", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98946ae94fd6f6b4cfac6a9b76aa61a1cb", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9898748825af255175ca093a6f60e53a96", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0cc9aab2cf24194717a84f0e0cc7e2b", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTransport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985fa3fc7ff54602b2bd4a645547016b5c", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cb483390c84d187c09c656e9e49ee46c", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransport_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b43fc598c2e49f51f6e4ff8a6237f2e7", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadBatch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989edff732533c649d969eb470b200ec55", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadBatch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98693fb971dceca843d56c5af2392b34da", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadCoordinator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981f581ca174aed63fcbf433341d6b2748", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadCoordinator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9801b798bdcdd8dfa0b78df68e135b74c0", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825d94ff96d9a571d5522a6df42652de2", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GoogleDataTransport.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a121f40cd5d9ebac4266151bad6c4be5", "path": "GoogleDataTransport/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984005b59521439a1356a5c2f603019011", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9834d20d683a4896da4525d09b4cc54d5f", "path": "GoogleDataTransport.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980cd89af91f8bbf48fd012c698930b93b", "path": "GoogleDataTransport-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9839a76a7a6b95314b7990b50aba875826", "path": "GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848010088c6cec798e7bd0685626d09c8", "path": "GoogleDataTransport-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c1755e906ea5c219fd719ca3ce78af00", "path": "GoogleDataTransport.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98526e8bbebba1f084823f8db008a08c71", "path": "GoogleDataTransport.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988fc714c1e35673499b3126c061ed5efe", "path": "ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a2a3c3b24af5f4970a34a164426644cc", "name": "Support Files", "path": "../Target Support Files/GoogleDataTransport", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980cc9ed045ff69365a02a3d706eae46c7", "name": "GoogleDataTransport", "path": "GoogleDataTransport", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98421480cc482e6182ef724ba72b5bb0c7", "path": "Base/Sources/GMSEmpty.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98708149d38e363be48ff6ea7d39533a61", "path": "Base/Frameworks/GoogleMapsBase.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987182609c5b860fc554b04f8747109036", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853fe713aeb72b475247e18811be83e8c", "name": "Base", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee0cfe4969cff87d58b18385796775e0", "path": "Maps/Sources/GMSEmpty.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e9844a486d39719861c17c0c69fd771a2fc", "path": "Maps/Frameworks/GoogleMaps.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98b0180a8a7a8a2e33246fdc147f41bb70", "path": "Maps/Frameworks/GoogleMapsCore.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984b318dabd3c0eb1dc5cc5fec7ffc2d87", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "wrapper.plug-in", "guid": "bfdfe7dc352907fc980b868725387e987857ef64f44230185a8470a4dd5bd67f", "path": "Maps/Resources/GoogleMapsResources/GoogleMaps.bundle", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985df46263da2c93cdc5e839d08cb290ee", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d78363a8e0e9435a26897af3f2c105f", "name": "Maps", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98dcfa6b5e97f43ebbb2b3829d2f6646a5", "path": "GoogleMaps-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984c2f18a68574f0378d9d37c300a7bfda", "path": "GoogleMaps.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9897873d3cf3aac8235a8c4edc865eadd5", "path": "GoogleMaps.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9897633664ab26230e4402cfe2f9b83bc7", "path": "ResourceBundle-GoogleMapsResources-GoogleMaps-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980dfcae6a2dfd98d548986b3a14e8913e", "name": "Support Files", "path": "../Target Support Files/GoogleMaps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ffdd01290ba8a7de9d24029ce9627ea", "name": "GoogleMaps", "path": "GoogleMaps", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98760cb230a3cf57a7082714639c43ce03", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULAppDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986f0011b5ea6d0541d41f6dddbc7f4224", "path": "GoogleUtilities/AppDelegateSwizzler/GULAppDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea07a5bbd0b31e8dd998ae55d3252f32", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULAppDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ebaa6d19a05df9c2e7f86227676cbc6f", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULApplication.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f75160d602fb8d0816819395aca0787", "path": "GoogleUtilities/Common/GULLoggerCodes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98553c50b5f8d3e0097422b51cf20ea8f6", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULSceneDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98475eeaff1868cb57c60ee2aa72452292", "path": "GoogleUtilities/AppDelegateSwizzler/GULSceneDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c20d42f4c3ed47324dd8b9778068778c", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULSceneDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9831c65cf7e4632ba6827f96bd4edc705d", "name": "AppDelegateSwizzler", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849b6247f1c050a844d7ffe060fddcbcf", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULAppEnvironmentUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9824dd4ebe1065262d6f09fed515c33aad", "path": "GoogleUtilities/Environment/GULAppEnvironmentUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dee171a683e4cac5145cce25aa651849", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULHeartbeatDateStorable.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dbd2e8c6dfe89fea43157dce2f80c055", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULHeartbeatDateStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984b251eb6aea0f8a7ec670542115c89a3", "path": "GoogleUtilities/Environment/GULHeartbeatDateStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c84bccc34dad8a5b67ca0f8bbe1e948", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULHeartbeatDateStorageUserDefaults.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987160831d1fb8d6a24f3fbedfdfcb085c", "path": "GoogleUtilities/Environment/GULHeartbeatDateStorageUserDefaults.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819ad8d7637233e6d7381477f3ab6f1cf", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843cf6fc669b912641fd0cf9fa17359d6", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98faa28aecc1ac56fe56c2f1bf826ff649", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9865adcb956c9fd7f669493eeb9c7c1556", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ac158d492af72365c335d59bf7f32dbb", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULNetworkInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9889dec225fd715eef87c7fa9e05e08dca", "path": "GoogleUtilities/Environment/NetworkInfo/GULNetworkInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98745b6956af406557803e48ecedea8e13", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULSecureCoding.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985761c9abe1bf1b578c48095bce5870c1", "path": "GoogleUtilities/Environment/GULSecureCoding.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b94e83c53af6fe802c168b81ad2b013d", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULURLSessionDataResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9826bc559549551dcb67d8e4da709bf674", "path": "GoogleUtilities/Environment/URLSessionPromiseWrapper/GULURLSessionDataResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983459a2d8acac5e8a9d0a4d44bee2f8d1", "path": "third_party/IsAppEncrypted/Public/IsAppEncrypted.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9899a2ac890362342c1f9b71bb4fb33036", "path": "third_party/IsAppEncrypted/IsAppEncrypted.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fdc3845294c94233e0a16989df8bc5c8", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/NSURLSession+GULPromises.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bec20e5c978990d3543676ef2e9ea3be", "path": "GoogleUtilities/Environment/URLSessionPromiseWrapper/NSURLSession+GULPromises.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ac32c424a0f1b859b00e139e5aa3281b", "name": "Environment", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989453ae1133511b72ffeaeecb54ee387b", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985f39aadb9469f8a2667afdedfb68553e", "path": "GoogleUtilities/Logger/GULLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b91221b054676074a6c30d6b28c55024", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLoggerLevel.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987dcf46f614f74b02c1b8e4051c362a52", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a274b15d1965a41f102f6f978ffdaae0", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULMutableDictionary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981595700ae078e263c9148e93f09abb4f", "path": "GoogleUtilities/Network/GULMutableDictionary.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c0f968be3d20a21e19a1735bfd35d6de", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetwork.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982fea3fdfb4fc4fcce77216db85b7df37", "path": "GoogleUtilities/Network/GULNetwork.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98efdc2413f12fd7ceffb06d644fbef6f0", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9826287e09b2156acb425d9970993535f9", "path": "GoogleUtilities/Network/GULNetworkConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f0b4dbfe1278a1309f5a26ed91be425", "path": "GoogleUtilities/Network/GULNetworkInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98129378f477d92927996ffbac36b7e576", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkLoggerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da07f5699f471e6ae9c391860cb16384", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkMessageCode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874d1ef6411a9a44ba1f9f5fe9f9a2c38", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkURLSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98420d7485553b2f64ce4628f5ff222496", "path": "GoogleUtilities/Network/GULNetworkURLSession.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9848d270acc550a584171e0c27dad5049f", "name": "Network", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983be2a2683f8307e67493735de6f7a744", "path": "GoogleUtilities/NSData+zlib/Public/GoogleUtilities/GULNSData+zlib.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9844d44ba10f7ed9ab4a42c7ca5baacbee", "path": "GoogleUtilities/NSData+zlib/GULNSData+zlib.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982e44369e88de48c71871255806004d4c", "name": "NSData+zlib", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9884e054f6bfc245b5b0248c2fa2d44f9d", "path": "GoogleUtilities/Privacy/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981d769a50fc6a8e3198e7eb936ebe8d81", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9810efacdc7fc2d8f2fe3e47abf4a20c71", "name": "Privacy", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980b7c0dffcf17b552e664169f9cd7a88d", "path": "GoogleUtilities/Reachability/Public/GoogleUtilities/GULReachabilityChecker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989f84398a429a22db13816f6cea3f5502", "path": "GoogleUtilities/Reachability/GULReachabilityChecker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985320588decfd72ce2f45c3893b738217", "path": "GoogleUtilities/Reachability/GULReachabilityChecker+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98313c5334a8093487d329c0d5518f6f3b", "path": "GoogleUtilities/Reachability/GULReachabilityMessageCode.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ea0b2dedca6840c23546fba92f65f876", "name": "Reachability", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e982165b0cf4e7e52aefe1c862b7738d706", "path": "GoogleUtilities.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9807708e1e877b4debfbd737d55dd0e9ab", "path": "GoogleUtilities-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980a032752b839e285ef5e7023103c8161", "path": "GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987eae91cf8bcd23089c877465146a6409", "path": "GoogleUtilities-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9838402d31f902c4695916b350e1da79ff", "path": "GoogleUtilities.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989d777f0ba5487392c39e2e1a4ff04b31", "path": "GoogleUtilities.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98930eef82baa9a56b0dc3899376e631b4", "path": "ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989fa5f541bdd4727f9addddce4f5c8cb5", "name": "Support Files", "path": "../Target Support Files/GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98800d9ee126ed28f24745af7d5d42c4a3", "path": "GoogleUtilities/UserDefaults/Public/GoogleUtilities/GULUserDefaults.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982f8cc68cec3f51960f705e08293547a9", "path": "GoogleUtilities/UserDefaults/GULUserDefaults.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987a9f8689c5ecb1eab749809fd333f45c", "name": "UserDefaults", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee4e9a65e92ffa3a3b27ef75b9c0dc35", "name": "GoogleUtilities", "path": "GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9812ab6c06ab61201ac346158b1ee3d0e1", "path": "Classes/ios/Scanners/MTBBarcodeScanner.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c0942423526a3693a47319c6f81d38c7", "path": "Classes/ios/Scanners/MTBBarcodeScanner.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d47c95c5f7b4a6532e22fc608b6468a6", "path": "MTBBarcodeScanner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815b23045a2dda1919104ac0383205727", "path": "MTBBarcodeScanner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bf82ac23089e975615da0107e6d89361", "path": "MTBBarcodeScanner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d003d715cc19e7ecfd2abbc4a5289c7b", "path": "MTBBarcodeScanner-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f40093661497cbce085513cd7a460d7b", "path": "MTBBarcodeScanner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986a5ce49dff5b4c18707e335cc7766d78", "path": "MTBBarcodeScanner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982aa47b9370de66939e28dfeb74f1aebf", "path": "MTBBarcodeScanner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98145d03f1d6c049324950c7a312c1a4fb", "name": "Support Files", "path": "../Target Support Files/MTBBarcodeScanner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820d5a9f57c059cf98be6ffd61c43c410", "name": "MTBBarcodeScanner", "path": "MTBBarcodeScanner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b59ec00d689b112bd56132dda616b3d4", "path": "pb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9881b74aa1ac6faadbcc221bbeffaa8863", "path": "pb_common.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd4f98e742a38ce8d50a3fefcacbd660", "path": "pb_common.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ef4ee0fb1413807476f8d592558fb314", "path": "pb_decode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98650c651e84e1a1b053b5a91901c7c1c8", "path": "pb_decode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986c83c42731b9e0da76fd3a171b2368ce", "path": "pb_encode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98867b31a01a462471f374b069b26cc941", "path": "pb_encode.h", "sourceTree": "<group>", "type": "file"}, {"guid": "bfdfe7dc352907fc980b868725387e981db4227142c5d81e4abcc561a11bed15", "name": "decode", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98f45d6efbe4b72a2efc74a9d7f37c4c09", "name": "encode", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e983ee244c3b6c98aeca05d47f3bce09dd7", "path": "spm_resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980945d45974bfc02b5df0ce3f2a38d021", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984fcf808003bb51ff7041d5564e2ce787", "path": "nanopb.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ccd8936717f8f85b789f84c199f6c626", "path": "nanopb-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b84e8366611a9d600bc32bfdf99db9fd", "path": "nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a1a764a140117db40f20d1f16b7b0a8", "path": "nanopb-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9832a42c6d09ce7381000be3cdf5b565e7", "path": "nanopb-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9828b057d64744815a2230ab8d8467b6de", "path": "nanopb.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980e90648bfb1e06cec88376b168026148", "path": "nanopb.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e2fc9822d790b3b2416f34c641c9d8a7", "path": "ResourceBundle-nanopb_Privacy-nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bceff5073ba859d7e954262250a7d882", "name": "Support Files", "path": "../Target Support Files/nanopb", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835869546d55d648306a1d901db5ba4fd", "name": "nanopb", "path": "nanopb", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983bca6a42889b369121ecac7d7cf659c3", "path": "Sources/FBLPromises/include/FBLPromise.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984aac0320dc402cf49a50e12343c5b2ff", "path": "Sources/FBLPromises/FBLPromise.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9839a6399b4b06c844be472b79129e402c", "path": "Sources/FBLPromises/include/FBLPromise+All.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989077a7885ee413490ef7cf46e8b11e5f", "path": "Sources/FBLPromises/FBLPromise+All.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9815d6fba893720a0e1335d605920a6190", "path": "Sources/FBLPromises/include/FBLPromise+Always.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98babb958d0a05ba280609f6d9772810fd", "path": "Sources/FBLPromises/FBLPromise+Always.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e6391b64f2b15bc64ca0ecb31ef8dc4", "path": "Sources/FBLPromises/include/FBLPromise+Any.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9811ac78869a79e7b85f2ecf6db5ce698e", "path": "Sources/FBLPromises/FBLPromise+Any.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b99454e6704caa15e1b6eeada620368", "path": "Sources/FBLPromises/include/FBLPromise+Async.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804a7e418268858a0f1efbef1ba1cf23b", "path": "Sources/FBLPromises/FBLPromise+Async.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d7ce6a5abe7f3a59282baf0e1c35b6cc", "path": "Sources/FBLPromises/include/FBLPromise+Await.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984301a8626335180ec878dd3b0fde82ba", "path": "Sources/FBLPromises/FBLPromise+Await.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d7535df2bc389cf1d5c384ae074c69ef", "path": "Sources/FBLPromises/include/FBLPromise+Catch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c54bb7c143e57fc103b362b55857fe2a", "path": "Sources/FBLPromises/FBLPromise+Catch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb3fd641186b91fcb1e5266552d046f2", "path": "Sources/FBLPromises/include/FBLPromise+Delay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980a1746545036417a8b2fddaed8990a89", "path": "Sources/FBLPromises/FBLPromise+Delay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad17aee0b71dfa278d0dd9b3b0db6f21", "path": "Sources/FBLPromises/include/FBLPromise+Do.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832b34dd402cd167c11df069a4996d1f0", "path": "Sources/FBLPromises/FBLPromise+Do.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f719bceb2dba101185f701914673568", "path": "Sources/FBLPromises/include/FBLPromise+Race.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9856c2c3d77ecb6ea8ad5e1d0fb8633741", "path": "Sources/FBLPromises/FBLPromise+Race.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9878b5c50c1614ac3942e8ad477afb559a", "path": "Sources/FBLPromises/include/FBLPromise+Recover.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b64e247dd3350bad79c59af9d601b4ab", "path": "Sources/FBLPromises/FBLPromise+Recover.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98465ef542d8b39e4c5e58333491af93be", "path": "Sources/FBLPromises/include/FBLPromise+Reduce.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a07c8e30f1f191d48888d7628fdbc678", "path": "Sources/FBLPromises/FBLPromise+Reduce.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893ea2d3c9522db9e8d12fa7e743262cc", "path": "Sources/FBLPromises/include/FBLPromise+Retry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9838dc9c6f44bde185c1c6d55db74f8aef", "path": "Sources/FBLPromises/FBLPromise+Retry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98defed71a42be925a4f633b2fa6cb26c7", "path": "Sources/FBLPromises/include/FBLPromise+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986b5c23870953d3f1893f05bac005dd14", "path": "Sources/FBLPromises/FBLPromise+Testing.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d2ac5675680fa3c568173c9e1b1bf429", "path": "Sources/FBLPromises/include/FBLPromise+Then.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c4cf85af3a68292f5200a2f5b06f859f", "path": "Sources/FBLPromises/FBLPromise+Then.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ca198be0ae48defeb472aabda9730ba", "path": "Sources/FBLPromises/include/FBLPromise+Timeout.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982ed653041f5428d38ba61ea11d3250db", "path": "Sources/FBLPromises/FBLPromise+Timeout.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f1ebd0365e193f5b7b7305c98f7e32ff", "path": "Sources/FBLPromises/include/FBLPromise+Validate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f86761ff5818f6075451f936e938a73", "path": "Sources/FBLPromises/FBLPromise+Validate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987201d7bff39055b9ba010cafeb8fa0db", "path": "Sources/FBLPromises/include/FBLPromise+Wrap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987d652d41063b66269150fd3dc47722b0", "path": "Sources/FBLPromises/FBLPromise+Wrap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be978942147007b2b7b69894a77d8e40", "path": "Sources/FBLPromises/include/FBLPromiseError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ba10b4e94817a514f781dba73efaf556", "path": "Sources/FBLPromises/FBLPromiseError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af274d3bce5511363abdbe711151fe9b", "path": "Sources/FBLPromises/include/FBLPromisePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ff3390308389f5b863346636a84e74f8", "path": "Sources/FBLPromises/include/FBLPromises.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e980ca36a48a1e14e27b5557e5c8be8ed51", "path": "Sources/FBLPromises/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984e0ca96225d4e04fc36e1e3de49a4d2a", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9810c399674e3efed54f8f5ae67ef1e9ce", "path": "PromisesObjC.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3377e5d50146da59117ac6d1b41673e", "path": "PromisesObjC-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fba6245ab590cdc87d7d1096b9cc3b7d", "path": "PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98497b8fabdbad9d3f7e2b359bb60ceb82", "path": "PromisesObjC-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d261391d15b566eea2a4d92726a7543e", "path": "PromisesObjC.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988788409978b3b999e5b491a287b7eff9", "path": "PromisesObjC.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9804c1aa85cf4f35badc9c63b0212c2992", "path": "ResourceBundle-FBLPromises_Privacy-PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dcffd98b101d1db46c1008191ebf5bf5", "name": "Support Files", "path": "../Target Support Files/PromisesObjC", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883e8358aa2bd8c3790d7efc9c3cb4a71", "name": "PromisesObjC", "path": "PromisesObjC", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ec65a93163c509183abc71c8d93a54d", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e9881a00cf5ab5358ebb737272eac90fa84", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/Code/Android/sale-management-mobile/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/Code/Android/sale-management-mobile/ios/Pods", "targets": ["TARGET@v11_hash=b27acd691d729b50577187fbd8d9171e", "TARGET@v11_hash=b0f6b265ad6903b17bfe6390feba5f71", "TARGET@v11_hash=64cdabc04a6d8e82b3c4b4d1c8cc1b16", "TARGET@v11_hash=acc541b3eae4d3213981016bc09ed1b6", "TARGET@v11_hash=55cf9762d48ce9aa9857253dc0357659", "TARGET@v11_hash=32140ee3617ac2e090e94db83b5270bc", "TARGET@v11_hash=1acc58d01a66713c23132b70f701aaf1", "TARGET@v11_hash=931b32d13c01fb3a03d5ad4487488d20", "TARGET@v11_hash=93117fa1c9269056ff1200496f1c265d", "TARGET@v11_hash=766a0bc8bff23eeffdb423e781131b65", "TARGET@v11_hash=fb3512919ac48a4bc42f6f9859a9d91a", "TARGET@v11_hash=db9260c613d559c11e71ce89c74cde97", "TARGET@v11_hash=277397195e384dc7bf8eccb473b7d9a3", "TARGET@v11_hash=4560795515cb1939805962381a9b7f03", "TARGET@v11_hash=a36aaf71e708cd7ed2399bbb22cc0133", "TARGET@v11_hash=91b77b44fd6775b1da06b6903d4cec80", "TARGET@v11_hash=fbbb0b7d9b5e81b316dcf68e0833b53f", "TARGET@v11_hash=22098b642df161924bac7acd14088f5d", "TARGET@v11_hash=ef4446bc54d69f3630605d159f1845d4", "TARGET@v11_hash=f978ad033da5e5b65c678ae6c9eb4b94", "TARGET@v11_hash=939122908ba0ac7c20a073b23b622ed0", "TARGET@v11_hash=446559e7baa9c7570231151e66ad1161", "TARGET@v11_hash=b4eae1ade2c2aa8202daad3fbedaae35", "TARGET@v11_hash=005a6e665f16b021dea865b4fe3677f8", "TARGET@v11_hash=b8d6352d4a6bc5441db5d43cbd7f4f0e", "TARGET@v11_hash=9aa96fb22bc2fd5bd5501b5cfe859ddb", "TARGET@v11_hash=f51ebdc28247cf03cfaf549a255b2714", "TARGET@v11_hash=e625b6bb3010996fd1d8a63ae8e18c1f", "TARGET@v11_hash=ac22dd13d5a9d4f9e3169dec838db54b", "TARGET@v11_hash=f70e80c895f91fe5bee94a122f7b12dd", "TARGET@v11_hash=0a1eb5772a025035776499bc9f671950", "TARGET@v11_hash=9225c5bae28fa5f3f1cc6b2552f24b39", "TARGET@v11_hash=9bbcadcf5e5e430e6bbbabf016bc039c", "TARGET@v11_hash=c3707f040e011caa8b027f99ba0239f3", "TARGET@v11_hash=390bc84179ed9c84001af1e7257ccffc", "TARGET@v11_hash=5a92be8a6f45654186c9388d380aa2da", "TARGET@v11_hash=c7221b2984a8a661a7d60d1fcd4aed91", "TARGET@v11_hash=96c0f0154b651c187b1d1ba694f574dc", "TARGET@v11_hash=33690019b7d256d188a77f59deda8c76", "TARGET@v11_hash=33b55642c792f8cfea693de3c7b314b1", "TARGET@v11_hash=cf869a65386b57902c0f10b198c2831f", "TARGET@v11_hash=70922431dcb8c05ce28357f9c11dabe0", "TARGET@v11_hash=19eac5f0d7bd25feec0d8bbc71d64f9b", "TARGET@v11_hash=3484debf56826bf690dfaa0eb085c83f", "TARGET@v11_hash=042a72b4ac6b311fe3fa0a90678c761b", "TARGET@v11_hash=309210601c6a30f811d4501d0fa49d5a", "TARGET@v11_hash=53e567570d3be1297073cc5bf1ed79a9", "TARGET@v11_hash=c80072331a5ed82ff2473111c92d480a", "TARGET@v11_hash=fcaccd8584dd935982f6ab178d476530", "TARGET@v11_hash=6c41b5188128431410c335ecd0513a50", "TARGET@v11_hash=dfa8bc2d3cd032341d4798007b47269e", "TARGET@v11_hash=2bdca346aa2d2a9f188ab9364e30d6d4", "TARGET@v11_hash=b2a54cb5f315224fcffe824ebb324a18", "TARGET@v11_hash=293f0a13bdb850b034fb3d127a6cd8a5", "TARGET@v11_hash=15693726607b1d571b5fab5788e3aa50", "TARGET@v11_hash=ba3f6b226529665aafc333c086c7d520", "TARGET@v11_hash=fe1dc90ee5fd008065aa2ccbb5d273b7", "TARGET@v11_hash=9207696123ff303a2776c26bf19a8dd4", "TARGET@v11_hash=c6f57400139d630b06d8e46d41fa70c0", "TARGET@v11_hash=b702cca64fa200ab918f8c2e11016899", "TARGET@v11_hash=e25bd1a6299213166b0acd37e02dce22", "TARGET@v11_hash=5525d3fd977c16141128cef234065636", "TARGET@v11_hash=88180c081cdda119e41d02d27bfe5e35"]}