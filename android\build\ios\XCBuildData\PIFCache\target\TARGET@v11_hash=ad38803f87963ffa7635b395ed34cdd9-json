{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9804174cd53abe940478b7286ec393bdfa", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/fl_location/fl_location-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/fl_location/fl_location-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/fl_location/fl_location.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "fl_location", "PRODUCT_NAME": "fl_location", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9853a795819367ab11de7cb7137618f463", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d1bc099ff7645f90d3b3d59c7065f998", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/fl_location/fl_location-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/fl_location/fl_location-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/fl_location/fl_location.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "fl_location", "PRODUCT_NAME": "fl_location", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9852039e1f36aee8865897eeec2e954517", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d1bc099ff7645f90d3b3d59c7065f998", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/fl_location/fl_location-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/fl_location/fl_location-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/fl_location/fl_location.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "fl_location", "PRODUCT_NAME": "fl_location", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98049e4eb9eb935db6c7bbaf9de97542a7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983be0a7a0e8a6b30140fe6762cd4ec6ff", "guid": "bfdfe7dc352907fc980b868725387e981618ca6dc052d290ff508e78f887781f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a8aa6ca0474825d3bb57e25dd0e91e3", "guid": "bfdfe7dc352907fc980b868725387e982fab8c14f1e30b11294fbfac74d9c988", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e5d1c4a04f6895946100545597008a6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d2ee02e7d0a04c1dc5cba927fdb27593", "guid": "bfdfe7dc352907fc980b868725387e98df68e79ca14095c8e1009b63638097fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af159f8bf43988731de35a2af66c817d", "guid": "bfdfe7dc352907fc980b868725387e98bb392d8ce1c55578ea5fa0c6a0b40739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fff01d671b9822a8c9ae6a6aa4a63e2d", "guid": "bfdfe7dc352907fc980b868725387e983d9656af3b20bd98df92abb4f4c40275"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840fa2a5905e969605245c4818058b37f", "guid": "bfdfe7dc352907fc980b868725387e9822c2b5a9ae3587786a7d10428b97a73b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c07fd8414758ff5317840ab1c3ed2346", "guid": "bfdfe7dc352907fc980b868725387e9883657d2cd539e3a6da34afc326526bcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e08dc6daa3e986e9b74811b738c95b87", "guid": "bfdfe7dc352907fc980b868725387e98ab3b455e3bb16a98f0573d8c32172500"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8d60533af30946e81c0c6d0dc683af7", "guid": "bfdfe7dc352907fc980b868725387e9885e9cdc6e0eee46e311453451b4d8a9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874170916016d00014f7cf33298ca2ade", "guid": "bfdfe7dc352907fc980b868725387e987978ec833461b8ca74996afc7af7c92e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f45e0d515d88a93779a6540c9f3fa28d", "guid": "bfdfe7dc352907fc980b868725387e9893fbd2c220d6037fcec152ac33b7a2dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895e44bc56d5b58f9b150ebb45f55042a", "guid": "bfdfe7dc352907fc980b868725387e9823fafb86be954f37b195eb2ad54abcba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2dfaf4851d3f3c70fb3cb94ffd6d608", "guid": "bfdfe7dc352907fc980b868725387e98fc5038c80a4dd9370f38d9efcab46d15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4784dfc1948a2f2ed27ac29820d5f08", "guid": "bfdfe7dc352907fc980b868725387e98703a48024386e62e07456716e3e0417d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851e8cdd5918815f9fd7ec7789dbb60c0", "guid": "bfdfe7dc352907fc980b868725387e9819736a1ea01e39c25809147b1f8dd140"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3962c988ef7ff32ea8279fc1309f0a7", "guid": "bfdfe7dc352907fc980b868725387e98aa490ba9d926f62143e668fd9fc873d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828ca034d1cc405f43f0cf0bddf9c6d27", "guid": "bfdfe7dc352907fc980b868725387e98c3e22d9759aa29f62071836b4d2a2844"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e5ec2a2755d6586b0fd33d84011f1a0", "guid": "bfdfe7dc352907fc980b868725387e984b3e38d6f6845db30bb4f4380f35b66b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809f7b7d4e275a51e523484d7cde1cde7", "guid": "bfdfe7dc352907fc980b868725387e9827023ce14b1db9ae8e6c7decbb78e26f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879aba2b17defdd8fb41d06d7e245beb0", "guid": "bfdfe7dc352907fc980b868725387e9880652c8a326c040ce9fd242563e378f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a2b439da4abbbd36cbd6422479b7c9e", "guid": "bfdfe7dc352907fc980b868725387e98e212d0f62a0cd5956442676b694b6cbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b38e7aafda9889255284cdbc53941de7", "guid": "bfdfe7dc352907fc980b868725387e9823260413b39efbabcca44891a7c084b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98504049218cfe215b2e6da6b2ea441a0b", "guid": "bfdfe7dc352907fc980b868725387e98a8eb699af7ca23dadf34d3f1290e4643"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9537d31243cd0838c5addc7a7f32f86", "guid": "bfdfe7dc352907fc980b868725387e984d4cd57545e32463b8cb031cace65ad2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2d13b4e7c02f1d68786099b81cdfadb", "guid": "bfdfe7dc352907fc980b868725387e98bd721e2bf0037e1fd77c4d6895c7d608"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b33bface4a7ea6d711c3d1c4ccd6f04f", "guid": "bfdfe7dc352907fc980b868725387e98a9fa7dd7c5ecabb034cccf501b5ff15c"}], "guid": "bfdfe7dc352907fc980b868725387e988e8cb925a1372ecc105bc6ece611008a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9801df0a884b25a75112fd1df7c415ae3b"}], "guid": "bfdfe7dc352907fc980b868725387e9867acf46bcb77b2d550d31027f44213ae", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c1af8939c63004397c0e5b77f9e87459", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98830ca7dc8459bd0ce9ae77cfdffd2a7a", "name": "fl_location", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e989afda0e086f17d634e9b7bf38f00c90c", "name": "fl_location.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}