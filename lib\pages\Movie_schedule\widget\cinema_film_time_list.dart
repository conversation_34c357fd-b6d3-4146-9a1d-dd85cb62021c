import 'dart:io';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
import 'package:flutter_app/pages/cinema/model/cinema_model.dart';

import '../../../core/index.dart';
import '../../cinema/choose/seat.dart';
import '../../cinema/choose/signalr_classic_example.dart';

class CinemaFilmTimeList extends StatefulWidget {
  final List<ShowCinemaModel> cinemas;
  final Function(ShowModel, Cinema) onShowSelected;
  final FilmModel film;

  const CinemaFilmTimeList({
    super.key,
    required this.cinemas,
    required this.film,
    required this.onShowSelected,
  });

  @override
  State<CinemaFilmTimeList> createState() => _CinemaFilmTimeListState();
}

class _CinemaFilmTimeListState extends State<CinemaFilmTimeList> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final displayCinemas = isExpanded ? widget.cinemas : widget.cinemas.take(5).toList();

    return Column(
      children: [
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: displayCinemas.length,
          itemBuilder: (context, index) {
            final cinema = displayCinemas[index];
            return _buildCinemaSection(context, cinema);
          },
        ),
        if (!isExpanded && widget.cinemas.length > 5)
          TextButton(
            onPressed: () {
              setState(() {
                isExpanded = true;
              });
            },
            child: const Text('Xem thêm'),
          ),
      ],
    );
  }

  Widget _buildCinemaSection(BuildContext context, ShowCinemaModel cinema) {
    return GestureDetector(
      onTap: () {
        setState(() {
          cinema.isExpand = !cinema.isExpand;
        });
      },
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Cinema name and address
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          cinema.cinemaName ?? 'Unknown Cinema',
                          style: const TextStyle(fontSize: 18),
                        ),
                        const SizedBox(height: 4),
                      ],
                    ),
                  ),
                  // Distance (if available)
                  if (cinema.distance != null)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.location_on, size: 16, color: Colors.blue.shade700),
                          const SizedBox(width: 4),
                          Text(
                            '${cinema.distance?.toStringAsFixed(1)} km',
                            style: TextStyle(
                              color: Colors.blue.shade700,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              if (cinema.isExpand)
                ...cinema.listFilm?.map((format) => _buildFormatSection(context, format, cinema)) ?? [],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormatSection(BuildContext context, ListFilmModel format, ShowCinemaModel cinema) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Format name (2D, 3D, etc.)
        const VSpacer(CSpace.base),
        if (format.filmFormatName != null && format.filmFormatName!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              format.filmFormatName?.toUpperCase() ?? '',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        // Show times grid
        if ((format.listShow ?? []).isEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text(
              'Không có suất chiếu',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          )
        else
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              spacing: 8,
              children: (format.listShow ?? []).map((show) {
                final startTime = show.startTime;
                final formatted = startTime != null ? DateFormat('HH:mm').format(startTime) : '--:--';

                // Check if show time is in the past
                final isPast = startTime != null && startTime.isBefore(DateTime.now().toLocal());

                return isPast
                    ? const SizedBox.shrink()
                    : GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => Platform.isAndroid? SignalRClassicExample(showId: show.showId?? '') : ChooseSeatScreen(
                                cinemaId: cinema.cinemaId,
                                cinemaName: cinema.cinemaName,
                                showTime: show,
                                film: widget.film,
                              ),
                            ),
                          );
                        },
                        child: Column(
                          children: [
                            Chip(
                              label: Text(
                                formatted,
                                style: TextStyle(
                                  color: isPast ? Colors.grey : Colors.black,
                                  decoration: isPast ? TextDecoration.lineThrough : null,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                              padding: const EdgeInsets.symmetric(horizontal: 8),
                              backgroundColor: isPast ? Colors.grey.shade200 : Colors.grey.shade300,
                            ),
                            Text('${(show.totalSeat ?? 0) - (show.seatSolded ?? 0)} trống')
                          ],
                        ),
                      );
              }).toList(),
            ),
          ),
        // const SizedBox(height: 16),
      ],
    );
  }
}
