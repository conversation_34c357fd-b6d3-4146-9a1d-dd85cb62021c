<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>FirebaseCoreInternal_Privacy.bundle/Info.plist</key>
		<data>
		ol4Ol5y50FASHnTNdocck9aFRIU=
		</data>
		<key>FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ifoThrqbbqoLG4yjAruMQRaf0Dw=
		</data>
		<key>Headers/FirebaseCoreInternal-Swift.h</key>
		<data>
		0vDAqgES1Zg+o95uPWjVkMUriFo=
		</data>
		<key>Headers/FirebaseCoreInternal-umbrella.h</key>
		<data>
		jGc9nk2373eRlgSnJVcHvlnDVa4=
		</data>
		<key>Info.plist</key>
		<data>
		rg7uE/vV2PsE2z99/9Z+/9MxQ0k=
		</data>
		<key>Modules/FirebaseCoreInternal.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		jLGlJ7CGp4BHkDn1vLMDkY1QucY=
		</data>
		<key>Modules/FirebaseCoreInternal.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		8WwiIxqgl0lhGy8b16NA2V//iKM=
		</data>
		<key>Modules/FirebaseCoreInternal.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/FirebaseCoreInternal.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		Va7NxlS6esQOrB8siHBKiQSMe0M=
		</data>
		<key>Modules/FirebaseCoreInternal.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		bNIhpOws8UagKV/zuq9jv+brP6s=
		</data>
		<key>Modules/FirebaseCoreInternal.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/FirebaseCoreInternal.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		qaJ98nECMw/iym+9gdwNdGpjlSk=
		</data>
		<key>Modules/FirebaseCoreInternal.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		pmiykQJ5FXoxe11d95HQPnLVHyk=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		BN8blYJtQ2QUd5K6lnyWPWdo0Bs=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>FirebaseCoreInternal_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			ol4Ol5y50FASHnTNdocck9aFRIU=
			</data>
			<key>hash2</key>
			<data>
			e1/LV/CRCtinCsxRRPAf0HmmktXUyvlLj29+Wjhs0FE=
			</data>
		</dict>
		<key>FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			ifoThrqbbqoLG4yjAruMQRaf0Dw=
			</data>
			<key>hash2</key>
			<data>
			W3/peUI97ePgivwppC8A9ghiddxUioTCl3QjWGPu0+8=
			</data>
		</dict>
		<key>Headers/FirebaseCoreInternal-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			0vDAqgES1Zg+o95uPWjVkMUriFo=
			</data>
			<key>hash2</key>
			<data>
			JZ0cNb8Xo1QD+sORRMwBj0WR0m1KRSQ+PtOtZ2DYziA=
			</data>
		</dict>
		<key>Headers/FirebaseCoreInternal-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			jGc9nk2373eRlgSnJVcHvlnDVa4=
			</data>
			<key>hash2</key>
			<data>
			ioqhudIaQObXJw3INKxYB2XE6YkuzYy0FRn3Bvi3bkg=
			</data>
		</dict>
		<key>Modules/FirebaseCoreInternal.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			jLGlJ7CGp4BHkDn1vLMDkY1QucY=
			</data>
			<key>hash2</key>
			<data>
			e5vDgv4wwLUwRbMuGKLkr2/Thd1+xSUU2v5SaHD7GK4=
			</data>
		</dict>
		<key>Modules/FirebaseCoreInternal.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			8WwiIxqgl0lhGy8b16NA2V//iKM=
			</data>
			<key>hash2</key>
			<data>
			FxO0SHjAQuupYjZX7pO3tykABMa18C/5yeqIqDZrB4k=
			</data>
		</dict>
		<key>Modules/FirebaseCoreInternal.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/FirebaseCoreInternal.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			Va7NxlS6esQOrB8siHBKiQSMe0M=
			</data>
			<key>hash2</key>
			<data>
			RMDR0x1/YIb6xJszV1c122rfqeTYDmW0QYoZ1wK3vnQ=
			</data>
		</dict>
		<key>Modules/FirebaseCoreInternal.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			bNIhpOws8UagKV/zuq9jv+brP6s=
			</data>
			<key>hash2</key>
			<data>
			KeGZ6IAjmmiIKfGVH2xhDIsdQ3/0ZnItSnpASUBMdoM=
			</data>
		</dict>
		<key>Modules/FirebaseCoreInternal.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/FirebaseCoreInternal.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			qaJ98nECMw/iym+9gdwNdGpjlSk=
			</data>
			<key>hash2</key>
			<data>
			Te8fcgiiKxWIpNpv7UEZg7/y14Y3jP+xAkUDyFYnizc=
			</data>
		</dict>
		<key>Modules/FirebaseCoreInternal.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			pmiykQJ5FXoxe11d95HQPnLVHyk=
			</data>
			<key>hash2</key>
			<data>
			xzsV8KJvk1k9FnnscQsg/bLALHUr8lZQvj89pvoVQ4o=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			BN8blYJtQ2QUd5K6lnyWPWdo0Bs=
			</data>
			<key>hash2</key>
			<data>
			I7WpWvmiEnMwky2Dl5xBzvxXQSHCjgV5scKVtvwmCu4=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
