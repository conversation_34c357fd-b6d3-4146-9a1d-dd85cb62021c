{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ddef766af7107370946b39aaae17c0b0", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/fl_location/fl_location-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/fl_location/fl_location-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/fl_location/fl_location.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "fl_location", "PRODUCT_NAME": "fl_location", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9853a795819367ab11de7cb7137618f463", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98adceb30fc842f5f41aece21e9e2c8a5a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/fl_location/fl_location-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/fl_location/fl_location-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/fl_location/fl_location.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "fl_location", "PRODUCT_NAME": "fl_location", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9852039e1f36aee8865897eeec2e954517", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98adceb30fc842f5f41aece21e9e2c8a5a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/fl_location/fl_location-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/fl_location/fl_location-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/fl_location/fl_location.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "fl_location", "PRODUCT_NAME": "fl_location", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98049e4eb9eb935db6c7bbaf9de97542a7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986dd536103f1462fdffe7391a1ef3be36", "guid": "bfdfe7dc352907fc980b868725387e981618ca6dc052d290ff508e78f887781f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d39de172c14848aaa0b7d2255d42b6a8", "guid": "bfdfe7dc352907fc980b868725387e982fab8c14f1e30b11294fbfac74d9c988", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e5d1c4a04f6895946100545597008a6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e60ebea36be081f5054caa4d871bb0ec", "guid": "bfdfe7dc352907fc980b868725387e98df68e79ca14095c8e1009b63638097fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f66a60c579d721909489ee9cb0e0c509", "guid": "bfdfe7dc352907fc980b868725387e98bb392d8ce1c55578ea5fa0c6a0b40739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982641a5266b88ff991d63267941bed994", "guid": "bfdfe7dc352907fc980b868725387e983d9656af3b20bd98df92abb4f4c40275"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bca8eecf49aa8da153da86d57fa3968", "guid": "bfdfe7dc352907fc980b868725387e9822c2b5a9ae3587786a7d10428b97a73b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854d45385321a1dc4dcd30675ba2a10dd", "guid": "bfdfe7dc352907fc980b868725387e9883657d2cd539e3a6da34afc326526bcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fd96a44a1fe525c972ac2ca3269fb2d", "guid": "bfdfe7dc352907fc980b868725387e98ab3b455e3bb16a98f0573d8c32172500"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984de65680af570bf56eaaa37f98f4e90d", "guid": "bfdfe7dc352907fc980b868725387e9885e9cdc6e0eee46e311453451b4d8a9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815e99025221da9f74adc4ad30e26451d", "guid": "bfdfe7dc352907fc980b868725387e987978ec833461b8ca74996afc7af7c92e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa16ca10a561e69a49622d39fae02c20", "guid": "bfdfe7dc352907fc980b868725387e9893fbd2c220d6037fcec152ac33b7a2dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982494d12ee3d5e7da650087a51af956e0", "guid": "bfdfe7dc352907fc980b868725387e9823fafb86be954f37b195eb2ad54abcba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878fe4770fb5c3c58b62f45d276ce7e0d", "guid": "bfdfe7dc352907fc980b868725387e98fc5038c80a4dd9370f38d9efcab46d15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf2854869c097675abecd4a266e42cf0", "guid": "bfdfe7dc352907fc980b868725387e98703a48024386e62e07456716e3e0417d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986de0a6bed2b57d276594384e3638c5fc", "guid": "bfdfe7dc352907fc980b868725387e9819736a1ea01e39c25809147b1f8dd140"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a682fe0faac1d20b14520ea53bda23a", "guid": "bfdfe7dc352907fc980b868725387e98aa490ba9d926f62143e668fd9fc873d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc2c6c8699f53f55ed6d61f280a91bc3", "guid": "bfdfe7dc352907fc980b868725387e98c3e22d9759aa29f62071836b4d2a2844"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98216accf2fec70fbc1249dbe08efc2554", "guid": "bfdfe7dc352907fc980b868725387e984b3e38d6f6845db30bb4f4380f35b66b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cb53363c57244cac41bdb3ee5f67407", "guid": "bfdfe7dc352907fc980b868725387e9827023ce14b1db9ae8e6c7decbb78e26f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ad0820029fc538b7c912021c5385477", "guid": "bfdfe7dc352907fc980b868725387e9880652c8a326c040ce9fd242563e378f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a153a755580b42f0db6bfbe4e098a844", "guid": "bfdfe7dc352907fc980b868725387e98e212d0f62a0cd5956442676b694b6cbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842edf9dcbaba48b22e53686623d8bc8d", "guid": "bfdfe7dc352907fc980b868725387e9823260413b39efbabcca44891a7c084b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982136a4cb8a4afe1eda4bb1fec6490797", "guid": "bfdfe7dc352907fc980b868725387e98a8eb699af7ca23dadf34d3f1290e4643"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98332c82c6f50535a15d1998b8abfe785e", "guid": "bfdfe7dc352907fc980b868725387e984d4cd57545e32463b8cb031cace65ad2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef6440e313354f6d8a6df222b16cce21", "guid": "bfdfe7dc352907fc980b868725387e98bd721e2bf0037e1fd77c4d6895c7d608"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821a72cfa4ab644c1065cb31933eee825", "guid": "bfdfe7dc352907fc980b868725387e98a9fa7dd7c5ecabb034cccf501b5ff15c"}], "guid": "bfdfe7dc352907fc980b868725387e988e8cb925a1372ecc105bc6ece611008a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9801df0a884b25a75112fd1df7c415ae3b"}], "guid": "bfdfe7dc352907fc980b868725387e9867acf46bcb77b2d550d31027f44213ae", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c1af8939c63004397c0e5b77f9e87459", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98830ca7dc8459bd0ce9ae77cfdffd2a7a", "name": "fl_location", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e989afda0e086f17d634e9b7bf38f00c90c", "name": "fl_location.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}