package vn.zenity.betacineplex.helper.view;

import android.content.Context;
import android.content.res.TypedArray;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;

import vn.zenity.betacineplex.R;
import vn.zenity.betacineplex.helper.extension.ViewGroup_ExtensionsKt;
import vn.zenity.betacineplex.model.FilmModel;
import vn.zenity.betacineplex.model.ShowModel;

public class ItemTimeBooking extends ConstraintLayout {

    public interface TimeSelectListener {
        void onTimeSelected(ShowModel time, FilmModel film);
    }

    private String time = "";
    private String seat = "";
    private TextView tvTime;
    private TextView tvSeat;
    private ShowModel showModel;
    private FilmModel filmModel;

    public void setListener(TimeSelectListener listener) {
        this.listener = listener;
    }

    private TimeSelectListener listener;

    public ItemTimeBooking(Context context, String time, String seat, ShowModel showModel, FilmModel filmModel, TimeSelectListener listener) {
        this(context, null);
        this.time = time;
        this.showModel = showModel;
        this.seat = seat;
        this.listener = listener;
        this.filmModel = filmModel;
        changeText();
    }

    public ItemTimeBooking(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ItemTimeBooking(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs);
    }

    private void init(AttributeSet attrs) {
        if (attrs != null) {
            TypedArray styledAttributes = getContext().obtainStyledAttributes(attrs, R.styleable.ItemTimeBooking, 0, 0);
            time = styledAttributes.getString(R.styleable.ItemTimeBooking_bookTime);
            seat = styledAttributes.getString(R.styleable.ItemTimeBooking_bookSeat);
            styledAttributes.recycle();
        }
        View view = ViewGroup_ExtensionsKt.inflate(this, R.layout.item_time_booking);
        tvTime = view.findViewById(R.id.tvTimeBooking);
        tvSeat = view.findViewById(R.id.tvSeatBooking);
        tvTime.setText(time);
        tvSeat.setText(seat);
        view.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.onTimeSelected(showModel, filmModel);
                }
            }
        });
        addView(view);
    }

    private void changeText() {
        if (tvTime == null) return;
        tvTime.setText(time);
        tvSeat.setText(seat);
    }

    public void setContent(String time, String seat) {
        this.time = time;
        this.seat = seat;
        changeText();
    }
}
