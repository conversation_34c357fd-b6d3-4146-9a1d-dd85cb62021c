package vn.zenity.betacineplex.view.user

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Base64
import io.reactivex.Observable
import io.reactivex.ObservableOnSubscribe
import io.reactivex.disposables.Disposable
import okhttp3.MediaType
import okhttp3.ResponseBody
import retrofit2.HttpException
import retrofit2.Response
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.DeviceHelper
import vn.zenity.betacineplex.helper.extension.PreferencesHelper
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.model.DDKCReponse
import vn.zenity.betacineplex.model.UserModel
import java.io.ByteArrayOutputStream
import java.lang.ref.WeakReference
import java.util.*

/**
 * Created by Zenity.
 */

class MemberPresenter : MemberContractor.Presenter {
    private var disposable: Disposable? = null
    private var disposableCardClass: Disposable? = null

    override fun uploadAvatar(fileImage: String, accountId: String) {
        view?.get()?.showLoading()
        disposable = Observable.create<String> {
            val bm = BitmapFactory.decodeFile(fileImage)
            val baos = ByteArrayOutputStream()
            bm.compress(Bitmap.CompressFormat.JPEG, 80, baos) //bm is the bitmap object
            val b = baos.toByteArray()
            it.onNext(Base64.encodeToString(b, Base64.DEFAULT))
            it.onComplete()
        }.applyOn().subscribe({
            disposable = APIClient.shared.accountAPI.uploadAvatar(hashMapOf("ImageBase64" to it, "Extension" to ".jpg"), accountId).applyOn()
                    .subscribe({
                        view?.get()?.hideLoading()
                        if (it.isSuccess) {
                            it.Data?.AvatarUrl?.let {
                                view?.get()?.updateAvatarSuccess(it, fileImage)
                            }
                        } else {
                            view?.get()?.showAlert(it.Message
                                    ?: R.string.upload_avatar_error.getString())
                        }
                    }, {
                        view?.get()?.hideLoading()
                        view?.get()?.showAlert(R.string.upload_avatar_error.getString())
                    })
        }, {
            view?.get()?.hideLoading()
            view?.get()?.showAlert(R.string.upload_avatar_error.getString())
        }
        )
    }

    override fun getUserProfile(accountId: String) {
//        if(BuildConfig.DEBUG) {
//            disposable = Observable.create<DDKCReponse<UserModel>> { emitter ->
//                val response: Response<String> = Response.error(401, ResponseBody.create(MediaType.parse("text/html"), "Errror"))
//                val error = HttpException(response)
//                emitter.onError(error)
//            }.applyOn().subscribe(
//                    { responseProfile ->
//                        if (responseProfile.isSuccess) {
//                            responseProfile.Data?.Token = Global.share().token
//                            Global.share().user = responseProfile.Data
//                            if (responseProfile.Data?.IsUpdatedFacebookPassword == false) {
//                                this.view?.get()?.showUpdateFBPassword()
//                            }
//                        }
//                    }, {
//            })
//            return
//        }
        disposable = APIClient.shared.accountAPI.getProfile(accountId).applyOn().subscribe(
                { responseProfile ->
                    if (responseProfile.isSuccess) {
                        responseProfile.Data?.Token = Global.share().token
                        Global.share().user = responseProfile.Data
                        if (responseProfile.Data?.IsUpdatedFacebookPassword == false) {
                            this.view?.get()?.showUpdateFBPassword()
                        }
                    }
                }, { _ ->
        })
    }

    override fun getCardClass() {
        disposableCardClass = APIClient.shared.accountAPI.getCardClass().applyOn()
                .subscribe({ response ->
                    response.Data?.let {
                        view?.get()?.updateListCard(it)
                    }
                }, {})
    }

    override fun unregisterFCMToken(token: String) {
        val deviceId = DeviceHelper.shared.deviceId()
        disposable = APIClient.shared.accountAPI.unregisterFCMToken(mapOf("DeviceId" to deviceId,
                "AccountId" to (Global.share().user?.AccountId ?: ""),
                "DeviceToken" to token,
                "DeviceType" to "android")).applyOn().subscribe({
            PreferencesHelper.shared.putValue(Constant.Key.isRegisterToken, false)
            view?.get()?.logoutSuccess()
        }, {
            view?.get()?.logoutSuccess()
        })
    }

    private var view: WeakReference<MemberContractor.View?>? = null
    override fun attachView(view: MemberContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
