<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:descendantFocusability="beforeDescendants"
    android:fitsSystemWindows="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:layout_marginTop="@dimen/statusBarHeight"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/account_info"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_small"
            app:srcCompat="@drawable/ic_menubuger_white" />
    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:isScrollContainer="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/padding_large">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_normal"
                android:text="@string/normal_info"
                android:textAllCaps="true"
                android:textColor="@color/textDark"
                android:textSize="@dimen/font_large"
                app:fontFamily="@font/oswald_regular" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingTop="@dimen/margin_large">

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/TextContent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/email"
                    app:textAllCaps="true" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvEmail"
                    style="@style/TextContent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_large"
                    tools:text="<EMAIL>" />
            </LinearLayout>

            <vn.zenity.betacineplex.helper.view.BetaEditText
                android:id="@+id/edtFullname"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                android:nextFocusDown="@+id/edtEmailRegister"
                android:nextFocusRight="@+id/edtEmailRegister"
                app:btedHint="@string/fullname"
                app:btedIcon="@drawable/name" />

            <vn.zenity.betacineplex.helper.view.BetaSelectionView
                android:id="@+id/selectionGender"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                app:btsvIcon="@drawable/sex"
                app:btsvHint="@string/gender" />


            <vn.zenity.betacineplex.helper.view.BetaSelectionView
                android:id="@+id/selectionBirthday"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                app:btsvIcon="@drawable/birthday"
                app:btsvHint="@string/birthday" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                android:text="@string/contact_info"
                android:textAllCaps="true"
                android:textColor="@color/textDark"
                android:textSize="@dimen/font_large"
                app:fontFamily="@font/oswald_regular" />

            <vn.zenity.betacineplex.helper.view.BetaEditText
                android:id="@+id/edtCardNumber"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                app:btedHint="@string/people_card_id"
                app:btedIcon="@drawable/cmnd" />

            <vn.zenity.betacineplex.helper.view.BetaEditText
                android:id="@+id/edtPhone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                android:inputType="phone"
                app:btedHint="@string/phone"
                app:btedIcon="@drawable/phone" />

            <vn.zenity.betacineplex.helper.view.BetaSelectionView
                android:id="@+id/selectionCity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                app:btsvIcon="@drawable/tower"
                app:btsvHint="@string/city" />

            <vn.zenity.betacineplex.helper.view.BetaSelectionView
                android:id="@+id/selectionDistrict"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                app:btsvIcon="@drawable/home"
                app:btsvHint="@string/district" />

            <vn.zenity.betacineplex.helper.view.BetaEditText
                android:id="@+id/edtAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                android:imeOptions="actionDone"
                app:btedHint="@string/address"
                app:btedIcon="@drawable/address" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnUpdate"
                style="@style/ButtonPrimary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:layout_marginTop="@dimen/margin_large"
                android:text="@string/update"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btnLogin" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</LinearLayout>
