{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d261391d15b566eea2a4d92726a7543e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98630d555dd139f6a4509c7408c91b471c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988788409978b3b999e5b491a287b7eff9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a779ca899f3f2864afc279916d4c1d7a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988788409978b3b999e5b491a287b7eff9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a6fb0ecae370a61ccce968b970e63849", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983bca6a42889b369121ecac7d7cf659c3", "guid": "bfdfe7dc352907fc980b868725387e98427a4fc6c7e37ebb114e142fd6ad1507", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839a6399b4b06c844be472b79129e402c", "guid": "bfdfe7dc352907fc980b868725387e982e33ab2d61acc0bf2477abd4a3e7dff6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815d6fba893720a0e1335d605920a6190", "guid": "bfdfe7dc352907fc980b868725387e9860351de34e9515bb049a472feed6a714", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e6391b64f2b15bc64ca0ecb31ef8dc4", "guid": "bfdfe7dc352907fc980b868725387e98af24ae62e3a64f80a79b1f2492795f42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b99454e6704caa15e1b6eeada620368", "guid": "bfdfe7dc352907fc980b868725387e984e543af44146814d6d0b182aa01b9cb1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7ce6a5abe7f3a59282baf0e1c35b6cc", "guid": "bfdfe7dc352907fc980b868725387e98b6b0aafbc6498b97bf1e60a08ada7aaf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7535df2bc389cf1d5c384ae074c69ef", "guid": "bfdfe7dc352907fc980b868725387e98c3a9ad97846d50de9945413842a2927f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb3fd641186b91fcb1e5266552d046f2", "guid": "bfdfe7dc352907fc980b868725387e9885af19a5b5b29c32e580ffaeb37615fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad17aee0b71dfa278d0dd9b3b0db6f21", "guid": "bfdfe7dc352907fc980b868725387e986a06f60957690e344fc63b73e21241cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f719bceb2dba101185f701914673568", "guid": "bfdfe7dc352907fc980b868725387e985330ad3f464b4ef800138c40495f723c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878b5c50c1614ac3942e8ad477afb559a", "guid": "bfdfe7dc352907fc980b868725387e9884fed9f3380b005bb4e1c2c1c67b6772", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98465ef542d8b39e4c5e58333491af93be", "guid": "bfdfe7dc352907fc980b868725387e98759a620501519debdd1ccf5fd73c0357", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893ea2d3c9522db9e8d12fa7e743262cc", "guid": "bfdfe7dc352907fc980b868725387e98a4773b6bd94c9b71cdaece2bf71dc622", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98defed71a42be925a4f633b2fa6cb26c7", "guid": "bfdfe7dc352907fc980b868725387e986dd6a932c2c18ad88f095efd36180f9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2ac5675680fa3c568173c9e1b1bf429", "guid": "bfdfe7dc352907fc980b868725387e989ae8e5ad48c4ab88434f1b80b515947f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ca198be0ae48defeb472aabda9730ba", "guid": "bfdfe7dc352907fc980b868725387e982cab6f3ee420e6d80399ee1b564bf072", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1ebd0365e193f5b7b7305c98f7e32ff", "guid": "bfdfe7dc352907fc980b868725387e98903962cfbb068fc50ec767c615c32cdc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987201d7bff39055b9ba010cafeb8fa0db", "guid": "bfdfe7dc352907fc980b868725387e98da9ec415364536dbfc953dec825f7ead", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be978942147007b2b7b69894a77d8e40", "guid": "bfdfe7dc352907fc980b868725387e98f051b4bf07f4d97f40cdfb5b2f9bc3fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af274d3bce5511363abdbe711151fe9b", "guid": "bfdfe7dc352907fc980b868725387e9837fa10a17bd6490b14efe94481268c6c", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff3390308389f5b863346636a84e74f8", "guid": "bfdfe7dc352907fc980b868725387e98b45af92e6b93a314ab6207bb75fa4208", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98497b8fabdbad9d3f7e2b359bb60ceb82", "guid": "bfdfe7dc352907fc980b868725387e986b8f1c965e176dd20deceb1bea7bf99b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b902a3b0889e4f11c6293934aa29b6c2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984aac0320dc402cf49a50e12343c5b2ff", "guid": "bfdfe7dc352907fc980b868725387e98e3ea2b95bb882977a05ad2d00b67722d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989077a7885ee413490ef7cf46e8b11e5f", "guid": "bfdfe7dc352907fc980b868725387e98a456a9e6e4d0071e35462ae56fb7485f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98babb958d0a05ba280609f6d9772810fd", "guid": "bfdfe7dc352907fc980b868725387e98401cfb5927eec4a332f254ad6f0ef8d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811ac78869a79e7b85f2ecf6db5ce698e", "guid": "bfdfe7dc352907fc980b868725387e9873075998290f45bc15c91dee15a3c7e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804a7e418268858a0f1efbef1ba1cf23b", "guid": "bfdfe7dc352907fc980b868725387e98b6f36fdc96e11755a3e27bfcc3f9821b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984301a8626335180ec878dd3b0fde82ba", "guid": "bfdfe7dc352907fc980b868725387e987da6aeb5c03de8b8898dc77eb29ca28e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c54bb7c143e57fc103b362b55857fe2a", "guid": "bfdfe7dc352907fc980b868725387e9806c753266ea57a6768bbbdc242b20ae0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1746545036417a8b2fddaed8990a89", "guid": "bfdfe7dc352907fc980b868725387e98c6517f74e4abb4adb2460d8c7008ea96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832b34dd402cd167c11df069a4996d1f0", "guid": "bfdfe7dc352907fc980b868725387e982d020e389ce1d46b271de1c7b7900e4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856c2c3d77ecb6ea8ad5e1d0fb8633741", "guid": "bfdfe7dc352907fc980b868725387e98360a26411cd375876319f8e5adeb019e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b64e247dd3350bad79c59af9d601b4ab", "guid": "bfdfe7dc352907fc980b868725387e98a8ba1be512eda6235880b4fd14e70250"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a07c8e30f1f191d48888d7628fdbc678", "guid": "bfdfe7dc352907fc980b868725387e98d985415a74f53830e2d4ee512ed75061"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838dc9c6f44bde185c1c6d55db74f8aef", "guid": "bfdfe7dc352907fc980b868725387e98159d4a93e5a07d120ea760a3408f8f6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b5c23870953d3f1893f05bac005dd14", "guid": "bfdfe7dc352907fc980b868725387e98c25be6454fec668ff4de5941a9042f03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4cf85af3a68292f5200a2f5b06f859f", "guid": "bfdfe7dc352907fc980b868725387e98b250ebd82309a64acfc5d64c366c9c39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ed653041f5428d38ba61ea11d3250db", "guid": "bfdfe7dc352907fc980b868725387e98bd8f492002542c1bf4b4b3ab0943938d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f86761ff5818f6075451f936e938a73", "guid": "bfdfe7dc352907fc980b868725387e987f94b822ffdfc7e59e6ccd1bb7853a26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d652d41063b66269150fd3dc47722b0", "guid": "bfdfe7dc352907fc980b868725387e984a3aaba4b956a8f99db9ec8eac882a68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba10b4e94817a514f781dba73efaf556", "guid": "bfdfe7dc352907fc980b868725387e984ca10519bed4431ce63117bab859850a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3377e5d50146da59117ac6d1b41673e", "guid": "bfdfe7dc352907fc980b868725387e98f34f795f5b28ffd805d932e4ee33b1c6"}], "guid": "bfdfe7dc352907fc980b868725387e98702842d32cd009a34215bf3632b9536c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9889532501a6bd8ee6c1531532e63dc282"}], "guid": "bfdfe7dc352907fc980b868725387e98635044c41da6d9775035b3e8aff75439", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9800bbbc285bc7e03492d316d105cdc99f", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9897c8b52c9272c91c6bd75662a4a2954a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}