package vn.zenity.betacineplex.view.notification

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.Notification

/**
 * Created by Zenity.
 */

interface NotificationContractor {
    interface View : IBaseView {
        fun showListNotifications(notifes: List<Notification>)
    }

    interface Presenter : IBasePresenter<View> {
        fun getListNotification(page: Int)
        fun readNotification(id: Int, screenCode: Int)
    }
}
