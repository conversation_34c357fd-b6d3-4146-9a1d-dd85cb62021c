{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98193e2f8ae16200aebd481430d9f99da8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98df9524d1e5854ecf46af2d4a07629a4f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9800827be1df83d79f76925f97e3d64d70", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989c5839061afa3fd6f9c833b89dff9409", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9800827be1df83d79f76925f97e3d64d70", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982f2f4f580231c384da9caec95c62707e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9812b42d909a0472e8757a37215a345f99", "guid": "bfdfe7dc352907fc980b868725387e98f403efa0252caa2218aa4f562b1435a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e5b915a7cadaae2e2a5df317816ed99", "guid": "bfdfe7dc352907fc980b868725387e9820d1b55e95fe26d80ee171eb23faa363", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec50e7ea690d511e40d5e1692f855433", "guid": "bfdfe7dc352907fc980b868725387e98fddc3fb6efda7bef6370a2abb4b2b197", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989808cc455a450311d1526a70b596c8ee", "guid": "bfdfe7dc352907fc980b868725387e9853595383656438ebfc356298e195ef45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b73ce91ad288a9bbb088c8ca895b80c0", "guid": "bfdfe7dc352907fc980b868725387e98ba2c2a76602e2a0ede12b1c69c909d26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c14aa881dd47a846552e4cc0dc71cfa8", "guid": "bfdfe7dc352907fc980b868725387e98acace69e0787e74398db42a4ac4d0990", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6c279334a772d8771095cbdfadc1db1", "guid": "bfdfe7dc352907fc980b868725387e98473abefe2e97b4250f9ab66d4d37ca79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a8ac51339e8f32e39514e68d5c4318c", "guid": "bfdfe7dc352907fc980b868725387e98c955b4915a6df631ecc4f068d331aeaf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981670a0e44f8f653c00d4453fd2a0e109", "guid": "bfdfe7dc352907fc980b868725387e98e3c8624ca44bb367a47f959b8e0bb904", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5cd58b03c8ee39e0ea685deca9e0dcc", "guid": "bfdfe7dc352907fc980b868725387e98c63797f739a391c286038d1204f01eaf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892cf08e2a3bb9c4de3d317debe679d56", "guid": "bfdfe7dc352907fc980b868725387e98134e689ab3f32565338975ad99881951", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bc8562546043dfdf954de71e641c8dc", "guid": "bfdfe7dc352907fc980b868725387e98401e70f035e39e026de82f843ae505f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e768a580c9ebd5e75faf55fb0d465fff", "guid": "bfdfe7dc352907fc980b868725387e987d5934db8b102e0d0fe42b197c12e11f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c7ba0ea5c94fe2d69412f28422fe00e", "guid": "bfdfe7dc352907fc980b868725387e98a8293d46e960df29a91fdbc02c87bc75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f81493b00af270a1fc36c99404972b4d", "guid": "bfdfe7dc352907fc980b868725387e9873cf88edad729603ec3cde28bb99dd18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae9828c82c6f00768096fd3fb1824c47", "guid": "bfdfe7dc352907fc980b868725387e98bfbd5471847b05f090d0bd8cc92c33b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d86b1917a1a956d3bb98b877730a1718", "guid": "bfdfe7dc352907fc980b868725387e988e7a4f7fde1e0cc3829ad93cd23116ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e37357f0bcb0ddc9e6f936a7ab18829", "guid": "bfdfe7dc352907fc980b868725387e98a091c54fd5fd993c85619067639af713", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986306787a2731d3e17c9f25913a54b82f", "guid": "bfdfe7dc352907fc980b868725387e98a53492c58b8dfc8be2ca68e21252150a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bca7843759862d9c4ebd002af0a8879d", "guid": "bfdfe7dc352907fc980b868725387e98a295bc8a33f6ec81f438f7e5964b9668", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822f8ba892695d5b1fca5fc23e27c20ca", "guid": "bfdfe7dc352907fc980b868725387e9897b61460795c1a5c004fc734264c3f7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864b6ee85e397b612cea0c361d6fa4e87", "guid": "bfdfe7dc352907fc980b868725387e985bd2b037a0ba40c03f5ee77b90e88b6e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983345c8e1ad89d3e831bcf3d96eeba8b1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c158a77cd8adcd7bad0a099500be85e", "guid": "bfdfe7dc352907fc980b868725387e9861fd65255494c2b52cc13d83741a23da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e18d1e1991a15f68a7344a463a9b7c6a", "guid": "bfdfe7dc352907fc980b868725387e980b67ba324187eab35eb7ee0321854785"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1b83e9bef38d18c9669fdb7155af249", "guid": "bfdfe7dc352907fc980b868725387e98e2b65c1d5484bd8fced43312bb4a95c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849756d6f5496258fe7bd9211150fb1d1", "guid": "bfdfe7dc352907fc980b868725387e98962a32a16253f8051c403a08cb56d7ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce68a11395d7bb67c626b08fab932a63", "guid": "bfdfe7dc352907fc980b868725387e981794c6bcbf3338bfd01b0e81a81bfee7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98562a1d83db972011b5c91fdbc634a1aa", "guid": "bfdfe7dc352907fc980b868725387e989737ef0d97e315b66026ef78239323a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e5c747747e3af8f37b47c7cebec7284", "guid": "bfdfe7dc352907fc980b868725387e98fab3625c847796ac6893383da080d3a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8cdb87ffb8cee4f0ec013620fca6a3", "guid": "bfdfe7dc352907fc980b868725387e984d1c1674515fcbbfc4b7d3d293dff401"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc9c7000f0ac977ec36c1fbf37318670", "guid": "bfdfe7dc352907fc980b868725387e98bf52a2cb067eb7f3a2f9011c377e02cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850060f17ddef13711625864a4a2f197e", "guid": "bfdfe7dc352907fc980b868725387e98f518ec7b9fd6ddeaa0f611f1b809c546"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c844606c4987a5cab97c5621ad88d394", "guid": "bfdfe7dc352907fc980b868725387e9882c6d77ca4f7a0ba4fe122ac07806feb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bacd8913200131dc37132f6a9b2f6a08", "guid": "bfdfe7dc352907fc980b868725387e984e14cbafd2f10c64d299d34f0e2347b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e085e17cc9ff3aebd454b3a946ba47df", "guid": "bfdfe7dc352907fc980b868725387e98b7fc5e92eda7ab991cbac5db18ee5c8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a44c9cca4d0d02181c514311ef02ae5", "guid": "bfdfe7dc352907fc980b868725387e98df861692408e68e1b2fe6b5087e731f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983661f0e71118b3374e026793593bf639", "guid": "bfdfe7dc352907fc980b868725387e98f3ca2e6b6933f7cd6707b15a6df83cc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980265c27c067daae9712f5137b503b212", "guid": "bfdfe7dc352907fc980b868725387e98d2a37afef486c14b593a1481a1162b26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cc7f8728fda962b28a83ac643d8b60d", "guid": "bfdfe7dc352907fc980b868725387e989919073e89b846f491f9b2f271aa3912"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98804ddd2eb21a1aa8700dd6e87334326a", "guid": "bfdfe7dc352907fc980b868725387e988bdd816b1c54505789ebb0470e506eec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b28370f208e023f838399da5b24cf121", "guid": "bfdfe7dc352907fc980b868725387e987cdc84e675f236d1610545297c0fd71c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e91711d185f31666ba0f360ec6c4178c", "guid": "bfdfe7dc352907fc980b868725387e98b66198f09a803dab21a097bc34fa44ff"}], "guid": "bfdfe7dc352907fc980b868725387e987e1da5a35db3aaf967aff4f01dc17009", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9894e75497f67c03b10475f6f09a6cdcb4"}], "guid": "bfdfe7dc352907fc980b868725387e985e34f42c0c6bb7ee798d4fc211ca9585", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985678fb7869d8ab2e92701d0ad9c2eee2", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9897ca0a74612512ac31ec99b7651af7ac", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}