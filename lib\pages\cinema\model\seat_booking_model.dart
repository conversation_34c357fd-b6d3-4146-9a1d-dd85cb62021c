import 'package:flutter_app/pages/cinema/model/seat_model.dart';
import 'package:flutter_app/pages/cinema/model/ticket_type.dart';

class SeatBookingModel {
  final int seatIndex;
  final String seatName;
  final String seatType;
  final String ticketTypeId;
  final int price;

  SeatBookingModel({
    required this.seatIndex,
    required this.seatName,
    required this.seatType,
    required this.ticketTypeId,
    required this.price,
  });

  factory SeatBookingModel.fromSeatAndTicket(SeatModel seat, TicketType ticketType) {
    String type = "STARDAR";
    if (seat.seatTypeEnum == SeatType.VIP) {
      type = "VIP";
    } else if (seat.seatTypeEnum == SeatType.COUPLE) {
      type = "DOUBLE";
    }

    return SeatBookingModel(
      seatIndex: seat.seatIndex ?? 0,
      seatName: seat.seatNumber ?? "",
      seatType: type,
      ticketTypeId: ticketType.ticketTypeId ?? "",
      price: ticketType.price ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'SeatIndex': seatIndex,
      'SeatName': seatName,
      'SeatType': seatType,
      'TicketTypeId': ticketTypeId,
      'Price': price,
    };
  }
}
