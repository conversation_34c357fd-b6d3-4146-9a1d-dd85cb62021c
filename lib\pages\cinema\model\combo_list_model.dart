import 'dart:convert';

/// Model cho danh sách combo - tương ứng với ComboListModel trong iOS
class ComboListModel {
  final List<ComboListModelElement>? elements;

  ComboListModel({this.elements});

  factory ComboListModel.fromJson(List<dynamic> json) {
    return ComboListModel(
      elements: json.map((e) => ComboListModelElement.fromJson(e)).toList(),
    );
  }

  static ComboListModel? fromJsonString(String jsonString) {
    try {
      final List<dynamic> jsonList = jsonDecode(jsonString);
      return ComboListModel.fromJson(jsonList);
    } catch (e) {
      print('Error parsing ComboListModel: $e');
      return null;
    }
  }
}

/// Tương ứng với ComboListModelElement trong iOS
class ComboListModelElement {
  final List<ItemInCombo>? itemInCombos;
  final String? comboPackageId;
  final ComboData? combo;
  final int? totalPriceAfterVAT;
  final int? totalPriceInCombo;
  final int? totalPriceBeforeVAT;
  final bool? isTicketPackage;

  ComboListModelElement({
    this.itemInCombos,
    this.comboPackageId,
    this.combo,
    this.totalPriceAfterVAT,
    this.totalPriceInCombo,
    this.totalPriceBeforeVAT,
    this.isTicketPackage,
  });

  factory ComboListModelElement.fromJson(Map<String, dynamic> json) {
    return ComboListModelElement(
      itemInCombos: json['ItemInCombos'] != null
          ? List<ItemInCombo>.from(
              json['ItemInCombos'].map((x) => ItemInCombo.fromJson(x)))
          : null,
      comboPackageId: json['ComboPackageId'],
      combo: json['Combo'] != null ? ComboData.fromJson(json['Combo']) : null,
      totalPriceAfterVAT: json['TotalPriceAfterVAT'],
      totalPriceInCombo: json['TotalPriceInCombo'],
      totalPriceBeforeVAT: json['TotalPriceBeforeVAT'],
      isTicketPackage: json['IsTicketPackage'],
    );
  }
}

/// Tương ứng với ComboData trong iOS
class ComboData {
  final String? id;
  final String? code;
  final String? name;
  final String? shortName;
  final String? startDate;
  final String? endDate;
  final String? barCode;
  final String? applicationId;
  final String? description;
  final bool? status;
  final int? comboPacketType;
  final int? order;
  final bool? isHot;
  final int? priceBeforeVAT;
  final dynamic discountPercent;
  final dynamic amountBeforeVAT;
  final dynamic vatPercent;
  final int? priceAfterVAT;
  final dynamic totalAmountAfterVAT;
  final String? createdOnDate;
  final String? createdByUser;
  final String? lastModifiedOnDate;
  final String? lastModifiedByUser;
  final String? groupId;
  final int? version;

  ComboData({
    this.id,
    this.code,
    this.name,
    this.shortName,
    this.startDate,
    this.endDate,
    this.barCode,
    this.applicationId,
    this.description,
    this.status,
    this.comboPacketType,
    this.order,
    this.isHot,
    this.priceBeforeVAT,
    this.discountPercent,
    this.amountBeforeVAT,
    this.vatPercent,
    this.priceAfterVAT,
    this.totalAmountAfterVAT,
    this.createdOnDate,
    this.createdByUser,
    this.lastModifiedOnDate,
    this.lastModifiedByUser,
    this.groupId,
    this.version,
  });

  factory ComboData.fromJson(Map<String, dynamic> json) {
    return ComboData(
      id: json['Id'],
      code: json['Code'],
      name: json['Name'],
      shortName: json['ShortName'],
      startDate: json['StartDate'],
      endDate: json['EndDate'],
      barCode: json['BarCode'],
      applicationId: json['ApplicationId'],
      description: json['Description'],
      status: json['Status'],
      comboPacketType: json['ComboPacketType'],
      order: json['Order'],
      isHot: json['IsHot'],
      priceBeforeVAT: json['PriceBeforeVAT'],
      discountPercent: json['DiscountPercent'],
      amountBeforeVAT: json['AmountBeforeVAT'],
      vatPercent: json['VATPercent'],
      priceAfterVAT: json['PriceAfterVAT'],
      totalAmountAfterVAT: json['TotalAmountAfterVAT'],
      createdOnDate: json['CreatedOnDate'],
      createdByUser: json['CreatedByUser'],
      lastModifiedOnDate: json['LastModifiedOnDate'],
      lastModifiedByUser: json['LastModifiedByUser'],
      groupId: json['GroupId'],
      version: json['Version'],
    );
  }
}

/// Tương ứng với ItemInCombo trong iOS
class ItemInCombo {
  final String? id;
  final String? comboPacketId;
  final String? itemId;
  final int? quantity;
  final bool? isItem;
  final dynamic description;
  final int? priceInCombo;
  final int? order;
  final int? priceBeforeVAT;
  final dynamic discountPercent;
  final dynamic amountBeforeVAT;
  final int? vatPercent;
  final int? priceAfterVAT;
  final dynamic totalAmountAfterVAT;
  final bool? isTicketType;

  ItemInCombo({
    this.id,
    this.comboPacketId,
    this.itemId,
    this.quantity,
    this.isItem,
    this.description,
    this.priceInCombo,
    this.order,
    this.priceBeforeVAT,
    this.discountPercent,
    this.amountBeforeVAT,
    this.vatPercent,
    this.priceAfterVAT,
    this.totalAmountAfterVAT,
    this.isTicketType,
  });

  factory ItemInCombo.fromJson(Map<String, dynamic> json) {
    return ItemInCombo(
      id: json['Id'],
      comboPacketId: json['Combo_Packet_Id'],
      itemId: json['Item_Id'],
      quantity: json['Quantity'],
      isItem: json['IsItem'],
      description: json['Description'],
      priceInCombo: json['PriceInCombo'],
      order: json['Order'],
      priceBeforeVAT: json['PriceBeforeVAT'],
      discountPercent: json['DiscountPercent'],
      amountBeforeVAT: json['AmountBeforeVAT'],
      vatPercent: json['VATPercent'],
      priceAfterVAT: json['PriceAfterVAT'],
      totalAmountAfterVAT: json['TotalAmountAfterVAT'],
      isTicketType: json['IsTicketType'],
    );
  }
}
