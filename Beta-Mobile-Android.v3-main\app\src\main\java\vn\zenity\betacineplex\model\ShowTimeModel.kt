package vn.zenity.betacineplex.model

import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.global.Constant

class ShowTimeModel {
    var FilmId = "4632f65c-3c44-4fdb-9956-37d884e211b8"
    var FilmName = "Đặc Vụ Bất Chấp"
    var TenRap = "Beta Mỹ Đình"
    var PhongChieu = "Phòng 1"
    var NgayChieu = "2018-05-02T00:00:00"
    var GioChieu = "2018-05-02T10:45:00"
    var MainPosterUrl: String? = null
    var TrailerUrl: String? = null
    var Duration = 0
    var FilmFormat = "2D Lồng tiếng"
    var Screen: FilmScreenModel? = null
    var SeatSolded = 6
    var TotalSeat = 156
    var Description: String? = null
    var Director: String? = null
    var Actors: String? = null
    var MainLanguage: String? = null
    var mapSeat: HashMap<Pair<Int, Int>, SeatScreenModel> = hashMapOf()
    var mapSeatIndex: HashMap<Int, Pair<Int, Int>> = hashMapOf()
    var mapNumberNotUsed = hashMapOf<Int, Int>()
    var TicketTypes: List<TicketTypes> = listOf()
    var FilmFormatCode: String? = null
    var FilmFormatName: String? = null
        get() {
            if (!App.shared().isLangVi() && FilmFormatName_F != null) {
                return FilmFormatName_F
            }
            return field
        }
    var FilmFormatName_F: String? = null
}

class FilmScreenModel {
    var ScreenId = "60db7558-2445-4f85-92e1-5f4a895fbe87"
    var Code = "Phòng 1"
    var CinemaId = "00000000-0000-0000-0000-000000000000"
    var SeatPosition: ArrayList<ArrayList<SeatScreenModel>>? = null
    var NumberRow = 13
    var NumberCol = 12
    var TotalSeat = 156
}

class SeatScreenModel {
    var SeatName = "D12"
    var Status: SeatStatusModel? = null
    var SeatType: SeatTypeModel? = null
    var ClassStyle = ""
    var SeatIndex = 0
    var rowIndex: Int? = null
    var columnIndex: Int? = null
    var SoldStatus: Int = Constant.SeatSoldStatus.EMPTY
    var ticketType: TicketTypes? = null
    var isShowDouble = false
    fun setTicketTypes(ticketTypes: List<TicketTypes>?) {
        ticketTypes?.let {
            ticketType = it.firstOrNull { it.SeatTypeId == SeatType?.Value }
        }
    }
}

class SeatStatusModel {
    var Name = "Đang sử dụng"
    var Value = "1"
    var Class = "seat-used" //seat-not-used
}

class SeatTypeModel {
    var Name = "Ghế thường"
    var Value = "c0f1e9a8-c9f5-4b0d-8b10-f3108996e60b"
    var Background = "#848484"
}

class TicketTypes {
    var PriceCardId: String? = null
    var TicketTypeId: String? = null
    var Price: Int = 0
    var TicketClassId: String? = null
    var IsPackage: Boolean = false
    var SeatTypeId: String? = null
    var ObjectId: String? = null
    var FimlFormatId: String? = null
    var VAT: Int = 0
    var Name: String? = null
    var Code: String? = null
    var Status: Boolean = false
    var PriceTicket: Int = 0
    var PriceBeforeVat: Int = 0
}