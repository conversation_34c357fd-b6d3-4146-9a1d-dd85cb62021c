import Foundation

@objc public class SignalRClient: NSObject {
    // MARK: - Properties

    // Connection properties
    private var connection: URLSessionWebSocketTask?
    private var session: URLSession?
    private var connectionId: String?
    private var isConnected: Bool = false
    private var hubName: String = "chooseSeatHub"
    private var baseUrl: String = ""
    private var reconnectTimer: Timer?
    private var methodHandlers: [String: (([Any]) -> Void)] = [:]

    // Callback for connection state changes
    private var connectionStateCallback: ((Bool) -> Void)?

    // Callback for receiving messages
    private var messageCallback: ((String, String, Int, Int) -> Void)?

    // Enable logging
    private let enableLogging: Bool

    // MARK: - Initialization

    @objc public init(hubName: String = "chooseSeatHub", enableLogging: Bool = true) {
        self.hubName = hubName
        self.enableLogging = enableLogging
        super.init()
        self.session = URLSession(configuration: .default, delegate: nil, delegateQueue: .main)
        self.log("SignalRClient initialized with hub: \(hubName)")
    }

    // MARK: - Logging

    private func log(_ message: String) {
        if enableLogging {
            print("SignalR: \(message)")
        }
    }

    // MARK: - Connection Management

    /// Connect to a SignalR hub
    /// - Parameters:
    ///   - url: The URL of the SignalR hub
    ///   - headers: Optional headers to include in the connection
    ///   - completion: Callback with connection result
    @objc public func connect(url: String, headers: [String: String], completion: @escaping (Bool, String?) -> Void) {
        // Close existing connection if any
        disconnect()

        self.baseUrl = url
        self.log("Connecting to URL: \(url)")

        // First, we need to negotiate with the SignalR server to get a connection ID
        negotiate(url: url, headers: headers) { [weak self] success, connectionId, error in
            guard let self = self else { return }

            if success, let connectionId = connectionId {
                self.log("Negotiation successful, connection ID: \(connectionId)")
                self.connectionId = connectionId
                self.connectWebSocket(url: url, connectionId: connectionId, headers: headers, completion: completion)
            } else {
                self.log("Negotiation failed: \(error ?? "Unknown error")")
                completion(false, error)
            }
        }
    }

    /// Negotiate with the SignalR server to get a connection ID
    /// - Parameters:
    ///   - url: The base URL of the SignalR hub
    ///   - headers: Headers to include in the request
    ///   - completion: Callback with negotiation result
    private func negotiate(url: String, headers: [String: String], completion: @escaping (Bool, String?, String?) -> Void) {
        // Create the negotiate URL
        let negotiateUrl = "\(url)/negotiate?clientProtocol=1.5&connectionData=%5B%7B%22name%22%3A%22\(hubName)%22%7D%5D"

        self.log("Negotiating with URL: \(negotiateUrl)")

        guard let url = URL(string: negotiateUrl) else {
            self.log("Invalid negotiate URL")
            completion(false, nil, "Invalid URL")
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "GET"

        // Add headers
        for (key, value) in headers {
            request.setValue(value, forHTTPHeaderField: key)
            self.log("Adding header: \(key)=\(value.prefix(20))...")
        }

        let task = URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }

            if let error = error {
                self.log("Negotiation error: \(error.localizedDescription)")
                completion(false, nil, error.localizedDescription)
                return
            }

            if let httpResponse = response as? HTTPURLResponse {
                self.log("Negotiation HTTP status: \(httpResponse.statusCode)")
            }

            guard let data = data else {
                self.log("No data received from negotiation")
                completion(false, nil, "No data received")
                return
            }

            if let responseString = String(data: data, encoding: .utf8) {
                self.log("Negotiation response: \(responseString)")
            }

            do {
                if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                    if let connectionId = json["ConnectionId"] as? String {
                        self.log("Connection ID from negotiation: \(connectionId)")
                        completion(true, connectionId, nil)
                    } else {
                        self.log("No ConnectionId in response")
                        completion(false, nil, "Invalid response format")
                    }
                } else {
                    self.log("Failed to parse JSON")
                    completion(false, nil, "Invalid response format")
                }
            } catch {
                self.log("JSON parsing error: \(error.localizedDescription)")
                completion(false, nil, "JSON parsing error: \(error.localizedDescription)")
            }
        }

        task.resume()
    }

    /// Connect to the SignalR hub using WebSockets
    /// - Parameters:
    ///   - url: The base URL of the SignalR hub
    ///   - connectionId: The connection ID from negotiation
    ///   - headers: Headers to include in the request
    ///   - completion: Callback with connection result
    private func connectWebSocket(url: String, connectionId: String, headers: [String: String], completion: @escaping (Bool, String?) -> Void) {
        // Create the WebSocket URL
        let wsUrl = url.replacingOccurrences(of: "http://", with: "ws://")
                      .replacingOccurrences(of: "https://", with: "wss://")

        let encodedConnectionId = connectionId.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? connectionId
        let webSocketUrl = "\(wsUrl)/connect?transport=webSockets&clientProtocol=1.5&connectionToken=\(encodedConnectionId)&connectionData=%5B%7B%22name%22%3A%22\(hubName)%22%7D%5D"

        self.log("Connecting WebSocket to URL: \(webSocketUrl)")

        guard let url = URL(string: webSocketUrl) else {
            self.log("Invalid WebSocket URL")
            completion(false, "Invalid WebSocket URL")
            return
        }

        var request = URLRequest(url: url)

        // Add headers
        for (key, value) in headers {
            request.setValue(value, forHTTPHeaderField: key)
            self.log("Adding header: \(key)=\(value.prefix(20))...")
        }

        let webSocketTask = session?.webSocketTask(with: request)
        self.connection = webSocketTask

        // Start receiving messages
        self.receiveMessage()

        // Start the connection
        webSocketTask?.resume()

        self.isConnected = true
        self.log("WebSocket connection started with ID: \(connectionId)")
        completion(true, connectionId)

        // Notify connection state change
        DispatchQueue.main.async {
            self.connectionStateCallback?(true)
        }

        // Start heartbeat to keep the connection alive
        self.startHeartbeat()
    }

    /// Receive messages from the WebSocket
    private func receiveMessage() {
        connection?.receive { [weak self] result in
            guard let self = self else { return }

            switch result {
            case .success(let message):
                switch message {
                case .string(let text):
                    self.log("Received WebSocket message: \(text.prefix(100))...")
                    self.handleMessage(text)
                case .data(let data):
                    if let text = String(data: data, encoding: .utf8) {
                        self.log("Received WebSocket data message: \(text.prefix(100))...")
                        self.handleMessage(text)
                    } else {
                        self.log("Received WebSocket data that couldn't be converted to string")
                    }
                @unknown default:
                    self.log("Received unknown WebSocket message type")
                    break
                }

                // Continue receiving messages
                self.receiveMessage()

            case .failure(let error):
                self.log("WebSocket receive error: \(error)")
                self.isConnected = false

                // Notify connection state change on main thread
                DispatchQueue.main.async {
                    self.connectionStateCallback?(false)
                }

                // Try to reconnect
                self.startReconnectTimer()
            }
        }
    }

    /// Handle messages received from the WebSocket
    /// - Parameter message: The message received
    private func handleMessage(_ message: String) {
        // Parse the message
        guard let data = message.data(using: .utf8) else {
            self.log("Failed to convert message to data")
            return
        }

        do {
            guard let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] else {
                self.log("Failed to parse message as JSON")
                return
            }

            // Kiểm tra xem có phải là tin nhắn hub không
            if let messageType = json["M"] as? [[String: Any]] {
                for hubMessage in messageType {
                    if let method = hubMessage["M"] as? String {
                        let hubName = hubMessage["H"] as? String ?? self.hubName
                        let args = hubMessage["A"] as? [Any] ?? []

                        self.log("Tin nhắn hub: hub=\(hubName), method=\(method), args=\(args)")

                        // Kiểm tra xem chúng ta có handler cho phương thức này không
                        if (method == "broadcastMessage" || method == "UpdateSeat") && args.count >= 4 {
                            // Thử nhiều định dạng tham số khác nhau
                            if let connectionId = args[0] as? String,
                               let showId = args[1] as? String,
                               let seatIndexStr = args[2] as? String,
                               let statusStr = args[3] as? String,
                               let seatIndex = Int(seatIndexStr),
                               let status = Int(statusStr) {

                                self.log("Tin nhắn broadcast: connectionId=\(connectionId), showId=\(showId), seatIndex=\(seatIndex), status=\(status)")

                                // Gọi callback tin nhắn trên main thread
                                DispatchQueue.main.async {
                                    self.messageCallback?(connectionId, showId, seatIndex, status)
                                }
                            } else if let seatIndex = args[0] as? Int,
                                     let status = args[1] as? Int,
                                     let showId = args.count > 2 ? args[2] as? String : "unknown" {

                                let connectionId = "server"
                                self.log("Tin nhắn broadcast (định dạng 2): seatIndex=\(seatIndex), status=\(status), showId=\(showId)")

                                // Gọi callback tin nhắn trên main thread
                                DispatchQueue.main.async {
                                    self.messageCallback?(connectionId, showId, seatIndex, status)
                                }
                            } else {
                                self.log("Tham số tin nhắn broadcast không hợp lệ: \(args)")
                            }
                        }

                        // Call any registered handlers for this method
                        if let handler = methodHandlers[method] {
                            self.log("Calling handler for method: \(method)")
                            DispatchQueue.main.async {
                                handler(args)
                            }
                        } else {
                            self.log("No handler registered for method: \(method)")
                        }
                    }
                }
            }
        } catch {
            self.log("Error parsing message: \(error.localizedDescription)")
        }
    }

    /// Start a heartbeat timer to keep the connection alive
    private func startHeartbeat() {
        self.log("Starting heartbeat timer")

        // Send a heartbeat every 30 seconds to keep the connection alive
        Timer.scheduledTimer(withTimeInterval: 30, repeats: true) { [weak self] _ in
            guard let self = self, self.isConnected else { return }

            let heartbeat = "{\"H\":\"signalr\",\"M\":\"keepAlive\",\"I\":0}"
            self.log("Sending heartbeat")

            self.connection?.send(.string(heartbeat)) { error in
                if let error = error {
                    self.log("Heartbeat error: \(error)")
                } else {
                    self.log("Heartbeat sent successfully")
                }
            }
        }
    }

    /// Start a timer to reconnect to the SignalR hub
    private func startReconnectTimer() {
        self.log("Starting reconnect timer")

        reconnectTimer?.invalidate()
        reconnectTimer = Timer.scheduledTimer(withTimeInterval: 5, repeats: false) { [weak self] _ in
            guard let self = self, !self.isConnected else { return }

            // Try to reconnect
            self.log("Attempting to reconnect")

            self.connect(url: self.baseUrl, headers: [:]) { success, error in
                if success {
                    self.log("Reconnected successfully")
                } else {
                    self.log("Reconnection failed: \(error ?? "Unknown error")")
                    // Try again later
                    self.startReconnectTimer()
                }
            }
        }
    }

    /// Disconnect from the SignalR hub
    @objc public func disconnect() {
        self.log("Disconnecting from SignalR hub")

        // Cancel reconnect timer
        reconnectTimer?.invalidate()
        reconnectTimer = nil

        if isConnected {
            // Cancel the WebSocket connection
            connection?.cancel(with: .goingAway, reason: nil)
            connection = nil
            isConnected = false

            // Notify connection state change on main thread
            DispatchQueue.main.async {
                self.connectionStateCallback?(false)
            }

            self.log("Disconnected from SignalR hub")
        } else {
            self.log("Already disconnected from SignalR hub")
        }
    }

    /// Invoke a method on the SignalR hub
    /// - Parameters:
    ///   - method: The name of the method to invoke
    ///   - arguments: The arguments to pass to the method
    ///   - completion: Callback with the result
    @objc public func invoke(method: String, arguments: [Any], completion: @escaping (Any?, String?) -> Void) {
        guard isConnected, let connectionId = connectionId else {
            self.log("Cannot invoke method \(method): Not connected")
            completion(nil, "Not connected")
            return
        }

        self.log("Invoking method: \(method) with arguments: \(arguments)")

        // Special handling for method names - convert to correct case for the server
        // SignalR is case-sensitive and the server expects specific method names
        let serverMethod: String
        switch method {
        case "JoinGroup", "joinGroup":
            // Trong repo iOS, phương thức được gọi là "JoinGroup" (không phải "Join")
            serverMethod = "JoinGroup"
        case "LeaveGroup", "leaveGroup":
            serverMethod = "LeaveGroup"
        case "sendMessage", "SendMessage":
            // Trong repo iOS, phương thức được gọi là "sendMessage" (không phải "Send")
            serverMethod = "sendMessage"
        default:
            serverMethod = method
        }

        self.log("Chuyển đổi tên phương thức từ '\(method)' thành '\(serverMethod)' cho máy chủ")

        // Create the invoke message
        let invocationId = UUID().uuidString

        var messageDict: [String: Any] = [
            "H": hubName,
            "M": serverMethod,
            "I": invocationId,
            "A": arguments
        ]

        do {
            let messageData = try JSONSerialization.data(withJSONObject: messageDict, options: [])
            if let messageString = String(data: messageData, encoding: .utf8) {
                self.log("Sending message: \(messageString)")

                connection?.send(.string(messageString)) { error in
                    if let error = error {
                        self.log("Lỗi khi gọi phương thức: \(error.localizedDescription)")
                        completion(nil, error.localizedDescription)
                    } else {
                        self.log("Phương thức gọi thành công: \(serverMethod)")

                        // Thêm một khoảng thời gian chờ ngắn để đảm bảo máy chủ có thời gian xử lý
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            completion(nil, nil)
                        }
                    }
                }
            } else {
                self.log("Failed to create message string")
                completion(nil, "Failed to create message string")
            }
        } catch {
            self.log("JSON serialization error: \(error.localizedDescription)")
            completion(nil, "JSON serialization error: \(error.localizedDescription)")
        }
    }

    /// Register a callback for a SignalR hub method
    /// - Parameters:
    ///   - method: The name of the method to register for
    ///   - callback: The callback to invoke when the method is called
    @objc public func on(method: String, callback: @escaping ([Any]) -> Void) {
        self.log("Registering handler for method: \(method)")
        methodHandlers[method] = callback
    }

    /// Unregister a callback for a SignalR hub method
    /// - Parameter method: The name of the method to unregister
    @objc public func off(method: String) {
        self.log("Unregistering handler for method: \(method)")
        methodHandlers.removeValue(forKey: method)
    }

    /// Set a callback for connection state changes
    /// - Parameter callback: The callback to invoke when the connection state changes
    @objc public func setConnectionStateCallback(_ callback: @escaping (Bool) -> Void) {
        self.log("Setting connection state callback")
        connectionStateCallback = callback

        // Call the callback with the current state
        callback(isConnected)
    }

    /// Set a callback for receiving messages
    /// - Parameter callback: The callback to invoke when a message is received
    @objc public func setMessageCallback(_ callback: @escaping (String, String, Int, Int) -> Void) {
        self.log("Setting message callback")
        messageCallback = callback
    }

    /// Get the connection ID
    /// - Returns: The connection ID, or nil if not connected
    @objc public func getConnectionId() -> String? {
        return connectionId
    }

    /// Check if connected
    /// - Returns: true if connected, false otherwise
    @objc public func isConnectedStatus() -> Bool {
        return isConnected
    }
}
