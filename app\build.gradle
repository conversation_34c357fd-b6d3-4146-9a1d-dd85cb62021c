apply plugin: 'com.android.application'

apply plugin: 'kotlin-android'

apply plugin: 'kotlin-android-extensions'

apply plugin: 'kotlin-kapt'

buildscript {
    ext.kotlin_version = '1.6.21'
    repositories {
        google()
        maven { url "https://jitpack.io" }
        mavenCentral()
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

android {

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    compileSdkVersion rootProject.ext.compileSdkVersion

    testBuildType "dev"

    defaultConfig {
        applicationId "com.beta.betacineplex"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 48
        versionName "2.7.6"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables.useSupportLibrary = true
        multiDexEnabled true

        renderscriptTargetApi 19

        renderscriptSupportModeEnabled true
    }

    signingConfigs {
        devconfig {
            keyAlias 'debug'
            keyPassword 'sdfoafojasdfji'
            storeFile file('../keystore/debug.keystore')
            storePassword 'sdfoafojasdfji'
        }

        cusconfig {
            keyAlias 'customer'
            keyPassword 'sdiidfjieiurier'
            storeFile file('../keystore/customer.keystore')
            storePassword 'sdiidfjieiurier'
        }

        prodconfig {
            keyAlias 'beta cineplex'
            keyPassword 'Betacorpvn@123'
            storeFile file('../keystore/beta_cineplex_app_key.jks')
            storePassword 'Betacorpvn@123'
        }
    }

    lintOptions {

        checkReleaseBuilds false

    }

    buildTypes {

        dev {
            applicationIdSuffix = ".dev"
            resValue "string", "app_name", "Beta Cineplex Dev"
            resValue "string", "facebook_app_id", "367174740769877"
            resValue "string", "facebook_client_token", "********************************"
            resValue "string", "fb_login_protocol_scheme", "fb367174740769877"
            resValue "string", "google_maps_key", "AIzaSyDtmexSsZiIT9duN3nRPH39pIpEESu3ps8"
            buildConfigField "String", "BASE_URL", '"http://dev.api.betacorp.vn/"'
            buildConfigField "String", "SHARE_DOMAIN", '"http://dev.cinemas.betacorp.vn"'
            buildConfigField "String", "HOST_FILE", '"http://dev.files.tms.betacorp.vn/"'
            buildConfigField "String", "AVATAR_HOST_FILE", '"http://dev.api.betacorp.vn/files/"'
            buildConfigField "String", "RE_CAPTCHA_KEY", '"6Lfl1rQUAAAAAOMR12WPXuj0CWf9PsJ9QujEkdS8"'
            buildConfigField "String", "MIXPANEL_TOKEN", '"7c957cceb74932260d4c3f8d63bdf590"'
            debuggable true
            signingConfig signingConfigs.devconfig
        }

        customer {
            applicationIdSuffix = ".customer"
            resValue "string", "app_name", "Beta Cinemas Test"
            resValue "string", "facebook_app_id", "367174740769877"
            resValue "string", "facebook_client_token", "********************************"
            resValue "string", "fb_login_protocol_scheme", "fb367174740769877"
            resValue "string", "google_maps_key", "AIzaSyDtmexSsZiIT9duN3nRPH39pIpEESu3ps8"
            buildConfigField "String", "BASE_URL", '"http://dev.api.betacorp.vn/"'
            buildConfigField "String", "SHARE_DOMAIN", '"http://dev.cinemas.betacorp.vn"'
            buildConfigField "String", "HOST_FILE", '"http://dev.files.tms.betacorp.vn/"'
            buildConfigField "String", "AVATAR_HOST_FILE", '"http://dev.api.betacorp.vn/files/"'
            buildConfigField "String", "RE_CAPTCHA_KEY", '"6Lfl1rQUAAAAAOMR12WPXuj0CWf9PsJ9QujEkdS8"'
            buildConfigField "String", "MIXPANEL_TOKEN", '"7c957cceb74932260d4c3f8d63bdf590"'
            debuggable true
            signingConfig signingConfigs.cusconfig
        }

        tprod {
            resValue "string", "app_name", "Test Beta Cinemas"
            resValue "string", "facebook_app_id", "367174740769877"
            resValue "string", "facebook_client_token", "********************************"
            resValue "string", "fb_login_protocol_scheme", "fb367174740769877"
            resValue "string", "google_maps_key", "AIzaSyDtmexSsZiIT9duN3nRPH39pIpEESu3ps8"
            buildConfigField "String", "BASE_URL", '"https://api.betacorp.vn/"'
            buildConfigField "String", "SHARE_DOMAIN", '"https://betacinemas.vn"'
            buildConfigField "String", "HOST_FILE", '"https://files.betacorp.vn/files/"'
            buildConfigField "String", "AVATAR_HOST_FILE", '"https://files.betacorp.vn/files/"'
            buildConfigField "String", "RE_CAPTCHA_KEY", '"6Ldl0bcUAAAAAHqvCDnPNwGaTpMXZr7DKZ4tUJ9t"'
            buildConfigField "String", "MIXPANEL_TOKEN", '"f9846dae54c28e43efcc22bee8df21f1"'
            minifyEnabled false
            debuggable true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.cusconfig
        }

        prod {
            resValue "string", "app_name", "Beta Cinemas"
            resValue "string", "facebook_app_id", "367174740769877"
            resValue "string", "facebook_client_token", "********************************"
            resValue "string", "fb_login_protocol_scheme", "fb367174740769877"
            resValue "string", "google_maps_key", "AIzaSyDtmexSsZiIT9duN3nRPH39pIpEESu3ps8"
            buildConfigField "String", "BASE_URL", '"https://api.betacorp.vn/"'
            buildConfigField "String", "SHARE_DOMAIN", '"https://betacinemas.vn"'
            buildConfigField "String", "HOST_FILE", '"https://files.betacorp.vn/files/"'
            buildConfigField "String", "AVATAR_HOST_FILE", '"https://files.betacorp.vn/files/"'
            buildConfigField "String", "RE_CAPTCHA_KEY", '"6Ldl0bcUAAAAAHqvCDnPNwGaTpMXZr7DKZ4tUJ9t"'
            buildConfigField "String", "MIXPANEL_TOKEN", '"f9846dae54c28e43efcc22bee8df21f1"'
            minifyEnabled false
            debuggable true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.prodconfig
        }
    }

    // Remove debug and release as it's not needed.
    android.variantFilter { variant ->
        if (variant.buildType.name == 'debug' || variant.buildType.name == 'release') {
            variant.setIgnore(true)
        }
    }
    // Always show the result of every unit test, even if it passes.
    testOptions.unitTests.all {
        testLogging {
            events 'passed', 'skipped', 'failed', 'standardOut', 'standardError'
        }
    }

    packagingOptions {
        exclude 'META-INF/proguard/androidx-annotations.pro'
        exclude 'lib/getLibs.ps1'
        exclude 'lib/getLibs.sh'
        exclude 'lib/gson-2.2.2.jar'
    }
}

ext.VERSION_PLAY_SERVICES = "17.0.0"

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'androidx.appcompat:appcompat:1.0.2'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'com.google.android.material:material:1.0.0'
    implementation 'androidx.vectordrawable:vectordrawable:1.0.1'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    testImplementation "junit:junit:$rootProject.junitVersion"
    androidTestImplementation 'androidx.test:runner:1.1.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'
    androidTestImplementation "junit:junit:$rootProject.ext.junitVersion"

    //multidex
    implementation 'androidx.multidex:multidex:2.0.1'

    //database

    // ViewModel and LiveData
    implementation 'androidx.lifecycle:lifecycle-extensions:2.0.0'
    annotationProcessor 'androidx.lifecycle:lifecycle-compiler:2.0.0'

    // Room
    implementation "androidx.room:room-runtime:2.4.3"
    kapt  "androidx.room:room-compiler:2.4.3"

    // Paging
    implementation 'androidx.paging:paging-runtime:2.0.0'

    // Test helpers for LiveData
    testImplementation 'androidx.arch.core:core-testing:2.0.0'

    // Test helpers for Room
    testImplementation 'androidx.room:room-testing:2.3.0'

    //Networking
    implementation "com.squareup.retrofit2:retrofit:$rootProject.ext.retrofitVersion"
    implementation "com.squareup.retrofit2:converter-gson:$rootProject.ext.retrofitVersion"
    implementation "com.squareup.retrofit2:adapter-rxjava2:$rootProject.ext.retrofitVersion"
    implementation "com.squareup.okhttp3:logging-interceptor:$rootProject.ext.okHttpVersion"
    implementation "com.squareup.okhttp3:okhttp:$rootProject.ext.okHttpVersion"
    implementation "io.reactivex.rxjava2:rxandroid:$rootProject.ext.rxAndroidVersion"
    implementation "io.reactivex.rxjava2:rxjava:$rootProject.ext.rxJavaVersion"
    implementation "io.reactivex.rxjava2:rxkotlin:$rootProject.ext.rxKotlinVersion"

    //Image
    implementation "com.github.bumptech.glide:glide:$rootProject.ext.glideVersion"
    implementation("com.github.bumptech.glide:okhttp3-integration:$rootProject.ext.glideOkHttpVersion") {
        exclude group: 'glide-parent'
    }

    //Refresh layout
    implementation "com.lcodecorex:tkrefreshlayout:$rootProject.ext.tkRefreshLayoutVersion"

    //Section RecyclerView
    implementation 'com.afollestad:sectioned-recyclerview:0.5.0'

    //Encryption
    implementation "org.bouncycastle:bcprov-jdk16:$rootProject.ext.bouncyCastleVersion"
    implementation 'androidx.legacy:legacy-support-core-utils:1.0.0'
    implementation("com.facebook.android:facebook-login:$rootProject.ext.facebookSdkVersion") {
        exclude group: 'com.android.support', module: 'support-v4'
        exclude group: 'com.android.support', module: 'appcompat-v7'

        implementation 'com.yarolegovich:discrete-scrollview:1.3.2'

        //facebook sdk
        implementation "com.android.support:cardview-v7:$rootProject.ext.supportLibraryVersion"
        implementation "com.android.support:customtabs:$rootProject.ext.supportLibraryVersion"
        implementation "com.android.support:support-annotations:$rootProject.ext.supportLibraryVersion"
        exclude group: 'com.android.support', module: 'cardview-v7'
        exclude group: 'com.android.support', module: 'customtabs'
        exclude group: 'com.android.support', module: 'support-annotations'
        exclude group: 'com.android.support', module: 'support-core-utils'
    }

    // ViewModel and LiveData
    implementation 'androidx.lifecycle:lifecycle-extensions:2.0.0'

    // alternatively, just ViewModel
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.0.0'

    //Room
    implementation project(':image-picker')

    //Image view
    implementation "de.hdodenhof:circleimageview:$rootProject.ext.circleImageVerion"
    implementation "com.makeramen:roundedimageview:$rootProject.ext.roundImageVersion"
    implementation files('libs/nineoldandroids-library-2.4.0.jar')
    implementation "com.google.android.gms:play-services-maps:$VERSION_PLAY_SERVICES"
    implementation "com.google.android.gms:play-services-location:$VERSION_PLAY_SERVICES"
    implementation 'com.facebook.android:facebook-android-sdk:4.31.0'
    implementation 'com.thoughtbot:expandablerecyclerview:1.3'
    implementation files('libs/YouTubeAndroidPlayerApi.jar')
    implementation 'com.github.drawers:SpinnerDatePicker:2.1.0'
    implementation files('libs/signalr-client-sdk.jar')
    implementation files('libs/signalr-client-sdk-android.jar')

    implementation platform('com.google.firebase:firebase-bom:30.4.1')
    implementation 'com.google.firebase:firebase-messaging:23.0.8'
    implementation 'com.google.firebase:firebase-core:21.1.1'

    implementation 'org.jsoup:jsoup:1.8.3'

    implementation 'com.budiyev.android:code-scanner:2.1.0'

    implementation 'me.relex:circleindicator:2.1.6'
    implementation "com.google.android.gms:play-services-safetynet:$VERSION_PLAY_SERVICES"

    implementation 'com.mixpanel.android:mixpanel-android:6.+'

    implementation 'androidx.work:work-runtime-ktx:2.7.0'
    implementation 'com.github.techvein:okhttp-curl-logging:1.0.4'
}

apply plugin: 'com.google.gms.google-services'
