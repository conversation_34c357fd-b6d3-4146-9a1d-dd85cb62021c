{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98563b3a7d9a51b5f370173bff1f571821", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_inappwebview_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_inappwebview_ios", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/ResourceBundle-flutter_inappwebview_ios_privacy-flutter_inappwebview_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "flutter_inappwebview_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e989f4016c9662a3785ecde8e31151882a0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833599cb5ac423a027e1bf0852fb36848", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_inappwebview_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_inappwebview_ios", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/ResourceBundle-flutter_inappwebview_ios_privacy-flutter_inappwebview_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "flutter_inappwebview_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98993306e865a317c3abbaaf878113f2db", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833599cb5ac423a027e1bf0852fb36848", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_inappwebview_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_inappwebview_ios", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/ResourceBundle-flutter_inappwebview_ios_privacy-flutter_inappwebview_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "flutter_inappwebview_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9850ff56ff85f4df14fdbcab0a1b74c5df", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981ccbc8bcca449321be2305a312843b8b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98de2f7c5d2f988619748f730e4a4ea1d3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4db296ec9dc59cbd048f343c559143c", "guid": "bfdfe7dc352907fc980b868725387e98f8d766e2f2c534eb892fadbd92d8a73a"}], "guid": "bfdfe7dc352907fc980b868725387e988466361d2f1606591f0083b5d597342b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b", "name": "flutter_inappwebview_ios-flutter_inappwebview_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f6265c1f1f4bdacc539aa5c84be2b7a5", "name": "flutter_inappwebview_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}