<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3dcc8e13-70a4-40e5-88b4-1a798df76dc0" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.dart_tool/extension_discovery/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/extension_discovery/devtools.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/extension_discovery/vs_code.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/.filecache" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/App.framework/App" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/app.dill" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/debug_ios_bundle_flutter_assets.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/debug_universal_framework.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/debug_unpack_ios.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/flutter_assets.d" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/gen_dart_plugin_registrant.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/gen_localizations.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/kernel_snapshot.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/kernel_snapshot_native_assets.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/kernel_snapshot_program.d" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/kernel_snapshot_program.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/native_assets.d" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/native_assets.dill" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/native_assets.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/native_assets.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/outputs.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/0746c26bf4aa7a231c3373a2fe8468c5/program.dill" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/.filecache" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/app.dill" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/debug_android_application.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/flutter_assets.d" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/gen_dart_plugin_registrant.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/gen_localizations.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/kernel_snapshot.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/kernel_snapshot_native_assets.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/kernel_snapshot_program.d" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/kernel_snapshot_program.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/native_assets.d" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/native_assets.dill" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/native_assets.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/native_assets.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/outputs.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/31565c9ce642619c545b823ae975f026/program.dill" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/.filecache" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/App.framework/App" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/app.dill" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/debug_ios_bundle_flutter_assets.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/debug_universal_framework.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/debug_unpack_ios.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/flutter_assets.d" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/gen_dart_plugin_registrant.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/gen_localizations.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/kernel_snapshot.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/kernel_snapshot_native_assets.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/kernel_snapshot_program.d" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/kernel_snapshot_program.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/native_assets.d" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/native_assets.dill" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/native_assets.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/native_assets.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/outputs.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/5a4c65df6e3bdea8453fa40353d5eaae/program.dill" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/.filecache" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/App.framework/App" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/app.dill" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/debug_ios_bundle_flutter_assets.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/debug_universal_framework.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/debug_unpack_ios.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/flutter_assets.d" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/gen_dart_plugin_registrant.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/gen_localizations.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/kernel_snapshot.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/kernel_snapshot_native_assets.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/kernel_snapshot_program.d" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/kernel_snapshot_program.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/native_assets.d" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/native_assets.dill" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/native_assets.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/native_assets.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/outputs.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/d0d60842d93ada08c1d1182901333519/program.dill" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/flutter_build/dart_plugin_registrant.dart" beforeDir="false" afterPath="$PROJECT_DIR$/.dart_tool/flutter_build/dart_plugin_registrant.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/package_config.json" beforeDir="false" afterPath="$PROJECT_DIR$/.dart_tool/package_config.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.dart_tool/package_config_subset" beforeDir="false" afterPath="$PROJECT_DIR$/.dart_tool/package_config_subset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.flutter-plugins" beforeDir="false" afterPath="$PROJECT_DIR$/.flutter-plugins" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.flutter-plugins-dependencies" beforeDir="false" afterPath="$PROJECT_DIR$/.flutter-plugins-dependencies" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/libraries/Dart_Packages.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/libraries/Dart_Packages.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/libraries/Flutter_Plugins.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/libraries/Flutter_Plugins.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/android/app/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/android/app/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/android/app/libs/signalr-client-sdk-android.jar" beforeDir="false" afterPath="$PROJECT_DIR$/android/app/libs/signalr-client-sdk-android.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/android/app/libs/signalr-client-sdk.jar" beforeDir="false" afterPath="$PROJECT_DIR$/android/app/libs/signalr-client-sdk.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/android/app/src/main/kotlin/com/betacineplex/SignalRPlugin.kt" beforeDir="false" afterPath="$PROJECT_DIR$/android/app/src/main/kotlin/com/betacineplex/SignalRPlugin.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/android/app/src/main/kotlin/com/flutter/flutter/flutter_app/MainActivity.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/450bafda7a2c70ce72787fca66c10593/_composite.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/450bafda7a2c70ce72787fca66c10593/gen_dart_plugin_registrant.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/450bafda7a2c70ce72787fca66c10593/gen_localizations.stamp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/AssetManifest.bin" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/AssetManifest.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/AssetManifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/AssetManifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/FontManifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/FontManifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/NOTICES.Z" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/NOTICES.Z" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/assets/fonts/Oswald/Oswald-Light.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/assets/translations/en.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/assets/translations/en.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/assets/translations/vi.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/assets/translations/vi.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/kernel_blob.bin" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/assets/debug/flutter_assets/kernel_blob.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/compile_and_runtime_not_namespaced_r_class_jar/debug/R.jar" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/compile_and_runtime_not_namespaced_r_class_jar/debug/R.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/.last_build_id" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/.last_build_id" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/assets/fonts/Oswald/Oswald-Light.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/assets/translations/en.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/assets/translations/en.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/assets/translations/vi.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/assets/translations/vi.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_build.d" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/flutter_build.d" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/libs.jar" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/flutter/debug/libs.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/merged.dir/values-fr-rCA/values-fr-rCA.xml" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/merged.dir/values-fr-rCA/values-fr-rCA.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/merged.dir/values-ro/values-ro.xml" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/merged.dir/values-ro/values-ro.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/merged.dir/values-te/values-te.xml" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/merged.dir/values-te/values-te.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/merged.dir/values-zh-rCN/values-zh-rCN.xml" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/merged.dir/values-zh-rCN/values-zh-rCN.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/merged.dir/values-zh-rHK/values-zh-rHK.xml" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/merged.dir/values-zh-rHK/values-zh-rHK.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/merger.xml" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/incremental/debug/mergeDebugResources/merger.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/incremental/mergeDebugAssets/merger.xml" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/incremental/mergeDebugAssets/merger.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/incremental/processDebugResources/resources-list-for-resources-debug.ap_.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_manifest/debug/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_manifest/debug/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_manifests/debug/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_manifests/debug/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res/debug/values-fr-rCA_values-fr-rCA.arsc.flat" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res/debug/values-fr-rCA_values-fr-rCA.arsc.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res/debug/values-ro_values-ro.arsc.flat" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res/debug/values-ro_values-ro.arsc.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res/debug/values-te_values-te.arsc.flat" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res/debug/values-te_values-te.arsc.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res/debug/values-zh-rCN_values-zh-rCN.arsc.flat" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res/debug/values-zh-rCN_values-zh-rCN.arsc.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res/debug/values-zh-rHK_values-zh-rHK.arsc.flat" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res/debug/values-zh-rHK_values-zh-rHK.arsc.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/debug.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/debug.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-af.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-af.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-am.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-am.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ar.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ar.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-as.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-as.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-az.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-az.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-b+sr+Latn.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-b+sr+Latn.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-be.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-be.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-bg.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-bg.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-bn.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-bn.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-bs.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-bs.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ca.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ca.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-cs.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-cs.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-da.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-da.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-de.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-de.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-el.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-el.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-en-rGB.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-en-rGB.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-es-rUS.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-es-rUS.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-es.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-es.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-et.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-et.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-eu.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-eu.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-fa.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-fa.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-fi.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-fi.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-fr-rCA.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-fr-rCA.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-fr.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-fr.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-gl.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-gl.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-gu.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-gu.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-hi.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-hi.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-hr.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-hr.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-hu.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-hu.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-hy.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-hy.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-in.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-in.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-is.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-is.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-it.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-it.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-iw.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-iw.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ja.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ja.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ka.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ka.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-kk.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-kk.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-km.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-km.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-kn.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-kn.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ko.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ko.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ky.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ky.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-lo.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-lo.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-lt.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-lt.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-lv.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-lv.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-mk.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-mk.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ml.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ml.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-mn.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-mn.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-mr.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-mr.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ms.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ms.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-my.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-my.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-nb.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-nb.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ne.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ne.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-nl.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-nl.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-or.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-or.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-pa.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-pa.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-pl.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-pl.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-pt-rBR.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-pt-rBR.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-pt-rPT.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-pt-rPT.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ro.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ro.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ru.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ru.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-si.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-si.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-sk.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-sk.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-sl.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-sl.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-sq.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-sq.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-sr.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-sr.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-sv.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-sv.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-sw.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-sw.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ta.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ta.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-te.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-te.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-th.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-th.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-tl.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-tl.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-tr.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-tr.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-uk.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-uk.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ur.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-ur.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-uz.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-uz.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-vi.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-vi.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-zh-rCN.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-zh-rCN.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-zh-rHK.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-zh-rHK.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-zh-rTW.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-zh-rTW.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-zu.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values-zu.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values.json" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/merged_res_blame_folder/debug/out/multi-v2/values.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/packaged_manifests/debug/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/packaged_manifests/debug/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/processed_res/debug/out/resources-debug.ap_" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/processed_res/debug/out/resources-debug.ap_" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/intermediates/source_set_path_map/debug/file-map.txt" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/intermediates/source_set_path_map/debug/file-map.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/app/outputs/logs/manifest-merger-debug-report.txt" beforeDir="false" afterPath="$PROJECT_DIR$/build/app/outputs/logs/manifest-merger-debug-report.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/audio_session/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/audio_session/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/cache.dill.track.dill" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/device_info_plus/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/device_info_plus/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/device_info_plus/kotlin/compileDebugKotlin/cacheable/last-build.bin" beforeDir="false" afterPath="$PROJECT_DIR$/build/device_info_plus/kotlin/compileDebugKotlin/cacheable/last-build.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/device_info_plus/kotlin/compileDebugKotlin/local-state/build-history.bin" beforeDir="false" afterPath="$PROJECT_DIR$/build/device_info_plus/kotlin/compileDebugKotlin/local-state/build-history.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/f872507f30e2ff2523f0f1d19160266e.cache.dill.track.dill" beforeDir="false" afterPath="$PROJECT_DIR$/build/f872507f30e2ff2523f0f1d19160266e.cache.dill.track.dill" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/firebase_core/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/firebase_core/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/firebase_messaging/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/firebase_messaging/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/fl_location/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/fl_location/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/fl_location/kotlin/compileDebugKotlin/cacheable/last-build.bin" beforeDir="false" afterPath="$PROJECT_DIR$/build/fl_location/kotlin/compileDebugKotlin/cacheable/last-build.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/fl_location/kotlin/compileDebugKotlin/local-state/build-history.bin" beforeDir="false" afterPath="$PROJECT_DIR$/build/fl_location/kotlin/compileDebugKotlin/local-state/build-history.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/flutter_contacts/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/flutter_contacts/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/flutter_contacts/kotlin/compileDebugKotlin/cacheable/last-build.bin" beforeDir="false" afterPath="$PROJECT_DIR$/build/flutter_contacts/kotlin/compileDebugKotlin/cacheable/last-build.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/flutter_contacts/kotlin/compileDebugKotlin/local-state/build-history.bin" beforeDir="false" afterPath="$PROJECT_DIR$/build/flutter_contacts/kotlin/compileDebugKotlin/local-state/build-history.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/flutter_inappwebview_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/flutter_inappwebview_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/flutter_inappwebview_android/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin" beforeDir="false" afterPath="$PROJECT_DIR$/build/flutter_inappwebview_android/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/flutter_keyboard_visibility/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/flutter_keyboard_visibility/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/flutter_plugin_android_lifecycle/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/flutter_plugin_android_lifecycle/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geocoding_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/geocoding_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/generated/source/buildConfig/debug/com/baseflow/geolocator/BuildConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/aar_metadata/debug/aar-metadata.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/annotation_processor_list/debug/annotationProcessors.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/compile_library_classes_jar/debug/classes.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/compile_r_class_jar/debug/R.jar" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/compile_symbol_list/debug/R.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/incremental/debug/packageDebugResources/merger.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/incremental/mergeDebugShaders/merger.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/incremental/packageDebugAssets/merger.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/BuildConfig.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/GeolocatorLocationService$LocalBinder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/GeolocatorLocationService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/GeolocatorPlugin$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/GeolocatorPlugin.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/LocationServiceHandlerImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/MethodCallHandlerImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/StreamHandlerImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/errors/ErrorCallback.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/errors/ErrorCodes$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/errors/ErrorCodes.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/errors/PermissionUndefinedException.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/AndroidIconResource.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/BackgroundNotification.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/FlutterLocationServiceListener.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/ForegroundNotificationOptions.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/FusedLocationClient$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/FusedLocationClient$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/FusedLocationClient.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/GeolocationManager.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/LocationAccuracy.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/LocationAccuracyManager.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/LocationAccuracyStatus.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/LocationClient.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/LocationManagerClient$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/LocationManagerClient.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/LocationMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/LocationOptions.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/LocationServiceListener.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/LocationServiceStatusReceiver.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/NmeaClient$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/NmeaClient.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/PositionChangedCallback.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/location/ServiceStatus.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/permission/LocationPermission$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/permission/LocationPermission.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/permission/PermissionManager.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/permission/PermissionResultCallback.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/permission/PermissionUtils.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/javac/debug/classes/com/baseflow/geolocator/utils/Utils.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/local_only_symbol_list/debug/R-def.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/merged_manifest/debug/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/navigation_json/debug/navigation.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/packaged_manifests/debug/output-metadata.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/outputs/logs/manifest-merger-debug-report.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/geolocator_android/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/google_maps_flutter_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/google_maps_flutter_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/image_gallery_saver/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/image_gallery_saver/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/image_picker_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/image_picker_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/integration_test/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/integration_test/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/.last_build_id" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/Alamofire" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/Headers/Alamofire-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/Headers/Alamofire-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/Modules/Alamofire.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/Modules/Alamofire.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/Modules/Alamofire.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/Modules/Alamofire.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/Modules/Alamofire.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/Modules/Alamofire.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/Modules/Alamofire.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/Modules/Alamofire.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Alamofire/Alamofire.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/App" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/.env.development" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/.env.production" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.bin" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/FontManifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/NOTICES.Z" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/c-13.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/c-16.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/c-18.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/fonts/Oswald/Oswald-Medium.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/fonts/PlusJakartaSans-VariableFont_wght.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/form/mail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/form/password.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/btnBookingdetail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_empty_couple_seat.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_empty_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_empty_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_empty_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_process_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_screen.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_select_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_select_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_select_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_set_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_set_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_set_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_sold_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_sold_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_sold_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/fill.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/opacity.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/avatar.jpeg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/background.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/d4af518117996ffbab547753b36d7886.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/login_background.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/petro_logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/placeholder.jpeg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_empty_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_empty_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_empty_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_selected_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_selected_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_selected_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_selecting_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_selecting_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_selecting_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_sold_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_sold_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_sold_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/json/trail_loading.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/arrow-down.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/arrow-right.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/arrow-up.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/close.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/copy.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/edit.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/forgot-password.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/csv.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/doc.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/docx.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/facebook.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/gmail%20(1).svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/gmail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/html.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/jpeg.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/jpg.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/json.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/mail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/pdf.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/phone.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/png.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/share.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/txt.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/vsdx.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/webp.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/xls.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/xlsx.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/zalo-20px.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/zalo.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/otp-verification.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/request_content.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/reset-password.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/search.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/share/facebook.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/share/gmail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/share/zalo.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/sort.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/splash/0.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/splash/1.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/splash/2.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/upload/camera-2.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/upload/camera.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/upload/image.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/upload/multiple-image.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/upload/video.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/translations/en.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/assets/translations/vi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/isolate_snapshot_data" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/kernel_blob.bin" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/flutter_inappwebview_web/assets/web/web_support.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/fonts/RobotoMono-Regular.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/highlight.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/squiggly.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/strikethrough.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/underline.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/wakelock_plus/assets/no_sleep.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/youtube_player_flutter/assets/speedometer.webp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/shaders/ink_sparkle.frag" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/vm_snapshot_data" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/FirebaseCore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/FirebaseCore_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers/FIRApp.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers/FIRConfiguration.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers/FIRLoggerLevel.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers/FIROptions.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers/FIRVersion.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers/FirebaseCore-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers/FirebaseCore.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/FirebaseCoreInternal" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/Headers/FirebaseCoreInternal-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/Headers/FirebaseCoreInternal-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/Modules/FirebaseCoreInternal.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/Modules/FirebaseCoreInternal.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/Modules/FirebaseCoreInternal.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/Modules/FirebaseCoreInternal.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/Modules/FirebaseCoreInternal.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/Modules/FirebaseCoreInternal.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/Modules/FirebaseCoreInternal.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/Modules/FirebaseCoreInternal.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseCoreInternal/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/FirebaseInstallations" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/FirebaseInstallations_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/FirebaseInstallations_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Headers/FIRInstallations.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Headers/FIRInstallationsAuthTokenResult.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Headers/FIRInstallationsErrors.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Headers/FirebaseInstallations-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Headers/FirebaseInstallations.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/FirebaseMessaging" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/FirebaseMessaging_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/FirebaseMessaging_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/Headers/FIRMessaging.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/Headers/FIRMessagingExtensionHelper.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/Headers/FirebaseMessaging-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/Headers/FirebaseMessaging.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Flutter" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/Flutter.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterAppDelegate.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterBinaryMessenger.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterCallbackCache.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterChannels.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterCodecs.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterDartProject.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterEngine.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterEngineGroup.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterHeadlessDartRunner.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterHourFormat.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterMacros.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterPlatformViews.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterTexture.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Headers/FlutterViewController.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Flutter.framework/icudtl.dat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/GoogleMapsUtils" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMSMarker+GMUClusteritem.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUCluster.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUClusterAlgorithm.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUClusterIconGenerator.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUClusterItem.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUClusterManager+Testing.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUClusterManager.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUClusterRenderer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUDefaultClusterIconGenerator+Testing.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUDefaultClusterIconGenerator.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUDefaultClusterRenderer+Testing.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUDefaultClusterRenderer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUFeature.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUGeoJSONParser.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUGeometry.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUGeometryCollection.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUGeometryContainer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUGeometryRenderer+Testing.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUGeometryRenderer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUGradient.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUGridBasedClusterAlgorithm.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUGroundOverlay.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUHeatmapTileLayer+Testing.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUHeatmapTileLayer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUKMLParser.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMULineString.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUMarkerClustering.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUNonHierarchicalDistanceBasedAlgorithm.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUPair.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUPlacemark.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUPoint.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUPolygon.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUSimpleClusterAlgorithm.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUStaticCluster.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUStyle.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUStyleMap.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUWeightedLatLng.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GMUWrappingDictionaryKey.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GQTBounds.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GQTPoint.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GQTPointQuadTree.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GQTPointQuadTreeChild.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GQTPointQuadTreeItem.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/Google-Maps-iOS-Utils-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GoogleMapsUtils-Bridging-Header.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers/GoogleMapsUtils-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Modules/GoogleMapsUtils.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Modules/GoogleMapsUtils.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Modules/GoogleMapsUtils.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Modules/GoogleMapsUtils.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Modules/GoogleMapsUtils.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Modules/GoogleMapsUtils.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Modules/GoogleMapsUtils.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Modules/GoogleMapsUtils.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/_CodeSignature/CodeDirectory" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/_CodeSignature/CodeRequirements" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/_CodeSignature/CodeRequirements-1" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/_CodeSignature/CodeSignature" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/GoogleDataTransport" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/GoogleDataTransport_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/GoogleDataTransport_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers/GDTCORClock.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers/GDTCORConsoleLogger.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers/GDTCOREndpoints.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers/GDTCOREvent.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers/GDTCOREventDataObject.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers/GDTCOREventTransformer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers/GDTCORProductData.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers/GDTCORTargets.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers/GDTCORTransport.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers/GoogleDataTransport-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers/GoogleDataTransport.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/Assets.car" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/Storage.mom" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileProto.mom" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileVersionID.mom" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/VersionInfo.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Assets.car" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/DroidSansMerged-Regular.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-1x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-2x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-3x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-1x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-2x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-3x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShaders.metallib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShadersSim.metallib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-1x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-2x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-3x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Tharlon-Regular.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ar.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/az.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_background.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass_night.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_my_location.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ca.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/cs.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/da.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/dav_one_way_16_256.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/de.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/el.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_AU.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_GB.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_IN.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_419.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_MX.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fi.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr_CA.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/he.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hi.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hr.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hu.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hy.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_night_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_large.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_border_waypoint_alert_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_night_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_night_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_location_off.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_qu_direction_mylocation.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/id.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/it.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ja.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ka.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ko.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lt.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lv.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ms.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/my.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nb.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nl.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pl.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture_dim.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_BR.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_PT.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ro.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_1-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_128-32.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_16-4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_2-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_256-64.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_32-8.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_4-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_64-16.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_8-2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ru.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sk.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sq.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sr.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sv.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sw.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/th.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/tr.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uk.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uz.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/vi.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_CN.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_HK.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_TW.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_left.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_right.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/ic_error.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/GoogleMaps.bundle/oss_licenses_maps.txt.gz" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleMaps/GoogleMapsResources.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/GoogleUtilities" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULAppDelegateSwizzler.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULAppEnvironmentUtil.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULApplication.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULHeartbeatDateStorable.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULHeartbeatDateStorage.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULHeartbeatDateStorageUserDefaults.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULKeychainStorage.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULKeychainUtils.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULLogger.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULLoggerLevel.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULMutableDictionary.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULNSData+zlib.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULNetwork.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULNetworkConstants.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULNetworkInfo.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULNetworkLoggerProtocol.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULNetworkMessageCode.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULNetworkURLSession.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULReachabilityChecker.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULSceneDelegateSwizzler.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULSecureCoding.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULURLSessionDataResponse.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GULUserDefaults.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/GoogleUtilities-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers/NSURLSession+GULPromises.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers/MTBBarcodeScanner-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers/MTBBarcodeScanner.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/MTBBarcodeScanner/MTBBarcodeScanner.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/MTBBarcodeScanner/MTBBarcodeScanner.framework/MTBBarcodeScanner" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/MTBBarcodeScanner/MTBBarcodeScanner.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/MTBBarcodeScanner/MTBBarcodeScanner.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Headers/OrderedSet-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Headers/OrderedSet-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Modules/OrderedSet.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Modules/OrderedSet.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Modules/OrderedSet.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Modules/OrderedSet.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Modules/OrderedSet.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Modules/OrderedSet.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Modules/OrderedSet.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Modules/OrderedSet.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/OrderedSet" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/OrderedSet_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/OrderedSet_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Pods_Runner.framework/Headers/Pods-Runner-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Pods_Runner.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Pods_Runner.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Pods_Runner.framework/Pods_Runner" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Pods_Runner.framework/_CodeSignature/CodeDirectory" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Pods_Runner.framework/_CodeSignature/CodeRequirements" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Pods_Runner.framework/_CodeSignature/CodeRequirements-1" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Pods_Runner.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Pods_Runner.framework/_CodeSignature/CodeSignature" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/FBLPromises" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/FBLPromises_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+All.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Always.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Any.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Async.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Await.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Catch.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Delay.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Do.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Race.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Recover.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Reduce.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Retry.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Testing.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Then.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Timeout.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Validate.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise+Wrap.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromise.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromiseError.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/FBLPromises.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers/PromisesObjC-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/PrivateHeaders/FBLPromisePrivate.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Assets.car" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Alamofire.framework/Alamofire" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Alamofire.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Alamofire.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/App" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/.env.development" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/.env.production" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/AssetManifest.bin" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/AssetManifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/FontManifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/NOTICES.Z" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/c-13.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/c-16.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/c-18.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/fonts/Oswald/Oswald-Medium.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/fonts/PlusJakartaSans-VariableFont_wght.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/form/mail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/form/password.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/btnBookingdetail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_couple_seat.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_process_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_screen.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_select_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_select_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_select_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_set_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_set_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_set_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_sold_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_sold_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_sold_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/fill.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/opacity.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/avatar.jpeg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/background.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/d4af518117996ffbab547753b36d7886.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/login_background.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/petro_logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/placeholder.jpeg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_empty_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_empty_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_empty_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selected_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selected_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selected_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selecting_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selecting_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selecting_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_sold_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_sold_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_sold_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/json/trail_loading.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/arrow-down.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/arrow-right.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/arrow-up.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/close.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/copy.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/edit.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/forgot-password.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/csv.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/doc.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/docx.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/facebook.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/gmail%20(1).svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/gmail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/html.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/jpeg.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/jpg.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/json.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/mail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/pdf.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/phone.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/png.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/share.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/txt.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/vsdx.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/webp.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/xls.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/xlsx.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/zalo-20px.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/zalo.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/otp-verification.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/request_content.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/reset-password.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/search.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/share/facebook.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/share/gmail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/share/zalo.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/sort.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/splash/0.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/splash/1.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/splash/2.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/upload/camera-2.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/upload/camera.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/upload/image.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/upload/multiple-image.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/upload/video.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/translations/en.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/translations/vi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/isolate_snapshot_data" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/kernel_blob.bin" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/flutter_inappwebview_web/assets/web/web_support.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/fonts/RobotoMono-Regular.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/highlight.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/squiggly.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/strikethrough.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/underline.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/wakelock_plus/assets/no_sleep.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/youtube_player_flutter/assets/speedometer.webp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/vm_snapshot_data" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework/FBLPromises" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework/FBLPromises_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework/FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework/FirebaseCore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreInternal.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreInternal.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework/FirebaseInstallations" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework/FirebaseInstallations_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework/FirebaseInstallations_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework/FirebaseMessaging" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework/FirebaseMessaging_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework/FirebaseMessaging_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Flutter" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/Flutter.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterChannels.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterCodecs.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterDartProject.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterEngine.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterHourFormat.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterMacros.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterTexture.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterViewController.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Flutter.framework/icudtl.dat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework/GoogleDataTransport" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework/GoogleDataTransport_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework/GoogleDataTransport_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework/GoogleUtilities" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/MTBBarcodeScanner.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/MTBBarcodeScanner.framework/MTBBarcodeScanner" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/MTBBarcodeScanner.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/OrderedSet.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/OrderedSet.framework/OrderedSet" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/OrderedSet.framework/OrderedSet_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/OrderedSet.framework/OrderedSet_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/OrderedSet.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SignalRSwift.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SignalRSwift.framework/SignalRSwift" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SignalRSwift.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Starscream.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Starscream.framework/Starscream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/Starscream.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SwiftSignalRClient.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SwiftSignalRClient.framework/SwiftSignalRClient" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SwiftSignalRClient.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/audio_session.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/audio_session.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/audio_session.framework/audio_session" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/device_info_plus.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/device_info_plus.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/device_info_plus.framework/device_info_plus" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/device_info_plus.framework/device_info_plus_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/device_info_plus.framework/device_info_plus_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fl_location.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fl_location.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/fl_location.framework/fl_location" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_contacts.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_contacts.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_contacts.framework/flutter_contacts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/Myh-pL-l6f-view-1n8-5e-oxa.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/navController.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/viewController.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/flutter_inappwebview_ios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/flutter_inappwebview_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/flutter_inappwebview_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_keyboard_visibility.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_keyboard_visibility.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_keyboard_visibility.framework/flutter_keyboard_visibility" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/geocoding_ios.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/geocoding_ios.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/geocoding_ios.framework/geocoding_ios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/geolocator_apple.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/geolocator_apple.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/geolocator_apple.framework/geolocator_apple" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework/image_gallery_saver" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker_ios.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker_ios.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker_ios.framework/image_picker_ios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/integration_test.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/integration_test.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/integration_test.framework/integration_test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/just_audio.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/just_audio.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/just_audio.framework/just_audio" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework/nanopb" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework/nanopb_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework/nanopb_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_filex.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_filex.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_filex.framework/open_filex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info_plus.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info_plus.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info_plus.framework/package_info_plus" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info_plus.framework/package_info_plus_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info_plus.framework/package_info_plus_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider_foundation.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider_foundation.framework/path_provider_foundation" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qr_code_scanner.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qr_code_scanner.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/qr_code_scanner.framework/qr_code_scanner" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences_foundation.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite_darwin.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite_darwin.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite_darwin.framework/sqflite_darwin" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/syncfusion_flutter_pdfviewer.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/syncfusion_flutter_pdfviewer.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/syncfusion_flutter_pdfviewer.framework/syncfusion_flutter_pdfviewer" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher_ios.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher_ios.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher_ios.framework/url_launcher_ios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player_avfoundation.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player_avfoundation.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player_avfoundation.framework/video_player_avfoundation" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/wakelock_plus.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/wakelock_plus.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/wakelock_plus.framework/wakelock_plus" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/wakelock_plus.framework/wakelock_plus_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/wakelock_plus.framework/wakelock_plus_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter_wkwebview.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter_wkwebview.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/Assets.car" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/Storage.mom" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileProto.mom" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileVersionID.mom" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/VersionInfo.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Assets.car" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/DroidSansMerged-Regular.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-1x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-2x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-3x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-1x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-2x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-3x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShaders.metallib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShadersSim.metallib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-1x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-2x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-3x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Tharlon-Regular.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ar.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/az.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_background.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass_night.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_my_location.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ca.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/cs.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/da.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/dav_one_way_16_256.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/de.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/el.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_AU.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_GB.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_IN.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_419.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_MX.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fi.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr_CA.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/he.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hi.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hr.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hu.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hy.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_night_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_large.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_border_waypoint_alert_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_night_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_night_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_location_off.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_qu_direction_mylocation.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/id.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/it.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ja.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ka.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ko.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lt.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lv.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ms.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/my.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nb.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nl.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pl.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture_dim.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_BR.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_PT.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ro.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_1-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_128-32.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_16-4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_2-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_256-64.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_32-8.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_4-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_64-16.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_8-2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ru.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sk.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sq.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sr.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sv.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sw.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/th.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/tr.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uk.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uz.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/vi.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_CN.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_HK.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_TW.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_left.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_right.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/ic_error.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/oss_licenses_maps.txt.gz" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleMapsResources.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/PkgInfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Runner" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/Runner.debug.dylib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/__preview.dylib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/firebase_messaging_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/firebase_messaging_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/google_maps_flutter_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/google_maps_flutter_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/permission_handler_apple_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.app/permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Runner.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/Headers/SignalRSwift-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/Headers/SignalRSwift-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/Modules/SignalRSwift.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/Modules/SignalRSwift.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/Modules/SignalRSwift.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/Modules/SignalRSwift.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/Modules/SignalRSwift.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/Modules/SignalRSwift.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/Modules/SignalRSwift.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/Modules/SignalRSwift.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/SignalRSwift" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SignalRSwift/SignalRSwift.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/Headers/Starscream-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/Headers/Starscream-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/Modules/Starscream.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/Modules/Starscream.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/Modules/Starscream.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/Modules/Starscream.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/Modules/Starscream.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/Modules/Starscream.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/Modules/Starscream.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/Modules/Starscream.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/Starscream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/Starscream/Starscream.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/Headers/SwiftSignalRClient-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/Headers/SwiftSignalRClient-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/Modules/SwiftSignalRClient.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/Modules/SwiftSignalRClient.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/Modules/SwiftSignalRClient.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/Modules/SwiftSignalRClient.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/Modules/SwiftSignalRClient.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/Modules/SwiftSignalRClient.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/Modules/SwiftSignalRClient.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/Modules/SwiftSignalRClient.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/SwiftSignalRClient" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/SwiftSignalRClient/SwiftSignalRClient.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Base/GoogleMapsBase.framework/GoogleMapsBase" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Base/GoogleMapsBase.framework/Headers/GMSCompatabilityMacros.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Base/GoogleMapsBase.framework/Headers/GMSCoordinateBounds.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Base/GoogleMapsBase.framework/Headers/GMSDeprecationMacros.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Base/GoogleMapsBase.framework/Headers/GoogleMapsBase.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Base/GoogleMapsBase.framework/Headers/module.modulemap.bak" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Base/GoogleMapsBase.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/GoogleMaps" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSAccessibilityLabels.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSAddress.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSAdvancedMarker.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSCALayer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSCameraPosition.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSCameraUpdate.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSCircle.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSCollisionBehavior.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSCoordinateBounds+GoogleMaps.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSFeature.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSFeatureLayer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSFeatureStyle.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSGeocoder.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSGeometryUtils.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSGroundOverlay.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSIndoorBuilding.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSIndoorDisplay.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSIndoorLevel.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSMapID.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSMapLayer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSMapStyle.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSMapView+Animation.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSMapView.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSMarker.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSMarkerAnimation.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSMarkerLayer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSMutablePath.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSOrientation.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSOverlay.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSOverlayLayer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPanorama.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPanoramaCamera.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPanoramaCameraUpdate.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPanoramaLayer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPanoramaLink.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPanoramaService.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPanoramaSource.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPanoramaView.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPath.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPinImage.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPinImageGlyph.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPinImageOptions.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPlaceFeature.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPolygon.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPolygonLayer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSPolyline.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSProjection.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSServices.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSStampStyle.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSStrokeStyle.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSStyleSpan.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSSyncTileLayer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSTileLayer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSUISettings.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GMSURLTileLayer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/GoogleMaps.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Headers/module.modulemap.bak" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMaps.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMapsCore.framework/GoogleMapsCore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMapsCore.framework/Headers/module.modulemap.bak" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleMaps/Maps/GoogleMapsCore.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/audio_session/audio_session.framework/Headers/AudioSessionPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/audio_session/audio_session.framework/Headers/DarwinAudioSession.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/audio_session/audio_session.framework/Headers/audio_session-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/audio_session/audio_session.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/audio_session/audio_session.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/audio_session/audio_session.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/audio_session/audio_session.framework/audio_session" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/device_info_plus/device_info_plus.framework/Headers/FPPDeviceInfoPlusPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/device_info_plus/device_info_plus.framework/Headers/device_info_plus-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/device_info_plus/device_info_plus.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/device_info_plus/device_info_plus.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/device_info_plus/device_info_plus.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/device_info_plus/device_info_plus.framework/device_info_plus" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/device_info_plus/device_info_plus.framework/device_info_plus_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/device_info_plus/device_info_plus.framework/device_info_plus_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/device_info_plus/device_info_plus_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/device_info_plus/device_info_plus_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Headers/FLTFirebaseCorePlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Headers/FLTFirebasePlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Headers/FLTFirebasePluginRegistry.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Headers/firebase_core-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Headers/messages.g.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/_CodeSignature/CodeDirectory" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/_CodeSignature/CodeRequirements" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/_CodeSignature/CodeRequirements-1" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/_CodeSignature/CodeSignature" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/firebase_core" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/Headers/FLTFirebaseMessagingPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/Headers/firebase_messaging-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/_CodeSignature/CodeDirectory" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/_CodeSignature/CodeRequirements" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/_CodeSignature/CodeRequirements-1" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/_CodeSignature/CodeSignature" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/firebase_messaging" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/Headers/FlLocationPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/Headers/fl_location-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/Headers/fl_location-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/Modules/fl_location.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/Modules/fl_location.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/Modules/fl_location.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/Modules/fl_location.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/Modules/fl_location.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/Modules/fl_location.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/Modules/fl_location.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/Modules/fl_location.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/fl_location/fl_location.framework/fl_location" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/Headers/FlutterContactsPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/Headers/flutter_contacts-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/Headers/flutter_contacts-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/Modules/flutter_contacts.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/Modules/flutter_contacts.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/Modules/flutter_contacts.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/Modules/flutter_contacts.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/Modules/flutter_contacts.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/Modules/flutter_contacts.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/Modules/flutter_contacts.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/Modules/flutter_contacts.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_contacts/flutter_contacts.framework/flutter_contacts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Headers/InAppWebViewFlutterPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Headers/flutter_inappwebview_ios-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Headers/flutter_inappwebview_ios-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Modules/flutter_inappwebview_ios.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Modules/flutter_inappwebview_ios.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Modules/flutter_inappwebview_ios.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Modules/flutter_inappwebview_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Modules/flutter_inappwebview_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Modules/flutter_inappwebview_ios.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Modules/flutter_inappwebview_ios.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Modules/flutter_inappwebview_ios.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/WebView.storyboardc/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/WebView.storyboardc/Myh-pL-l6f-view-1n8-5e-oxa.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/WebView.storyboardc/navController.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/WebView.storyboardc/viewController.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/flutter_inappwebview_ios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/flutter_inappwebview_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios.framework/flutter_inappwebview_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_inappwebview_ios/flutter_inappwebview_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_keyboard_visibility/flutter_keyboard_visibility.framework/Headers/FlutterKeyboardVisibilityPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_keyboard_visibility/flutter_keyboard_visibility.framework/Headers/flutter_keyboard_visibility-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_keyboard_visibility/flutter_keyboard_visibility.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_keyboard_visibility/flutter_keyboard_visibility.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_keyboard_visibility/flutter_keyboard_visibility.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/flutter_keyboard_visibility/flutter_keyboard_visibility.framework/flutter_keyboard_visibility" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geocoding_ios/geocoding_ios.framework/Headers/CLPlacemarkExtensions.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geocoding_ios/geocoding_ios.framework/Headers/GeocodingHandler.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geocoding_ios/geocoding_ios.framework/Headers/GeocodingPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geocoding_ios/geocoding_ios.framework/Headers/geocoding_ios-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geocoding_ios/geocoding_ios.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geocoding_ios/geocoding_ios.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geocoding_ios/geocoding_ios.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geocoding_ios/geocoding_ios.framework/geocoding_ios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/ActivityTypeMapper.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/AuthorizationStatusMapper.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/ErrorCodes.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/GeolocationHandler.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/GeolocationHandler_Test.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/GeolocatorPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/GeolocatorPlugin_Test.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/LocationAccuracyHandler.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/LocationAccuracyMapper.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/LocationDistanceMapper.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/LocationMapper.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/LocationServiceStreamHandler.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/PermissionHandler.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/PermissionUtils.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/PositionStreamHandler.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/ServiceStatus.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Headers/geolocator-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/geolocator_apple" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/geolocator_apple_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple.framework/geolocator_apple_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/geolocator_apple/geolocator_apple_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/FGMClusterManagersController.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/FGMMarkerUserData.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/FLTGoogleMapHeatmapController.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/FLTGoogleMapJSONConversions.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/FLTGoogleMapTileOverlayController.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/FLTGoogleMapsPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/GoogleMapCircleController.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/GoogleMapController.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/GoogleMapController_Test.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/GoogleMapMarkerController.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/GoogleMapMarkerController_Test.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/GoogleMapPolygonController.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/GoogleMapPolylineController.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/GoogleMapPolylineController_Test.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/google_maps_flutter_ios-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers/messages.g.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/_CodeSignature/CodeDirectory" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/_CodeSignature/CodeRequirements" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/_CodeSignature/CodeRequirements-1" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/_CodeSignature/CodeSignature" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios.framework/google_maps_flutter_ios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/google_maps_flutter_ios/google_maps_flutter_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/Headers/ImageGallerySaverPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/Headers/image_gallery_saver-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/Headers/image_gallery_saver-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/Modules/image_gallery_saver.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/Modules/image_gallery_saver.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/Modules/image_gallery_saver.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/Modules/image_gallery_saver.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/Modules/image_gallery_saver.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/Modules/image_gallery_saver.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/Modules/image_gallery_saver.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/Modules/image_gallery_saver.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_gallery_saver/image_gallery_saver.framework/image_gallery_saver" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/Headers/FLTImagePickerImageUtil.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/Headers/FLTImagePickerMetaDataUtil.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/Headers/FLTImagePickerPhotoAssetUtil.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/Headers/FLTImagePickerPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/Headers/FLTImagePickerPlugin_Test.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/Headers/FLTPHPickerSaveImageToPathOperation.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/Headers/image_picker_ios-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/Headers/messages.g.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/image_picker_ios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/image_picker_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios.framework/image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/image_picker_ios/image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/integration_test/integration_test.framework/Headers/FLTIntegrationTestRunner.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/integration_test/integration_test.framework/Headers/IntegrationTestIosTest.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/integration_test/integration_test.framework/Headers/IntegrationTestPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/integration_test/integration_test.framework/Headers/integration_test-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/integration_test/integration_test.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/integration_test/integration_test.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/integration_test/integration_test.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/integration_test/integration_test.framework/integration_test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Headers/AudioPlayer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Headers/AudioSource.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Headers/BetterEventChannel.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Headers/ClippingAudioSource.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Headers/ConcatenatingAudioSource.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Headers/IndexedAudioSource.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Headers/IndexedPlayerItem.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Headers/JustAudioPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Headers/LoadControl.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Headers/LoopingAudioSource.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Headers/UriAudioSource.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Headers/just_audio-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/just_audio/just_audio.framework/just_audio" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Headers/nanopb-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Headers/pb.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Headers/pb_common.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Headers/pb_decode.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Headers/pb_encode.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/nanopb" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/nanopb_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/nanopb_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/nanopb/nanopb_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/nanopb/nanopb_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/open_filex/open_filex.framework/Headers/OpenFilePlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/open_filex/open_filex.framework/Headers/open_filex-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/open_filex/open_filex.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/open_filex/open_filex.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/open_filex/open_filex.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/open_filex/open_filex.framework/open_filex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/package_info_plus/package_info_plus.framework/Headers/FPPPackageInfoPlusPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/package_info_plus/package_info_plus.framework/Headers/package_info_plus-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/package_info_plus/package_info_plus.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/package_info_plus/package_info_plus.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/package_info_plus/package_info_plus.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/package_info_plus/package_info_plus.framework/package_info_plus" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/package_info_plus/package_info_plus.framework/package_info_plus_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/package_info_plus/package_info_plus.framework/package_info_plus_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/package_info_plus/package_info_plus_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/package_info_plus/package_info_plus_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/Headers/path_provider_foundation-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/Headers/path_provider_foundation-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/Modules/path_provider_foundation.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/Modules/path_provider_foundation.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/Modules/path_provider_foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/Modules/path_provider_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/path_provider_foundation" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/path_provider_foundation/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/AppTrackingTransparencyPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/AssistantPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/AudioVideoPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/BackgroundRefreshStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/BluetoothPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/Codec.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/ContactPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/CriticalAlertsPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/EventPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/LocationPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/MediaLibraryPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/NotificationPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/PermissionHandlerEnums.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/PermissionHandlerPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/PermissionManager.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/PermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/PhonePermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/PhotoPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/SensorPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/SpeechPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/StoragePermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/UnknownPermissionStrategy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Headers/permission_handler_apple-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/_CodeSignature/CodeDirectory" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/_CodeSignature/CodeRequirements" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/_CodeSignature/CodeRequirements-1" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/_CodeSignature/CodeSignature" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple.framework/permission_handler_apple" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/permission_handler_apple/permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/Headers/FlutterQrPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/Headers/qr_code_scanner-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/Headers/qr_code_scanner-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/Modules/qr_code_scanner.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/Modules/qr_code_scanner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/Modules/qr_code_scanner.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/Modules/qr_code_scanner.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/Modules/qr_code_scanner.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/Modules/qr_code_scanner.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/Modules/qr_code_scanner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/Modules/qr_code_scanner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/qr_code_scanner/qr_code_scanner.framework/qr_code_scanner" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/Headers/shared_preferences_foundation-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/Headers/shared_preferences_foundation-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/Modules/shared_preferences_foundation.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/Modules/shared_preferences_foundation.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/shared_preferences_foundation" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/shared_preferences_foundation/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/sqflite_darwin/sqflite_darwin.framework/Headers/SqfliteImportPublic.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/sqflite_darwin/sqflite_darwin.framework/Headers/SqflitePluginPublic.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/sqflite_darwin/sqflite_darwin.framework/Headers/sqflite_darwin-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/sqflite_darwin/sqflite_darwin.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/sqflite_darwin/sqflite_darwin.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/sqflite_darwin/sqflite_darwin.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/sqflite_darwin/sqflite_darwin.framework/sqflite_darwin" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/sqflite_darwin/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/sqflite_darwin/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/sqflite_darwin/sqflite_darwin_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/sqflite_darwin/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/Headers/SyncfusionFlutterPdfViewerPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/Headers/syncfusion_flutter_pdfviewer-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/Headers/syncfusion_flutter_pdfviewer-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/Modules/syncfusion_flutter_pdfviewer.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/Modules/syncfusion_flutter_pdfviewer.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/Modules/syncfusion_flutter_pdfviewer.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/Modules/syncfusion_flutter_pdfviewer.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/Modules/syncfusion_flutter_pdfviewer.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/Modules/syncfusion_flutter_pdfviewer.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/Modules/syncfusion_flutter_pdfviewer.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/Modules/syncfusion_flutter_pdfviewer.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/syncfusion_flutter_pdfviewer/syncfusion_flutter_pdfviewer.framework/syncfusion_flutter_pdfviewer" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/Headers/url_launcher_ios-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/Headers/url_launcher_ios-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/Modules/url_launcher_ios.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/Modules/url_launcher_ios.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/Modules/url_launcher_ios.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/Modules/url_launcher_ios.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/Modules/url_launcher_ios.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/Modules/url_launcher_ios.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/Modules/url_launcher_ios.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/Modules/url_launcher_ios.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/url_launcher_ios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/url_launcher_ios/url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/Headers/AVAssetTrackUtils.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/Headers/FVPAVFactory.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/Headers/FVPDisplayLink.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/Headers/FVPFrameUpdater.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/Headers/FVPVideoPlayer.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/Headers/FVPVideoPlayerPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/Headers/FVPVideoPlayerPlugin_Test.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/Headers/FVPVideoPlayer_Test.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/Headers/messages.g.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/Headers/video_player_avfoundation-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/video_player_avfoundation" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/video_player_avfoundation/video_player_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/wakelock_plus/wakelock_plus.framework/Headers/UIApplication+idleTimerLock.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/wakelock_plus/wakelock_plus.framework/Headers/WakelockPlusPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/wakelock_plus/wakelock_plus.framework/Headers/messages.g.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/wakelock_plus/wakelock_plus.framework/Headers/wakelock_plus-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/wakelock_plus/wakelock_plus.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/wakelock_plus/wakelock_plus.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/wakelock_plus/wakelock_plus.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/wakelock_plus/wakelock_plus.framework/wakelock_plus" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/wakelock_plus/wakelock_plus.framework/wakelock_plus_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/wakelock_plus/wakelock_plus.framework/wakelock_plus_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/wakelock_plus/wakelock_plus_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/wakelock_plus/wakelock_plus_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/Headers/webview_flutter_wkwebview-Swift.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/Headers/webview_flutter_wkwebview-umbrella.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/Modules/webview_flutter_wkwebview.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/Modules/webview_flutter_wkwebview.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/Modules/webview_flutter_wkwebview.swiftmodule/arm64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/Modules/webview_flutter_wkwebview.swiftmodule/arm64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/Modules/webview_flutter_wkwebview.swiftmodule/arm64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/Modules/webview_flutter_wkwebview.swiftmodule/x86_64-apple-ios-simulator.abi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/Modules/webview_flutter_wkwebview.swiftmodule/x86_64-apple-ios-simulator.swiftdoc" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/Modules/webview_flutter_wkwebview.swiftmodule/x86_64-apple-ios-simulator.swiftmodule" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/webview_flutter_wkwebview" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/webview_flutter_wkwebview_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/webview_flutter_wkwebview_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/Debug-iphonesimulator/webview_flutter_wkwebview/webview_flutter_wkwebview_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/project/PROJECT@v11_mod=0bbef894e295c6f65492d212da404cfc_hash=bfdfe7dc352907fc980b868725387e98plugins=1OJSG6M1FOV3XYQCBH7Z29RZ0FPR9XDE1-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/project/PROJECT@v11_mod=26a33961d7f45aa96a7576c51b51e84a_hash=bfdfe7dc352907fc980b868725387e98plugins=1OJSG6M1FOV3XYQCBH7Z29RZ0FPR9XDE1-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/project/PROJECT@v11_mod=2cfff77e99edef8d09a1f1e8e659f823_hash=bfdfe7dc352907fc980b868725387e98plugins=1OJSG6M1FOV3XYQCBH7Z29RZ0FPR9XDE1-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/project/PROJECT@v11_mod=332783160e79a2f023104e6785bef91d_hash=bfdfe7dc352907fc980b868725387e98plugins=1OJSG6M1FOV3XYQCBH7Z29RZ0FPR9XDE1-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/project/PROJECT@v11_mod=3c60fd73c8173b1e59b818b5fbebd70f_hash=bfdfe7dc352907fc980b868725387e98plugins=1OJSG6M1FOV3XYQCBH7Z29RZ0FPR9XDE1-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/project/PROJECT@v11_mod=a2114bdd7c3320ebf822b4b48163e510_hash=bfdfe7dc352907fc980b868725387e98plugins=1OJSG6M1FOV3XYQCBH7Z29RZ0FPR9XDE1-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/project/PROJECT@v11_mod=c15e586e65708ab1d7d6fa1d00c12ffa_hash=bfdfe7dc352907fc980b868725387e98plugins=1OJSG6M1FOV3XYQCBH7Z29RZ0FPR9XDE1-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/project/PROJECT@v11_mod=ea4274a40bd2f864b4e61070a1d9f627_hash=bfdfe7dc352907fc980b868725387e98plugins=1OJSG6M1FOV3XYQCBH7Z29RZ0FPR9XDE1-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/project/PROJECT@v11_mod=f22692b746d616a0c8804b954b170e87_hash=bfdfe7dc352907fc980b868725387e98plugins=1OJSG6M1FOV3XYQCBH7Z29RZ0FPR9XDE1-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=0208c15aef17b96a995d375e81965875-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=03f444b94e927ccd2e57335ff6a773d6-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=06615aff6fdf2091e241fc2743bc2fbe-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=06df9b223e09132423622a667abca920-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=079ba1fa3c1636a78b0fd80d77e2a442-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=08fb53b6410733cd86e1f627f16ea9be-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=0924f75b93c6306c8868d0b83b78bedf-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=09560f31fd4e9056895eaf9b94462490-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=09be72a97479d54aec84b68ba007d52d-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=0a6948508a463ae1f792689b59fc91de-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=0bca166e6b511c38231c9aa82c875024-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=0c29f271e468261707e1e8f90f4ee9dc-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=0d51ada5a36a4226020dc4584fb3a892-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=0e1d238e4708fb5121c6741481b2de73-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=0e4862fffe6bfb0008314fb66f10965a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=0eba60e40d8c739a98ecceae45dbc9fe-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=0f223f2577dd7d0a197bb46d03e57d36-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=0fca365747abea6273b13d7ffa150c1c-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=10173d561f9f0282d9a80af0a66373a9-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=1169e574ab455509ebd1bc4237225dd8-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=1300b4f36c05bbf4a1abe82af568ef2a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=14008880e772dae8e52ccdf50af6371d-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=1479859410ab63561ac0aa4b3c979ab3-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=150163662e6dd3b39b5d923501bae1ac-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=17a4eebf059c6a09054817a2abad1022-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=18ddc0d5d5a029992c79ef2284773239-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=19c4932210662c90a6e99a8fafab83c8-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=1a282fae476dbb5db3b1ab94020774ef-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=1bb8e8eb6e665421d588474c9e9d7e20-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=1c0d17be7dab10a19f62c0f1d2157750-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=1c5c787483d0609e904b9117078a1201-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=1d26f315c69fb191d8712d43e366527b-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=1d86212088e4f479e3456221e6536db6-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=1ff0d27af628773594e876f6d8f87787-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=1ff935cd2b0d9399678352505b814faa-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=20aa0a323d6cb2f786a3315cda7a1002-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=20ae0d652407fd60c1c2a8a9ca3a27bd-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=222d133faccb2a412226e4d06384d55b-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=23aa8e24806cdc557694b11d7a3580b4-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=2547b483a85b23906c4053c90f5e77fb-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=2591191c9b84bafb846fac9707ffbad9-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=25c332434af78e3fa9d7c4a11d019867-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=26548d940cc4d22e02dc2fe6b73266ec-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=28212c17eb4c7ab2abc2b213e9e3926a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=28f3f01666233327a6bebd764467a1fd-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=2a528a891987beba2355946d90b20cd3-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=2aab5dfe17647173d70cc005dc8a0ac4-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=2c5aa051d49c0b184ceb69f082714ca1-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=2cd813507824f9ad391c48b0fda7e10a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=2d4d852f1d7993a881d82d7e9c43e7be-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=2d6efeb44dcae631aff5c0c213a1481d-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=2def283251c57a3be4a2980602d8583a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=2e02cd8383bda045b119f91d583629e0-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=2eae1db282d82e06d72af6f5f2dc99e0-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=2f9a5c1b4e9bedf4858faed2b58b85b8-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=35417077e31a8ac34176534720f4ec0e-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=35dc49e844b255c3c02373b9de912ca7-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=388a808baee80ea3632a1cc5c03d68c7-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=38d68f8749b5a9662b58d4a08f6320b7-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=38fccc7deaaf6ff8ca015a939d34abf4-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=39ad77933f63f5acac7319fd7c8d49ed-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=39df2125a87fece71d7f2233660a094d-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=3cd1b2ebc8fe6e4485f347a1d65fdd07-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=3edf6c7f34ad174e6b4c1b2c6ae1622c-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=3f5409fa682b24a75f775c31a190eac8-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=3f5ec50eaa2fd817a5c2878515a978b8-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=3f8ccdd6caddaaa684844759b7bf6a8d-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=416ab39e302ff319021dfddf9762cdce-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=4359eeb4e84c8359b7192e015e5bfc21-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=43a3fd3c72a59285d517e8a336a304b2-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=4470d3dfb857eaf0a0500110d894eada-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=47f3df3b8d4413677e61e038960ef306-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=4802917a5385ef9146b32d1a0db955fa-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=48885f03c43f2603826fd51db4a0050a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=497d9cd132c3ffc5a37210db58cf12b6-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=4b637aad0dce6b71cb0f84f267e8c5c3-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=4d4bb8c46204b103b1b2426a5ab43e93-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=4e5adbb63393f16a37e0fb0ba86e6ae0-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=4f52f791c97ad5cfbafcca9339fa3b58-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=4fb5d683296ec21dce043a7fa77a1eb4-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=500ec1948561d929561250aa7dfad20e-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=53f621d87f0e774a48489ae54fb927be-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=545f51e4960830b980791fa6eb506dcc-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=54c8949edc335f5b0f8a3e43313d432a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=55b1d8fb36c3d9d6813604c8294642a4-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=566059a93f45745b712a9ac323d8cd3c-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=57920b31cf99e8930522485915963361-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=591c87d55003e48f8b2876149a2c9cd4-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=597cce97493b7887ab84b89e1d198e99-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=59967a680b0c99bd5c0ccfdced6db8f7-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=5a07c9af072e22867fb3c42e67ecb716-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=5c3e04bda73a8eb987f65bb949963097-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=5dd2d653533e93d52e1ea44ca992f7d8-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=5e1ea56409e399f76bccfb665cad1959-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=5ec1897f309d36be241f2f0dd9395661-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=5ffd0f9bd7535be3ac3e8f5fe89729c7-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=60e35f6e1a5f2dc483dd7d94d605ee77-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=632cb14d4e49504a47c2d3d90af849bc-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=649d6e46d8e27b2d1a452246b8eb3b5c-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=6643095b8dc2d7d2df7de6afcf3b06cc-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=6707464d2cb16ec94a9b8f168ada6166-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=674dcff974f19b0c4b3cdc0a1fa5a67c-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=6872a0356b3e5308c8b3443b74e79c32-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=6a734744ed7858cb5659705f38f27e95-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=6b65728891c75f0ee14d3d8eab2ebd95-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=6b7d0190766a4994ed2d7d6e695056c5-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=6b84b0b26a16337d81923db251136574-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=6dec10f157c40e136f738269d48b6820-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=6e326e9db684c6c60f5611cf2aca7658-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=6f8ce37c9a9ba599da49c821059c1977-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=6fcc5c460b5de776e2b9b07dc4556d29-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=7059246738b3b93d181896d8df9fa66a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=7076d1afe43834dc1b1b8512a75429f2-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=70791348f00ce982d3a0d54a8aab76b3-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=71717267177e4be0474f3b684ad85db8-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=73ebc0800cf47f7540f829dd39c2a253-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=73f6f1479e48d80649f3348b0b3c378c-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=74125fbef4986e343647f775f40d26cf-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=74c6dafeea76d6e2fed21c4f6e38600c-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=7608e2a127ab9ae3d9a8efc48d643c63-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=76b1edfd25cd491311bed6e5196636a2-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=76d3be1cf6accfba174b42fc801a233d-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=7763abea7cc1e1fc2ac52f19ed8745b2-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=77eb372fd79cb6923b82acf831d6f554-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=79d4d495b2f8bd880288023eb5227e7d-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=7b2fbc8b26e60838a110edd5ec3c278d-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=7b5740603999eff5de2021509115f99b-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=7c3c1e8faf88abf013d55f295a0b2937-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=7ebd6f0428781de7b38d41d698d2a0c3-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=7ee3fc0dfa4b1ceca72b8d8ce663d58e-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=7f500d4342cb577bdceee9fccdaa4929-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=7fdf0179f30d857fce7d1e0116bbc6dc-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=7ff95a9c7cbc6b8688ef21601db88930-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=82cddf572c7df1a30b3ba2f4e9e1da17-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=831d894cc5901d22479ce5d718774a35-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=83c83534bed1046a67ed99200a600166-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=83e158ae0865e70f7eb05170918f7837-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=844ecd2d0be61020f2a586e8d1dd8782-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=85be05e6bac24d82c949397b5ace2a6c-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=88f11a6c4e810b279c44368cabc34bb3-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=88f796e3d79352a9f813aea01f6142e6-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=891d583d2b81cd848cc963cc1688cec6-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=8a1791c41c51d1f450473293e0e53d61-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=8ac1415c61724b7072dd1da24fad7613-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=8ade3a6523b1f32ddd777d94b0857089-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=8bc0a0bc4f3da3f7028320c1e4032492-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=8f023402b376f429af3ebfdc553e89e1-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=8f7fc6bd132b495259ed5c9ba881a2f4-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=904443c13f16ac3c2e1f633d2a06d3e9-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=905fcb81a935ecd7770672e6c9be2447-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=91eddeb8486b6fdbd71a3572494d81d7-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=9369a8128dabb53203ee79de20f10edc-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=93c7821cb415da2bdef04cb237cb6c06-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=963aecc786a3ab74b672b5022b02395d-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=9698f51dd690786cbfbcdb22466c9b62-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=96a266f9241666416246bd3d9c881576-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=97b69086defa3b7a0b583ce27d7adf57-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=98aaccd32df23c339ac0ede7f4adfd6b-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=99b045be6d077b2de2619e9515214eba-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=9a34dd75d8b2202539978c66e5178a45-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=9aed2a96b4bbc2f35a0321adf87df637-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=9d3871e14a564e1d8cefe020cf8aa748-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=9db6671952784c8addd7c24d02359ac5-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=9df50be4519b78f8fb5fc8819b5abd76-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=9ea51362dccfc49032a8b0dc2c84af2f-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=9ffa1c466c260da3a54d307d1a9bad89-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=a03a274a9d29d6b81a0c30426c4db1ce-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=a137beb7a6e09ce05480f1e70f626692-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=a13e0b578b86f45b68918dd0e91b07ba-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=a33ffd56d01201ffda4fd979fd658525-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=a42a3a7b248f978068a39149c3079633-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=a5896f0b114b0de159288ef4cbb1e169-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=a5df2257e0d5728281f0d63474d21e6c-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=a885857ba595aa5b3de44e93c76733bb-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=aa427424607eb1fea71402dbed11905d-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=aa8b7e6294484850c8ee623bd5f1e5ff-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=ac74a65619a2be4111992a8dd8a5b9ce-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=af5c891b566e2a2928d3fd7efacc508e-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=b002df5dcaeb3e85a83ec693a1e20fb4-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=b225bf2cea65ba15b946f9e7fd9a5863-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=b266fda6d209365737a793ca4084e635-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=b2c9250e37bd83d2b6228fd6964dc45b-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=b4a557f5d1ddc340158e6d0e7367084f-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=b5652263ab62b7dca961abe29b925416-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=b581c27f41b8ab222de0bf99168c4318-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=b66b33b52cb1f84e6740a08ac31c686a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=b684911865b27088ea5d04f60531f2cb-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=b8137a9f871fab7e39b49cc855525735-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=b861bd48a17e37d46abe7999f0181f74-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=b9128603577a3136e64b7c5b9e9a1175-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=b9aea3e0fc917f2f3a135a8e44cc9dec-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=bbe89a723f1d6bef3545fab1ba4f2f60-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=bbff61851f0032420b38b9f04b46395e-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=bc87a1dd30a77c8b1c0f2ffcba6b355e-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=bcecde1ba695887d95957b3d63912baa-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=be5ebacf9d394979583426aad3c3da19-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=c02a8b2e5afb9b97e958eea25457cacb-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=c07d8cd2ac1dfa3e226f7a94732fc41e-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=c08fb9de465b0b6edfaee529203a5b0a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=c11a809f77965c7d46738610984a8acd-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=c16e16adb3cf282e293152222289f371-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=c1abc3b54f1e9c75d89eb2f236fbe807-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=c49f6bc40f37cb941c34fe6dbb3f18e3-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=c629b20b58719381933871387a920b6e-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=c63fef71d73d339008480428ad82bd7d-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=ca6e68369b82074be689dc38e85f59e5-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=cc2ad4168373a19c2d9c263223f9d9e7-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=cd5e63e12ebad6c7cae65362bbb3e682-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=ce2c388ab8bb19a6316b351a7916557a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=cf71855236a4dc0db6a2828ce33b8dea-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=d065082922473264bf222dc87fa4445a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=d14812efe979826646395945bcee1393-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=d17865386609429be702b08514a01650-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=d1920d2cb1cfa742461e76a14b0f30be-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=d2ee8499eca0ccee68bfcf7e73596345-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=d37e1d64330063953707bb840ef819a3-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=d384116f539c1466e960e16c41869be0-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=d48a0308966eb5e0ae5d005795914aa5-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=d56a09f655802e4bb2f7f0ddd93aa030-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=d619d1d799cb7d75615cfc50023ffd1a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=d6df3f19e35bb722cae248c99d374b3e-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=d946b96036e1c3fcb094ad3ae7858796-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=d9dd333530f21ff240b84f16f2db9230-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=da3416a6438ec99d27af22e1deea0260-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=dcaea5a4c93e823ccf353867815c7151-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=e17063944b35c917ed312514902ddea0-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=e62d7089061cc2e3179fae00370b6931-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=e64ff171b4e2c9e9472f7f233d111823-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=e717a6361c2d041cd24a548d664dc964-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=e78e9a46a705a255a0622e24648da555-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=e800f2e4d55b3a62f0449fb9122979f7-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=ead88ef0cd6d9826c47a2e88311c1280-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=ec6375fdfaf6b0db4e5780af8a1761f8-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=ecad894a5d36181d21516b99689d9518-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=ed2dd98e0fa1d446da86c9f707f68b3e-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=eef456dabb70a9c81b6d22014ae41c82-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=f1c57a6accb5399fe476336b90118e9a-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=f20f846a28ea8cfa3015f2fa389c1659-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=f222af1cc2cc4c90e5a65bc9fe47b5ec-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=f29d222e7a08cacf052a32c2566bc89c-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=f31e1427c7941edc2abce2ae0a5a4fc4-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=f555a01e6c7c2d06799438e73fb44c00-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=f583c32e54d55b7f0a4e3a08f47605ec-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=f5c2cc47d7712cf3b442c72721ab10d4-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=f9d9c9fbe2b86f942e7797d5804386bc-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=fb576654d64b581ed0723f77c952073f-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=fceae915c6ba842df1bcfd0bff244866-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=fe770409daa46f865d97ba69182290e0-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/target/TARGET@v11_hash=fee06ae3ebab1824f28867e6e5f2b013-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/workspace/WORKSPACE@v11_hash=(null)_subobjects=07ec4c7c19652cf3e7636ac8fb50f753-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/workspace/WORKSPACE@v11_hash=(null)_subobjects=5203892a9420ca27910a4e8f71111f40-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/workspace/WORKSPACE@v11_hash=(null)_subobjects=530df33f0151add86db123b4acc85961-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/workspace/WORKSPACE@v11_hash=(null)_subobjects=77b14552a2d0bbda57236745254d6fb5-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/workspace/WORKSPACE@v11_hash=(null)_subobjects=9fc19a784296679014a0248a59852e96-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/workspace/WORKSPACE@v11_hash=(null)_subobjects=ad37bde4cac8bcb9b237d0f49e39a65b-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/workspace/WORKSPACE@v11_hash=(null)_subobjects=c7952571b2292713eb52d89ef573312c-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/workspace/WORKSPACE@v11_hash=(null)_subobjects=f3f7a5dfe1eb6cf45f0dec861b7236e5-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/XCBuildData/PIFCache/workspace/WORKSPACE@v11_hash=(null)_subobjects=fecaba5dc20f07e614e224143d872e32-json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/AppFrameworkInfo.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Assets.car" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Base.lproj/Main.storyboardc/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Alamofire.framework/Alamofire" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Alamofire.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Alamofire.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/App" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/.env.development" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/.env.production" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/AssetManifest.bin" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/AssetManifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/FontManifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/NOTICES.Z" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/c-13.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/c-16.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/c-18.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/fonts/Oswald/Oswald-Medium.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/fonts/PlusJakartaSans-VariableFont_wght.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/form/mail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/form/password.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/btnBookingdetail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_couple_seat.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_process_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_screen.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_select_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_select_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_select_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_set_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_set_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_set_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_sold_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_sold_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_sold_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/fill.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/opacity.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/avatar.jpeg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/background.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/d4af518117996ffbab547753b36d7886.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/login_background.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/petro_logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/placeholder.jpeg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_empty_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_empty_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_empty_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selected_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selected_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selected_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selecting_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selecting_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selecting_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_sold_couple_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_sold_normal_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/images/seats/ic_sold_vip_seat.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/json/trail_loading.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/p.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/arrow-down.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/arrow-right.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/arrow-up.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/close.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/copy.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/edit.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/forgot-password.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/csv.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/doc.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/docx.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/facebook.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/gmail%20(1).svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/gmail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/html.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/jpeg.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/jpg.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/json.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/mail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/pdf.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/phone.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/png.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/share.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/txt.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/vsdx.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/webp.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/xls.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/xlsx.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/zalo-20px.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/zalo.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/otp-verification.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/request_content.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/reset-password.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/search.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/share/facebook.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/share/gmail.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/share/zalo.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/sort.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/splash/0.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/splash/1.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/splash/2.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/upload/camera-2.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/upload/camera.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/upload/image.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/upload/multiple-image.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/svgs/upload/video.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/translations/en.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/assets/translations/vi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/isolate_snapshot_data" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/kernel_blob.bin" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/flutter_inappwebview_web/assets/web/web_support.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/fonts/RobotoMono-Regular.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/highlight.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/squiggly.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/strikethrough.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/underline.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/wakelock_plus/assets/no_sleep.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/packages/youtube_player_flutter/assets/speedometer.webp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/App.framework/flutter_assets/vm_snapshot_data" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FBLPromises.framework/FBLPromises" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FBLPromises.framework/FBLPromises_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FBLPromises.framework/FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FBLPromises.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FBLPromises.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework/FirebaseCore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseCoreInternal.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseCoreInternal.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework/FirebaseInstallations" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework/FirebaseInstallations_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework/FirebaseInstallations_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework/FirebaseMessaging" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework/FirebaseMessaging_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework/FirebaseMessaging_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Flutter" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/Flutter.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterChannels.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterCodecs.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterDartProject.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterEngine.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterHourFormat.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterMacros.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterPlugin.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterTexture.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Headers/FlutterViewController.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/Modules/module.modulemap" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Flutter.framework/icudtl.dat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework/GoogleDataTransport" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework/GoogleDataTransport_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework/GoogleDataTransport_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework/GoogleUtilities" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/MTBBarcodeScanner.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/MTBBarcodeScanner.framework/MTBBarcodeScanner" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/MTBBarcodeScanner.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/OrderedSet.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/OrderedSet.framework/OrderedSet" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/OrderedSet.framework/OrderedSet_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/OrderedSet.framework/OrderedSet_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/OrderedSet.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/SignalRSwift.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/SignalRSwift.framework/SignalRSwift" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/SignalRSwift.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Starscream.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Starscream.framework/Starscream" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/Starscream.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/SwiftSignalRClient.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/SwiftSignalRClient.framework/SwiftSignalRClient" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/SwiftSignalRClient.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/audio_session.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/audio_session.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/audio_session.framework/audio_session" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/device_info_plus.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/device_info_plus.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/device_info_plus.framework/device_info_plus" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/device_info_plus.framework/device_info_plus_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/device_info_plus.framework/device_info_plus_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/fl_location.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/fl_location.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/fl_location.framework/fl_location" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_contacts.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_contacts.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_contacts.framework/flutter_contacts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/Myh-pL-l6f-view-1n8-5e-oxa.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/navController.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/viewController.nib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/flutter_inappwebview_ios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/flutter_inappwebview_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_inappwebview_ios.framework/flutter_inappwebview_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_keyboard_visibility.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_keyboard_visibility.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/flutter_keyboard_visibility.framework/flutter_keyboard_visibility" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/geocoding_ios.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/geocoding_ios.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/geocoding_ios.framework/geocoding_ios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/geolocator_apple.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/geolocator_apple.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/geolocator_apple.framework/geolocator_apple" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/image_gallery_saver.framework/image_gallery_saver" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/image_picker_ios.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/image_picker_ios.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/image_picker_ios.framework/image_picker_ios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/integration_test.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/integration_test.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/integration_test.framework/integration_test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/just_audio.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/just_audio.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/just_audio.framework/just_audio" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/nanopb.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/nanopb.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/nanopb.framework/nanopb" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/nanopb.framework/nanopb_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/nanopb.framework/nanopb_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/open_filex.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/open_filex.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/open_filex.framework/open_filex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/package_info_plus.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/package_info_plus.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/package_info_plus.framework/package_info_plus" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/package_info_plus.framework/package_info_plus_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/package_info_plus.framework/package_info_plus_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/path_provider_foundation.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/path_provider_foundation.framework/path_provider_foundation" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/qr_code_scanner.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/qr_code_scanner.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/qr_code_scanner.framework/qr_code_scanner" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/shared_preferences_foundation.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/sqflite_darwin.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/sqflite_darwin.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/sqflite_darwin.framework/sqflite_darwin" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/syncfusion_flutter_pdfviewer.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/syncfusion_flutter_pdfviewer.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/syncfusion_flutter_pdfviewer.framework/syncfusion_flutter_pdfviewer" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/url_launcher_ios.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/url_launcher_ios.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/url_launcher_ios.framework/url_launcher_ios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/video_player_avfoundation.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/video_player_avfoundation.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/video_player_avfoundation.framework/video_player_avfoundation" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/wakelock_plus.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/wakelock_plus.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/wakelock_plus.framework/wakelock_plus" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/wakelock_plus.framework/wakelock_plus_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/wakelock_plus.framework/wakelock_plus_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/webview_flutter_wkwebview.framework/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/webview_flutter_wkwebview.framework/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/Assets.car" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/Storage.mom" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileProto.mom" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileVersionID.mom" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/VersionInfo.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Assets.car" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/DroidSansMerged-Regular.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-1x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-2x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-3x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-1x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-2x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-3x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShaders.metallib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShadersSim.metallib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-1x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-2x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-3x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Tharlon-Regular.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ar.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/az.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_background.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass_night.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_my_location.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ca.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/cs.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/da.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/dav_one_way_16_256.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/de.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/el.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_AU.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_GB.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_IN.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_419.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_MX.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fi.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr_CA.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/he.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hi.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hr.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hu.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hy.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_night_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_large.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_border_waypoint_alert_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_night_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_night_32pt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_location_off.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_qu_direction_mylocation.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/id.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/it.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ja.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ka.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ko.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lt.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lv.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ms.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/my.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nb.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nl.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pl.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture_dim.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_BR.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_PT.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ro.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_1-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_128-32.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_16-4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_2-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_256-64.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_32-8.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_4-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_64-16.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_8-2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ru.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sk.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sq.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sr.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sv.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sw.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/th.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/tr.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uk.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uz.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/vi.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_CN.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_HK.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_TW.lproj/GMSCore.strings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_left.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_right.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/ic_error.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/GoogleMaps.bundle/oss_licenses_maps.txt.gz" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleMapsResources.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/GoogleService-Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/PkgInfo" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Runner" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/Runner.debug.dylib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/_CodeSignature/CodeResources" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/__preview.dylib" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/firebase_messaging_Privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/firebase_messaging_Privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/google_maps_flutter_ios_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/google_maps_flutter_ios_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/permission_handler_apple_privacy.bundle/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/iphonesimulator/Runner.app/permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ios/pod_inputs.fingerprint" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/build/just_audio/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/just_audio/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/open_filex/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/open_filex/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/package_info_plus/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/package_info_plus/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/path_provider_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/path_provider_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/permission_handler_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/permission_handler_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/qr_code_scanner/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/qr_code_scanner/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/shared_preferences_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/shared_preferences_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/sqflite_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/sqflite_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/syncfusion_flutter_pdfviewer/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/syncfusion_flutter_pdfviewer/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/url_launcher_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/url_launcher_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/video_player_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/video_player_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/wakelock_plus/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/wakelock_plus/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/webview_flutter_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/build/webview_flutter_android/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/Movie_schedule/_film_choose_time.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/Movie_schedule/_film_choose_time.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/Movie_schedule/index.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/Movie_schedule/index.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/Movie_schedule/index_new.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/cinema/choose/seat.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/cinema/choose/seat.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/cinema/choose/signalr_example.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/pages/cinema/widget/calendar_header.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/pages/cinema/widget/calendar_header.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pubspec.lock" beforeDir="false" afterPath="$PROJECT_DIR$/pubspec.lock" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2x74L45TkfjwgZUDBDt3a1r4qs6" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "Flutter.main.dart.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "dart.analysis.tool.window.visible": "false",
    "git-widget-placeholder": "backupWindow",
    "io.flutter.reload.alreadyRun": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/geneat/beta-moible-flutter",
    "settings.editor.selected.configurable": "flutter.settings",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="main.dart" type="FlutterRunConfigurationType" factoryName="Flutter">
      <option name="filePath" value="$PROJECT_DIR$/lib/main.dart" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="3dcc8e13-70a4-40e5-88b4-1a798df76dc0" name="Changes" comment="" />
      <created>1747275718808</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747275718808</updated>
    </task>
    <servers />
  </component>
</project>