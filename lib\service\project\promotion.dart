
import 'package:http/http.dart';

import '../../models/index.dart';
import '../index.dart';

class SPromotion {
  late String endpoint;
  late Map<String, String> headers;
  late Future<MApi?> Function({required Response result}) checkAuth;

  SPromotion(this.endpoint, this.headers, this.checkAuth);

  /// Get promotion categories
  /// Equivalent to Ecm.getNewPromotion in Swift
  /// If English language is selected, append "/en" to the URL
  Future<MApi?> getPromotionCategories({bool isEnglish = false}) async {
    String url = '$endpoint/api/v1/ecm/categories/news-promotion';
    if (isEnglish) {
      url += '/en';
    }

    return checkAuth(
      result: await BaseHttp.get(
        url: url,
        headers: headers,
        queryParameters: {},
      ),
    );
  }

  /// Get news categories
  /// Equivalent to Ecm.getNewEvent in Swift
  /// If English language is selected, append "/en" to the URL
  Future<MApi?> getNewsCategories({bool isEnglish = false}) async {
    String url = '$endpoint/api/v1/ecm/categories/news-events';
    if (isEnglish) {
      url += '/en';
    }

    return checkAuth(
      result: await BaseHttp.get(
        url: url,
        headers: headers,
        queryParameters: {},
      ),
    );
  }

  /// Get news/promotion items by category ID
  /// Equivalent to Ecm.getNewForCategory in Android
  Future<MApi?> getItemsByCategory({
    required String categoryId,
    int pageSize = 100,
    int pageNumber = 1,
  }) async {
    Map<String, dynamic> queryParameters = {
      'pageSize': pageSize.toString(),
      'pageNumber': pageNumber.toString(),
    };

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/ecm/$categoryId/news',
        headers: headers,
        queryParameters: queryParameters,
      ),
    );
  }

  /// Get news/promotion item by category ID without pagination
  /// Equivalent to Ecm.getNewForCategoryNoParams in Android
  Future<MApi?> getItemsByCategoryNoParams({
    required String categoryId,
  }) async {
    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/ecm/$categoryId/news',
        headers: headers,
        queryParameters: {},
      ),
    );
  }

  /// Get news/promotion item by ID
  /// Equivalent to Ecm.getNewWithId in Swift
  Future<MApi?> getItemById({
    required String itemId,
    int? pageSize,
    int? pageNumber,
  }) async {
    Map<String, dynamic> queryParameters = {};

    if (pageSize != null) {
      queryParameters['pageSize'] = pageSize.toString();
    }

    if (pageNumber != null) {
      queryParameters['pageNumber'] = pageNumber.toString();
    }

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/ecm/%7B$itemId%7D/news',
        headers: headers,
        queryParameters: queryParameters,
      ),
    );
  }

  /// Get free vouchers
  /// Equivalent to Ecm.getFreeVoucher in Swift
  Future<MApi?> getFreeVouchers() async {
    Map<String, dynamic> queryParameters = {
      'pageSize': '10',
      'pageNumber': '1',
    };

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/ecm/voucher-public/news',
        headers: headers,
        queryParameters: queryParameters,
      ),
    );
  }

  // Legacy methods for backward compatibility
  Future<MApi?> getNewPromotion({bool? isShowing}) async {
    return getPromotionCategories();
  }

  Future<MApi?> getNewEvent({bool? isShowing}) async {
    return getNewsCategories();
  }

  Future<MApi?> getEvent({required String categoryId}) async {
    return getItemsByCategory(categoryId: categoryId, pageSize: 1000);
  }

  /// Get voucher code for a specific storyline
  /// Equivalent to Ecm.getVoucherCode in Android
  Future<MApi?> getVoucherCode({required String storylineId}) async {
    return checkAuth(
      result: await BaseHttp.post(
        url: '$endpoint/api/v2/erp/storyline/$storylineId/voucher-code',
        headers: headers,
        body: {},
      ),
    );
  }
}
