package vn.zenity.betacineplex.model

/**
 * Created by tinhvv on 7/10/18.
 */

class NewNotification {
    var Id: Int? = null
    var NotificationCampaignId: Int? = null
    var AccountId: String? = null
    var ReadStatus: Boolean? = null
    var LastModifiedOnDate: String? = null
    var Title: String? = null
    var ScreenCode: Int = 0
    var RefId: String? = null
    var CreatedOnDate: String? = null
}

class NotificationDetail {
    var ImageThumb: String? = null
    var Title: String? = null
    var Content: String? = null
}

class NunberUnread {
    var Total: Int = 0
    var TotalRead: Int = 0
    var TotalUnRead: Int = 0
}