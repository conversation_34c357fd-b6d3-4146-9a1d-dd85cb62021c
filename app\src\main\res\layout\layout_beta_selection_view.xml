<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/border_gray_radius"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="@dimen/padding_small">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivIcon"
        android:layout_width="26dp"
        android:layout_height="26dp"
        android:layout_marginEnd="@dimen/padding_small"
        app:srcCompat="@drawable/name" />

    <View
        android:layout_width="1dp"
        android:layout_height="30dp"
        android:background="@color/grayLine" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvContent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:maxLines="1"
        android:paddingLeft="@dimen/padding_small"
        android:paddingRight="@dimen/padding_small"
        android:singleLine="true"
        android:textColor="@color/textDark"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/oswald_regular" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/btnDown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/ic_arrowdown" />
</LinearLayout>