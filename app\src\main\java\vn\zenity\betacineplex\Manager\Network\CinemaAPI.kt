package vn.zenity.betacineplex.Manager.Network

import retrofit2.http.GET
import kotlin.collections.ArrayList
import io.reactivex.Observable
import io.reactivex.Single
import retrofit2.http.Path
import retrofit2.http.Query
import vn.zenity.betacineplex.model.*

/**
 * Created by tinhvv on 4/14/18.
 */
interface CinemaAPI {
    @GET("api/v1/erp/cinemas") // Lấy tất cả các rạp -> màn hình danh sách rạp
    fun listCinema(): Observable<DDKCReponse<ArrayList<CinemaModel>>>

    @GET("api/v1/erp/cinemas/{cinemaId}")
    fun getCinemaDetail(@Path("cinemaId") cinemaId: String): Observable<DDKCReponse<CinemaModel>>

    @GET("api/v1/erp/cites/cinemas") // Lấy các rạp theo
    fun listCinemaByProvince(): Observable<DDKCReponse<ArrayList<CinemaProvinceModel>>>

    @GET("api/v1/erp/cinemas/{cinemaId}/show-dates") //Lấy ngày đang mở bán vé
    fun getShowDates(@Path("cinemaId") cinemaId: String): Observable<DDKCReponse<List<String>>>

    @GET("api/v2/erp/cinemas/{cinemaId}/shows") //Lấy danh sách phim đang bán vé
    fun getShows(@Path("cinemaId") cinemaId: String,
                 @Query("dateShow") dateShow: String): Observable<DDKCReponse<ArrayList<FilmModel>>>
}