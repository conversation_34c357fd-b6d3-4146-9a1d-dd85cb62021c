import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
import 'package:flutter_app/pages/cinema/choose/native_signalr_service.dart'
    as native;
import 'package:flutter_app/pages/cinema/choose/signal_connect.dart';
import 'package:flutter_app/pages/cinema/model/cinema_model.dart';
import 'package:flutter_app/pages/cinema/model/list_seat_model.dart';
import 'package:flutter_app/pages/cinema/model/seat_model.dart';
import 'package:flutter_app/pages/cinema/model/ticket_type.dart';
import 'package:flutter_app/pages/cinema/payment/payment_screen.dart';
import 'package:flutter_app/pages/cinema/widget/seat_grid.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/services/signalr_classic/signalr_classic_service.dart'
    as classic;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';

import '../../../constants/index.dart';
import '../../../cubit/index.dart';
import '../../../utils/index.dart';

class ChooseSeatScreen extends StatefulWidget {
  final String? cinemaId;
  final String? cinemaName;
  final ShowModel? showTime;
  final FilmModel? film;

  const ChooseSeatScreen({
    super.key,
    this.cinemaId,
    this.cinemaName,
    this.showTime,
    this.film,
  });

  @override
  State<ChooseSeatScreen> createState() => _ChooseSeatScreenState();
}

class _ChooseSeatScreenState extends State<ChooseSeatScreen> {
  ListSeatModel? _listSeat;
  List<SeatModel> _selectedSeats = [];
  bool _isLoading = true;
  String? _error;
  int _totalPrice = 0;
  Timer? _timer;
  int _remainingTime = 600; // 5 minutes in seconds
  bool _isConnected = false;
  double timeStartBooking = DateTime.now().millisecondsSinceEpoch / 1000;

  // Use the SignalRService singleton
  final SignalRService _signalRService = SignalRService.instance;

  // Use the native SignalRService on iOS
  final native.NativeSeatSignalRService _nativeSignalRService =
      native.NativeSeatSignalRService.instance;

  // Use the SignalRClassicService for SignalR Classic
  final classic.SignalRClassicService _signalRClassicService =
      classic.SignalRClassicService();

  @override
  void initState() {
    super.initState();
    _getSeatData();
    _initSignalR();
    _startTimer();
    context.read<AuthC>().check(context: context);
  }

  @override
  void dispose() {
    _timer?.cancel();

    // Leave the SignalR group before disposing
    if (widget.showTime?.showId != null && _signalRClassicService.isConnected) {
      _signalRClassicService.leaveGroup(widget.showTime!.showId!);
    }

    // Stop the appropriate SignalR service
    if (Platform.isIOS) {
      _nativeSignalRService.dispose();
    } else {
      // Try to disconnect SignalR Classic first
      try {
        if (_signalRClassicService.isConnected) {
          _signalRClassicService.disconnect();
        }
      } catch (e) {
        print("Error disconnecting SignalR Classic: $e");
      }

      // Also stop the Dart SignalR service
      _signalRService.stop();
    }

    super.dispose();
  }

  /// Initialize SignalR connection
  Future<void> _initSignalR() async {
    if (Platform.isIOS) {
      // Use native implementation on iOS
      _initNativeSignalR();
    } else {
      // Try to use SignalR Classic first
      try {
        await _initSignalRClassic();
      } catch (e) {
        print("Error initializing SignalR Classic: $e");
        // Fall back to Dart implementation
        _initDartSignalR();
      }
    }
  }

  /// Initialize SignalR Classic connection
  Future<void> _initSignalRClassic() async {
    // Initialize the SignalR Classic service
    await _signalRClassicService.initialize();

    // Add connection listener
    _signalRClassicService.addConnectionListener((isConnected) {
      setState(() {
        _isConnected = isConnected;
      });

      if (isConnected) {
        print(
            'SignalR Classic connected with ID: ${_signalRClassicService.connectionId}');
      } else {
        print('SignalR Classic disconnected');
      }
    });

    // Add data listener
    _signalRClassicService.addDataListener((data) {
      print(
          'SignalR Classic data received: ${data.connectionId}, ${data.showId}, ${data.seatIndex}, ${data.seatStatus}');

      // Validate the message is for the current show
      if (data.showId != widget.showTime?.showId) {
        print(
            "Ignoring message for different show: ${data.showId} (current: ${widget.showTime?.showId})");
        return;
      }

      // Only process updates from other users
      if (_signalRClassicService.connectionId != data.connectionId) {
        print(
            "Processing seat update from user ${data.connectionId}: Seat index ${data.seatIndex}, status ${data.seatStatus}");
        _setSeatIndex(data.seatIndex, data.seatStatus);
      } else {
        print(
            "Ignoring own seat update: Seat index ${data.seatIndex}, status ${data.seatStatus}");
      }
    });

    // Connect to the SignalR hub
    final connected = await _signalRClassicService.connect(
      // 'https://www.betacinemas.vn',
      'http://dev.betacineplex.vn',
      hubName: 'chooseSeatHub',
    );

    if (!connected) {
      throw Exception("Failed to connect to SignalR Classic");
    }

    // Join the group for this show
    if (widget.showTime?.showId != null) {
      final joinedGroup =
          await _signalRClassicService.joinGroup(widget.showTime!.showId!);
      if (joinedGroup) {
        print("Successfully joined SignalR group: ${widget.showTime!.showId!}");
      } else {
        print("Failed to join SignalR group: ${widget.showTime!.showId!}");
      }
    }
  }

  /// Initialize native SignalR connection on iOS
  Future<void> _initNativeSignalR() async {
    // Add connection listener to track connection state
    _nativeSignalRService
        .addConnectionListener(_onNativeSignalRConnectionState);

    // Add data listener to receive seat updates
    _nativeSignalRService.addDataListener(_onNativeSeatUpdate);

    try {
      // Start the SignalR connection
      // Sử dụng URL chính xác như trong iOS: Config.SignalRURL = "https://www.betacinemas.vn/signalr/hubs"
      await _nativeSignalRService.startSignalR(
        customUrl:
            'http://dev.betacineplex.vn/signalr/hubs', /*'https://www.betacinemas.vn/signalr/hubs'*/
      );

      // Không cần gọi joinGroup như trong iOS
      // iOS không gọi phương thức joinGroup mà chỉ đăng ký lắng nghe sự kiện broadcastMessage
      // và lọc tin nhắn dựa trên showId
      print(
          "SignalR connection established - listening for updates for show ID: ${widget.showTime?.showId}");
    } catch (e) {
      print("Error initializing native SignalR: $e");
      _showConnectionError(
          "Không thể kết nối đến máy chủ. Vui lòng thử lại sau.");
    }
  }

  /// Initialize Dart SignalR connection on Android
  Future<void> _initDartSignalR() async {
    // Add connection listener to track connection state
    _signalRService.addConnectionListener(_onSignalRConnectionState);

    // Add data listener to receive seat updates
    _signalRService.addDataListener(_onSeatUpdate);

    try {
      // Start the SignalR connection
      await _signalRService.startSignalR();

      // Join the group for this show if we have a show ID
      if (widget.showTime?.showId != null) {
        await _joinGroup();
      }
    } catch (e) {
      print("Error initializing SignalR: $e");
      _showConnectionError(
          "Không thể kết nối đến máy chủ. Vui lòng thử lại sau.");
    }
  }

  /// Handle connection state changes for Dart implementation
  void _onSignalRConnectionState(bool isConnected) {
    if (!mounted) return;

    setState(() {
      _isConnected = isConnected;
    });

    if (isConnected) {
      print('SignalR connected');
      // Join the group when connected
      if (widget.showTime?.showId != null) {
        _joinGroup();
      }
    } else {
      print('SignalR disconnected');
    }
  }

  /// Handle connection state changes for native implementation
  void _onNativeSignalRConnectionState(bool isConnected) {
    print(
        'Native SignalR connection state changed: $isConnected (previous state: $_isConnected)');

    if (!mounted) return;

    setState(() {
      _isConnected = isConnected;
    });

    if (isConnected) {
      print('Native SignalR connected');
      // Không cần gọi joinGroup như trong iOS
      // Chỉ cần lắng nghe sự kiện broadcastMessage
      print("Ready to receive updates for show ID: ${widget.showTime?.showId}");

      // In ra thông tin kết nối chi tiết
      final connectionId = _nativeSignalRService.connectionId;
      print("Connection ID seat: $connectionId");

      // Kiểm tra xem trạng thái kết nối có đồng bộ không
      print(
          "Connection state check - Native service: ${_nativeSignalRService.isConnected}, Local variable: $_isConnected");
    } else {
      print('Native SignalR disconnected');
    }
  }

  /// Handle seat updates from Dart SignalR
  void _onSeatUpdate(SeatSignalrResponse data) {
    print(
        'Seat update received: ${data.collectionId}, ${data.showId}, ${data.seatIndex}, ${data.seatStatus}');

    // Validate the message is for the current show
    if (data.showId != widget.showTime?.showId) {
      print(
          "Ignoring message for different show: ${data.showId} (current: ${widget.showTime?.showId})");
      return;
    }

    // Only process updates from other users
    if (_signalRService.connectionId() != data.collectionId) {
      print(
          "Processing seat update from user ${data.collectionId}: Seat index ${data.seatIndex}, status ${data.seatStatus}");
      _setSeatIndex(data.seatIndex, data.seatStatus);
    } else {
      print(
          "Ignoring own seat update: Seat index ${data.seatIndex}, status ${data.seatStatus}");
    }
  }

  /// Handle seat updates from native SignalR
  void _onNativeSeatUpdate(native.SeatSignalrResponse data) {
    print(
        'Native seat update received: ${data.connectionId}, ${data.showId}, ${data.seatIndex}, ${data.seatStatus}');

    // Validate the message is for the current show
    if (data.showId != widget.showTime?.showId) {
      print(
          "Ignoring message for different show: ${data.showId} (current: ${widget.showTime?.showId})");
      return;
    }

    // Only process updates from other users
    if (_nativeSignalRService.connectionId != data.connectionId) {
      print(
          "Processing seat update from user ${data.connectionId}: Seat index ${data.seatIndex}, status ${data.seatStatus}");
      _setSeatIndex(data.seatIndex, data.seatStatus);
    } else {
      print(
          "Ignoring own seat update: Seat index ${data.seatIndex}, status ${data.seatStatus}");
    }
  }

  /// Updates the status of a seat based on its index
  /// This is called when receiving updates from other users via SignalR
  ///
  /// @param seatIndex The index of the seat to update
  /// @param status The new status of the seat (0 = empty, 1 = selecting, 2 = sold)
  void _setSeatIndex(int seatIndex, int status) {
    if (_listSeat == null ||
        _listSeat!.screen == null ||
        _listSeat!.screen!.seatPosition == null) {
      print("Cannot update seat: Seat data not loaded");
      return;
    }

    // Find the seat with the given index
    SeatModel? targetSeat;
    for (var row in _listSeat!.screen!.seatPosition!) {
      for (var seat in row) {
        if (seat.seatIndex == seatIndex) {
          targetSeat = seat;
          break;
        }
      }
      if (targetSeat != null) break;
    }

    if (targetSeat == null) {
      print("Cannot update seat: Seat with index $seatIndex not found");
      return;
    }

    // Update the seat status
    if (!mounted) return;

    setState(() {
      if (targetSeat != null) {
        if (status == SeatSoldStatus.EMPTY.index) {
          // If the seat is being deselected, remove it from selected seats
          _selectedSeats
              .removeWhere((s) => s.seatNumber == targetSeat!.seatNumber);
          targetSeat.soldStatus = SeatSoldStatus.EMPTY;

          // If it's a couple seat, also update its partner
          if (targetSeat.coupleSeat != null) {
            _selectedSeats.removeWhere(
                (s) => s.seatNumber == targetSeat?.coupleSeat!.seatNumber);
            targetSeat.coupleSeat!.soldStatus = SeatSoldStatus.EMPTY;
          }
        } else if (status == SeatSoldStatus.SELECTING.index) {
          // If the seat is being selected by someone else, mark it as selecting
          targetSeat.soldStatus = SeatSoldStatus.SELECTING;

          // If it's a couple seat, also update its partner
          if (targetSeat.coupleSeat != null) {
            targetSeat.coupleSeat!.soldStatus = SeatSoldStatus.SELECTING;
          }
        } else if (status == SeatSoldStatus.SOLD.index) {
          // If the seat is being marked as sold, update its status
          targetSeat.soldStatus = SeatSoldStatus.SOLD;

          // If it's a couple seat, also update its partner
          if (targetSeat.coupleSeat != null) {
            targetSeat.coupleSeat!.soldStatus = SeatSoldStatus.SOLD;
          }
        }
      }
    });

    // Update the total price if needed
    _updateTotalPrice();
  }

  /// Join the seat selection group for real-time updates (Dart implementation)
  Future<void> _joinGroup() async {
    if (!_isConnected || widget.showTime?.showId == null) {
      print("Cannot join group: Not connected or show ID is null");
      return;
    }

    try {
      print("Joining group: ${widget.showTime!.showId!}");

      // Use the invoke method to join the group
      await _signalRService.invoke("JoinGroup", [widget.showTime!.showId!]);

      print("Successfully joined group: ${widget.showTime!.showId!}");
    } catch (e) {
      print("Error joining group: $e");

      // If joining fails, try again after a delay
      if (mounted && _isConnected) {
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted && _isConnected) {
            _joinGroup();
          }
        });
      }
    }
  }

  /// Shows a connection error message to the user
  void _showConnectionError(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'Thử lại',
          onPressed: () {
            _initSignalR();
          },
        ),
      ),
    );
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        if (_remainingTime > 0) {
          _remainingTime--;
        } else {
          _timer?.cancel();
          _showTimeoutDialog();
        }
      });
    });
  }

  void _showTimeoutDialog() {
    UDialog().showError(
      title: 'Hết thời gian',
      text: 'Thời gian chọn ghế đã hết. Vui lòng thử lại.',
      onTap: () {
        Navigator.of(context).pop();
        Navigator.of(context).pop();
      },
    );
    // showDialog(
    //   context: context,
    //   barrierDismissible: false,
    //   builder: (context) => AlertDialog(
    //     title: const Text('Hết thời gian'),
    //     content: const Text('Thời gian chọn ghế đã hết. Vui lòng thử lại.'),
    //     actions: [
    //       TextButton(
    //         onPressed: () {
    //           Navigator.of(context).pop();
    //           Navigator.of(context).pop();
    //         },
    //         child: const Text('OK'),
    //       ),
    //     ],
    //   ),
    // );
  }

  Future<void> _getSeatData() async {
    var token = await ApiService.getToken();
    print('token: ${token ?? "not available"}');

    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final api = RepositoryProvider.of<Api>(context).film;
      final response = await api.getShowSeat(id: widget.showTime?.showId ?? '');

      if (!mounted) return;

      if (response != null) {
        final seatData = ListSeatModel.fromJson(response.data);

        // Process couple seats to link them together
        _processCoupleSeatLinks(seatData);

        setState(() {
          _listSeat = seatData;
          // _selectedSeats = seatData.showSeats!.sublist(0,1);
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
          _error = "Không thể tải thông tin ghế ngồi";
        });
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _error = "Lỗi: $e";
      });
    }
  }

  // Process and link couple seats based on their position in the grid
  void _processCoupleSeatLinks(ListSeatModel listSeat) {
    if (listSeat.screen?.seatPosition == null) return;

    for (var row in listSeat.screen!.seatPosition!) {
      for (int i = 0; i < row.length; i++) {
        final seat = row[i];

        // Skip if not a couple seat or already processed
        if (seat.seatTypeEnum != SeatType.COUPLE || seat.coupleSeat != null)
          continue;

        // Find the adjacent seat that forms the couple
        // Check the next seat in the row
        if (i < row.length - 1) {
          final nextSeat = row[i + 1];
          if (nextSeat.seatTypeEnum == SeatType.COUPLE &&
              nextSeat.coupleSeat == null &&
              _areAdjacentSeatNumbers(seat.seatNumber, nextSeat.seatNumber)) {
            // Link the seats
            seat.coupleSeat = nextSeat;
            nextSeat.coupleSeat = seat;
          }
        }
      }
    }
  }

  // Check if two seat numbers are adjacent (e.g., A1 and A2)
  bool _areAdjacentSeatNumbers(String? seat1, String? seat2) {
    if (seat1 == null || seat2 == null || seat1.isEmpty || seat2.isEmpty)
      return false;

    // Check if they have the same row letter
    if (seat1.substring(0, 1) != seat2.substring(0, 1)) return false;

    // Extract the seat numbers
    int? num1 = int.tryParse(seat1.substring(1));
    int? num2 = int.tryParse(seat2.substring(1));

    if (num1 == null || num2 == null) return false;

    // Check if they are consecutive numbers
    return (num1 + 1 == num2) || (num2 + 1 == num1);
  }

  /// Sends a seat status update to the SignalR hub
  /// This matches the iOS implementation's sendSeat method
  ///
  /// @param seat The seat being updated
  /// @param status The new status of the seat (0 = empty, 1 = selecting, 2 = sold)
  Future<void> _sendSeat(SeatModel seat, int status) async {
    // Kiểm tra trạng thái kết nối từ native code thay vì biến _isConnected
    bool isConnectedNow =
        Platform.isIOS ? _nativeSignalRService.isConnected : _isConnected;

    if (!isConnectedNow) {
      print(
          "Cannot send seat update: SignalR not connected (native: ${_nativeSignalRService.isConnected}, local: $_isConnected)");
      return;
    }

    try {
      print(
          "Sending seat update: Seat ${seat.seatNumber} (${seat.seatIndex}) to status $status");

      if (Platform.isIOS) {
        // Use the native SignalR service on iOS
        await _nativeSignalRService.sendSeat(
            widget.showTime?.showId ?? "", seat.seatIndex ?? 0, status);
      } else {
        // Try to use SignalR Classic first
        try {
          if (_signalRClassicService.isConnected) {
            await _signalRClassicService.sendSeat(
                widget.showTime?.showId ?? "", seat.seatIndex ?? 0, status);
          } else {
            // Fall back to Dart SignalR service
            await _signalRService.sendSeat(
                widget.showTime?.showId ?? "", seat.seatIndex ?? 0, status);
          }
        } catch (e) {
          // Fall back to Dart SignalR service
          print("Error sending seat update with SignalR Classic: $e");
          await _signalRService.sendSeat(
              widget.showTime?.showId ?? "", seat.seatIndex ?? 0, status);
        }
      }

      print("Seat update sent successfully");
    } catch (e) {
      print("Error sending seat update: $e");

      // Show error to user
      if (mounted && _isConnected) {
        UDialog().showError(
          text: "Lỗi cập nhật ghế: $e",
        );

        ///thay thế snackbar thông báo bằng udialog().showError
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(content: Text('Lỗi cập nhật ghế: $e')),
        // );
      }
    }
  }

  void _selectSeat(SeatModel seat) {
    // Kiểm tra trạng thái kết nối từ native code thay vì biến _isConnected
    bool isConnectedNow =
        Platform.isIOS ? _nativeSignalRService.isConnected : _isConnected;

    if (!isConnectedNow) {
      print(
          "Cannot select seat: SignalR not connected (native: ${_nativeSignalRService.isConnected}, local: $_isConnected)");
      // ScaffoldMessenger.of(context).showSnackBar(
      //   const SnackBar(content: Text('Không thể kết nối đến máy chủ. Vui lòng thử lại sau.')),
      // );
      UDialog().showError(
        text:
            'Không thể kết nối đến máy chủ. Vui lòng đợi một lúc hoặc thử lại sau.',
      );
      return;
    }

    if (seat.soldStatus != SeatSoldStatus.EMPTY) return;
    if (seat.isWay == true || seat.isBroken == true || seat.isNotUsed == true)
      return;

    // Check if we've reached the maximum number of seats
    if (_selectedSeats.length >= 8 ||
        (seat.seatTypeEnum == SeatType.COUPLE && _selectedSeats.length >= 7)) {
      UDialog().showError(
        title: 'Lỗi chọn ghế',
        text: 'Bạn chỉ có thể chọn tối đa 8 ghế (bao gồm cả ghế đôi)',
      );
      // ScaffoldMessenger.of(context).showSnackBar(
      //   const SnackBar(content: Text('Bạn chỉ có thể chọn tối đa 8 ghế')),
      // );
      return;
    }

    // Check if selecting this seat would leave a single seat between occupied seats
    if (!_checkValidSeatSelection(seat)) {
      return;
    }

    if (!mounted) return;

    setState(() {
      _selectedSeats.add(seat);
      _sendSeat(seat, SeatSoldStatus.SELECTING.index);

      if (seat.coupleSeat != null) {
        _selectedSeats.add(seat.coupleSeat!);
        _sendSeat(seat.coupleSeat!, SeatSoldStatus.SELECTING.index);
      }
    });

    _updateTotalPrice();
  }

  bool _checkValidSeatSelection(SeatModel seat) {
    // Tìm hàng ghế chứa ghế hiện tại
    List<SeatModel>? seatRow;
    int seatIndex = -1;

    for (var row in _listSeat!.screen!.seatPosition!) {
      final index = row.indexWhere((s) => s.seatIndex == seat.seatIndex);
      if (index != -1) {
        seatRow = row;
        seatIndex = index;
        break;
      }
    }

    if (seatRow == null || seatIndex == -1) return true;

    // Tạo một bản sao của danh sách ghế đã chọn và thêm ghế hiện tại vào
    final List<SeatModel> simulatedSelectedSeats = List.from(_selectedSeats);
    simulatedSelectedSeats.add(seat);

    // Nếu là ghế đôi, thêm cả ghế đôi vào danh sách mô phỏng
    if (seat.coupleSeat != null) {
      simulatedSelectedSeats.add(seat.coupleSeat!);
    }

    // Tạo một bản sao của hàng ghế với trạng thái mô phỏng
    final List<SeatModel> simulatedRow = List.from(seatRow);
    for (var selectedSeat in simulatedSelectedSeats) {
      final index =
          simulatedRow.indexWhere((s) => s.seatIndex == selectedSeat.seatIndex);
      if (index != -1) {
        simulatedRow[index] =
            selectedSeat.copyWith(soldStatus: SeatSoldStatus.SELECTING);
      }
    }

    // Kiểm tra 1: Không để ghế trống ở giữa
    // Tìm các nhóm ghế đã chọn trong hàng
    final List<List<int>> selectedGroups = [];
    List<int> currentGroup = [];

    for (int i = 0; i < simulatedRow.length; i++) {
      final seat = simulatedRow[i];

      // Nếu ghế đang được chọn hoặc đã bán
      if (seat.soldStatus != SeatSoldStatus.EMPTY &&
          !seat.isWay &&
          !seat.isBroken &&
          !seat.isNotUsed) {
        currentGroup.add(i);
      } else if (currentGroup.isNotEmpty) {
        // Kết thúc nhóm hiện tại
        selectedGroups.add(List.from(currentGroup));
        currentGroup = [];
      }
    }

    // Thêm nhóm cuối cùng nếu có
    if (currentGroup.isNotEmpty) {
      selectedGroups.add(currentGroup);
    }

    // Kiểm tra ghế trống giữa các nhóm
    for (int i = 0; i < selectedGroups.length - 1; i++) {
      final group1 = selectedGroups[i];
      final group2 = selectedGroups[i + 1];

      // Nếu có đúng 1 ghế trống giữa 2 nhóm
      if (group1.last + 2 == group2.first) {
        final emptySeat = simulatedRow[group1.last + 1];
        if (emptySeat.soldStatus == SeatSoldStatus.EMPTY &&
            !emptySeat.isWay &&
            !emptySeat.isBroken &&
            !emptySeat.isNotUsed) {
          String seatName = emptySeat.seatNumber ?? '';
          UDialog().showError(
            title: 'Lỗi chọn ghế',
            text: 'Không thể để trống ghế $seatName giữa các ghế đã chọn',
          );
          return false;
        }
      }
    }

    // Kiểm tra 2: Không để ghế trống ở bên trái hoặc bên phải
    for (final group in selectedGroups) {
      // Kiểm tra số ghế trống bên trái
      int leftEmptyCount = 0;
      int index = group.first - 1;
      while (index >= 0) {
        final seat = simulatedRow[index];
        if (seat.soldStatus == SeatSoldStatus.EMPTY &&
            !seat.isWay &&
            !seat.isBroken &&
            !seat.isNotUsed) {
          leftEmptyCount++;
        } else {
          break;
        }
        index--;
      }

      // Kiểm tra số ghế trống bên phải
      int rightEmptyCount = 0;
      index = group.last + 1;
      while (index < simulatedRow.length) {
        final seat = simulatedRow[index];
        if (seat.soldStatus == SeatSoldStatus.EMPTY &&
            !seat.isWay &&
            !seat.isBroken &&
            !seat.isNotUsed) {
          rightEmptyCount++;
        } else {
          break;
        }
        index++;
      }

      // Nếu có đúng 1 ghế trống ở bên trái và có ít nhất 1 ghế trống ở bên phải
      if (leftEmptyCount == 1 && rightEmptyCount > 0) {
        String seatName = simulatedRow[group.first - 1].seatNumber ?? '';
        UDialog().showError(
          title: 'Lỗi chọn ghế',
          text:
              'Không thể để trống ghế $seatName ở bên trái của các ghế đã chọn',
        );
        return false;
      }

      // Nếu có đúng 1 ghế trống ở bên phải và có ít nhất 1 ghế trống ở bên trái
      if (rightEmptyCount == 1 && leftEmptyCount > 0) {
        String seatName = simulatedRow[group.last + 1].seatNumber ?? '';
        UDialog().showError(
          title: 'Lỗi chọn ghế',
          text:
              'Không thể để trống ghế $seatName ở bên phải của các ghế đã chọn',
        );
        return false;
      }
    }

    return true;
  }

  void _deselectSeat(SeatModel seat) {
    // Kiểm tra trạng thái kết nối từ native code thay vì biến _isConnected
    bool isConnectedNow =
        Platform.isIOS ? _nativeSignalRService.isConnected : _isConnected;

    if (!isConnectedNow) {
      print(
          "Cannot deselect seat: SignalR not connected (native: ${_nativeSignalRService.isConnected}, local: $_isConnected)");
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content:
                Text('Không thể kết nối đến máy chủ. Vui lòng thử lại sau.')),
      );
      return;
    }

    setState(() {
      _selectedSeats.removeWhere((s) => s.seatNumber == seat.seatNumber);
      _sendSeat(seat, SeatSoldStatus.EMPTY.index);

      if (seat.coupleSeat != null) {
        _selectedSeats
            .removeWhere((s) => s.seatNumber == seat.coupleSeat!.seatNumber);
        _sendSeat(seat.coupleSeat!, SeatSoldStatus.EMPTY.index);
      }
    });

    _updateTotalPrice();
  }

  void _updateTotalPrice() {
    int total = 0;

    for (var seat in _selectedSeats) {
      if (seat.seatTypeEnum == SeatType.NORMAL) {
        final normalTicket = _listSeat?.ticketTypes?.firstWhere(
          (ticket) => ticket.isNormal,
          orElse: () => TicketType(price: 0),
        );
        total += normalTicket?.price ?? 0;
      } else if (seat.seatTypeEnum == SeatType.VIP) {
        final vipTicket = _listSeat?.ticketTypes?.firstWhere(
          (ticket) => ticket.isVip,
          orElse: () => TicketType(price: 0),
        );
        total += vipTicket?.price ?? 0;
      } else if (seat.seatTypeEnum == SeatType.COUPLE) {
        final coupleTicket = _listSeat?.ticketTypes?.firstWhere(
          (ticket) => ticket.isCouple,
          orElse: () => TicketType(price: 0),
        );
        total += coupleTicket?.price ?? 0;
      }
    }

    setState(() {
      _totalPrice = total;
    });
  }

  void _proceedToPayment() {
    if (_selectedSeats.isEmpty) {
      UDialog().showError(
        title: 'Lỗi chọn ghế',
        text: 'Vui lòng chọn ít nhất một ghế trước khi thanh toán',
      );
      return;
    }

    // Check age restriction if needed
    String? ageConfirmText;
    final restrictAge = widget.film?.FilmRestrictAgeName ?? "";
    if (restrictAge.contains("13")) {
      ageConfirmText = "Phim này chỉ dành cho khán giả từ 13 tuổi trở lên";
    } else if (restrictAge.contains("16")) {
      ageConfirmText = "Phim này chỉ dành cho khán giả từ 16 tuổi trở lên";
    } else if (restrictAge.contains("18")) {
      ageConfirmText = "Phim này chỉ dành cho khán giả từ 18 tuổi trở lên";
    }

    if (ageConfirmText != null) {
      UDialog().showConfirm(
          text: ageConfirmText,
          title: 'Xác nhận độ tuổi',
          btnCancelOnPress: () {
            Navigator.pop(context);
          },
          btnOkOnPress: () {
            Navigator.pop(context);
            _navigateToPayment();
          },
          btnCancelText: 'Hủy',
          btnOkText: 'Xác nhận');
      // showDialog(
      //   context: context,
      //   builder: (context) => AlertDialog(
      //     title: const Text('Xác nhận độ tuổi'),
      //     content: Text(ageConfirmText!),
      //     actions: [
      //       TextButton(
      //         onPressed: () => Navigator.pop(context),
      //         child: const Text('Hủy'),
      //       ),
      //       TextButton(
      //         onPressed: () {
      //           Navigator.pop(context);
      //           _navigateToPayment();
      //         },
      //         child: const Text('Xác nhận'),
      //       ),
      //     ],
      //   ),
      // );
    } else {
      _navigateToPayment();
    }
  }

  void _navigateToPayment() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaymentScreen(
          cinemaId: widget.cinemaId,
          cinemaName: widget.cinemaName,
          showTime: widget.showTime,
          film: widget.film,
          selectedSeats: _selectedSeats,
          listSeat: _listSeat,
          totalPrice: _totalPrice,
          remainingTime: _remainingTime,
          timeStartBooking: timeStartBooking,
        ),
      ),
    );
  }

  void _showAllSeats() {
    final message = _selectedSeats.map((seat) => seat.seatNumber).join("    ");

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Ghế đã chọn'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    final formatter = NumberFormat("#,###", "vi_VN");

    return Scaffold(
      appBar: appBar(
        title: 'Đặt vé theo phim',
        titleColor: Colors.white,
        actions: [
          TextButton.icon(
            onPressed: _showAllSeats,
            icon: const Icon(Icons.event_seat, color: Colors.white),
            label: Text(
              '${_selectedSeats.length}',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text(_error!))
              : SafeArea(
                  child: Column(
                    children: [
                      // Film info header with background image - giống iOS
                      Container(
                        height: 120, // Tăng chiều cao như iOS
                        width: double.infinity,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withAlpha(179), // 0.7 * 255 = 179
                              Colors.black.withAlpha(230), // 0.9 * 255 = 230
                            ],
                          ),
                        ),
                        child: Stack(
                          children: [
                            // Background image with opacity
                            if (widget.film?.MainPosterUrl != null)
                              Positioned.fill(
                                child: Opacity(
                                  opacity: 0.3, // Giữ nguyên độ mờ
                                  child: Image.network(
                                    '${ApiService.baseUrlImage}/${widget.film!.MainPosterUrl}',
                                    fit: BoxFit.cover,
                                    errorBuilder: (_, __, ___) => Container(
                                      color: Colors.grey.shade800,
                                    ),
                                  ),
                                ),
                              ),

                            // Film details centered
                            Positioned.fill(
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 16),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // Film name - giống iOS với font lớn hơn
                                    Text(
                                      widget.film?.Name ?? 'Unknown Film',
                                      textAlign: TextAlign.center,
                                      style: const TextStyle(
                                        fontSize: 22, // Tăng kích thước font
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(
                                        height: 10), // Tăng khoảng cách

                                    // Film details - giống iOS với font nhỏ hơn
                                    Text(
                                      '${_listSeat?.formatName ?? ''} | ${Convert.date(_listSeat?.gioChieu ?? '')} ${Convert.hours(_listSeat?.gioChieu ?? '')} | Phòng: ${_listSeat?.phongChieu ?? ''}',
                                      textAlign: TextAlign.center,
                                      style: const TextStyle(
                                        fontSize: 15, // Tăng kích thước font
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Combined seat pricing information and legend - giống iOS
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color:
                              Colors.grey.shade100, // Màu nền sáng hơn như iOS
                          border: Border(
                            bottom: BorderSide(
                                color: Colors.grey.shade300,
                                width: 1), // Đường viền đậm hơn
                          ),
                        ),
                        child: Row(
                          crossAxisAlignment:
                              CrossAxisAlignment.start, // Căn đầu các phần tử
                          children: [
                            // Seat legend - giống iOS
                            Expanded(
                              flex: 4, // Tăng flex để có nhiều không gian hơn
                              child: Column(
                                spacing: CSpace.base,
                                crossAxisAlignment:
                                    CrossAxisAlignment.start, // Căn phải
                                children: [
                                  _buildLegendItem('Ghế trống',
                                      'assets/icon/cinema/ic_empty_vip_seat.svg'),
                                  _buildLegendItem('Ghế đang được giữ',
                                      'assets/icon/cinema/ic_process_vip_seat.svg'),
                                  _buildLegendItem('Ghế đang chọn',
                                      'assets/icon/cinema/ic_select_vip_seat.svg'),
                                  _buildLegendItem('Ghế đã bán',
                                      'assets/icon/cinema/ic_sold_vip_seat.svg'),
                                  _buildLegendItem('Ghế đã đặt trước',
                                      'assets/icon/cinema/ic_set_vip_seat.svg'),

                                  // Wrap(
                                  //   spacing: 8,
                                  //   runSpacing: 8,
                                  //   alignment: WrapAlignment.end,
                                  //   children: [
                                  //     ],
                                  // ),
                                ],
                              ),
                            ),
                            // Seat pricing information - giống iOS
                            Expanded(
                              flex: 3, // Tăng flex để có nhiều không gian hơn
                              child: _listSeat?.ticketTypes != null &&
                                      _listSeat!.ticketTypes!.isNotEmpty
                                  ? Column(
                                      // crossAxisAlignment: CrossAxisAlignment.end,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        ...(_buildTicketTypePriceLabels()),
                                      ],
                                    )
                                  : Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        _buildPriceLabel(
                                          'Ghế thường',
                                          '0đ',
                                          svgAsset:
                                              'assets/icon/cinema/ic_empty_normal_seat.svg',
                                        ),
                                        _buildPriceLabel(
                                          'Ghế VIP',
                                          '0đ',
                                          svgAsset:
                                              'assets/icon/cinema/ic_empty_vip_seat.svg',
                                        ),
                                        _buildPriceLabel(
                                          'Ghế đôi',
                                          '0đ',
                                          svgAsset:
                                              'assets/icon/cinema/ic_empty_couple_seat.svg',
                                        ),
                                      ],
                                    ),
                            ),
                          ],
                        ),
                      ),

                      // Screen indicator - giống iOS
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(
                            top: 4, bottom: 4), // Tăng margin như iOS
                        child: Column(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black
                                        .withAlpha(77), // 0.3 * 255 = 77
                                    blurRadius: 15, // Tăng độ mờ
                                    offset: const Offset(0, 5),
                                    spreadRadius: 1, // Tăng độ lan
                                  ),
                                ],
                              ),
                              child: SvgPicture.asset(
                                'assets/icon/cinema/ic_screen.svg',
                                width: double.infinity,
                                height: 40, // Tăng chiều cao
                              ),
                            ),
                            const SizedBox(height: 4), // Tăng khoảng cách
                            const Text(
                              'MÀN HÌNH',
                              style: TextStyle(
                                fontSize: 12, // Tăng kích thước font
                                color: Colors.grey,
                                fontWeight: FontWeight.w600, // Đậm hơn
                                letterSpacing: 1.0, // Thêm khoảng cách chữ
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Seat grid - giống iOS
                      Expanded(
                        flex: 5, // Increased flex to make it much taller
                        child: SeatGrid(
                          seatPositions: _listSeat?.screen?.seatPosition ?? [],
                          selectedSeats: _selectedSeats,
                          onSeatSelected: _selectSeat,
                          onSeatDeselected: _deselectSeat,
                        ),
                      ),

                      // Timer and booking button - giống iOS
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8), // Tăng padding
                        decoration: BoxDecoration(
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color:
                                  Colors.black.withAlpha(26), // 0.1 * 255 = 26
                              blurRadius: 6, // Tăng độ mờ
                              offset: const Offset(0, -3), // Điều chỉnh offset
                              spreadRadius: 1, // Tăng độ lan
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            // Timer - giống iOS
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  const Text(
                                    'Thời gian còn lại: ',
                                    style: TextStyle(
                                      fontSize: 22,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.black,
                                    ),
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10,
                                        vertical: 0), // Tăng padding
                                    // decoration: BoxDecoration(
                                    //   color: _remainingTime < 60 ? Colors.red : Colors.green,
                                    //   borderRadius: BorderRadius.circular(6), // Tăng bo góc
                                    // ),
                                    child: Text(
                                      _formatTime(_remainingTime),
                                      style: const TextStyle(
                                        color: Colors.black,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 22, // Tăng kích thước font
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // Only show the price and continue button if seats are selected - giống iOS
                            if (_selectedSeats.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.only(
                                    top: 12), // Tăng padding
                                child: Row(
                                  children: [
                                    Expanded(
                                      flex: 2, // Tăng flex cho phần giá
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const Text(
                                            'Tổng tiền',
                                            style: TextStyle(
                                              fontSize:
                                                  15, // Tăng kích thước font
                                              color: Colors.grey,
                                            ),
                                          ),
                                          Text(
                                            '${formatter.format(_totalPrice)} đ',
                                            style: const TextStyle(
                                              fontSize:
                                                  20, // Tăng kích thước font
                                              fontWeight: FontWeight.bold,
                                              color: Colors.red,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      // flex: 3, // Tăng flex cho nút tiếp tục
                                      child: InkWell(
                                        onTap: _proceedToPayment,
                                        child: Container(
                                          height: 50, // Tăng chiều cao
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              SvgPicture.asset(
                                                'assets/icon/cinema/btnBookingdetail.svg',
                                                width: double.infinity,
                                                fit: BoxFit.fill,
                                              ),
                                              const Text(
                                                'Tiếp tục',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize:
                                                      18, // Tăng kích thước font
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
    );
  }

  Widget _buildLegendItem(String label, String svgAsset) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      // decoration: BoxDecoration(
      //   color: Colors.grey.shade50,
      //   borderRadius: BorderRadius.circular(4),
      //   border: Border.all(color: Colors.grey.shade200),
      // ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            svgAsset,
            width: 20,
            height: 20,
          ),
          const SizedBox(width: 3),
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildTicketTypePriceLabels() {
    final formatter = NumberFormat('#,###', 'vi_VN');
    final List<Widget> items = [];

    // Group ticket types by seat type
    final Map<String, TicketType> seatTypeToTicket = {};

    for (var ticket in _listSeat!.ticketTypes!) {
      if (ticket.seatTypeId != null) {
        seatTypeToTicket[ticket.seatTypeId!] = ticket;
      }
    }

    // Normal seat
    if (seatTypeToTicket.containsKey('c0f1e9a8-c9f5-4b0d-8b10-f3108996e60b')) {
      final ticket = seatTypeToTicket['c0f1e9a8-c9f5-4b0d-8b10-f3108996e60b']!;
      final price = ticket.price ?? 0;
      items.add(_buildPriceLabel(
        'Ghế thường',
        '${formatter.format(price)} đ',
        svgAsset: 'assets/icon/cinema/ic_empty_normal_seat.svg',
      ));
    }

    // VIP seat
    if (seatTypeToTicket.containsKey('9f2dda7f-d09e-4d58-a504-5a6311345aae')) {
      final ticket = seatTypeToTicket['9f2dda7f-d09e-4d58-a504-5a6311345aae']!;
      final price = ticket.price ?? 0;
      items.add(_buildPriceLabel(
        'Ghế VIP',
        '${formatter.format(price)} đ',
        svgAsset: 'assets/icon/cinema/ic_empty_vip_seat.svg',
      ));
    }

    // Couple seat
    if (seatTypeToTicket.containsKey('9beee28c-8cae-41d0-bd01-b0b22108432c')) {
      final ticket = seatTypeToTicket['9beee28c-8cae-41d0-bd01-b0b22108432c']!;
      final price = ticket.price! * 2 ?? 0;
      items.add(_buildPriceLabel(
        'Ghế đôi',
        '${formatter.format(price)} đ',
        svgAsset: 'assets/icon/cinema/ic_empty_couple_seat.svg',
      ));
    }

    // If no items were added, use default items
    if (items.isEmpty) {
      items.add(_buildPriceLabel(
        'Ghế thường',
        '80,000đ',
        svgAsset: 'assets/icon/cinema/ic_empty_normal_seat.svg',
      ));
      items.add(_buildPriceLabel(
        'Ghế VIP',
        '100,000đ',
        svgAsset: 'assets/icon/cinema/ic_empty_vip_seat.svg',
      ));
      items.add(_buildPriceLabel(
        'Ghế đôi',
        '160,000đ',
        svgAsset: 'assets/icon/cinema/ic_empty_couple_seat.svg',
      ));
    }

    return items;
  }

  Widget _buildPriceLabel(String seatType, String price, {String? svgAsset}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (svgAsset != null)
            Padding(
              padding: const EdgeInsets.only(right: 12),
              child: SizedBox(
                width: 20,
                height: 20,
                child: SvgPicture.asset(
                  svgAsset,
                  width: 20,
                  height: 20,
                ),
              ),
            ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                seatType,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade700,
                ),
              ),
              Text(
                price,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
