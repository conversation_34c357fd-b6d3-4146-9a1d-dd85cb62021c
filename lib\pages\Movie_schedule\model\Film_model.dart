import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_app/pages/cinema/model/cinema_model.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/service/language_service.dart';


class FilmModel {
  final String? FilmId;
  final String? Code;
  final String? Name;
  final String? Name_F;
  final String? ShortDescription;
  final String? ShortDescription_F;
  final String? OpeningDate;
  final String? Rate;
  final int? Duration;
  final String? FilmGenreName;
  final String? FilmGenreName_F;
  final String? FilmRestrictAgeName;
  final String? SubtitleName;
  final String? SubtitleName_F;
  final String? FilmFormatName;
  final String? FilmFormatName_F;
  final String? TrailerURL;
  final String? DistributorId;
  final int? Order;
  final bool? Status;
  final bool? NowShowing;
  final String? Director;
  final String? Actors;
  final String? MainPosterUrl;
  final List<ListPosterUrlModel>? ListPosterUrl;
  final List<ListFilmGenreModel>? ListFilmGenre;
  final List<ShowModel>? listFilm;
  final String? MainLanguage;
  final String? RestrictAgeString;
  final String? DubbingName;
  final bool? HasShow;
  final bool? HasSneakShow;
  final bool IsHot;
  final List<FilmFormat>? filmFormat;

  FilmModel({
    this.FilmId,
    this.Code,
    this.Name,
    this.Name_F,
    this.ShortDescription,
    this.DistributorId,
    this.ShortDescription_F,
    this.OpeningDate,
    this.Rate,
    this.Duration,
    this.FilmGenreName,
    this.FilmGenreName_F,
    this.FilmRestrictAgeName,
    this.SubtitleName,
    this.listFilm,
    this.SubtitleName_F,
    this.FilmFormatName,
    this.FilmFormatName_F,
    this.TrailerURL,
    this.Order,
    this.Status,
    this.NowShowing,
    this.Director,
    this.Actors,
    this.MainPosterUrl,
    this.ListPosterUrl,
    this.ListFilmGenre,
    this.MainLanguage,
    this.RestrictAgeString,
    this.DubbingName,
    this.HasShow,
    this.HasSneakShow,
    this.IsHot = false,
    this.filmFormat,
  });

  factory FilmModel.fromJson(Map<String, dynamic> json) {
    return FilmModel(
      FilmId: json['FilmGroupId'],
      Code: json['Code'],
      Name: json['Name'],
      Name_F: json['Name_F'],
      DistributorId: json['DistributorId'],
      ShortDescription: json['ShortDescription'],
      ShortDescription_F: json['ShortDescription_F'],
      OpeningDate: json['OpeningDate'],
      Rate: json['Rate'],
      Duration: json['Duration'],
      FilmGenreName: json['FilmGenreName'],
      FilmGenreName_F: json['FilmGenreName_F'],
      FilmRestrictAgeName: json['FilmRestrictAgeName'],
      SubtitleName: json['SubtitleName'],
      SubtitleName_F: json['SubtitleName_F'],
      FilmFormatName: json['FilmFormatName'],
      FilmFormatName_F: json['FilmFormatName_F'],
      TrailerURL: json['TrailerURL'],
      Order: json['Order'],
      Status: json['Status'],
      NowShowing: json['NowShowing'],
      Director: json['Director'],
      Actors: json['Actors'],
      MainPosterUrl: json['MainPosterUrl'],
      ListPosterUrl: (json['ListPosterUrl'] as List?)
          ?.map((e) => ListPosterUrlModel.fromJson(e))
          .toList(),
      ListFilmGenre: (json['ListFilmGenre'] as List?)
          ?.map((e) => ListFilmGenreModel.fromJson(e))
          .toList(),
      MainLanguage: json['MainLanguage'],
      RestrictAgeString: json['RestrictAge'],
      DubbingName: json['DubbingName'],
      HasShow: json['HasShow'],
      HasSneakShow: json['HasSneakShow'],
      IsHot: json['IsHot'] ?? false,
      filmFormat: (json['ListFilmFormat'] as List?)
          ?.map((e) => FilmFormat.fromJson(e))
          .toList(),
    );
  }

  String? getName() {
    final languageService = LanguageService();
    return languageService.getLocalizedField(Name, Name_F);
  }

  String getFormatName() {
    final languageService = LanguageService();
    final formatName = languageService.getLocalizedField(FilmFormatName, FilmFormatName_F);
    return formatName.isNotEmpty ? "($formatName)" : "";
  }

  String getFilmPoster() {
    var poster = MainPosterUrl;
    poster ??= ListPosterUrl?.firstWhere((e) => e.AbsolutePath != null).AbsolutePath;
    return '${ApiService.baseUrlImage}/$poster';
  }

  String? getFilmGenre() {
    final languageService = LanguageService();

    if (ListFilmGenre != null && ListFilmGenre!.isNotEmpty) {
      final genre = ListFilmGenre!
          .take(2)
          .map((e) => languageService.getLocalizedField(e.Name, e.Name_F))
          .where((e) => e.isNotEmpty)
          .join(", ");
      return genre;
    }
    return languageService.getLocalizedField(FilmGenreName, FilmGenreName_F);
  }

  String getFullOptions() {
    // final formatName = getFilmFormatFromList() ?? getFilmFormatName() ?? "";
    final openDate = getOpenDate();
    final duration = "$Duration phút";
    return/*$formatName |*/ " $openDate | $duration";
  }

  String getOpenDate() {
    if (OpeningDate == null) return "";
    final date = DateTime.parse(OpeningDate!);
    return "${date.hour}:${date.minute} ${date.day}/${date.month}/${date.year}";
  }

  String getHalfOptions() {
    final languageService = LanguageService();
    final filmType = languageService.getLocalizedField(FilmGenreName, FilmGenreName_F);
    final minuteText = "Home.Minute".tr();
    final duration = "$Duration $minuteText";
    return "$filmType | $duration";
  }

  String getFinalOptions([String? format]) {
    final languageService = LanguageService();

    // Get localized format name
    String? formatName;
    if (format != null) {
      formatName = format;
    } else if (filmFormat?.isNotEmpty == true) {
      formatName = languageService.getLocalizedField(
        filmFormat!.first.FilmFormatName?.replaceAll("(", "").replaceAll(")", ""),
        filmFormat!.first.FilmFormatName_F?.replaceAll("(", "").replaceAll(")", "")
      );
    } else {
      formatName = languageService.getLocalizedField(
        FilmFormatName?.replaceAll("(", "").replaceAll(")", ""),
        FilmFormatName_F?.replaceAll("(", "").replaceAll(")", "")
      );
    }

    // Get localized genre name
    final genre = languageService.getLocalizedField(FilmGenreName, FilmGenreName_F);

    // Use translation for "minute"
    final minuteText = "Home.Minute".tr();

    return "${formatName.isEmpty ? "" : formatName} | $genre | ${Duration ?? 0} $minuteText";
  }
}

class FilmFormat {
  final String? FilmFormatName;
  final String? FilmFormatName_F;
  final String? FilmFormatCode;

  FilmFormat({
    this.FilmFormatName,
    this.FilmFormatName_F,
    this.FilmFormatCode,
  });

  factory FilmFormat.fromJson(Map<String, dynamic> json) {
    return FilmFormat(
      FilmFormatName: json['FilmFormatName'],
      FilmFormatName_F: json['FilmFormatName_F'],
      FilmFormatCode: json['FilmFormatCode'],
    );
  }

  String getLocalizedName() {
    final languageService = LanguageService();
    return languageService.getLocalizedField(FilmFormatName, FilmFormatName_F);
  }
}

class ListPosterUrlModel {
  final String? AbsolutePath;

  ListPosterUrlModel({this.AbsolutePath});

  factory ListPosterUrlModel.fromJson(Map<String, dynamic> json) {
    return ListPosterUrlModel(
      AbsolutePath: json['AbsolutePath'],
    );
  }
}

class ListFilmGenreModel {
  // {"GenreId":"e5c7c3ce-1478-4e68-ad79-812f8b7acabb","Code":"kinh-di","Name":"Kinh dị","Name_F":null,"Order":1},
  final String? GenreId;
  final String? Code;
  final int? order;
  final String? Name;
  final String? Name_F;

  ListFilmGenreModel({this.Name, this.Name_F, this.GenreId, this.Code, this.order});

  factory ListFilmGenreModel.fromJson(Map<String, dynamic> json) {
    return ListFilmGenreModel(
      Name: json['Name'] as String?,
      Name_F: json['Name_F'] as String?,
      GenreId: json['GenreId'] as String?,
      Code: json['Code'] as String?,
      order: json['Order'] is int ? json['Order'] : int.tryParse(json['Order']?.toString() ?? ''),
    );
  }

  String getLocalizedName() {
    final languageService = LanguageService();
    return languageService.getLocalizedField(Name, Name_F);
  }
}

class ShowCinemaModel {
  // "CinemaId": "381f745f-c110-4d0c-9117-3a79f36ba9c4",
  // "CinemaName": "Beta Tây Sơn",
  // "CinemaName_F": "Beta Tay Son",
  // "Address": "Tầng 5, Số 229 Tây Sơn, Ngã Tư Sở, Đống Đa, Hà Nội",
  // "Address_F": "5th Floor, No. 229 Tay Son Street, Nga Tu So Ward, Dong Da District, Hanoi",
  // "Latitude": "21.005546",
  // "Longtitude": "105.8183652",
  // "Distance": 0,

  final String? cinemaId;
  final String? cinemaName;
  final String? cinemaNameF;
  final String? address;
  final String? addressF;
  final String? latitude;
  final String? longitude;
  double? distance; // Made non-final so it can be updated with location data
  final List<ListFilmModel>? listFilm;
  bool isExpand;

  ShowCinemaModel({
    // this.cinema,
    this.cinemaId,
    this.cinemaName,
    this.cinemaNameF,
    this.address,
    this.addressF,
    this.latitude,
    this.longitude,
    this.distance,
    this.listFilm,
    this.isExpand = false ,
  });

  factory ShowCinemaModel.fromJson(Map<String, dynamic> json) {
    return ShowCinemaModel(
      // cinema: json['Cinema'] != null ? Cinema.fromJson(json['Cinema']) : null,
      cinemaId: json['CinemaId'] as String?,
      cinemaName: json['CinemaName'] as String?,
      cinemaNameF: json['CinemaName_F'] as String?,
      address: json['Address'] as String?,
      addressF: json['Address_F'] as String?,
      latitude: json['Latitude'] as String?,
      longitude: json['Longtitude'] as String?,
      // Parse distance as double
      distance: json['Distance']?.toDouble(),
      listFilm: (json['ListFilm'] as List?)?.map((e) => ListFilmModel.fromJson(e)).toList(),
    );
  }

  String getLocalizedName() {
    final languageService = LanguageService();
    return languageService.getLocalizedField(cinemaName, cinemaNameF);
  }

  String getLocalizedAddress() {
    final languageService = LanguageService();
    return languageService.getLocalizedField(address, addressF);
  }
}

class ListFilmModel {
  // {
  // "StartTime": "2025-05-06T15:00:00",
  // "SeatSolded": 40,
  // "ShowId": "dbadb22a-19f5-4c29-bb6a-47b125fc4782",
  // "TimeToLock": -15,
  // "TotalSeat": 299,
  // "ScreenClass": "normal",
  // "ScreenDesc": "",
  // "ScreenTitle": "",
  // "ScreenImageUrl": "",
  // "IsShowScreenIntro": false
  // },
  final String? filmId;
  final String? filmFormatCode;
  final String? filmFormatName;
  final String? filmFormatNameF;
  final int? sumOfShow;
  final List<ShowModel>? listShow;
  final String? filmRestrictAgeName;
  final String? filmRestrictAgeNameF;
  final String? filmRestrictAgeCode;
  final String? filmRestrictAgeCodeF;
  final String? filmRestrictAge;
  final String? filmRestrictAgeF;

  ListFilmModel({
    // this.filmFormat,
    this.filmId,
    this.filmFormatCode,
    this.filmFormatName,
    this.filmFormatNameF,
    this.filmRestrictAgeName,
    this.filmRestrictAgeNameF,
    this.filmRestrictAgeCode,
    this.filmRestrictAgeCodeF,
    this.filmRestrictAge,
    this.filmRestrictAgeF,
    this.sumOfShow,
    this.listShow,
  });

  factory ListFilmModel.fromJson(Map<String, dynamic> json) {
    return ListFilmModel(
      filmId: json['FilmId'] as String?,
      filmFormatCode: json['FilmFormatCode'] as String?,
      filmFormatName: json['FilmFormatName'] as String?,
      filmFormatNameF: json['FilmFormatName_F'] as String?,
      filmRestrictAgeName: json['FilmRestrictAgeName'] as String?,
      filmRestrictAgeNameF: json['FilmRestrictAgeName_F'] as String?,
      filmRestrictAgeCode: json['FilmRestrictAgeCode'] as String?,
      filmRestrictAgeCodeF: json['FilmRestrictAgeCode_F'] as String?,
      filmRestrictAge: json['FilmRestrictAge'] as String?,
      filmRestrictAgeF: json['FilmRestrictAge_F'] as String?,
      sumOfShow: json['SumOfShow'] is int ? json['SumOfShow'] : int.tryParse(json['SumOfShow']?.toString() ?? ''),
      // Parse listShow as a list of ShowModel
      listShow: (json['ListShow'] as List?)?.map((e) => ShowModel.fromJson(e)).toList(),
    );
  }

  String getLocalizedFormatName() {
    final languageService = LanguageService();
    return languageService.getLocalizedField(filmFormatName, filmFormatNameF);
  }

  String getLocalizedRestrictAgeName() {
    final languageService = LanguageService();
    return languageService.getLocalizedField(filmRestrictAgeName, filmRestrictAgeNameF);
  }
}
