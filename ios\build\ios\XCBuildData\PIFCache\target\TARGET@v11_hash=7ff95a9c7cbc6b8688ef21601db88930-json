{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98563b3a7d9a51b5f370173bff1f571821", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b89882dc28a6d5c860523e82f554cc84", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833599cb5ac423a027e1bf0852fb36848", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e29cd749ce03643e9abbafc9d7ee4c45", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833599cb5ac423a027e1bf0852fb36848", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982fd3c6a7f4d84d77d3348746052d187a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989660fec100591501ae0c9bd70a64f44b", "guid": "bfdfe7dc352907fc980b868725387e98807bebc10b9b2347b3c38ab76dcd39c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfd455992d23e15dbe675b4fe6a54cb8", "guid": "bfdfe7dc352907fc980b868725387e9855236db105a1790362e0f3f85da2820e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f978f0bcd85da9c3f3f0ea66389fce", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9876a78f289cc8ef03af2928134cd78572", "guid": "bfdfe7dc352907fc980b868725387e988114bf115703b57c8ae21d5409dd19e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98777df29e27f62cf4583eca1afddf1a24", "guid": "bfdfe7dc352907fc980b868725387e98b588a7e8d8ddd4478d80982232420613"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c16f4bcaeaf277a9ef31ac3530b6676b", "guid": "bfdfe7dc352907fc980b868725387e98c25bd75abc455f60626b1b6d2e12965f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98965eb56bc84009f1ce1f123d05deb7e9", "guid": "bfdfe7dc352907fc980b868725387e9886bb1e038f2ec0e9b0706aa88efa09e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8f5bb6f4beec5ffc6b17fb9dff17455", "guid": "bfdfe7dc352907fc980b868725387e9867d7f020d92c0259475431094f026daa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbede2f0b028e5c4b459a899aec520c4", "guid": "bfdfe7dc352907fc980b868725387e983cbfbd91e9e8b5ed232e4a3e97ee6ea0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e68a2df59adee106201b9108e28c57a", "guid": "bfdfe7dc352907fc980b868725387e985677e626286185176d39b4c7a3131d79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98629cb6661232d004d244e6475fb18834", "guid": "bfdfe7dc352907fc980b868725387e98d470e112a689c44d32d123ec1e9a49dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98483e2bca7512732d7a4b1d16def1fe9c", "guid": "bfdfe7dc352907fc980b868725387e98ca6c1fe797300662ce54309c21e1f90d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984100d7a485b52c30c0fe7f4c68fd6ab8", "guid": "bfdfe7dc352907fc980b868725387e98e2db67651016b0e5a242bf079bcd1b7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0a0479bf8bf4e635e698a133ae2f557", "guid": "bfdfe7dc352907fc980b868725387e9893f552456b99ece4b3b598b98c2d0951"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bcbda802cf12cc6e1c9bf66177e43e3", "guid": "bfdfe7dc352907fc980b868725387e98e64e2f7a87cc703185c2211316deb4fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d15d5d2f50a57acac387f42b8b9ddf9", "guid": "bfdfe7dc352907fc980b868725387e98831f9cc115b07d97e9aba4500b47d900"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98597d55e1921f2d9c0312811c7bf3bc9a", "guid": "bfdfe7dc352907fc980b868725387e9813af1cf6f1aa063237c27f62b8da4f4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987543dcbc5a19154c48c2f38566f0c68b", "guid": "bfdfe7dc352907fc980b868725387e986ed5364d2e170f445d04136ca20dd611"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981293efc7296d66d6f1e2baa50d4c181a", "guid": "bfdfe7dc352907fc980b868725387e98e480306e9ebc93b19dbf732f19c8e307"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6ac9dcfb3600ec1ebb53af0e9c18abd", "guid": "bfdfe7dc352907fc980b868725387e98a0b529fc765425975daa2152012b3d31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818eae9f26f670469f687328fb819aee8", "guid": "bfdfe7dc352907fc980b868725387e988ce54280e8249a8ef34a9f874fa2a3d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf677865be388f7e7bcd7c6369e5b69e", "guid": "bfdfe7dc352907fc980b868725387e98c4eb53ccf283dd9fadcbc4ab328696ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98391f0e399e9323370d1461cfeb1bdedd", "guid": "bfdfe7dc352907fc980b868725387e988c7b5a9b08093316782f2062a6bb914d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986411806226875998fddec00a3df3b4d6", "guid": "bfdfe7dc352907fc980b868725387e98023d74f18fb22dba3fb8cd08619adb5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e2b4bec849e87987191bcab9f9ab8d7", "guid": "bfdfe7dc352907fc980b868725387e9845ac40f0b799c7c7d7c8542c2caa56cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f506bdac2c8d144da2be5d30945dccad", "guid": "bfdfe7dc352907fc980b868725387e98c518fd94f66a19215b72cded5acabcb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862114e6f48ade0b58f4be1078aa96118", "guid": "bfdfe7dc352907fc980b868725387e9865436ccd5e5292ab3eb9a2c726d4fd55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bee3054309bf941ace83fb3c519e539", "guid": "bfdfe7dc352907fc980b868725387e98c9c0f8cc39cd1f4c2791c1b6bcd95b24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d63438c77d791882b21bad1f4826f0a1", "guid": "bfdfe7dc352907fc980b868725387e98222e67f8fb32506a7edcf8a0ac719b69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e54bab0fbb9be73957a154083798220f", "guid": "bfdfe7dc352907fc980b868725387e985359bf3f299bad8a26efcc1796d7c1cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e51b0e6b22ce0100ef97e0b88f4682b", "guid": "bfdfe7dc352907fc980b868725387e9894b1e9a9c15f1d4d6dab730cad9a7356"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d9f41dc79b253c8234c0bdd93a66c4f", "guid": "bfdfe7dc352907fc980b868725387e981eed0494494d3067db554c50722a6670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835c32e680e60e2bed3c30c9810a29cfb", "guid": "bfdfe7dc352907fc980b868725387e988b7cd12bea006b45c3dbb4e16996e9d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4d35e344bf43afb578c499761cb5049", "guid": "bfdfe7dc352907fc980b868725387e98d7495c6870d2ecefef04b9c19bbca9b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98719175e7d09a1ed821bd90c61c1b8050", "guid": "bfdfe7dc352907fc980b868725387e983bd56d9a4345e817c88f1f030a933016"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd3d4a06cf48cdbc20d17481475e0eb9", "guid": "bfdfe7dc352907fc980b868725387e98faeeab2baa047c58c10f40717f073aab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfa2b458416b2e3b9c00e2a151037814", "guid": "bfdfe7dc352907fc980b868725387e98cea48adeb08a5939b6bef50534e25be1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895342d39d22b664f399ceeeb8c56753c", "guid": "bfdfe7dc352907fc980b868725387e984c86b1becfedef31481dcbad2b17a5c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866cb952e78c02996d8f7e269622a4dd4", "guid": "bfdfe7dc352907fc980b868725387e98da3e64c4fb8555d91305261dc4ff94d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889db4b7deb4daf7d35d272e787aa9044", "guid": "bfdfe7dc352907fc980b868725387e98bda20fd5eba9b97c8a836079351c8cd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e981e950cb4360e9a4d504f2805a6482", "guid": "bfdfe7dc352907fc980b868725387e9806a749e93be9d437dd5f0ddf38711a74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b2ccbbd1827436a5f4fe813079537a7", "guid": "bfdfe7dc352907fc980b868725387e98fdb2b1d48c92bc84d4a594cf3335c801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98708e858b4a8f440b01fbac25abeabb1b", "guid": "bfdfe7dc352907fc980b868725387e98e15c16685b281da13549a9fa2a0b8813"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834045299f28e3508e55b4d10a79b942f", "guid": "bfdfe7dc352907fc980b868725387e98cc497b3c5b4a04b925785d634ff1e14c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e942d6fd1af1a1e185c330ca1906663", "guid": "bfdfe7dc352907fc980b868725387e981ba2cec9e73183ea8decd900c0ab5b8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1aa417dcae09a10ed9129927072e7c0", "guid": "bfdfe7dc352907fc980b868725387e9800260b0ff6bd074c8bdbb7a8c7ac0674"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2d6c329b8a5b7a20217ad70278a5b13", "guid": "bfdfe7dc352907fc980b868725387e98fe3de5bd3d36a17e0d9aa10a554a91b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fe49844cadf152c835e7ac13116f379", "guid": "bfdfe7dc352907fc980b868725387e986d32a954ad28f8654b283264f8000ce4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b522fe85f9dd7a890285cc904a328ef", "guid": "bfdfe7dc352907fc980b868725387e98235040965da858be0295f7b555c2e118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98756bf204bc7f30725435c66327bb99af", "guid": "bfdfe7dc352907fc980b868725387e982fbf2a8321bfaa1dd406916c3062e75e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d7a6edc780e71edfc757959a0582f78", "guid": "bfdfe7dc352907fc980b868725387e98d2cc5d5d922868210f4715e81b020063"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7dcfdb98a02963e8d02fc24b169db3b", "guid": "bfdfe7dc352907fc980b868725387e9817d42fd3684b39a67bed167a984be3b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b51301c62121eceb41afd9f90d98d4c", "guid": "bfdfe7dc352907fc980b868725387e982186516c38e2b047b8d85b9e3abec8f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d916bddf219f08a95b7b0cc1ba800a75", "guid": "bfdfe7dc352907fc980b868725387e983ba67aae03bec559656bb0fcb9a543ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838e911a9f3ee2d8273ed26de4bc4ea33", "guid": "bfdfe7dc352907fc980b868725387e981f15a3129063fc818f963bbd86131b10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98837fed23477adf4094a87c33f657222d", "guid": "bfdfe7dc352907fc980b868725387e980c04b925abe5a7dbcd2bce08777fa56a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887b01b59fb68f44ce347509566b44600", "guid": "bfdfe7dc352907fc980b868725387e988a8775245f5f0001bf1ff488c004e6c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f3e9da7fb2551ac492f1c57305ea47", "guid": "bfdfe7dc352907fc980b868725387e98b980576b5b2e5f46ce92bf1270d8250c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3a918eae8d1553760b1aa758039e23d", "guid": "bfdfe7dc352907fc980b868725387e982d2886a197ccb6c2dac6a56d578acaa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98383a661aaaf566fb81de6ab28792aca4", "guid": "bfdfe7dc352907fc980b868725387e984e73d88ae412b41a6f15245d2d5c16ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987257074f7b830a65901bea2e5058599c", "guid": "bfdfe7dc352907fc980b868725387e984eeeaa430cd3c651fec8c5bd40d762e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac2629f5d1b8911b864df8ad517d27ea", "guid": "bfdfe7dc352907fc980b868725387e984921e40e36d4c16aea97616d7b65ec84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982869e1bf5f519f7846768eede6d95eb8", "guid": "bfdfe7dc352907fc980b868725387e9802fa17a9388f990ed77caff2a3799c8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984903d11ec03ff00cf2a793fa72b5f837", "guid": "bfdfe7dc352907fc980b868725387e98c09a7ffa31156a82f74af06abc9ab20a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f1718533502763edcea493586d08918", "guid": "bfdfe7dc352907fc980b868725387e986531517c4dbf0bee7d8fc09647df214f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac96b3c9c2b589e360875c2261d7d41", "guid": "bfdfe7dc352907fc980b868725387e98b56566c8310d63c0e874e3feda6f60cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d896d14339e1063e0800fa6d1ed3b35", "guid": "bfdfe7dc352907fc980b868725387e98fea6e8fdefc722fe9995117b432c1180"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b863c67c95591591515ad97986b9a8b4", "guid": "bfdfe7dc352907fc980b868725387e9800eb72f48284f19e8aeb3435bd73353e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885fb04462314a4ce1350ddac36b9b6aa", "guid": "bfdfe7dc352907fc980b868725387e9879e53455071d94a93e77b6a6769ba4ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889dd79b908583134bddb1a6465486bee", "guid": "bfdfe7dc352907fc980b868725387e982c0e37396d42e8820d4f94ae1b478e55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c64d86d19f435b83a1161fe4a7bf03d5", "guid": "bfdfe7dc352907fc980b868725387e9819b5b42036bb6397bb0db36c68e51c06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98759c34bea67492ed52de219bead156a6", "guid": "bfdfe7dc352907fc980b868725387e98e5ea4a3398e3e2c8541039f9b5b7ccf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d70567a2e9eeed594b8ee2ce052f45b0", "guid": "bfdfe7dc352907fc980b868725387e986a94a9d362f4b91d447246f6dd18200a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830fe187b438c8d4aa82721ebf837ff3c", "guid": "bfdfe7dc352907fc980b868725387e98399c41c7701fbf6e0e4b68cf7346d6fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8db5c0fcb5a8b36c98accf9288380a2", "guid": "bfdfe7dc352907fc980b868725387e983550e714aa8401daab485d95b3476a24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0e976cba29d840d794b63d1caeab022", "guid": "bfdfe7dc352907fc980b868725387e98c68be0c0d0ac92ea76aae4429e97ee89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f47e83814f4c1bf637ecca360daea5a", "guid": "bfdfe7dc352907fc980b868725387e98ffcb057fbbb7653d76250d4829f8398f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fab7e0e5b58f0189513f6913f537f15c", "guid": "bfdfe7dc352907fc980b868725387e9870a2ed7e56708a76e685aa58afdf1ccd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832541c5e868b747a1658503c487a1a59", "guid": "bfdfe7dc352907fc980b868725387e989d8d8fa1f80f856f4d19e1d444f01fe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98153c11fbb09f4a9a59a9c5fb29ada8a8", "guid": "bfdfe7dc352907fc980b868725387e988e3cdc0b82521f3db176f80d8a58cf55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f73bb69d0c31d2a9b6f349d987dfcdf", "guid": "bfdfe7dc352907fc980b868725387e98a8cdf1d27bc6d8ac8204f41d35e953bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bfc6d2be27ac962d8ffe12bdccf854a", "guid": "bfdfe7dc352907fc980b868725387e983fc914a8448b684bcd62fad4f10fc0e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b3d814aafcc0fa294cd9815c37e1b8c", "guid": "bfdfe7dc352907fc980b868725387e989238987c6d4dc35d3e5a322f03007bd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98681e9dd1a1dfa7a5c90ad56369497b21", "guid": "bfdfe7dc352907fc980b868725387e982eab10a4ae07b13a5937761a89cfc75b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98080aa3d9aeb51efb08f90d0ce4b146cc", "guid": "bfdfe7dc352907fc980b868725387e98f8571cdf6d6b84a85351970ad65fff0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc2d2ddf24e4b18b549ad871952f376e", "guid": "bfdfe7dc352907fc980b868725387e9881892e040425214db3fb2578de4ee2d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863fdf3dcda7b4963d6946094e95c4920", "guid": "bfdfe7dc352907fc980b868725387e98aacee0605a3cd4696ff801429ed87b22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a710a403ae3fdbb8ee5508a627516754", "guid": "bfdfe7dc352907fc980b868725387e98d9b5dde53a97a88c61a30832bd559cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d91d5e1515430b8b4aef225618e535c5", "guid": "bfdfe7dc352907fc980b868725387e98a1aa824512ffbe0aa8647091959b911c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846e8d140e5f1f145eae797c5ced155b4", "guid": "bfdfe7dc352907fc980b868725387e98b188cb742539656a82a35a3f16981bcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a15966a8e1cc22a864d7ce425b426bab", "guid": "bfdfe7dc352907fc980b868725387e982c3450d903e9d96a5538e5a09aaa07b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98133087cfaf3fcad025c637b0ec1dbb20", "guid": "bfdfe7dc352907fc980b868725387e981f9782dc8dd741f99ab0e81bb8860697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98888fa23e0d4c1d8cefb67e18796a6ad6", "guid": "bfdfe7dc352907fc980b868725387e984025d19e45142061f02f60c129fc05fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fea8d244a152c9967c78fdbcb74bdcb2", "guid": "bfdfe7dc352907fc980b868725387e98a01b381e328902ff95f15bfd07e38f5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882b5abbdd53e071f2b37432462673536", "guid": "bfdfe7dc352907fc980b868725387e988a30d3d8acd4e6d20602b67da47193be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d32dea7ebdd7267041aa8525e6977fd8", "guid": "bfdfe7dc352907fc980b868725387e981a0cc038ae1b1fd25719adda27976cce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840f2e9d093159de1cab461287dba48ad", "guid": "bfdfe7dc352907fc980b868725387e9874905207c3e9df630c4cc2cc4b8f326a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821538a53b609e5320fc09a7fffc7410f", "guid": "bfdfe7dc352907fc980b868725387e98dbff2b3ddff5858342d98184e5628d72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987de7029f4f382de6c82359251e08c21c", "guid": "bfdfe7dc352907fc980b868725387e9815758cc3f525d96d70df78b67a43d851"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d25f685b6cdf137b9867889bfb4a64a8", "guid": "bfdfe7dc352907fc980b868725387e9804e4f56dc3167681cf691e8596c71146"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aac7f20d4c50b7596837cde69527cab", "guid": "bfdfe7dc352907fc980b868725387e9853ccc12ddfac15e8ffa7df5827562029"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857b98f16490cdf4d5cf494193c82a831", "guid": "bfdfe7dc352907fc980b868725387e986f0a1052aa0ca0cf8f1905181b39665f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9badb9acfebeb6d7aaffc394c22b13d", "guid": "bfdfe7dc352907fc980b868725387e98f9ac30e6751ea202187da901159227a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988085a2b8b85261d2462473cce79c068c", "guid": "bfdfe7dc352907fc980b868725387e98223c0539cd38711d7a444ccf4ac8bb58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867ee4758adc270591d82ad16bef44b1b", "guid": "bfdfe7dc352907fc980b868725387e98459f9b8d84f37936b5a3961f1212486a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b031c4d790b1946e1f1d4027d3c7bc9", "guid": "bfdfe7dc352907fc980b868725387e98163c620f28a83f56ae09907b19a50459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983542142e866feff11730a5642df25eab", "guid": "bfdfe7dc352907fc980b868725387e98fac7be43458c86a240c9c227d1209259"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981161f640de10c505cbba9b2d5599c7ec", "guid": "bfdfe7dc352907fc980b868725387e98b8750ef6b70c38f5d3efed43794b1885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c13bfd87858dd0f495c0cbf14ab98144", "guid": "bfdfe7dc352907fc980b868725387e98f656c306a73ee677f40cdfa9f9ca4557"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834952becefba95eec80f21451a0e4cb8", "guid": "bfdfe7dc352907fc980b868725387e9821526f14d99f3d770dc12a04027008fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98847ac44c46beebe5329a4b172b764593", "guid": "bfdfe7dc352907fc980b868725387e987b6b74deff72959662529d87a81ecb8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812ede1346c2c140137e0917399d3c02f", "guid": "bfdfe7dc352907fc980b868725387e984ff2b8aa6691cf2cf57960c7512f48a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d5eb06e41a38a2ec7ba5ecc8ef5a7e9", "guid": "bfdfe7dc352907fc980b868725387e9837579c29f04e4c3539ec94bc445472f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987df5feeb151a5b4ed9dc7c6b479d7faf", "guid": "bfdfe7dc352907fc980b868725387e986945e5c3e4e41047a8980cb599c88d0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b6ca5168235e27ad586767d46086cd9", "guid": "bfdfe7dc352907fc980b868725387e983bd0acd644bb0ce8df00d1c587ead8ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98057c94c616644250754f26d348c41ca2", "guid": "bfdfe7dc352907fc980b868725387e98d946d86b98f2497902130a0062ebb0a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8dbd283b4e231dc561d2d4d4ba59228", "guid": "bfdfe7dc352907fc980b868725387e98d55d34d789a1f86ac8f3ad9acbb944b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b629c506690001813f7229a4fb8e522d", "guid": "bfdfe7dc352907fc980b868725387e98981c16ccd4d794cf02683c13599f7221"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a801e92a4c79d9e8daf68f970017c758", "guid": "bfdfe7dc352907fc980b868725387e98ddc8ba56e16a719422a7dc504cf71724"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2fcee7e9767d0f8513cbe635d27e63b", "guid": "bfdfe7dc352907fc980b868725387e98a25d3bfff5529b1dd5969eb1a9425c5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be86c3d27a9c0e04ba4c56c41546efec", "guid": "bfdfe7dc352907fc980b868725387e98456eb65b214512cac8c9321656ce3a72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803bd4f034a54ccf0665a1103e28c7187", "guid": "bfdfe7dc352907fc980b868725387e982fae223ba487719295cf409bf39b44e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d53172f22c5892dd2e9ae5901a2acf7e", "guid": "bfdfe7dc352907fc980b868725387e98b6fd83cd4dac6dd0f302fc9fddc5720b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c45251dee6bfdbdf8c2cf1897ee60dff", "guid": "bfdfe7dc352907fc980b868725387e98c5276f2250204d3bcbac5138a0874abf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815fffa12e4ce8f0a46045ebe6ea51673", "guid": "bfdfe7dc352907fc980b868725387e9895aecedfcb719baaead44cbb1729c5e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e24be9f37109139f553808db2bcb39c", "guid": "bfdfe7dc352907fc980b868725387e98a24fd2b423e05dd6d9186fd39827ab84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2dd73965259e8d67d35e28b427e0e7b", "guid": "bfdfe7dc352907fc980b868725387e98f02306045a58d555bff9be2e61d79d11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816ab237de54a5efbe2250f1808bfe598", "guid": "bfdfe7dc352907fc980b868725387e98467fa1467989c14c4013c3a50fa45e13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864d1395d7de8172edeb4e465387a371b", "guid": "bfdfe7dc352907fc980b868725387e985db12a2072ed67e7cf46c4787669da4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98475b46a8d737da5b3c063ce363a24915", "guid": "bfdfe7dc352907fc980b868725387e9872c44737bd90b83b1c578d0a29f262cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98535ef1c5a87450438b9d6db739be7f17", "guid": "bfdfe7dc352907fc980b868725387e9883ab9baced37a0d9c4ff931dc6447f3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d24a51b007caa643a2f86a17af706db", "guid": "bfdfe7dc352907fc980b868725387e986dec1fa323eb105bd353a2a879ca4d07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f05b8a89a8a6ba19e745d7bedb746dc", "guid": "bfdfe7dc352907fc980b868725387e98fd2bf27ca6ef5155fea67bbae44572cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf6ccae7549b5581a1c3926a01492af1", "guid": "bfdfe7dc352907fc980b868725387e9827160be7aaceae07b878cfad218603e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f911ca3f268da734ceb51bcb084444a", "guid": "bfdfe7dc352907fc980b868725387e98285146f588a09e3f0c8cc0afcd61543c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f7442bd79a7a76cd1e580406cafed29", "guid": "bfdfe7dc352907fc980b868725387e980e247b30b26c89bee42e7d6f81d0f3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bb1e40e97948351bb00d633f661173e", "guid": "bfdfe7dc352907fc980b868725387e98ea3ee11b2da1ad3ceaaf3433256ca91a"}], "guid": "bfdfe7dc352907fc980b868725387e98cac5758aaab041b306b3055c248885f4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98027d34dd1e1770c79dd54f5404987fd6"}], "guid": "bfdfe7dc352907fc980b868725387e98d8f70b5ff677ed02b77232961e037957", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b7d49ad1fe66522e6eab9248dd2331d6", "targetReference": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d28149b8c1f009a58f11c091e7d83e4", "guid": "bfdfe7dc352907fc980b868725387e986d52c275603be68942df62606ca0d8e3"}], "guid": "bfdfe7dc352907fc980b868725387e98ba929dd1b11c60f9a4f8fd06e4eaa3e7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e985f0ec3a68eeed5241cb87afb05bcc380", "name": "OrderedSet"}, {"guid": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b", "name": "flutter_inappwebview_ios-flutter_inappwebview_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98a562549a031aeda8bf3440b79b3420bc", "name": "flutter_inappwebview_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9810acd6d3a97e7ef91b90dda5618dc5c0", "name": "flutter_inappwebview_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}