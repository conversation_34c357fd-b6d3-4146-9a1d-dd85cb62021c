<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="@dimen/padding_normal">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/padding_small"
            android:layout_marginRight="@dimen/padding_small"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@drawable/shape_white_radius"
            android:padding="@dimen/padding_normal">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvVoucherId"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                tools:text="VC00156723511416"
                android:textSize="@dimen/font_normal"
                android:textColor="@color/textBlack"
                app:fontFamily="@font/oswald_regular" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvVoucherStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="Miễn phí"
                android:textSize="12sp"
                android:textColor="@color/colorPrimaryDark"
                app:fontFamily="@font/oswald_light" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="match_parent"
            android:layout_height="25dp"
            android:layerType="software"
            android:layout_marginLeft="@dimen/padding_small"
            android:layout_marginRight="@dimen/padding_small"
            android:layout_marginTop="-10dp"
            android:src="@drawable/line_white_dash" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/padding_small"
        android:layout_marginRight="@dimen/padding_small"
        android:layout_marginTop="-10dp"
        android:background="@drawable/shape_white_radius"
        android:padding="@dimen/padding_normal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDescription"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            app:fontFamily="@font/sanspro_bold"
            android:textColor="@color/text1e1f28"
            android:textSize="20sp"
            tools:text="Giảm 5K cho mỗi Combo khi mua vé kèm Combo cho 2 người"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvContent"
            app:layout_constraintTop_toBottomOf="@+id/tvDescription"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            app:fontFamily="@font/sanspro_italic"
            android:textColor="@color/text1e1f28"
            android:textSize="14sp"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="Giảm 5K cho mỗi Combo khi mua vé kèm Combo cho 2 ngườiGiảm 5K cho mỗi Combo khi mua vé kèm Combo cho 2 người"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDeadline"
            app:layout_constraintTop_toBottomOf="@+id/tvContent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            app:fontFamily="@font/sanspro_semi_bold"
            android:textColor="@color/textRed"
            android:textSize="14sp"
            tools:text="HSD: 05/07/2019"/>

        <LinearLayout android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            android:layout_marginBottom="@dimen/activity_vertical_margin"
            app:layout_constraintTop_toBottomOf="@+id/tvDeadline"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnUse"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:text="@string/use"
                android:layout_marginRight="10dp"
                style="@style/ButtonPrimary"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/btnGive"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:text="@string/give"
                app:textAllCaps="true"
                android:textColor="@color/colorPrimaryDark"
                tools:textColor="@color/textGray"
                android:textSize="20sp"
                app:fontFamily="@font/oswald_regular"
                android:gravity="center"
                android:layout_marginLeft="10dp"
                android:background="@drawable/border_colorapp_radius"/>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>