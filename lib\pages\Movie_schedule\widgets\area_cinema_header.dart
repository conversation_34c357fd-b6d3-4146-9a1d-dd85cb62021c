import 'package:flutter/material.dart';
import 'dart:math' as math;

// TODO: Import or define TableSection/CinemaShowSection model
// Assuming CinemaShowSection from film_choose_time_page.dart
// import '../film_choose_time_page.dart';

// Placeholder if not imported
class CinemaShowSection {
  final String title;
  final String subtitle;
   bool isOpen;

  CinemaShowSection({
    required this.title,
    required this.subtitle,
    this.isOpen = false,
  });
}

class AreaCinemaHeader extends StatelessWidget {
  final CinemaShowSection section;
  final VoidCallback onTapAction;

  const AreaCinemaHeader({
    super.key,
    required this.section,
    required this.onTapAction,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTapAction, // Corresponds to @IBAction func didTapOnHeader
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        height: 50, // Matches heightForHeaderInSection
        color: Colors.grey[200], // Placeholder color
        // TODO: Match the exact background/styling of the original header
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Title and Subtitle
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  section.title,
                  style: const TextStyle(fontWeight: FontWeight.bold), // TODO: Match style
                ),
                if (section.subtitle.isNotEmpty)
                  Text(
                    section.subtitle,
                    style: const TextStyle(color: Colors.grey, fontSize: 12), // TODO: Match style
                  ),
              ],
            ),
            // Arrow Icon
            Transform.rotate(
              // Rotate based on isOpen state
              angle: section.isOpen ? -math.pi / 2 : 0,
              child: const Icon(
                Icons.arrow_forward_ios, // TODO: Use the exact arrow icon if different
                size: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// TODO: Ensure CinemaShowSection model is correctly defined/imported.
// TODO: Refine styling (colors, fonts, sizes, padding) to match the original Swift UI.
