import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// Model class for seat response from SignalR
class SeatSignalrResponse {
  final String connectionId;
  final String showId;
  final int seatIndex;
  final int seatStatus;

  SeatSignalrResponse({
    required this.connectionId,
    required this.showId,
    required this.seatIndex,
    required this.seatStatus,
  });

  factory SeatSignalrResponse.fromMap(Map<String, dynamic> map) {
    return SeatSignalrResponse(
      connectionId: map['connectionId'] ?? '',
      showId: map['showId'] ?? '',
      seatIndex: map['seatIndex'] ?? 0,
      seatStatus: map['seatStatus'] ?? 0,
    );
  }
}

/// Enum for SignalR connection states
enum SignalRConnectionState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error,
}

/// Typedef for connection state listener
typedef ConnectionStateListener = void Function(SignalRConnectionState state);

/// Typedef for data listener
typedef DataListener = void Function(SeatSignalrResponse data);

/// SignalR service that uses platform channels to communicate with native implementations
class SignalRService {
  // Singleton pattern
  static final SignalRService _instance = SignalRService._internal();
  factory SignalRService() => _instance;
  SignalRService._internal();

  // Method channel
  final MethodChannel _methodChannel = const MethodChannel('com.betacineplex/signalr');
  final EventChannel _connectionStateChannel = const EventChannel('com.betacineplex/signalr/connection_state');
  final EventChannel _dataChannel = const EventChannel('com.betacineplex/signalr/data');

  // Stream controllers
  final StreamController<SignalRConnectionState> _connectionStateController = 
      StreamController<SignalRConnectionState>.broadcast();
  final StreamController<SeatSignalrResponse> _dataController = 
      StreamController<SeatSignalrResponse>.broadcast();

  // Listeners
  final List<ConnectionStateListener> _connectionListeners = [];
  final List<DataListener> _dataListeners = [];

  // Connection state
  SignalRConnectionState _connectionState = SignalRConnectionState.disconnected;
  SignalRConnectionState get connectionState => _connectionState;

  // Connection ID
  String _connectionId = '';
  String get connectionId => _connectionId;

  /// Initialize the SignalR service
  Future<void> initialize() async {
    // Set up event channels
    _connectionStateChannel.receiveBroadcastStream().listen(_handleConnectionState);
    _dataChannel.receiveBroadcastStream().listen(_handleData);

    // Initialize native side
    try {
      await _methodChannel.invokeMethod('initialize');
    } catch (e) {
      debugPrint('Error initializing SignalR: $e');
    }
  }

  /// Start the SignalR connection
  Future<bool> start({String? url}) async {
    try {
      final result = await _methodChannel.invokeMethod('start', {'url': url});
      return result ?? false;
    } catch (e) {
      debugPrint('Error starting SignalR: $e');
      return false;
    }
  }

  /// Stop the SignalR connection
  Future<bool> stop() async {
    try {
      final result = await _methodChannel.invokeMethod('stop');
      return result ?? false;
    } catch (e) {
      debugPrint('Error stopping SignalR: $e');
      return false;
    }
  }

  /// Send a seat selection/deselection message
  Future<bool> sendSeat(String showId, int seatIndex, int status) async {
    try {
      final result = await _methodChannel.invokeMethod('sendSeat', {
        'showId': showId,
        'seatIndex': seatIndex,
        'status': status,
      });
      return result ?? false;
    } catch (e) {
      debugPrint('Error sending seat: $e');
      return false;
    }
  }

  /// Add a connection state listener
  void addConnectionListener(ConnectionStateListener listener) {
    _connectionListeners.add(listener);
  }

  /// Remove a connection state listener
  void removeConnectionListener(ConnectionStateListener listener) {
    _connectionListeners.remove(listener);
  }

  /// Add a data listener
  void addDataListener(DataListener listener) {
    _dataListeners.add(listener);
  }

  /// Remove a data listener
  void removeDataListener(DataListener listener) {
    _dataListeners.remove(listener);
  }

  /// Handle connection state changes from native side
  void _handleConnectionState(dynamic event) {
    final stateIndex = event['state'] as int;
    _connectionState = SignalRConnectionState.values[stateIndex];
    _connectionId = event['connectionId'] ?? '';
    
    // Notify listeners
    for (final listener in _connectionListeners) {
      listener(_connectionState);
    }
    
    // Add to stream
    _connectionStateController.add(_connectionState);
  }

  /// Handle data from native side
  void _handleData(dynamic event) {
    final data = SeatSignalrResponse.fromMap(Map<String, dynamic>.from(event));
    
    // Notify listeners
    for (final listener in _dataListeners) {
      listener(data);
    }
    
    // Add to stream
    _dataController.add(data);
  }

  /// Get a stream of connection state changes
  Stream<SignalRConnectionState> get connectionStateStream => _connectionStateController.stream;

  /// Get a stream of data
  Stream<SeatSignalrResponse> get dataStream => _dataController.stream;

  /// Dispose the service
  void dispose() {
    _connectionStateController.close();
    _dataController.close();
  }
}
