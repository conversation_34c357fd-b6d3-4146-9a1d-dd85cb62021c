{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cd585f20ebac1c91a4c818b120de855e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983b3ceecbc14078747945b0eaf189e6c7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e7f08b390a2127e4b127a39abcc1c83b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98792887048d837b07c4afc4c45c7d2dc7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e7f08b390a2127e4b127a39abcc1c83b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983e6f3e820747b0632a43cf17e4538d85", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eac1dc727db1e236cde84a91197370a0", "guid": "bfdfe7dc352907fc980b868725387e98f1034732a0052e430b654f8882a129dd", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9843adef304e5eccf0fc031cb19257ad08", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fdb11bac1016f1458a27775d3712adba", "guid": "bfdfe7dc352907fc980b868725387e98e1f79af8116594b871fb62b5364717de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983402d91a9e8a77b6b2be40609b20ee42", "guid": "bfdfe7dc352907fc980b868725387e986fc6de81f438ab1ae2e7d5323afe94f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fd66e846e4ae53e7eefa13bc451d63b", "guid": "bfdfe7dc352907fc980b868725387e9803d6cf0123a4e2abf671cdbdce76c155"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98951e20831ab9532198eba8e4a1cd91e2", "guid": "bfdfe7dc352907fc980b868725387e98acbd067a4d791def4b0dcd7f1d9a1d6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849b4ca43b68aeba9cab0a201aca722cf", "guid": "bfdfe7dc352907fc980b868725387e9817876bb512d138d388458467e2b59556"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980056204fe939593eb99cb2e925c32cb0", "guid": "bfdfe7dc352907fc980b868725387e9887e00a7b7a8f2f59a3b348db45ced717"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806bfe8a758577b6f899d98b102157276", "guid": "bfdfe7dc352907fc980b868725387e9866732941c3efd5fd8f1dce5397edeb83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873ba8783b5fff94bb88d335695d8062b", "guid": "bfdfe7dc352907fc980b868725387e9850d141f03766ec16ac5b349e55368303"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98986422701859496245ff5f0114d2d19a", "guid": "bfdfe7dc352907fc980b868725387e983a4c4b0df40762f78f4f06c369520f70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af9709a92a4c32c08b7335c913942681", "guid": "bfdfe7dc352907fc980b868725387e989936f997c7a9985fef9be0b81c5c1730"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d555cabf390813675e39cf0a8f2d40b", "guid": "bfdfe7dc352907fc980b868725387e988c9079ff75233bdac91dd3f97f729aaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb64c7aecff1bf299692b3e13056f72e", "guid": "bfdfe7dc352907fc980b868725387e986cc164a286ab9d260c86f9d56dc29ddd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa9c23c50feb7f6fa63cdb6e1bdab426", "guid": "bfdfe7dc352907fc980b868725387e989d50051b22d97e901134dd20875f398b"}], "guid": "bfdfe7dc352907fc980b868725387e987c8c73a0c73eb933f68e9d4925e78cf6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98fe1a82d8aa260b720e69f460e77dd882"}], "guid": "bfdfe7dc352907fc980b868725387e98564ee00f16fc6604a0144a133b4ba124", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98349e77faae0f7d06f700705674ad69a1", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e9841d8e0310b842628a071d1e7dcfe587f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}