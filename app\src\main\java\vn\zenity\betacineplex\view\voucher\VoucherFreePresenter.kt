package vn.zenity.betacineplex.view.voucher

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class VoucherFreePresenter : VoucherFreeContractor.Presenter {

    private var disposable: Disposable? = null
    override fun getPublicVoucher(page: Int) {
        view?.get()?.showLoading()
        disposable = APIClient.shared.ecmAPI.getPublicVouchers(page, 1000).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        view?.get()?.showVoucher(it.Data ?: listOf())
                    } else {
                        view?.get()?.showAlert(it.Message ?: R.string.error_server.getString())
                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.showAlert(it.message ?: R.string.error_server.getString())
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<VoucherFreeContractor.View?>? = null
    override fun attachView(view: VoucherFreeContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
