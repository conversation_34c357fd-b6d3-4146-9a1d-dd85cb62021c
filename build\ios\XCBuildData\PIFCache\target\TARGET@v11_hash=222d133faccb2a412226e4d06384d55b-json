{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98726475594eefcabcb62abe901aceb946", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98877e1d92f63eebcafd81112959b7b106", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d20afbcacdd6f5bc3809afa341a9328e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c68b00e16769665cc095069d499bfb71", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d20afbcacdd6f5bc3809afa341a9328e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98db5d1d97177c19a1ec25dc8c4c4736ec", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984c0fa29d0c77f00d5ab03387a38a237d", "guid": "bfdfe7dc352907fc980b868725387e9851e6e92475dc9b42c2f83bbcf6557fe3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98412a97bc45f5348a886817970c5dd7c9", "guid": "bfdfe7dc352907fc980b868725387e98a207bb681ef17621e4bdca5d865f4f6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eef06b2425726d129d71d1fa424c6b19", "guid": "bfdfe7dc352907fc980b868725387e986967f9e9313dd3730e8076f446fb643e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd6c7ae867d0eb20b555991f57caad8e", "guid": "bfdfe7dc352907fc980b868725387e98d2af2be25fa41b0309fe17b7ff89c99b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf4574e7f20858bd8a371606a2420de5", "guid": "bfdfe7dc352907fc980b868725387e982c6be06e098a9dde4c65071eeac57779"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984affbee3a825462fe9ee2f55e1dec9cd", "guid": "bfdfe7dc352907fc980b868725387e98f4e3a7e05d6c23a0eacf43c3bc81c545"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e83806e6e5d35b35f0a4f80e90b86210", "guid": "bfdfe7dc352907fc980b868725387e984acd7751cc6654042e56925141c12a8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6e7f029bb55c88c7de7915b39cccc56", "guid": "bfdfe7dc352907fc980b868725387e980339fdcd102c159c409d8f1772b80bed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb589dd4c11b4283f7bd02088e377655", "guid": "bfdfe7dc352907fc980b868725387e98f62019922c607154db95d1abd400ba85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbfb9858f3e8a7a75233f50979bd2b96", "guid": "bfdfe7dc352907fc980b868725387e980882ff739bd5f8d4474ea086df92e0e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985523e2a3300eefdb3f656f904bc7faf5", "guid": "bfdfe7dc352907fc980b868725387e98352ec71df091b526ab438457902aed03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab328eb0dd035b2a427c8f3deb885e96", "guid": "bfdfe7dc352907fc980b868725387e98c2f653948b18e65e7f517f466eaa7f04", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0e75dd8f2594af16dd75756d58e14c1", "guid": "bfdfe7dc352907fc980b868725387e986f2cb237f0a74ed268c42c83e3d90000"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f53301b07fa8fc414aad0005c9f49e3f", "guid": "bfdfe7dc352907fc980b868725387e98fda5d0a3451fe3e9876d3b104c56e0b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874a5b23130bc90a5d282398847d3dee5", "guid": "bfdfe7dc352907fc980b868725387e98a072883813e78b09401fc46e1a1935dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eb70eaf84679aaf9211723f6b9457fe", "guid": "bfdfe7dc352907fc980b868725387e98b656b8b46656b64a9b7f5851b61a6b29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab0e069d9592c0c9c80d01b7cab3923d", "guid": "bfdfe7dc352907fc980b868725387e985bffd7f51f96c12d929cb743ea763759", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877654ceb20fc7bbb8989b0d9ec3be9ff", "guid": "bfdfe7dc352907fc980b868725387e98b28c1fb7bdd77d21c6ed095af54e82e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3a56586973f42dcf10c66dc406c85ac", "guid": "bfdfe7dc352907fc980b868725387e98159bd2d0481ea6435457402a67e9135c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d2563d1e493fb469aa294e9611c51c1", "guid": "bfdfe7dc352907fc980b868725387e985b4e6856c15beb6217baf7cf8878a423"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c0fc601fa7d5c2a05db5c71198bc29e", "guid": "bfdfe7dc352907fc980b868725387e98fade7fa029c7a2d44fca0ee20ad0849d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98463e7c1529bac23545aa9f238b9c5f9e", "guid": "bfdfe7dc352907fc980b868725387e987384153fef4d8a22ed5f5af762eda4ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb9ab84165c36b09e5170420582876a9", "guid": "bfdfe7dc352907fc980b868725387e98b1c6ec2b81c0f2e9f5b940a1b0289afb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d053227a65af1bc08e30dfb2e7fa6937", "guid": "bfdfe7dc352907fc980b868725387e983bca9636901c65bac78211a2ffbed3d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1e48baf571ee3c20314cdef75f4c315", "guid": "bfdfe7dc352907fc980b868725387e98eca85fd9758a08ca1c101e041c74e96e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d13fc644ddccf726aed012c58887d4c6", "guid": "bfdfe7dc352907fc980b868725387e980f41e4534653808c93c728270f1f8776"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872f9fb327a4431cace7074ef5cbc7b49", "guid": "bfdfe7dc352907fc980b868725387e9880eb20c4e9b2ebfadd3f4ed86d82b9df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ea4ab2aea6fb2b22e2fdfd6e523fae4", "guid": "bfdfe7dc352907fc980b868725387e98681219dabe9f0521bcfc864f6c889ea1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875c563a19f2c6a1e3832d0238d134315", "guid": "bfdfe7dc352907fc980b868725387e986f42e3cb1132118a38e774ce5dbe740c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98547d818c8ec5fc17979f09d8a58017f4", "guid": "bfdfe7dc352907fc980b868725387e981948ccd5f09a75455b8e4814320db218"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b01fe409bb8156b84cf8a2a51abd43a", "guid": "bfdfe7dc352907fc980b868725387e9871f00c430cde3598bbffe00535cfc628"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98682ea03d8ea2a274b08a3936df8d1d69", "guid": "bfdfe7dc352907fc980b868725387e98474f1b9a57dcb0c79e17531761813602"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d63d29047b6ffcb36934a3dad529f01", "guid": "bfdfe7dc352907fc980b868725387e9878b7e77cb84cb75ce0761645be0d169f"}], "guid": "bfdfe7dc352907fc980b868725387e98a72e5809272002219c101470745cdc8b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9865affc2faff13804bd8dcfd0b956abf9", "guid": "bfdfe7dc352907fc980b868725387e982de5c1f255bc3319e2c39e91d52ea2fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863c3c45ea76b3ad424e0d35d5d478a1f", "guid": "bfdfe7dc352907fc980b868725387e98ad5d6c45d3618653e3100457dd1afda2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aef52b80a24453c2776e3f34660ef6c2", "guid": "bfdfe7dc352907fc980b868725387e98eff7bf6768b4ed65494356e2d61b2d61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c819c7dc8806f32b79dcdf70d74a4f86", "guid": "bfdfe7dc352907fc980b868725387e98082f2944424d429213249f85d1d57dfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98173e490edb2c5415ef189960ac5c79b5", "guid": "bfdfe7dc352907fc980b868725387e980080228880111ccf08b9bab3b69a0083"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb9c67de55b1add8a111191c34976229", "guid": "bfdfe7dc352907fc980b868725387e9867253ddd3648d43d1889c8f883fb499c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fdf9f9167b29fbbe514759f1e2cfd82", "guid": "bfdfe7dc352907fc980b868725387e9866f6482336a2bb68fda175d6a4b7e4ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cc910ce29f8c80f7d8aac84537a129e", "guid": "bfdfe7dc352907fc980b868725387e984c4a1e2e6a048655c067f779b1ef2e23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98768af00e2e7d4c17580aa978c50d340a", "guid": "bfdfe7dc352907fc980b868725387e989403de0ba8ecafef5e3785d4457e7c4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980326b88b8ba010841091002ea8cbdb11", "guid": "bfdfe7dc352907fc980b868725387e98fe37153f297309d552447932221a0ca7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb974c0c5c35af9511017f6a408d9203", "guid": "bfdfe7dc352907fc980b868725387e984e88cf641e903e67de246eca3e85f3af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bd93538dfb85afca7cb8ef4ffa5e8c4", "guid": "bfdfe7dc352907fc980b868725387e98300ed1f6e4e19b8473e3911eccf271d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98909bb47bb4903ad9987843d0c12c19f0", "guid": "bfdfe7dc352907fc980b868725387e9852211ac3ce6db18ecddc49d70314bc67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980419838b0781dc47952306dec456fc5d", "guid": "bfdfe7dc352907fc980b868725387e98375c002930d131517a6b623e8a5f8686"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843aeabb4bf12068c5dc5e7b23a37c43f", "guid": "bfdfe7dc352907fc980b868725387e98de1c207717fbb9e8c0139cd4fcd5995c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eb298a785115c7e0ba02f82eab0641b", "guid": "bfdfe7dc352907fc980b868725387e9869122ab5668497e1fc706a1b07d3ea69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b93b5c63dbc5e2f7cd18a34a55e2944", "guid": "bfdfe7dc352907fc980b868725387e981ac96e27dfb6fa02a7f6f6ca901b3ff8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855d0ce857f1d32b2cc1566454b54fca8", "guid": "bfdfe7dc352907fc980b868725387e9831184fb7e2bc7a798e341f33e5835f84"}], "guid": "bfdfe7dc352907fc980b868725387e9802bb8f55c539edc76b78085b33953f37", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98284b8709607485d28e799ae3f67d8f5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "guid": "bfdfe7dc352907fc980b868725387e987920e473a2ab545854c53f304b3fefa8"}], "guid": "bfdfe7dc352907fc980b868725387e98b7a48a88d164889fe8c5d485f56ceefa", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d13415e99024de9eae4c915de88c4fdc", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98a6109df942ebb1daa1daed6d22c2d1f7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}