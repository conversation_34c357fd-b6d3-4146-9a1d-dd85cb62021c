{"configVersion": 2, "packages": [{"name": "_flutterfire_internals", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.40", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "archive", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/archive-3.6.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/args-2.6.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/async-2.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "audio_session", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/audio_session-0.1.23", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "barcode", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/barcode-2.2.8", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "bidi", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/bidi-2.0.12", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "bloc", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/bloc-8.1.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "boolean_selector", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "cached_network_image", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/cached_network_image-3.4.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/cached_network_image_web-1.3.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cart_stepper", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/cart_stepper-4.3.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "characters", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/characters-1.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "chewie", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/chewie-1.10.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "clock", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "collection", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/collection-1.19.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "convert", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cross_file", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "csslib", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/csslib-1.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "cupertino_icons", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dbus", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/dbus-0.7.10", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "device_info_plus", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/device_info_plus-10.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "device_info_plus_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dots_indicator", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/dots_indicator-2.1.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dotted_border", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/dotted_border-2.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dropdown_button2", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/dropdown_button2-2.3.9", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "easy_localization", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/easy_localization-3.0.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "easy_logger", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/easy_logger-0.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "equatable", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/equatable-2.0.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "excel", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/excel-2.1.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "extended_image", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/extended_image-8.3.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "extended_image_library", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/extended_image_library-4.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "fake_async", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "faker", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/faker-2.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "ffi", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/ffi-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/file-7.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_linux", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_macos", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_windows", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "firebase_core", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/firebase_core-3.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/firebase_core_web-2.17.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/firebase_messaging-15.0.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.42", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging_web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/firebase_messaging_web-3.8.12", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "fixnum", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "fl_location", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/fl_location-4.4.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fl_location_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/fl_location_platform_interface-5.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fl_location_web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/fl_location_web-4.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter", "rootUri": "file:///D:/flutter%20SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_bloc", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_bloc-8.1.6", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_cache_manager", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_cache_manager-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_contacts", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_contacts-1.1.9+2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_dotenv", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_dotenv-5.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_driver", "rootUri": "file:///D:/flutter%20SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_driver", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_hooks", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_hooks-0.20.5", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_html", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_html-3.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_inappwebview", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview-6.0.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_inappwebview_android", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_internal_annotations", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_inappwebview_ios", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_macos", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_web-1.0.8", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_keyboard_visibility", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_linux", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_macos", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_platform_interface-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_windows", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_lints", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_localizations", "rootUri": "file:///D:/flutter%20SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_localizations", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.24", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_slidable", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_slidable-3.1.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_svg", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_svg-2.0.16", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_test", "rootUri": "file:///D:/flutter%20SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_web_plugins", "rootUri": "file:///D:/flutter%20SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_widget_from_html", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_widget_from_html-0.15.3", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_widget_from_html_core", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/flutter_widget_from_html_core-0.15.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fuchsia_remote_debug_protocol", "rootUri": "file:///D:/flutter%20SDK/flutter_windows_3.27.1-stable/flutter/packages/fuchsia_remote_debug_protocol", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "fwfh_cached_network_image", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/fwfh_cached_network_image-0.14.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fwfh_chewie", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/fwfh_chewie-0.14.8", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fwfh_just_audio", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/fwfh_just_audio-0.15.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fwfh_svg", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/fwfh_svg-0.8.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fwfh_url_launcher", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/fwfh_url_launcher-0.9.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fwfh_webview", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/fwfh_webview-0.15.3", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "geocoding", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/geocoding-2.2.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "geocoding_android", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/geocoding_android-3.3.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "geocoding_ios", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/geocoding_ios-2.3.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "geocoding_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/geocoding_platform_interface-3.2.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "go_router", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/go_router-14.6.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "google_maps", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/google_maps-8.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_maps_flutter", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/google_maps_flutter-2.10.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_maps_flutter_android", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_android-2.14.11", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "google_maps_flutter_ios", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "google_maps_flutter_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.9.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "google_maps_flutter_web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_web-0.5.10", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "html", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/html-0.15.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/http-1.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "http_client_helper", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/http_client_helper-3.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "http_parser", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/http_parser-4.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/image-4.3.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "image_gallery_saver", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/image_gallery_saver-2.0.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "image_picker", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/image_picker-1.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_android", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+18", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "image_picker_for_web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_ios", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_linux", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_picker_macos", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_picker_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_windows", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "integration_test", "rootUri": "file:///D:/flutter%20SDK/flutter_windows_3.27.1-stable/flutter/packages/integration_test", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "intl", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/intl-0.19.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "introduction_screen", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/introduction_screen-3.1.14", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "js", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/js-0.6.7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "just_audio", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/just_audio-0.9.42", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "just_audio_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/just_audio_platform_interface-4.3.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "just_audio_web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/just_audio_web-0.4.13", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/leak_tracker-10.0.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.8", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "list_counter", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/list_counter-1.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "logging", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "lottie", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/lottie-3.3.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "mask_text_input_formatter", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/mask_text_input_formatter-2.9.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "matcher", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/matcher-0.12.16+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "material_color_utilities", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "message_pack_dart", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/message_pack_dart-2.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "meta", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/meta-1.15.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "nested", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "octo_image", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/octo_image-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "open_filex", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/open_filex-4.6.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "package_info_plus", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/package_info_plus-8.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "package_info_plus_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-3.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "path", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/path-1.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_drawing", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/path_drawing-1.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path_parsing", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/path_parsing-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/path_provider_android-2.2.15", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "path_provider_foundation", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pdf", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/pdf-3.11.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "permission_handler", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/permission_handler-11.3.1", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "permission_handler_android", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/permission_handler_android-12.0.13", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "permission_handler_apple", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.5", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "permission_handler_html", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.2.3", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "permission_handler_windows", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/petitparser-6.0.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pinput", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/pinput-5.0.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "platform", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/platform-3.1.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pool", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/pool-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "process", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/process-5.0.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "provider", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/provider-6.1.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "qr", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/qr-3.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "qr_code_scanner", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/qr_code_scanner-1.0.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "qr_flutter", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/qr_flutter-4.1.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "rxdart", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/rxdart-0.28.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sanitize_html", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/sanitize_html-2.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "shared_preferences", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/shared_preferences-2.3.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_foundation", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shelf", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/shelf-1.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "signalr_netcore", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/signalr_netcore-1.4.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sky_engine", "rootUri": "file:///D:/flutter%20SDK/flutter_windows_3.27.1-stable/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "smooth_page_indicator", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/smooth_page_indicator-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "source_span", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/source_span-1.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "sprintf", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sqflite", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/sqflite-2.4.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "sqflite_android", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/sqflite_android-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "sqflite_common", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/sqflite_common-2.5.4+6", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "sqflite_darwin", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "sqflite_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "sse", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/sse-4.1.8", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sse_channel", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/sse_channel-0.1.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "stack_trace", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/stack_trace-1.12.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/stream_channel-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "stream_transform", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/string_scanner-1.3.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sync_http", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/sync_http-0.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "syncfusion_flutter_charts", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "syncfusion_flutter_core", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "syncfusion_flutter_datepicker", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_datepicker-26.2.14", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "syncfusion_flutter_pdf", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_pdf-26.2.14", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "syncfusion_flutter_pdfviewer", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_pdfviewer-26.2.14", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "syncfusion_flutter_signaturepad", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_signaturepad-26.2.14", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "syncfusion_pdfviewer_macos", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/syncfusion_pdfviewer_macos-26.2.14", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "syncfusion_pdfviewer_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/syncfusion_pdfviewer_platform_interface-26.2.14", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "syncfusion_pdfviewer_web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/syncfusion_pdfviewer_web-26.2.14", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "syncfusion_pdfviewer_windows", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/syncfusion_pdfviewer_windows-26.2.14", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "synchronized", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/synchronized-3.3.0+3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "term_glyph", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test_api", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/test_api-0.7.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "timeline_tile", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/timeline_tile-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "tuple", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/tuple-2.0.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "typed_data", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "universal_platform", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/universal_platform-1.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "url_launcher", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/url_launcher-6.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_android", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/url_launcher_android-6.3.14", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher_ios", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_linux", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/url_launcher_web-2.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_windows", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "uuid", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_graphics", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/vector_graphics-1.1.15", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_codec", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/vector_graphics_codec-1.1.12", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_compiler", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/vector_graphics_compiler-1.1.16", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_math", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "video_player", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/video_player-2.9.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "video_player_android", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/video_player_android-2.7.16", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "video_player_avfoundation", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/video_player_avfoundation-2.6.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "video_player_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/video_player_platform_interface-6.2.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "video_player_web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/video_player_web-2.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vm_service", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/vm_service-14.3.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "wakelock_plus", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/wakelock_plus-1.2.10", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "wakelock_plus_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/web-0.5.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web_socket", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/web_socket-1.0.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket_channel", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/web_socket_channel-3.0.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "webdriver", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/webdriver-3.0.4", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "webview_flutter", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/webview_flutter-4.10.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "webview_flutter_android", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/webview_flutter_android-4.2.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "webview_flutter_platform_interface", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/webview_flutter_platform_interface-2.11.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "webview_flutter_wkwebview", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/webview_flutter_wkwebview-3.20.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "win32", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/win32-5.9.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "win32_registry", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/win32_registry-1.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "xdg_directories", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "youtube_player_flutter", "rootUri": "file:///D:/flutter_pub/Cache/hosted/pub.dev/youtube_player_flutter-9.0.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_app", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.3"}], "generated": "2025-05-22T02:48:23.015519Z", "generator": "pub", "generatorVersion": "3.6.0", "flutterRoot": "file:///D:/flutter%20SDK/flutter_windows_3.27.1-stable/flutter", "flutterVersion": "3.27.1", "pubCache": "file:///D:/flutter_pub/Cache"}