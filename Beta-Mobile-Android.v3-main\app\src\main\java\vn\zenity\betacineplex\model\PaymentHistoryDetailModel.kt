package vn.zenity.betacineplex.model

import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.dateConvertFormat
import vn.zenity.betacineplex.helper.extension.toVNDCurrency

/**
 * Created by tinhvv on 6/1/18.
 */

enum class PaymentType(val value: String) {
    CASH("CASH"),
    CARD("CARD"),
    VOUCHER("VOUCHER"),
    BETAID("BETAID"),
    ONEPAY("ONEPAY"),
//    AIRPAY("AIRPAY"),
    SHOPEEPAY_ONLINE("SHOPEEPAY_ONLINE"),
    MOMO("MOMO"),
    ZALOPAY("ZALOPAY"),
}

class PaymentHistoryDetailModel {
    var FilmModel: FilmModel? = null
    var CinemaName: String? = null
    var ScreenName: String? = null
    var TotalPayment: Int = 0
    var QuantityPoint: Int = 0
    var SpendingPoint: Int = 0
    var No: String? = null
    var ListTicketType: List<TicketType>? = null
    var PaymentModel: List<Payment>? = null
    var ListCombo: List<Combo>? = null
    var ShowTime: String? = null
    var DateExpiredPoint: String? = null

    var showTime: Pair<String?, String?>? = null
        get() {
            val date = ShowTime?.dateConvertFormat(Constant.DateFormat.default, Constant.DateFormat.dateSavis)
            val time = ShowTime?.dateConvertFormat(Constant.DateFormat.default, Constant.DateFormat.hourMinute)
            return Pair(date, time)
        }

    var seatName: Pair<Int, String>? = null
        get() {
            ListTicketType?.let {
                var seats: ArrayList<String> = arrayListOf()
                var total = 0
                it.forEach {
                    total += it.ListSeatName?.size ?: 0
                    val seat = it.Name + " " + it.ListSeatName?.joinToString()
                    seats.add(seat)
                }
                return Pair(total, seats.joinToString(separator = "\n"))
            } ?: run {
                return Pair(0, "")
            }
        }
    var defaultPrice: String = "0đ"
    var total: String = defaultPrice
        get() {
            PaymentModel?.let {
                it.get(0).let {
                    return it.Values.toVNDCurrency()
                }
            } ?: kotlin.run { return defaultPrice }
        }

    var combos: Pair<Int, String>? = null
        get() {
            ListCombo?.let {
                val comboString = it.map { "${it.Name} (${it.Quantity ?: 0})" }.joinToString()
                return Pair(it.sumBy { it.Quantity ?: 0 }, comboString)
            } ?: kotlin.run {
                return Pair(0, defaultPrice)
            }
        }

    fun paymentValue(type: PaymentType): Int {
        if (PaymentModel == null) {
            return 0
        }
        val model = PaymentModel?.filter { it.PaymentTypeCode == type.value || (type == PaymentType.CARD && it.PaymentTypeCode == PaymentType.ONEPAY.value) }?.sumBy { it.Values }
        model?.let {
            return if (it > 0)
                it
            else 0
        } ?: kotlin.run {
            return 0
        }
    }
}

class TicketType {
    var TicketTypeId: String? = null
    var Name: String? = null
    var ListSeatName: List<String>? = null
}

class Payment {
    var PaymentTypeName: String? = null
    var PaymentTypeCode: String? = null
    var Values: Int = 0
}

class Combo {
    var ComboId: String? = null
    var Name: String? = null
    var Quantity: Int? = null
}