{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c1405dd5142b38f18f57a6c2a15aec0", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SignalRSwift/SignalRSwift-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SignalRSwift/SignalRSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SignalRSwift/SignalRSwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SignalRSwift", "PRODUCT_NAME": "SignalRSwift", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d43eed6a06db1c33415b157ad77154f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b7895ca188c697480531aa1e5105d2b6", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SignalRSwift/SignalRSwift-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SignalRSwift/SignalRSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SignalRSwift/SignalRSwift.modulemap", "PRODUCT_MODULE_NAME": "SignalRSwift", "PRODUCT_NAME": "SignalRSwift", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e08ae6e555aeaf7b10d742f102715d05", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b7895ca188c697480531aa1e5105d2b6", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SignalRSwift/SignalRSwift-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SignalRSwift/SignalRSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SignalRSwift/SignalRSwift.modulemap", "PRODUCT_MODULE_NAME": "SignalRSwift", "PRODUCT_NAME": "SignalRSwift", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98324224f7fadbc65e0ce88b1d9a4cc9d6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818ded952da16123213d802a1e007f0be", "guid": "bfdfe7dc352907fc980b868725387e98353ade778715f0dd2ae5172359f534fb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98941f97d6c49c66c02288a9e5985747ac", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989b704c885155b1e43449c98d1f887d4b", "guid": "bfdfe7dc352907fc980b868725387e9898294fec1da25aac167c5fe0208e0a6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae811476455f335e07bccd1570f2750e", "guid": "bfdfe7dc352907fc980b868725387e9812279689171d3b88be3678c1f4376381"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6b3fb0ecefc79a581eed6677c12d88a", "guid": "bfdfe7dc352907fc980b868725387e983d6dae778859d7233d844f6baf663f57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982946938c593ad0180c486f17c9a8fe3f", "guid": "bfdfe7dc352907fc980b868725387e980b9fe9c134f469d3ad035b2ab2695e9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de106a5a3c98b4f5adbfbe88e7885177", "guid": "bfdfe7dc352907fc980b868725387e981e1b98aed71399c1d7d23e529724e130"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f96f60c3612c6bf8505eadd365d9b262", "guid": "bfdfe7dc352907fc980b868725387e98e72bc1feb542052d221f018c20255541"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9d7460b44455f475865e1aad29e0df1", "guid": "bfdfe7dc352907fc980b868725387e98c3e16ddf430fee361916ba1b5a91d6b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b01e06d6e6e53fc2a5a2489f1df1e7d7", "guid": "bfdfe7dc352907fc980b868725387e9866b507751f81d662b9915b6b8a69c83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848347605015bf0c96cf9e86130d88155", "guid": "bfdfe7dc352907fc980b868725387e98da7c2a6f29098437c349fa1893854dec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf73e8df9163c32b45fb55741625aaa0", "guid": "bfdfe7dc352907fc980b868725387e9805d8ac41548e35cf91f96e9c20149fd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98982c729a000027a5a393b23d6184dc04", "guid": "bfdfe7dc352907fc980b868725387e986b49ce40ea4456aa890ce45ff0f30e3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dcc821bf62c6e1b2cd24d474bb7f732", "guid": "bfdfe7dc352907fc980b868725387e9855e28ab35ef249392471e48b3facff3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebfe5188b997ab1d793cf6bd3611a148", "guid": "bfdfe7dc352907fc980b868725387e98ff0ef6e2277043a3248a75be1a60c961"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7fd1933123371826fe3d048a8809fbf", "guid": "bfdfe7dc352907fc980b868725387e983990470068a5cb3d142d3cb78b53201f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834ac8f0c7ad09fd6b9020d1a7e23426b", "guid": "bfdfe7dc352907fc980b868725387e98802faa2973c7cf7436dbe6682c61e699"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f50f90e439898afae643feeafe21ec2f", "guid": "bfdfe7dc352907fc980b868725387e98252d852a7a10a60ad2a3998e1fa16dbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98194f45678d2af8d25efc447647cd32b4", "guid": "bfdfe7dc352907fc980b868725387e9894c5971e55e255ad1aca288aca8bb86c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841f86d6827b661c321ecbc63414e609f", "guid": "bfdfe7dc352907fc980b868725387e98267a66e3a4c0ebf6f7b70478179865b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3f6d2ad2a357b03b4a3904ea146a966", "guid": "bfdfe7dc352907fc980b868725387e9849b105a980a350be629b6f70af99e1ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a8d6d5ea1171e9ee9e554d6c63a0474", "guid": "bfdfe7dc352907fc980b868725387e9848016832e212845865ff110b1a1b8457"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827a920f27a91c438461472afe0fb0ae6", "guid": "bfdfe7dc352907fc980b868725387e983fca078dde3dece60eb852feb9f762ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a1a4fa1c7a4a8c287e4426dd36183dc", "guid": "bfdfe7dc352907fc980b868725387e98dea23315d925d63ef0dcbd91309e6e2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983acd050ea4945e5cf2c90304d7d5333c", "guid": "bfdfe7dc352907fc980b868725387e98bdefabf0075cb5a64dc984b4fbef147e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e49de64d55e900efd7b0d930e8d584a9", "guid": "bfdfe7dc352907fc980b868725387e98ddfc45ae5e3c71116a1799938a4d4e38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875de997e0cd7e22aca5874db6cd9ab56", "guid": "bfdfe7dc352907fc980b868725387e98897fe3cb1f392992389e8d065629fe91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881b65b0437263723c5db6fad2b63222c", "guid": "bfdfe7dc352907fc980b868725387e98eca5c442cceb69bb2e8357bb2390e6fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a06dbb0bfa143e3f4c8b3c34213bc6a1", "guid": "bfdfe7dc352907fc980b868725387e9854d3cfd8a071399c6f1d7d3ee7160854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0bc671227e4e5fb02e609409be13b94", "guid": "bfdfe7dc352907fc980b868725387e98212dac27dc5b5d49411a28c281f6a2c4"}], "guid": "bfdfe7dc352907fc980b868725387e98d40eace1639e609de656ab99e0106431", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9823938f1d259ed2a9308e86ff4691aedd"}], "guid": "bfdfe7dc352907fc980b868725387e9817217965595d6da1e9e9383e98ec6b44", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9871fb38c5a47a746825b70137c1ed878e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ba3679b4c4428e6a6d83c5308d3af99b", "name": "Alamofire"}, {"guid": "bfdfe7dc352907fc980b868725387e989eef64033d2a36546e801665ce04331a", "name": "Starscream"}], "guid": "bfdfe7dc352907fc980b868725387e98a21c1828358ae4b89fae66b2213f5235", "name": "SignalRSwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e984979cf070ced94194e7d37d86925c39b", "name": "SignalRSwift.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}