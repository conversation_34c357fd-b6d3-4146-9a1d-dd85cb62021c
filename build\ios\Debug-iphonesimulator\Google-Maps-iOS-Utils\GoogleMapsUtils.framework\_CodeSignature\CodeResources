<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/GMSMarker+GMUClusteritem.h</key>
		<data>
		vblmJ8G95+620sVcrbKe4rP6PIA=
		</data>
		<key>Headers/GMUCluster.h</key>
		<data>
		0yXlZpK6qDeF8QoCHw4Z77cSo8I=
		</data>
		<key>Headers/GMUClusterAlgorithm.h</key>
		<data>
		aBuv2hlVKpQc3L50WktT6MMPdU4=
		</data>
		<key>Headers/GMUClusterIconGenerator.h</key>
		<data>
		/MozjXiLJLQsUB+Tmb5bav7/6Ow=
		</data>
		<key>Headers/GMUClusterItem.h</key>
		<data>
		eGGWxKKKle80egaSPTiWpLMryQs=
		</data>
		<key>Headers/GMUClusterManager+Testing.h</key>
		<data>
		KsDvlbSitgUDI1krqpfyyNBY0PU=
		</data>
		<key>Headers/GMUClusterManager.h</key>
		<data>
		By1/Wvwfm7tCPSRJk9hpcbbtqaM=
		</data>
		<key>Headers/GMUClusterRenderer.h</key>
		<data>
		igEJpeGAmC9+uzr8YNgnvh2iMh0=
		</data>
		<key>Headers/GMUDefaultClusterIconGenerator+Testing.h</key>
		<data>
		qzF8PlORfgI3E8lVh1TJSGX7Nvo=
		</data>
		<key>Headers/GMUDefaultClusterIconGenerator.h</key>
		<data>
		kzMSmHvL5TnFmDhnhLhBhLXbEu0=
		</data>
		<key>Headers/GMUDefaultClusterRenderer+Testing.h</key>
		<data>
		CkNwYasUu+9hlHuj9JYqLl4v6rA=
		</data>
		<key>Headers/GMUDefaultClusterRenderer.h</key>
		<data>
		8HGGdss3S2GWN/Lpa16HmXX1NKE=
		</data>
		<key>Headers/GMUFeature.h</key>
		<data>
		6LeQFoAnohTgsxIK/e292RIYnik=
		</data>
		<key>Headers/GMUGeoJSONParser.h</key>
		<data>
		C+cltx5NhfEM6sf8eZfyAIn79nk=
		</data>
		<key>Headers/GMUGeometry.h</key>
		<data>
		w1raLM5pUXx6Sr3NMusxlcZ7vSA=
		</data>
		<key>Headers/GMUGeometryCollection.h</key>
		<data>
		UsQrU+CjlV+RWAP4G5RGTRJRPsY=
		</data>
		<key>Headers/GMUGeometryContainer.h</key>
		<data>
		Xt0Xsve7q+tPJquExANps+rUiD4=
		</data>
		<key>Headers/GMUGeometryRenderer+Testing.h</key>
		<data>
		PFmXBEU30yKA53JBeXxJPI9dF+w=
		</data>
		<key>Headers/GMUGeometryRenderer.h</key>
		<data>
		lg4MnrPZX00Q2OEIG4XGANGPV0I=
		</data>
		<key>Headers/GMUGradient.h</key>
		<data>
		VrMGrPc1y6Vte/EZOkI/myFniEw=
		</data>
		<key>Headers/GMUGridBasedClusterAlgorithm.h</key>
		<data>
		Ek69ADlUS51n/a2Y/CdrsQ+ZT74=
		</data>
		<key>Headers/GMUGroundOverlay.h</key>
		<data>
		9z/ru/7HHp48Yt7+Kw3/V+kRR6k=
		</data>
		<key>Headers/GMUHeatmapTileLayer+Testing.h</key>
		<data>
		mOq0rXEC7AgkwAIPZJYPLxY88YM=
		</data>
		<key>Headers/GMUHeatmapTileLayer.h</key>
		<data>
		sa2rL5MGPNghLo16gnCq5BXUrYk=
		</data>
		<key>Headers/GMUKMLParser.h</key>
		<data>
		j2E/uqLTRGYRKwuHMMMCYm6E/kk=
		</data>
		<key>Headers/GMULineString.h</key>
		<data>
		452wFlW49MmpatpQrZoNcKTQTBw=
		</data>
		<key>Headers/GMUMarkerClustering.h</key>
		<data>
		lQ6Q7DPzuzS1goekLoMTt3wH1TY=
		</data>
		<key>Headers/GMUNonHierarchicalDistanceBasedAlgorithm.h</key>
		<data>
		G7rMeyz4OkGMcHuOL4btZ9IVSPo=
		</data>
		<key>Headers/GMUPair.h</key>
		<data>
		HYwiyIcuDEfPtEkBZN5TbnGsA9c=
		</data>
		<key>Headers/GMUPlacemark.h</key>
		<data>
		Ii3S82rRKtpizRw5bZN67qBuO8o=
		</data>
		<key>Headers/GMUPoint.h</key>
		<data>
		AFf0D74yYDwesJSq8Nko27ppDeI=
		</data>
		<key>Headers/GMUPolygon.h</key>
		<data>
		+8XjRfe/oRw6Po0FT9kCx5hf0P4=
		</data>
		<key>Headers/GMUSimpleClusterAlgorithm.h</key>
		<data>
		YUKi9DChIUCFDgkSA2WoLaWGK9I=
		</data>
		<key>Headers/GMUStaticCluster.h</key>
		<data>
		cOPMFEZR71rVJCuC+i66d1596uM=
		</data>
		<key>Headers/GMUStyle.h</key>
		<data>
		iJ974Z5OK7lKHI9HGlCRaJQxnRQ=
		</data>
		<key>Headers/GMUStyleMap.h</key>
		<data>
		aAQyBGfPy9ftJ7VvfjmP+2pnHEQ=
		</data>
		<key>Headers/GMUWeightedLatLng.h</key>
		<data>
		PKi5eFl0ERaTl0YFa8GqC1RFVCQ=
		</data>
		<key>Headers/GMUWrappingDictionaryKey.h</key>
		<data>
		w0nHp9wFsrNUyDZfHO9jNcHGNGc=
		</data>
		<key>Headers/GQTBounds.h</key>
		<data>
		tNJlU/+YS0ttxlJvfu3BQM7sF0I=
		</data>
		<key>Headers/GQTPoint.h</key>
		<data>
		7+EFvo7oOzVy0g0qSzHAPO0VM9E=
		</data>
		<key>Headers/GQTPointQuadTree.h</key>
		<data>
		+WLe1vlCeO1VoSn99SmrmiQr3ro=
		</data>
		<key>Headers/GQTPointQuadTreeChild.h</key>
		<data>
		VtB81qYx1fd/PLwm/GCSs9bX8Uk=
		</data>
		<key>Headers/GQTPointQuadTreeItem.h</key>
		<data>
		NcXZGUE62tPf1v6daLLbD70kmoU=
		</data>
		<key>Headers/Google-Maps-iOS-Utils-umbrella.h</key>
		<data>
		ni2CnN1NJQkzEHZaTUZhS+Ml9uU=
		</data>
		<key>Headers/GoogleMapsUtils-Bridging-Header.h</key>
		<data>
		0usHf+e4Exw0jfxmi8lU/cALb4k=
		</data>
		<key>Headers/GoogleMapsUtils-Swift.h</key>
		<data>
		+EId8J2ivrTirfkvkcY28yNFZiU=
		</data>
		<key>Info.plist</key>
		<data>
		XMYIQyQrbc3gabnajfQPjyxP2RA=
		</data>
		<key>Modules/GoogleMapsUtils.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		oRPqZ3Nt9yFN6IdBOMTgJtDgGME=
		</data>
		<key>Modules/GoogleMapsUtils.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		4Nn5vWJ/TmcFnJQ5HmNY5U/AcKk=
		</data>
		<key>Modules/GoogleMapsUtils.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/GoogleMapsUtils.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		pDmprfEXGaHcAkdCyXA2OG2+vMY=
		</data>
		<key>Modules/GoogleMapsUtils.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		DOJRzzaLrUvAYWpKM1LSXIJE2XU=
		</data>
		<key>Modules/GoogleMapsUtils.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/GoogleMapsUtils.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		MziThj5pad1ytwhrm8lnVxqDqgI=
		</data>
		<key>Modules/GoogleMapsUtils.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		26gR4yoVJ03oYavHl4IhsbcNcjE=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		yRLVHFO7rrSx78LGLHPNSrpJp50=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/GMSMarker+GMUClusteritem.h</key>
		<dict>
			<key>hash</key>
			<data>
			vblmJ8G95+620sVcrbKe4rP6PIA=
			</data>
			<key>hash2</key>
			<data>
			YuGDPFEpJ2Caw3GVCjiexKhWapZ8IvCkTgAPxhKfO1k=
			</data>
		</dict>
		<key>Headers/GMUCluster.h</key>
		<dict>
			<key>hash</key>
			<data>
			0yXlZpK6qDeF8QoCHw4Z77cSo8I=
			</data>
			<key>hash2</key>
			<data>
			sKdMX+SdCULmy9M6nyvoaR9efAqQogx1Je0/A3ITizs=
			</data>
		</dict>
		<key>Headers/GMUClusterAlgorithm.h</key>
		<dict>
			<key>hash</key>
			<data>
			aBuv2hlVKpQc3L50WktT6MMPdU4=
			</data>
			<key>hash2</key>
			<data>
			8AwtmATjhbPusB2Hdcj/a2vJ/QI8dxwO4qg3On5APvQ=
			</data>
		</dict>
		<key>Headers/GMUClusterIconGenerator.h</key>
		<dict>
			<key>hash</key>
			<data>
			/MozjXiLJLQsUB+Tmb5bav7/6Ow=
			</data>
			<key>hash2</key>
			<data>
			l8/AEdBWsQ0GUrrAvEjhX7FQTlvzaG+OmDAq9d+gN2M=
			</data>
		</dict>
		<key>Headers/GMUClusterItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			eGGWxKKKle80egaSPTiWpLMryQs=
			</data>
			<key>hash2</key>
			<data>
			I1eETmtJ29EhK2SzFJu/f5UxrZVBUEbeNNOAhu62J/c=
			</data>
		</dict>
		<key>Headers/GMUClusterManager+Testing.h</key>
		<dict>
			<key>hash</key>
			<data>
			KsDvlbSitgUDI1krqpfyyNBY0PU=
			</data>
			<key>hash2</key>
			<data>
			3egyroWw+VbJiySo8S9mT2ajprcAgHrBJukLksk2qTU=
			</data>
		</dict>
		<key>Headers/GMUClusterManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			By1/Wvwfm7tCPSRJk9hpcbbtqaM=
			</data>
			<key>hash2</key>
			<data>
			TNbCJKFFVhQU4oKkdIOGfC7fnvTjKE9+i3SX2oUoCPQ=
			</data>
		</dict>
		<key>Headers/GMUClusterRenderer.h</key>
		<dict>
			<key>hash</key>
			<data>
			igEJpeGAmC9+uzr8YNgnvh2iMh0=
			</data>
			<key>hash2</key>
			<data>
			6nY2vnw2eKebZj+LaGzxtvmveMmg5kXS7lAd4qgqEDo=
			</data>
		</dict>
		<key>Headers/GMUDefaultClusterIconGenerator+Testing.h</key>
		<dict>
			<key>hash</key>
			<data>
			qzF8PlORfgI3E8lVh1TJSGX7Nvo=
			</data>
			<key>hash2</key>
			<data>
			Gcmyu6xoiyWfqwvLdbk8NiTkoxq309CUJ3F+n1KUElA=
			</data>
		</dict>
		<key>Headers/GMUDefaultClusterIconGenerator.h</key>
		<dict>
			<key>hash</key>
			<data>
			kzMSmHvL5TnFmDhnhLhBhLXbEu0=
			</data>
			<key>hash2</key>
			<data>
			oB7Qhsm0k22ZC+zoeZwxn0bt1aGH+q5WXzYXWrZkUWM=
			</data>
		</dict>
		<key>Headers/GMUDefaultClusterRenderer+Testing.h</key>
		<dict>
			<key>hash</key>
			<data>
			CkNwYasUu+9hlHuj9JYqLl4v6rA=
			</data>
			<key>hash2</key>
			<data>
			nHFWHk95cv0eLT++JFjrMKOwFKD7c3qD1Bx3M6hz89k=
			</data>
		</dict>
		<key>Headers/GMUDefaultClusterRenderer.h</key>
		<dict>
			<key>hash</key>
			<data>
			8HGGdss3S2GWN/Lpa16HmXX1NKE=
			</data>
			<key>hash2</key>
			<data>
			S9n0T+zKr1lomMoamDkAGOG7vn3i/ONSByLnoBITuhE=
			</data>
		</dict>
		<key>Headers/GMUFeature.h</key>
		<dict>
			<key>hash</key>
			<data>
			6LeQFoAnohTgsxIK/e292RIYnik=
			</data>
			<key>hash2</key>
			<data>
			DuvxduHLfgX3bnS4crdDq+lrrHqackFoCtq1pfpFlQY=
			</data>
		</dict>
		<key>Headers/GMUGeoJSONParser.h</key>
		<dict>
			<key>hash</key>
			<data>
			C+cltx5NhfEM6sf8eZfyAIn79nk=
			</data>
			<key>hash2</key>
			<data>
			u5QJ/u8zFIgD8gfJWSfLIJSNIL6SNvUUCo8bC6z8qF8=
			</data>
		</dict>
		<key>Headers/GMUGeometry.h</key>
		<dict>
			<key>hash</key>
			<data>
			w1raLM5pUXx6Sr3NMusxlcZ7vSA=
			</data>
			<key>hash2</key>
			<data>
			4uLRqxojb66sVHrJpqF/vaNEyWARG1m6S0kkqsNwy+o=
			</data>
		</dict>
		<key>Headers/GMUGeometryCollection.h</key>
		<dict>
			<key>hash</key>
			<data>
			UsQrU+CjlV+RWAP4G5RGTRJRPsY=
			</data>
			<key>hash2</key>
			<data>
			1GCkLV8xMLZ9iIHNs4G13eO+TzgZfeKUgkv+AUtOHy0=
			</data>
		</dict>
		<key>Headers/GMUGeometryContainer.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xt0Xsve7q+tPJquExANps+rUiD4=
			</data>
			<key>hash2</key>
			<data>
			sx1ZiPklQYC8nfXWOtMk65xs0Dtwvc0XK3rgnk6Oyu4=
			</data>
		</dict>
		<key>Headers/GMUGeometryRenderer+Testing.h</key>
		<dict>
			<key>hash</key>
			<data>
			PFmXBEU30yKA53JBeXxJPI9dF+w=
			</data>
			<key>hash2</key>
			<data>
			J4DyIv5XuLUaguhtAO3oYVztA3PXCCbNIdKIEAUeiZo=
			</data>
		</dict>
		<key>Headers/GMUGeometryRenderer.h</key>
		<dict>
			<key>hash</key>
			<data>
			lg4MnrPZX00Q2OEIG4XGANGPV0I=
			</data>
			<key>hash2</key>
			<data>
			w7tKHN9mHRaOs9KPV7GtbIeR/dZkvqVXW9ef3C0AA9o=
			</data>
		</dict>
		<key>Headers/GMUGradient.h</key>
		<dict>
			<key>hash</key>
			<data>
			VrMGrPc1y6Vte/EZOkI/myFniEw=
			</data>
			<key>hash2</key>
			<data>
			5m11HmDN00wtvPIBqwlGhOJct+aFv18tf57tYHzM81Q=
			</data>
		</dict>
		<key>Headers/GMUGridBasedClusterAlgorithm.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ek69ADlUS51n/a2Y/CdrsQ+ZT74=
			</data>
			<key>hash2</key>
			<data>
			a/av5pigQXGsmGce7n/PidCbZwGwGZze6iidJJu0pPE=
			</data>
		</dict>
		<key>Headers/GMUGroundOverlay.h</key>
		<dict>
			<key>hash</key>
			<data>
			9z/ru/7HHp48Yt7+Kw3/V+kRR6k=
			</data>
			<key>hash2</key>
			<data>
			uZBka+cqILSxNglJDkhzC9Hwu6OpUu6Z+KbwF/MsJE0=
			</data>
		</dict>
		<key>Headers/GMUHeatmapTileLayer+Testing.h</key>
		<dict>
			<key>hash</key>
			<data>
			mOq0rXEC7AgkwAIPZJYPLxY88YM=
			</data>
			<key>hash2</key>
			<data>
			BVudqsSKkyQMBOevGTLcBtZtWNcOCS7SbGOuUyK2X3U=
			</data>
		</dict>
		<key>Headers/GMUHeatmapTileLayer.h</key>
		<dict>
			<key>hash</key>
			<data>
			sa2rL5MGPNghLo16gnCq5BXUrYk=
			</data>
			<key>hash2</key>
			<data>
			ll+pXeaKhVQ/ER+zhQ52lCiZwiNq+Bzy8Cmk6RAfk3c=
			</data>
		</dict>
		<key>Headers/GMUKMLParser.h</key>
		<dict>
			<key>hash</key>
			<data>
			j2E/uqLTRGYRKwuHMMMCYm6E/kk=
			</data>
			<key>hash2</key>
			<data>
			u1qbHDvGyHXd3CWfwsz9qL3Wg4AtDiLShpod8ILM028=
			</data>
		</dict>
		<key>Headers/GMULineString.h</key>
		<dict>
			<key>hash</key>
			<data>
			452wFlW49MmpatpQrZoNcKTQTBw=
			</data>
			<key>hash2</key>
			<data>
			TS82kz1mgsoI7yxFi9KuthUhKpwnhO7Ew8UK2CEReVw=
			</data>
		</dict>
		<key>Headers/GMUMarkerClustering.h</key>
		<dict>
			<key>hash</key>
			<data>
			lQ6Q7DPzuzS1goekLoMTt3wH1TY=
			</data>
			<key>hash2</key>
			<data>
			hBfk9RrV+R+f9UnIJg/2crvC5Q06+ViZfFZk5M0TJpU=
			</data>
		</dict>
		<key>Headers/GMUNonHierarchicalDistanceBasedAlgorithm.h</key>
		<dict>
			<key>hash</key>
			<data>
			G7rMeyz4OkGMcHuOL4btZ9IVSPo=
			</data>
			<key>hash2</key>
			<data>
			u01sWGYFn2GiL95sqdzIsHsbNdaYM1I936hkN3yyTL4=
			</data>
		</dict>
		<key>Headers/GMUPair.h</key>
		<dict>
			<key>hash</key>
			<data>
			HYwiyIcuDEfPtEkBZN5TbnGsA9c=
			</data>
			<key>hash2</key>
			<data>
			q9n4x7i6dF0rC1p7Wu+DYpNBr2o0P/c7pL3vC7Kktnc=
			</data>
		</dict>
		<key>Headers/GMUPlacemark.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ii3S82rRKtpizRw5bZN67qBuO8o=
			</data>
			<key>hash2</key>
			<data>
			z9VhZUpB9JtKzZL0Y+hpajIgfE0AkwZydMjPgy46ZK0=
			</data>
		</dict>
		<key>Headers/GMUPoint.h</key>
		<dict>
			<key>hash</key>
			<data>
			AFf0D74yYDwesJSq8Nko27ppDeI=
			</data>
			<key>hash2</key>
			<data>
			IvMZjZMr5zwO4jyJoPK818EnCSIWuqYnurG0vK36gLc=
			</data>
		</dict>
		<key>Headers/GMUPolygon.h</key>
		<dict>
			<key>hash</key>
			<data>
			+8XjRfe/oRw6Po0FT9kCx5hf0P4=
			</data>
			<key>hash2</key>
			<data>
			PhQLpfE30OpTgFPc9W/d2pYr/T7z8EmrTljPe9C/qbY=
			</data>
		</dict>
		<key>Headers/GMUSimpleClusterAlgorithm.h</key>
		<dict>
			<key>hash</key>
			<data>
			YUKi9DChIUCFDgkSA2WoLaWGK9I=
			</data>
			<key>hash2</key>
			<data>
			b7VShTqibVie9iFZ0XIbb24BYG2V1MDxzzFYjV0ZAYU=
			</data>
		</dict>
		<key>Headers/GMUStaticCluster.h</key>
		<dict>
			<key>hash</key>
			<data>
			cOPMFEZR71rVJCuC+i66d1596uM=
			</data>
			<key>hash2</key>
			<data>
			/47fIjssXEo9K7uGLhtVSZg5UuGAegBSAEhjw2AU7tA=
			</data>
		</dict>
		<key>Headers/GMUStyle.h</key>
		<dict>
			<key>hash</key>
			<data>
			iJ974Z5OK7lKHI9HGlCRaJQxnRQ=
			</data>
			<key>hash2</key>
			<data>
			6EnPM+NNjqHD6ttYIuipSNGA273fHgzAD4ipyqtW6JY=
			</data>
		</dict>
		<key>Headers/GMUStyleMap.h</key>
		<dict>
			<key>hash</key>
			<data>
			aAQyBGfPy9ftJ7VvfjmP+2pnHEQ=
			</data>
			<key>hash2</key>
			<data>
			ZUtB9akPPUcAV+CbWjBNJCfFVTYAV34byCnFlbgYuKM=
			</data>
		</dict>
		<key>Headers/GMUWeightedLatLng.h</key>
		<dict>
			<key>hash</key>
			<data>
			PKi5eFl0ERaTl0YFa8GqC1RFVCQ=
			</data>
			<key>hash2</key>
			<data>
			zFcTb6qdGPydk+1UFJWbrDsuf1HeU3Tq1uHERQlbSXA=
			</data>
		</dict>
		<key>Headers/GMUWrappingDictionaryKey.h</key>
		<dict>
			<key>hash</key>
			<data>
			w0nHp9wFsrNUyDZfHO9jNcHGNGc=
			</data>
			<key>hash2</key>
			<data>
			yOph2GQXyMo9eZaeb5neUGEQv/JlZuB7aySU1AvfyP8=
			</data>
		</dict>
		<key>Headers/GQTBounds.h</key>
		<dict>
			<key>hash</key>
			<data>
			tNJlU/+YS0ttxlJvfu3BQM7sF0I=
			</data>
			<key>hash2</key>
			<data>
			lfwBn3o6B3JhZ8bS9Ht4PAg+5cte26UI+TAEPnyG2EE=
			</data>
		</dict>
		<key>Headers/GQTPoint.h</key>
		<dict>
			<key>hash</key>
			<data>
			7+EFvo7oOzVy0g0qSzHAPO0VM9E=
			</data>
			<key>hash2</key>
			<data>
			LiWuPXGvCGKNc2xTQEIC1wpa/graMP1Pxc14/SkexgE=
			</data>
		</dict>
		<key>Headers/GQTPointQuadTree.h</key>
		<dict>
			<key>hash</key>
			<data>
			+WLe1vlCeO1VoSn99SmrmiQr3ro=
			</data>
			<key>hash2</key>
			<data>
			jrKU9vNAmSXXHdlbuZmy0AD25+Y0wmBsAS3XjtC1bic=
			</data>
		</dict>
		<key>Headers/GQTPointQuadTreeChild.h</key>
		<dict>
			<key>hash</key>
			<data>
			VtB81qYx1fd/PLwm/GCSs9bX8Uk=
			</data>
			<key>hash2</key>
			<data>
			2B0M7dpUwldxi1QJBDmp8aMgzwuwrbIIltl0x9e4toU=
			</data>
		</dict>
		<key>Headers/GQTPointQuadTreeItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			NcXZGUE62tPf1v6daLLbD70kmoU=
			</data>
			<key>hash2</key>
			<data>
			KOBtBd0Z3sby2hssQ2TsSfzSI6rAlhaba5rl9NUd+1k=
			</data>
		</dict>
		<key>Headers/Google-Maps-iOS-Utils-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			ni2CnN1NJQkzEHZaTUZhS+Ml9uU=
			</data>
			<key>hash2</key>
			<data>
			1c+i1BPICoyIWOFxOnwk50KLk0zxGj9ujLLDFxK7dW8=
			</data>
		</dict>
		<key>Headers/GoogleMapsUtils-Bridging-Header.h</key>
		<dict>
			<key>hash</key>
			<data>
			0usHf+e4Exw0jfxmi8lU/cALb4k=
			</data>
			<key>hash2</key>
			<data>
			2Rzyffs5j4VQ06CCnJSDcCr6O8PnHiMenauCmUWpGOA=
			</data>
		</dict>
		<key>Headers/GoogleMapsUtils-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			+EId8J2ivrTirfkvkcY28yNFZiU=
			</data>
			<key>hash2</key>
			<data>
			a33O1Fmy7BhDDqZlZ/JaHAtVAUHjxDDJlOdS7Gkbe44=
			</data>
		</dict>
		<key>Modules/GoogleMapsUtils.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			oRPqZ3Nt9yFN6IdBOMTgJtDgGME=
			</data>
			<key>hash2</key>
			<data>
			Mtwv5uDesIAGhJeYG2ewWUtBxOLJGH+qSUGgoXmgMgU=
			</data>
		</dict>
		<key>Modules/GoogleMapsUtils.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			4Nn5vWJ/TmcFnJQ5HmNY5U/AcKk=
			</data>
			<key>hash2</key>
			<data>
			m61j8ULG+WZmmxYyj0IV+4bIiamZaEMMdnkkwg/pxl4=
			</data>
		</dict>
		<key>Modules/GoogleMapsUtils.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/GoogleMapsUtils.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			pDmprfEXGaHcAkdCyXA2OG2+vMY=
			</data>
			<key>hash2</key>
			<data>
			hBVTTgAXT174xuTCdf3NYZeSurwpShQXydge62AQfZ8=
			</data>
		</dict>
		<key>Modules/GoogleMapsUtils.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			DOJRzzaLrUvAYWpKM1LSXIJE2XU=
			</data>
			<key>hash2</key>
			<data>
			fO5CEfLnOLePse4oNMOQu1FyXyYdnSXw/FuVPwfW8kY=
			</data>
		</dict>
		<key>Modules/GoogleMapsUtils.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/GoogleMapsUtils.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			MziThj5pad1ytwhrm8lnVxqDqgI=
			</data>
			<key>hash2</key>
			<data>
			17A/yYTgyoIu/2DNtJo4kx1K/7bCAdlG2Yg/hGock4c=
			</data>
		</dict>
		<key>Modules/GoogleMapsUtils.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			26gR4yoVJ03oYavHl4IhsbcNcjE=
			</data>
			<key>hash2</key>
			<data>
			Lqxd+UTZlzx3y65Of22qFOKU3v9TtS4MIYvI/sQwOf8=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			yRLVHFO7rrSx78LGLHPNSrpJp50=
			</data>
			<key>hash2</key>
			<data>
			p/MIWvuU1rg68VPYSCfIi29rhVP+UAKLeK9h5R8dzuo=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
