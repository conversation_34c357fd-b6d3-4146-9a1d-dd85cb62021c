package vn.zenity.betacineplex.model

import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey

/**
 * Created by tinhvv on 4/14/18.
 */
@Entity(tableName = "UserModel")
class UserModel {
    var FirstName : String? = null
    var LastName : String? = null
    var AccountTypeId : String? = null
    var AccountTypeName : String? = null
    var TeamId : String? = null
    var Picture : String? = null
    var PersonalIncomeTaxCode : String? = null
    var EnterpriseId : String? = null
    var Rating : Int? = null
    var IsDeleted : Boolean? = null
    var Status : Int? = null
    var CreatedByUserId : String? = null
    var CreatedOnDate : String? = null
    var LastModifiedByUserId : String? = null
    var LastModifiedOnDate : String? = null
    var Description : String? = null
    var PhoneOffice : String? = null
    var Email : String? = null
    var PersonalId : String? = null
    var BirthDate : String? = null
    var Gender : Int? = null
    var RegisterPlaceId : String? = null
    var RegisterPlaceName : String? = null
    var RegisterSiteId : String? = null
    var RegisterSiteName : String? = null
    var ReferenceCode : String? = null
    var ReferenceAccountId : String? = null
    var ReferenceAccountRefCode : String? = null
    var AddressStreet : String? = null
    var AddressDistrict : String? = null
    var AddressCityId : String? = null
    var AddressDistrictId : String? = null
    var AddressCity : String? = null
    var BillingAddressStreet : String? = null
    var BillingAddressDistrict : String? = null
    var BillingAddressCity : String? = null
    var ShippingAddressDistrict : String? = null
    var ShippingAddressStreet : String? = null
    var ShippingAddressCity : String? = null
    var IsReceiveEmail : Boolean? = null
    var IsReceiveSMS : Boolean? = null
    var OnlineId : String? = null
    var IsInvalidEmail : Boolean? = null
    var TotalAccumulatedPoints : Int? = null
    var TotalBillPayment : Int? = null
    var AvailablePoint : Int? = null
    var TotalPoint : Int? = null
    var AccountId : String? = null
    var FullName : String? = null
    var Code : String? = null
    var ClassId : String? = null
    var CardId : String? = null
    var CardNumber : String? = null
    var ApplicationId : String? = null
    var ClassName: String? = null
    var ClassCode: String? = null
    var Token: String? = null
    var QuantityOfVoucher: Int = 0
    var AlmostExpiredPoint : Int = 0
    var AlmostExpiredPointDate : String? = null
    @PrimaryKey
    var UserId: String = "1"
    @Ignore
    var IsUpdatedFacebookPassword: Boolean? = null
    @Ignore
    var TotalRemainingBillsToUpgradeClass: Double? = null
}