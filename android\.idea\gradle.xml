<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <compositeConfiguration>
          <compositeBuild compositeDefinitionSource="SCRIPT">
            <builds>
              <build path="$PROJECT_DIR$/../../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_tools/gradle" name="gradle">
                <projects>
                  <project path="$PROJECT_DIR$/../../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_tools/gradle" />
                </projects>
              </build>
            </builds>
          </compositeBuild>
        </compositeConfiguration>
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="#GRADLE_LOCAL_JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/../../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_tools/gradle" />
            <option value="$PROJECT_DIR$/../../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/integration_test/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/audio_session-0.1.23/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/device_info_plus-10.1.2/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/firebase_core-3.3.0/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/firebase_messaging-15.0.4/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/fl_location-4.4.2/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/flutter_contacts-1.1.9+2/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.24/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/geocoding_android-3.3.1/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_android-2.14.11/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/image_gallery_saver-2.0.3/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+18/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/just_audio-0.9.42/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/open_filex-4.6.0/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/package_info_plus-8.1.2/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/path_provider_android-2.2.15/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/permission_handler_android-12.0.13/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/qr_code_scanner-1.0.1/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.0/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/sqflite_android-2.4.0/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_pdfviewer-26.2.14/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/url_launcher_android-6.3.14/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/video_player_android-2.7.16/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/wakelock_plus-1.2.10/android" />
            <option value="$PROJECT_DIR$/../../../flutter_pub/Cache/hosted/pub.dev/webview_flutter_android-4.2.0/android" />
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/app" />
          </set>
        </option>
        <option name="resolveExternalAnnotations" value="false" />
      </GradleProjectSettings>
    </option>
  </component>
</project>