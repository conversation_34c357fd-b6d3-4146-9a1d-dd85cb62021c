-- Merging decision tree log ---
application
INJECTED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:15:3-53:17
MERGED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:15:5-48:19
MERGED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:15:5-48:19
MERGED from [:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-15:19
MERGED from [:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-15:19
MERGED from [:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-17:19
MERGED from [:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-17:19
MERGED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:5-48:19
MERGED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:5-48:19
MERGED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-34:19
MERGED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-34:19
MERGED from [:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-15:19
MERGED from [:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-15:19
MERGED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:17:5-28:19
MERGED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:17:5-28:19
MERGED from [:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-14:19
MERGED from [:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-14:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] D:\android studio\android-gradle\caches\transforms-3\bfdfee815eba7eb4b2884d94fa874804\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] D:\android studio\android-gradle\caches\transforms-3\bfdfee815eba7eb4b2884d94fa874804\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] D:\android studio\android-gradle\caches\transforms-3\10098dcd1a5b8af4dcea16ead962d5f8\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] D:\android studio\android-gradle\caches\transforms-3\10098dcd1a5b8af4dcea16ead962d5f8\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] D:\android studio\android-gradle\caches\transforms-3\46f7af62ea67e4a2387e6d4857fbe996\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] D:\android studio\android-gradle\caches\transforms-3\46f7af62ea67e4a2387e6d4857fbe996\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] D:\android studio\android-gradle\caches\transforms-3\aaba7304373fbcae3e54bae331a34728\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] D:\android studio\android-gradle\caches\transforms-3\aaba7304373fbcae3e54bae331a34728\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] D:\android studio\android-gradle\caches\transforms-3\073739101f0e25bcc101d5a10b488e78\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] D:\android studio\android-gradle\caches\transforms-3\073739101f0e25bcc101d5a10b488e78\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] D:\android studio\android-gradle\caches\transforms-3\d552f6a7ccd94a500b0ef15a133e6620\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] D:\android studio\android-gradle\caches\transforms-3\d552f6a7ccd94a500b0ef15a133e6620\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\android studio\android-gradle\caches\transforms-3\80c4d18896cdbc773b2f1f60789b5e42\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\android studio\android-gradle\caches\transforms-3\80c4d18896cdbc773b2f1f60789b5e42\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\android studio\android-gradle\caches\transforms-3\b8b48f8a0b6f56e011530f8218d0434e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\android studio\android-gradle\caches\transforms-3\b8b48f8a0b6f56e011530f8218d0434e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] D:\android studio\android-gradle\caches\transforms-3\f6637771952336731f0e0751842691c1\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] D:\android studio\android-gradle\caches\transforms-3\f6637771952336731f0e0751842691c1\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml
manifest
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:1:1-89:12
INJECTED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:1:1-89:12
INJECTED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:1:1-89:12
INJECTED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:1:1-89:12
MERGED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:1:1-89:12
MERGED from [:just_audio] D:\geneat\beta-moible-flutter\build\just_audio\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:audio_session] D:\geneat\beta-moible-flutter\build\audio_session\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:syncfusion_flutter_pdfviewer] D:\geneat\beta-moible-flutter\build\syncfusion_flutter_pdfviewer\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:device_info_plus] D:\geneat\beta-moible-flutter\build\device_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-50:12
MERGED from [:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-17:12
MERGED from [:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-19:12
MERGED from [:flutter_contacts] D:\geneat\beta-moible-flutter\build\flutter_contacts\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-50:12
MERGED from [:flutter_keyboard_visibility] D:\geneat\beta-moible-flutter\build\flutter_keyboard_visibility\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:google_maps_flutter_android] D:\geneat\beta-moible-flutter\build\google_maps_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-36:12
MERGED from [:flutter_plugin_android_lifecycle] D:\geneat\beta-moible-flutter\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:geocoding_android] D:\geneat\beta-moible-flutter\build\geocoding_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-17:12
MERGED from [:image_gallery_saver] D:\geneat\beta-moible-flutter\build\image_gallery_saver\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:integration_test] D:\geneat\beta-moible-flutter\build\integration_test\intermediates\merged_manifest\debug\AndroidManifest.xml:7:1-14:12
MERGED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-30:12
MERGED from [:wakelock_plus] D:\geneat\beta-moible-flutter\build\wakelock_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:package_info_plus] D:\geneat\beta-moible-flutter\build\package_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:path_provider_android] D:\geneat\beta-moible-flutter\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:permission_handler_android] D:\geneat\beta-moible-flutter\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:qr_code_scanner] D:\geneat\beta-moible-flutter\build\qr_code_scanner\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-11:12
MERGED from [:shared_preferences_android] D:\geneat\beta-moible-flutter\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:sqflite_android] D:\geneat\beta-moible-flutter\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-16:12
MERGED from [:video_player_android] D:\geneat\beta-moible-flutter\build\video_player_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:webview_flutter_android] D:\geneat\beta-moible-flutter\build\webview_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.preference:preference:1.2.1] D:\android studio\android-gradle\caches\transforms-3\5d18fffccc249c12ecbab6b8a87e0427\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\android studio\android-gradle\caches\transforms-3\d0e385fe82401a0191f1cf9d187cd9f0\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\android studio\android-gradle\caches\transforms-3\ea3b19e143447a44a4f630074d7ef7d3\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] D:\android studio\android-gradle\caches\transforms-3\bfdfee815eba7eb4b2884d94fa874804\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.media:media:1.7.0] D:\android studio\android-gradle\caches\transforms-3\ef3b1689b5598393cd3c27b001894219\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] D:\android studio\android-gradle\caches\transforms-3\b3a78d3d4f57d7607051978acffd5ad0\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] D:\android studio\android-gradle\caches\transforms-3\e7b23cb7045a4238e7d3dac5d99ded32\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] D:\android studio\android-gradle\caches\transforms-3\e3aaa976bf48d31afbd7495fbc793e85\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\android studio\android-gradle\caches\transforms-3\b088aec0414d8905b0878d7c76776e03\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] D:\android studio\android-gradle\caches\transforms-3\10098dcd1a5b8af4dcea16ead962d5f8\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] D:\android studio\android-gradle\caches\transforms-3\380545117e526ead43aea605fc3841be\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] D:\android studio\android-gradle\caches\transforms-3\ae745bfc25579013d36c19c5f9a74682\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] D:\android studio\android-gradle\caches\transforms-3\afb727d7255b74bcd93b4e4cfd6abce8\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.2] D:\android studio\android-gradle\caches\transforms-3\ae4c8d377bd5af017000a82e04b9cc6a\transformed\jetified-activity-ktx-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] D:\android studio\android-gradle\caches\transforms-3\46f7af62ea67e4a2387e6d4857fbe996\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\android studio\android-gradle\caches\transforms-3\7b2f4c776a2eed893c29097f950de2a3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] D:\android studio\android-gradle\caches\transforms-3\cf412c43d8630c970b116e6a754e870d\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] D:\android studio\android-gradle\caches\transforms-3\8225ffeb08d708e7efb1b852e11a728d\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] D:\android studio\android-gradle\caches\transforms-3\43922f3e57c2c912eec57434cbb6744d\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] D:\android studio\android-gradle\caches\transforms-3\72213405becc4e3fee4f515f45917a7a\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] D:\android studio\android-gradle\caches\transforms-3\df4c21b81a26f0ad57bb1ac65a9353ce\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] D:\android studio\android-gradle\caches\transforms-3\306c33704296a4fe5e2dcae2aa0510ce\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] D:\android studio\android-gradle\caches\transforms-3\5a935e7c71e47ade6b8134b2e4a99145\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] D:\android studio\android-gradle\caches\transforms-3\0fd8cafb08e54981095cfb944a5eaa6b\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] D:\android studio\android-gradle\caches\transforms-3\0e502190d728b4ada7e3274fa39bc377\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] D:\android studio\android-gradle\caches\transforms-3\89f7ee5dec93d6fdbe46619f84004c7c\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\android studio\android-gradle\caches\transforms-3\f924149d08275535fbe2237a3517bce8\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\android studio\android-gradle\caches\transforms-3\48c9a35c82f156be1684c8ab76ef6b03\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window-java:1.2.0] D:\android studio\android-gradle\caches\transforms-3\bd4dee573af8be3f16540957b9ce3ccb\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\android studio\android-gradle\caches\transforms-3\26a60ada1f441fb9c504beff02890e3a\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] D:\android studio\android-gradle\caches\transforms-3\84b849765ae3a0585085c897356abe50\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] D:\android studio\android-gradle\caches\transforms-3\512b2a25e16a1406e73ee3b5e6cd16c3\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] D:\android studio\android-gradle\caches\transforms-3\aaba7304373fbcae3e54bae331a34728\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] D:\android studio\android-gradle\caches\transforms-3\073739101f0e25bcc101d5a10b488e78\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] D:\android studio\android-gradle\caches\transforms-3\d552f6a7ccd94a500b0ef15a133e6620\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] D:\android studio\android-gradle\caches\transforms-3\3b03807329c558a737d9cb34e921c2d5\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.2] D:\android studio\android-gradle\caches\transforms-3\0e2fd2d0d3b3f462505a0fe60954a8a8\transformed\jetified-activity-1.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.7] D:\android studio\android-gradle\caches\transforms-3\3fa529772ea57a731b1670835fe88b86\transformed\jetified-exoplayer-dash-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.7] D:\android studio\android-gradle\caches\transforms-3\ba0ad4e788eea70cc6a2b2bb6401c2ec\transformed\jetified-exoplayer-hls-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.7] D:\android studio\android-gradle\caches\transforms-3\49b73216d607432e0ea1d73713265142\transformed\jetified-exoplayer-smoothstreaming-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] D:\android studio\android-gradle\caches\transforms-3\10bd4d9e9b2285a923396ec591922feb\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.media3:media3-extractor:1.4.1] D:\android studio\android-gradle\caches\transforms-3\b97e85d15116e35f31a48814951c9ffb\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] D:\android studio\android-gradle\caches\transforms-3\8e8cfdfca2fed9a36303de6558111f75\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] D:\android studio\android-gradle\caches\transforms-3\d1c6ca21e38aebdea3a21b474f1fea49\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] D:\android studio\android-gradle\caches\transforms-3\952ea5692a75d1e571f0e9b907c4d51b\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] D:\android studio\android-gradle\caches\transforms-3\a0255ee869e0fc9c81ec4ee41d6fb69e\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] D:\android studio\android-gradle\caches\transforms-3\498ea636dd52f3dfacca05201fed9575\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] D:\android studio\android-gradle\caches\transforms-3\ea11c70848bf190b4501a39623b3146f\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] D:\android studio\android-gradle\caches\transforms-3\064578f244b8d16663633b26ab8f77b2\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] D:\android studio\android-gradle\caches\transforms-3\c7c8052904e2aea6e28d130ad764f6ec\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] D:\android studio\android-gradle\caches\transforms-3\480f42f117dc1a9e116f073852421ba1\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] D:\android studio\android-gradle\caches\transforms-3\66dd79550d656daefbd5340e0b36eaa6\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.12.1] D:\android studio\android-gradle\caches\transforms-3\d9352224f62c4de4a648980e9fa8307c\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] D:\android studio\android-gradle\caches\transforms-3\47fd36105431f1b3a7fc8d4577d1da05\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] D:\android studio\android-gradle\caches\transforms-3\a6627928ae6ca4effd2ae52bc4fb3c05\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\android studio\android-gradle\caches\transforms-3\224f191d7f2b3dea0a093e66ce599108\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\android studio\android-gradle\caches\transforms-3\7d5cb9b46c47f5d51782956d05dcbf0e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\android studio\android-gradle\caches\transforms-3\ac0b4107a65953cf574dc61bb0f487ef\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] D:\android studio\android-gradle\caches\transforms-3\617dcfbe9c747e0a623f2b2928118718\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] D:\android studio\android-gradle\caches\transforms-3\4b32c78aedcec6ef964493449704f182\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] D:\android studio\android-gradle\caches\transforms-3\96153ba6471db1591a8ad507cb8743e6\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] D:\android studio\android-gradle\caches\transforms-3\df25f792bd7929e68110e56202d48c6b\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] D:\android studio\android-gradle\caches\transforms-3\780980c5fffe5f1d8911df0d661e93d9\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] D:\android studio\android-gradle\caches\transforms-3\10c0e6eb7795a6b2572890521ef957a0\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:rules:1.2.0] D:\android studio\android-gradle\caches\transforms-3\6e6f0188dedde90a58c1cda83b734d1b\transformed\rules-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test.espresso:espresso-core:3.2.0] D:\android studio\android-gradle\caches\transforms-3\fcb4e06a1132bf00622ec38d25a54a4c\transformed\espresso-core-3.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:runner:1.2.0] D:\android studio\android-gradle\caches\transforms-3\b679faddc9b2da94f07b18a7efd73f79\transformed\runner-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\android studio\android-gradle\caches\transforms-3\80c4d18896cdbc773b2f1f60789b5e42\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] D:\android studio\android-gradle\caches\transforms-3\efe0694dace90ef39afbb4014ccbf9eb\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\android studio\android-gradle\caches\transforms-3\0f413336d6acc6993d308d9233d75cc6\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\android studio\android-gradle\caches\transforms-3\a30800fadeebb686ceafc7305c462452\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\android studio\android-gradle\caches\transforms-3\b8b48f8a0b6f56e011530f8218d0434e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.test:monitor:1.2.0] D:\android studio\android-gradle\caches\transforms-3\ea0f229d0f52c089924a5edfe5fb21b8\transformed\monitor-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] D:\android studio\android-gradle\caches\transforms-3\b9a50c1c97a749090b266913d039be95\transformed\jetified-exoplayer-datasource-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] D:\android studio\android-gradle\caches\transforms-3\c4ac009bd7a8695da1fa641602ac563c\transformed\jetified-exoplayer-extractor-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] D:\android studio\android-gradle\caches\transforms-3\2f2bdd8528169b4153e607e035db1d09\transformed\jetified-exoplayer-decoder-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] D:\android studio\android-gradle\caches\transforms-3\6eeade3cc16ef6cda2361fe1af8448b1\transformed\jetified-exoplayer-database-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] D:\android studio\android-gradle\caches\transforms-3\ce681d9e9c6f7483243720339b95438f\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\android studio\android-gradle\caches\transforms-3\d5bd0ecfcad5ba0480f408282b227112\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] D:\android studio\android-gradle\caches\transforms-3\2e399c83aa542c2ed56f1478f74e07c2\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] D:\android studio\android-gradle\caches\transforms-3\816de67a043c85c65edcb4b869c41588\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] D:\android studio\android-gradle\caches\transforms-3\23758b5220b8e6bb75f72b2697cc0025\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.window.extensions.core:core:1.0.0] D:\android studio\android-gradle\caches\transforms-3\18808df49342fe99a75fef099d0adc82\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\android studio\android-gradle\caches\transforms-3\b2f1b84b5ce098eab91a2bef3fceeba5\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] D:\android studio\android-gradle\caches\transforms-3\8986f688eefa665e17093134d9994b83\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\android studio\android-gradle\caches\transforms-3\e2f7dc12e3088048c76469886c0dfee3\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.1] D:\android studio\android-gradle\caches\transforms-3\a5178fb8c5e6674507cb656179d6f3cf\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] D:\android studio\android-gradle\caches\transforms-3\f6637771952336731f0e0751842691c1\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:17:1-56:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] D:\android studio\android-gradle\caches\transforms-3\70293f4cd51317d3237eb0fd7493835d\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] D:\android studio\android-gradle\caches\transforms-3\8a4ff5fe9689d9d16e81293464e31865\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:17:1-24:12
INJECTED from D:\geneat\beta-moible-flutter\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\geneat\beta-moible-flutter\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\geneat\beta-moible-flutter\android\app\src\debug\AndroidManifest.xml:1:1-7:12
	package
		INJECTED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:1:1-89:12
		INJECTED from D:\geneat\beta-moible-flutter\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:1:1-89:12
		INJECTED from D:\geneat\beta-moible-flutter\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:1:1-89:12
		INJECTED from D:\geneat\beta-moible-flutter\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:3:3-65
MERGED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:3:3-65
MERGED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:3:3-65
MERGED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-67
MERGED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] D:\android studio\android-gradle\caches\transforms-3\380545117e526ead43aea605fc3841be\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] D:\android studio\android-gradle\caches\transforms-3\380545117e526ead43aea605fc3841be\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:3:20-62
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:4:3-77
	android:name
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:4:20-74
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:5:3-79
MERGED from [:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-81
MERGED from [:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-81
	android:name
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:5:20-76
uses-permission#android.permission.ACCESS_LOCATION_EXTRA_COMMANDS
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:6:3-87
	android:name
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:6:20-84
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:7:3-83
	android:name
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:7:20-80
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:8:3-78
MERGED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-12:38
MERGED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-12:38
	android:maxSdkVersion
		ADDED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-35
	android:name
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:8:20-75
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:9:3-79
	android:name
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:9:20-76
uses-permission#android.permission.READ_PRIVILEGED_PHONE_STATE
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:10:3-84
	android:name
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:10:20-81
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:11:3-75
MERGED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-77
MERGED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:23:5-77
	android:name
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:11:20-72
uses-permission#android.permission.READ_CONTACTS
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:12:3-70
	android:name
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:12:20-67
uses-permission#android.permission.WRITE_CONTACTS
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:13:3-71
	android:name
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:13:20-68
queries
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:59:3-64:13
MERGED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-13:15
MERGED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-13:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:60:5-63:14
action#android.intent.action.PROCESS_TEXT
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:61:7-67
	android:name
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:61:15-64
data
ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:62:7-45
	android:mimeType
		ADDED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:62:13-42
uses-sdk
INJECTED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml
INJECTED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml
MERGED from [:just_audio] D:\geneat\beta-moible-flutter\build\just_audio\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:just_audio] D:\geneat\beta-moible-flutter\build\just_audio\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:audio_session] D:\geneat\beta-moible-flutter\build\audio_session\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:audio_session] D:\geneat\beta-moible-flutter\build\audio_session\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:syncfusion_flutter_pdfviewer] D:\geneat\beta-moible-flutter\build\syncfusion_flutter_pdfviewer\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:syncfusion_flutter_pdfviewer] D:\geneat\beta-moible-flutter\build\syncfusion_flutter_pdfviewer\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:device_info_plus] D:\geneat\beta-moible-flutter\build\device_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:device_info_plus] D:\geneat\beta-moible-flutter\build\device_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_contacts] D:\geneat\beta-moible-flutter\build\flutter_contacts\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_contacts] D:\geneat\beta-moible-flutter\build\flutter_contacts\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_keyboard_visibility] D:\geneat\beta-moible-flutter\build\flutter_keyboard_visibility\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_keyboard_visibility] D:\geneat\beta-moible-flutter\build\flutter_keyboard_visibility\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:google_maps_flutter_android] D:\geneat\beta-moible-flutter\build\google_maps_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:google_maps_flutter_android] D:\geneat\beta-moible-flutter\build\google_maps_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-8:41
MERGED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-8:41
MERGED from [:flutter_plugin_android_lifecycle] D:\geneat\beta-moible-flutter\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_plugin_android_lifecycle] D:\geneat\beta-moible-flutter\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:geocoding_android] D:\geneat\beta-moible-flutter\build\geocoding_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:geocoding_android] D:\geneat\beta-moible-flutter\build\geocoding_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:image_gallery_saver] D:\geneat\beta-moible-flutter\build\image_gallery_saver\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:image_gallery_saver] D:\geneat\beta-moible-flutter\build\image_gallery_saver\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:integration_test] D:\geneat\beta-moible-flutter\build\integration_test\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-12:41
MERGED from [:integration_test] D:\geneat\beta-moible-flutter\build\integration_test\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-12:41
MERGED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-8:41
MERGED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-8:41
MERGED from [:wakelock_plus] D:\geneat\beta-moible-flutter\build\wakelock_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:wakelock_plus] D:\geneat\beta-moible-flutter\build\wakelock_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:package_info_plus] D:\geneat\beta-moible-flutter\build\package_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:package_info_plus] D:\geneat\beta-moible-flutter\build\package_info_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:path_provider_android] D:\geneat\beta-moible-flutter\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:path_provider_android] D:\geneat\beta-moible-flutter\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:permission_handler_android] D:\geneat\beta-moible-flutter\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:permission_handler_android] D:\geneat\beta-moible-flutter\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:qr_code_scanner] D:\geneat\beta-moible-flutter\build\qr_code_scanner\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-9:67
MERGED from [:qr_code_scanner] D:\geneat\beta-moible-flutter\build\qr_code_scanner\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-9:67
MERGED from [:shared_preferences_android] D:\geneat\beta-moible-flutter\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:shared_preferences_android] D:\geneat\beta-moible-flutter\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:sqflite_android] D:\geneat\beta-moible-flutter\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:sqflite_android] D:\geneat\beta-moible-flutter\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:video_player_android] D:\geneat\beta-moible-flutter\build\video_player_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:video_player_android] D:\geneat\beta-moible-flutter\build\video_player_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:webview_flutter_android] D:\geneat\beta-moible-flutter\build\webview_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:webview_flutter_android] D:\geneat\beta-moible-flutter\build\webview_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.preference:preference:1.2.1] D:\android studio\android-gradle\caches\transforms-3\5d18fffccc249c12ecbab6b8a87e0427\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] D:\android studio\android-gradle\caches\transforms-3\5d18fffccc249c12ecbab6b8a87e0427\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\android studio\android-gradle\caches\transforms-3\d0e385fe82401a0191f1cf9d187cd9f0\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\android studio\android-gradle\caches\transforms-3\d0e385fe82401a0191f1cf9d187cd9f0\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\android studio\android-gradle\caches\transforms-3\ea3b19e143447a44a4f630074d7ef7d3\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\android studio\android-gradle\caches\transforms-3\ea3b19e143447a44a4f630074d7ef7d3\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] D:\android studio\android-gradle\caches\transforms-3\bfdfee815eba7eb4b2884d94fa874804\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] D:\android studio\android-gradle\caches\transforms-3\bfdfee815eba7eb4b2884d94fa874804\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] D:\android studio\android-gradle\caches\transforms-3\ef3b1689b5598393cd3c27b001894219\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] D:\android studio\android-gradle\caches\transforms-3\ef3b1689b5598393cd3c27b001894219\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] D:\android studio\android-gradle\caches\transforms-3\b3a78d3d4f57d7607051978acffd5ad0\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] D:\android studio\android-gradle\caches\transforms-3\b3a78d3d4f57d7607051978acffd5ad0\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] D:\android studio\android-gradle\caches\transforms-3\e7b23cb7045a4238e7d3dac5d99ded32\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] D:\android studio\android-gradle\caches\transforms-3\e7b23cb7045a4238e7d3dac5d99ded32\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] D:\android studio\android-gradle\caches\transforms-3\e3aaa976bf48d31afbd7495fbc793e85\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] D:\android studio\android-gradle\caches\transforms-3\e3aaa976bf48d31afbd7495fbc793e85\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\android studio\android-gradle\caches\transforms-3\b088aec0414d8905b0878d7c76776e03\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\android studio\android-gradle\caches\transforms-3\b088aec0414d8905b0878d7c76776e03\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] D:\android studio\android-gradle\caches\transforms-3\10098dcd1a5b8af4dcea16ead962d5f8\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] D:\android studio\android-gradle\caches\transforms-3\10098dcd1a5b8af4dcea16ead962d5f8\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] D:\android studio\android-gradle\caches\transforms-3\380545117e526ead43aea605fc3841be\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] D:\android studio\android-gradle\caches\transforms-3\380545117e526ead43aea605fc3841be\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] D:\android studio\android-gradle\caches\transforms-3\ae745bfc25579013d36c19c5f9a74682\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] D:\android studio\android-gradle\caches\transforms-3\ae745bfc25579013d36c19c5f9a74682\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] D:\android studio\android-gradle\caches\transforms-3\afb727d7255b74bcd93b4e4cfd6abce8\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] D:\android studio\android-gradle\caches\transforms-3\afb727d7255b74bcd93b4e4cfd6abce8\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] D:\android studio\android-gradle\caches\transforms-3\ae4c8d377bd5af017000a82e04b9cc6a\transformed\jetified-activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] D:\android studio\android-gradle\caches\transforms-3\ae4c8d377bd5af017000a82e04b9cc6a\transformed\jetified-activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] D:\android studio\android-gradle\caches\transforms-3\46f7af62ea67e4a2387e6d4857fbe996\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] D:\android studio\android-gradle\caches\transforms-3\46f7af62ea67e4a2387e6d4857fbe996\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\android studio\android-gradle\caches\transforms-3\7b2f4c776a2eed893c29097f950de2a3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\android studio\android-gradle\caches\transforms-3\7b2f4c776a2eed893c29097f950de2a3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\android studio\android-gradle\caches\transforms-3\cf412c43d8630c970b116e6a754e870d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\android studio\android-gradle\caches\transforms-3\cf412c43d8630c970b116e6a754e870d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] D:\android studio\android-gradle\caches\transforms-3\8225ffeb08d708e7efb1b852e11a728d\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] D:\android studio\android-gradle\caches\transforms-3\8225ffeb08d708e7efb1b852e11a728d\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] D:\android studio\android-gradle\caches\transforms-3\43922f3e57c2c912eec57434cbb6744d\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] D:\android studio\android-gradle\caches\transforms-3\43922f3e57c2c912eec57434cbb6744d\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] D:\android studio\android-gradle\caches\transforms-3\72213405becc4e3fee4f515f45917a7a\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] D:\android studio\android-gradle\caches\transforms-3\72213405becc4e3fee4f515f45917a7a\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] D:\android studio\android-gradle\caches\transforms-3\df4c21b81a26f0ad57bb1ac65a9353ce\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] D:\android studio\android-gradle\caches\transforms-3\df4c21b81a26f0ad57bb1ac65a9353ce\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] D:\android studio\android-gradle\caches\transforms-3\306c33704296a4fe5e2dcae2aa0510ce\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] D:\android studio\android-gradle\caches\transforms-3\306c33704296a4fe5e2dcae2aa0510ce\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] D:\android studio\android-gradle\caches\transforms-3\5a935e7c71e47ade6b8134b2e4a99145\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] D:\android studio\android-gradle\caches\transforms-3\5a935e7c71e47ade6b8134b2e4a99145\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] D:\android studio\android-gradle\caches\transforms-3\0fd8cafb08e54981095cfb944a5eaa6b\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] D:\android studio\android-gradle\caches\transforms-3\0fd8cafb08e54981095cfb944a5eaa6b\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] D:\android studio\android-gradle\caches\transforms-3\0e502190d728b4ada7e3274fa39bc377\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] D:\android studio\android-gradle\caches\transforms-3\0e502190d728b4ada7e3274fa39bc377\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] D:\android studio\android-gradle\caches\transforms-3\89f7ee5dec93d6fdbe46619f84004c7c\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] D:\android studio\android-gradle\caches\transforms-3\89f7ee5dec93d6fdbe46619f84004c7c\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\android studio\android-gradle\caches\transforms-3\f924149d08275535fbe2237a3517bce8\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\android studio\android-gradle\caches\transforms-3\f924149d08275535fbe2237a3517bce8\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\android studio\android-gradle\caches\transforms-3\48c9a35c82f156be1684c8ab76ef6b03\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\android studio\android-gradle\caches\transforms-3\48c9a35c82f156be1684c8ab76ef6b03\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] D:\android studio\android-gradle\caches\transforms-3\bd4dee573af8be3f16540957b9ce3ccb\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] D:\android studio\android-gradle\caches\transforms-3\bd4dee573af8be3f16540957b9ce3ccb\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\android studio\android-gradle\caches\transforms-3\26a60ada1f441fb9c504beff02890e3a\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\android studio\android-gradle\caches\transforms-3\26a60ada1f441fb9c504beff02890e3a\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] D:\android studio\android-gradle\caches\transforms-3\84b849765ae3a0585085c897356abe50\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] D:\android studio\android-gradle\caches\transforms-3\84b849765ae3a0585085c897356abe50\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] D:\android studio\android-gradle\caches\transforms-3\512b2a25e16a1406e73ee3b5e6cd16c3\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] D:\android studio\android-gradle\caches\transforms-3\512b2a25e16a1406e73ee3b5e6cd16c3\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] D:\android studio\android-gradle\caches\transforms-3\aaba7304373fbcae3e54bae331a34728\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] D:\android studio\android-gradle\caches\transforms-3\aaba7304373fbcae3e54bae331a34728\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] D:\android studio\android-gradle\caches\transforms-3\073739101f0e25bcc101d5a10b488e78\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] D:\android studio\android-gradle\caches\transforms-3\073739101f0e25bcc101d5a10b488e78\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] D:\android studio\android-gradle\caches\transforms-3\d552f6a7ccd94a500b0ef15a133e6620\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] D:\android studio\android-gradle\caches\transforms-3\d552f6a7ccd94a500b0ef15a133e6620\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] D:\android studio\android-gradle\caches\transforms-3\3b03807329c558a737d9cb34e921c2d5\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] D:\android studio\android-gradle\caches\transforms-3\3b03807329c558a737d9cb34e921c2d5\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.2] D:\android studio\android-gradle\caches\transforms-3\0e2fd2d0d3b3f462505a0fe60954a8a8\transformed\jetified-activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] D:\android studio\android-gradle\caches\transforms-3\0e2fd2d0d3b3f462505a0fe60954a8a8\transformed\jetified-activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.7] D:\android studio\android-gradle\caches\transforms-3\3fa529772ea57a731b1670835fe88b86\transformed\jetified-exoplayer-dash-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.7] D:\android studio\android-gradle\caches\transforms-3\3fa529772ea57a731b1670835fe88b86\transformed\jetified-exoplayer-dash-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.7] D:\android studio\android-gradle\caches\transforms-3\ba0ad4e788eea70cc6a2b2bb6401c2ec\transformed\jetified-exoplayer-hls-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.7] D:\android studio\android-gradle\caches\transforms-3\ba0ad4e788eea70cc6a2b2bb6401c2ec\transformed\jetified-exoplayer-hls-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.7] D:\android studio\android-gradle\caches\transforms-3\49b73216d607432e0ea1d73713265142\transformed\jetified-exoplayer-smoothstreaming-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.7] D:\android studio\android-gradle\caches\transforms-3\49b73216d607432e0ea1d73713265142\transformed\jetified-exoplayer-smoothstreaming-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] D:\android studio\android-gradle\caches\transforms-3\10bd4d9e9b2285a923396ec591922feb\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] D:\android studio\android-gradle\caches\transforms-3\10bd4d9e9b2285a923396ec591922feb\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-extractor:1.4.1] D:\android studio\android-gradle\caches\transforms-3\b97e85d15116e35f31a48814951c9ffb\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] D:\android studio\android-gradle\caches\transforms-3\b97e85d15116e35f31a48814951c9ffb\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] D:\android studio\android-gradle\caches\transforms-3\8e8cfdfca2fed9a36303de6558111f75\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] D:\android studio\android-gradle\caches\transforms-3\8e8cfdfca2fed9a36303de6558111f75\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] D:\android studio\android-gradle\caches\transforms-3\d1c6ca21e38aebdea3a21b474f1fea49\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] D:\android studio\android-gradle\caches\transforms-3\d1c6ca21e38aebdea3a21b474f1fea49\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] D:\android studio\android-gradle\caches\transforms-3\952ea5692a75d1e571f0e9b907c4d51b\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] D:\android studio\android-gradle\caches\transforms-3\952ea5692a75d1e571f0e9b907c4d51b\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] D:\android studio\android-gradle\caches\transforms-3\a0255ee869e0fc9c81ec4ee41d6fb69e\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] D:\android studio\android-gradle\caches\transforms-3\a0255ee869e0fc9c81ec4ee41d6fb69e\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] D:\android studio\android-gradle\caches\transforms-3\498ea636dd52f3dfacca05201fed9575\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] D:\android studio\android-gradle\caches\transforms-3\498ea636dd52f3dfacca05201fed9575\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] D:\android studio\android-gradle\caches\transforms-3\ea11c70848bf190b4501a39623b3146f\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] D:\android studio\android-gradle\caches\transforms-3\ea11c70848bf190b4501a39623b3146f\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] D:\android studio\android-gradle\caches\transforms-3\064578f244b8d16663633b26ab8f77b2\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] D:\android studio\android-gradle\caches\transforms-3\064578f244b8d16663633b26ab8f77b2\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] D:\android studio\android-gradle\caches\transforms-3\c7c8052904e2aea6e28d130ad764f6ec\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] D:\android studio\android-gradle\caches\transforms-3\c7c8052904e2aea6e28d130ad764f6ec\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] D:\android studio\android-gradle\caches\transforms-3\480f42f117dc1a9e116f073852421ba1\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] D:\android studio\android-gradle\caches\transforms-3\480f42f117dc1a9e116f073852421ba1\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] D:\android studio\android-gradle\caches\transforms-3\66dd79550d656daefbd5340e0b36eaa6\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] D:\android studio\android-gradle\caches\transforms-3\66dd79550d656daefbd5340e0b36eaa6\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.12.1] D:\android studio\android-gradle\caches\transforms-3\d9352224f62c4de4a648980e9fa8307c\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] D:\android studio\android-gradle\caches\transforms-3\d9352224f62c4de4a648980e9fa8307c\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] D:\android studio\android-gradle\caches\transforms-3\47fd36105431f1b3a7fc8d4577d1da05\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] D:\android studio\android-gradle\caches\transforms-3\47fd36105431f1b3a7fc8d4577d1da05\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] D:\android studio\android-gradle\caches\transforms-3\a6627928ae6ca4effd2ae52bc4fb3c05\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] D:\android studio\android-gradle\caches\transforms-3\a6627928ae6ca4effd2ae52bc4fb3c05\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\android studio\android-gradle\caches\transforms-3\224f191d7f2b3dea0a093e66ce599108\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\android studio\android-gradle\caches\transforms-3\224f191d7f2b3dea0a093e66ce599108\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\android studio\android-gradle\caches\transforms-3\7d5cb9b46c47f5d51782956d05dcbf0e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\android studio\android-gradle\caches\transforms-3\7d5cb9b46c47f5d51782956d05dcbf0e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\android studio\android-gradle\caches\transforms-3\ac0b4107a65953cf574dc61bb0f487ef\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\android studio\android-gradle\caches\transforms-3\ac0b4107a65953cf574dc61bb0f487ef\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] D:\android studio\android-gradle\caches\transforms-3\617dcfbe9c747e0a623f2b2928118718\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] D:\android studio\android-gradle\caches\transforms-3\617dcfbe9c747e0a623f2b2928118718\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] D:\android studio\android-gradle\caches\transforms-3\4b32c78aedcec6ef964493449704f182\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] D:\android studio\android-gradle\caches\transforms-3\4b32c78aedcec6ef964493449704f182\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] D:\android studio\android-gradle\caches\transforms-3\96153ba6471db1591a8ad507cb8743e6\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] D:\android studio\android-gradle\caches\transforms-3\96153ba6471db1591a8ad507cb8743e6\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] D:\android studio\android-gradle\caches\transforms-3\df25f792bd7929e68110e56202d48c6b\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] D:\android studio\android-gradle\caches\transforms-3\df25f792bd7929e68110e56202d48c6b\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] D:\android studio\android-gradle\caches\transforms-3\780980c5fffe5f1d8911df0d661e93d9\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] D:\android studio\android-gradle\caches\transforms-3\780980c5fffe5f1d8911df0d661e93d9\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] D:\android studio\android-gradle\caches\transforms-3\10c0e6eb7795a6b2572890521ef957a0\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] D:\android studio\android-gradle\caches\transforms-3\10c0e6eb7795a6b2572890521ef957a0\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.2.0] D:\android studio\android-gradle\caches\transforms-3\6e6f0188dedde90a58c1cda83b734d1b\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.2.0] D:\android studio\android-gradle\caches\transforms-3\6e6f0188dedde90a58c1cda83b734d1b\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] D:\android studio\android-gradle\caches\transforms-3\fcb4e06a1132bf00622ec38d25a54a4c\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] D:\android studio\android-gradle\caches\transforms-3\fcb4e06a1132bf00622ec38d25a54a4c\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] D:\android studio\android-gradle\caches\transforms-3\b679faddc9b2da94f07b18a7efd73f79\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] D:\android studio\android-gradle\caches\transforms-3\b679faddc9b2da94f07b18a7efd73f79\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\android studio\android-gradle\caches\transforms-3\80c4d18896cdbc773b2f1f60789b5e42\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\android studio\android-gradle\caches\transforms-3\80c4d18896cdbc773b2f1f60789b5e42\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] D:\android studio\android-gradle\caches\transforms-3\efe0694dace90ef39afbb4014ccbf9eb\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] D:\android studio\android-gradle\caches\transforms-3\efe0694dace90ef39afbb4014ccbf9eb\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\android studio\android-gradle\caches\transforms-3\0f413336d6acc6993d308d9233d75cc6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\android studio\android-gradle\caches\transforms-3\0f413336d6acc6993d308d9233d75cc6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\android studio\android-gradle\caches\transforms-3\a30800fadeebb686ceafc7305c462452\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\android studio\android-gradle\caches\transforms-3\a30800fadeebb686ceafc7305c462452\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\android studio\android-gradle\caches\transforms-3\b8b48f8a0b6f56e011530f8218d0434e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\android studio\android-gradle\caches\transforms-3\b8b48f8a0b6f56e011530f8218d0434e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.2.0] D:\android studio\android-gradle\caches\transforms-3\ea0f229d0f52c089924a5edfe5fb21b8\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.2.0] D:\android studio\android-gradle\caches\transforms-3\ea0f229d0f52c089924a5edfe5fb21b8\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] D:\android studio\android-gradle\caches\transforms-3\b9a50c1c97a749090b266913d039be95\transformed\jetified-exoplayer-datasource-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] D:\android studio\android-gradle\caches\transforms-3\b9a50c1c97a749090b266913d039be95\transformed\jetified-exoplayer-datasource-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] D:\android studio\android-gradle\caches\transforms-3\c4ac009bd7a8695da1fa641602ac563c\transformed\jetified-exoplayer-extractor-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] D:\android studio\android-gradle\caches\transforms-3\c4ac009bd7a8695da1fa641602ac563c\transformed\jetified-exoplayer-extractor-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] D:\android studio\android-gradle\caches\transforms-3\2f2bdd8528169b4153e607e035db1d09\transformed\jetified-exoplayer-decoder-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] D:\android studio\android-gradle\caches\transforms-3\2f2bdd8528169b4153e607e035db1d09\transformed\jetified-exoplayer-decoder-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] D:\android studio\android-gradle\caches\transforms-3\6eeade3cc16ef6cda2361fe1af8448b1\transformed\jetified-exoplayer-database-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] D:\android studio\android-gradle\caches\transforms-3\6eeade3cc16ef6cda2361fe1af8448b1\transformed\jetified-exoplayer-database-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] D:\android studio\android-gradle\caches\transforms-3\ce681d9e9c6f7483243720339b95438f\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] D:\android studio\android-gradle\caches\transforms-3\ce681d9e9c6f7483243720339b95438f\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\android studio\android-gradle\caches\transforms-3\d5bd0ecfcad5ba0480f408282b227112\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\android studio\android-gradle\caches\transforms-3\d5bd0ecfcad5ba0480f408282b227112\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] D:\android studio\android-gradle\caches\transforms-3\2e399c83aa542c2ed56f1478f74e07c2\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] D:\android studio\android-gradle\caches\transforms-3\2e399c83aa542c2ed56f1478f74e07c2\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] D:\android studio\android-gradle\caches\transforms-3\816de67a043c85c65edcb4b869c41588\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] D:\android studio\android-gradle\caches\transforms-3\816de67a043c85c65edcb4b869c41588\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] D:\android studio\android-gradle\caches\transforms-3\23758b5220b8e6bb75f72b2697cc0025\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] D:\android studio\android-gradle\caches\transforms-3\23758b5220b8e6bb75f72b2697cc0025\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] D:\android studio\android-gradle\caches\transforms-3\18808df49342fe99a75fef099d0adc82\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] D:\android studio\android-gradle\caches\transforms-3\18808df49342fe99a75fef099d0adc82\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\android studio\android-gradle\caches\transforms-3\b2f1b84b5ce098eab91a2bef3fceeba5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\android studio\android-gradle\caches\transforms-3\b2f1b84b5ce098eab91a2bef3fceeba5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\android studio\android-gradle\caches\transforms-3\8986f688eefa665e17093134d9994b83\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\android studio\android-gradle\caches\transforms-3\8986f688eefa665e17093134d9994b83\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\android studio\android-gradle\caches\transforms-3\e2f7dc12e3088048c76469886c0dfee3\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\android studio\android-gradle\caches\transforms-3\e2f7dc12e3088048c76469886c0dfee3\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.1] D:\android studio\android-gradle\caches\transforms-3\a5178fb8c5e6674507cb656179d6f3cf\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] D:\android studio\android-gradle\caches\transforms-3\a5178fb8c5e6674507cb656179d6f3cf\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] D:\android studio\android-gradle\caches\transforms-3\f6637771952336731f0e0751842691c1\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] D:\android studio\android-gradle\caches\transforms-3\f6637771952336731f0e0751842691c1\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] D:\android studio\android-gradle\caches\transforms-3\70293f4cd51317d3237eb0fd7493835d\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] D:\android studio\android-gradle\caches\transforms-3\70293f4cd51317d3237eb0fd7493835d\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] D:\android studio\android-gradle\caches\transforms-3\8a4ff5fe9689d9d16e81293464e31865\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] D:\android studio\android-gradle\caches\transforms-3\8a4ff5fe9689d9d16e81293464e31865\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
INJECTED from D:\geneat\beta-moible-flutter\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\geneat\beta-moible-flutter\android\app\src\debug\AndroidManifest.xml
	tools:overrideLibrary
		ADDED from [:qr_code_scanner] D:\geneat\beta-moible-flutter\build\qr_code_scanner\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-64
	android:targetSdkVersion
		INJECTED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\geneat\beta-moible-flutter\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\geneat\beta-moible-flutter\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] D:\android studio\android-gradle\caches\transforms-3\380545117e526ead43aea605fc3841be\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] D:\android studio\android-gradle\caches\transforms-3\380545117e526ead43aea605fc3841be\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:10:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] D:\android studio\android-gradle\caches\transforms-3\380545117e526ead43aea605fc3841be\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] D:\android studio\android-gradle\caches\transforms-3\380545117e526ead43aea605fc3841be\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] D:\android studio\android-gradle\caches\transforms-3\10bd4d9e9b2285a923396ec591922feb\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] D:\android studio\android-gradle\caches\transforms-3\10bd4d9e9b2285a923396ec591922feb\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-common:1.4.1] D:\android studio\android-gradle\caches\transforms-3\498ea636dd52f3dfacca05201fed9575\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] D:\android studio\android-gradle\caches\transforms-3\498ea636dd52f3dfacca05201fed9575\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] D:\android studio\android-gradle\caches\transforms-3\66dd79550d656daefbd5340e0b36eaa6\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] D:\android studio\android-gradle\caches\transforms-3\66dd79550d656daefbd5340e0b36eaa6\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] D:\android studio\android-gradle\caches\transforms-3\ce681d9e9c6f7483243720339b95438f\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] D:\android studio\android-gradle\caches\transforms-3\ce681d9e9c6f7483243720339b95438f\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-76
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-19:72
	android:exported
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-37
	android:permission
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-69
	android:name
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:20:9-26:19
	android:exported
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
	android:name
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-25:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:17-78
	android:name
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-35:20
	android:exported
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-36
	android:permission
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:13-73
	android:name
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:32:13-34:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:17-81
	android:name
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:25-78
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:9-41:19
MERGED from [:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-14:19
MERGED from [:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-14:19
MERGED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:13-40:85
	android:value
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:40:17-82
	android:name
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:39:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:9-47:38
	android:authorities
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-88
	android:exported
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:46:13-37
	android:initOrder
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:47:13-35
	android:name
		ADDED from [:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:12:17-124
service#com.pravera.fl_location.service.LocationServicesStatusIntentService
ADDED from [:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:72
	android:enabled
		ADDED from [:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-35
	android:exported
		ADDED from [:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-37
	android:permission
		ADDED from [:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-69
	android:name
		ADDED from [:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-95
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-90
	android:name
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:21-87
activity#com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity
ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-20:47
	android:exported
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-37
	android:configChanges
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-137
	android:theme
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-44
	android:name
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-112
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity
ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-24:55
	android:exported
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-120
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity
ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:9-28:55
	android:exported
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:26:13-114
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance
ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:9-33:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:32:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:31:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:33:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-134
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance
ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:34:9-38:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:37:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:38:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:35:13-128
receiver#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver
ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:40:9-43:40
	android:enabled
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-35
	android:exported
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-37
	android:name
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:41:13-119
meta-data#io.flutter.embedded_views_preview
ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:45:9-47:36
	android:value
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:47:13-33
	android:name
		ADDED from [:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:46:13-61
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-19:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-47
	android:authorities
		ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-74
	android:exported
		ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-37
	android:name
		ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-18:75
	android:resource
		ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:17-72
	android:name
		ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-33:19
	android:enabled
		ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-36
	android:exported
		ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-37
	tools:ignore
		ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:13-40
	android:name
		ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:26:13-28:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:27:17-94
	android:name
		ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:27:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:36
	android:value
		ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:32:17-33
	android:name
		ADDED from [:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-63
service#com.baseflow.geolocator.GeolocatorLocationService
ADDED from [:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-14:56
	android:enabled
		ADDED from [:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-35
	android:exported
		ADDED from [:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-37
	android:foregroundServiceType
		ADDED from [:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-53
	android:name
		ADDED from [:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-76
	android:name
		ADDED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:13:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:14:5-75
	android:name
		ADDED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:14:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:15:5-75
	android:name
		ADDED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:15:22-72
provider#com.crazecoder.openfile.FileProvider
ADDED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-27:20
	android:grantUriPermissions
		ADDED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-47
	android:authorities
		ADDED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-88
	android:exported
		ADDED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-37
	tools:replace
		ADDED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-48
	android:name
		ADDED from [:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-64
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-13:74
	android:exported
		ADDED from [:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
	android:theme
		ADDED from [:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-71
	android:name
		ADDED from [:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] D:\android studio\android-gradle\caches\transforms-3\380545117e526ead43aea605fc3841be\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] D:\android studio\android-gradle\caches\transforms-3\380545117e526ead43aea605fc3841be\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:61:17-119
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\android studio\android-gradle\caches\transforms-3\80c4d18896cdbc773b2f1f60789b5e42\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\android studio\android-gradle\caches\transforms-3\80c4d18896cdbc773b2f1f60789b5e42\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
meta-data#com.google.android.gms.version
MERGED from [com.google.android.gms:play-services-basement:18.3.0] D:\android studio\android-gradle\caches\transforms-3\d552f6a7ccd94a500b0ef15a133e6620\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.android.gms:play-services-basement:18.3.0] D:\android studio\android-gradle\caches\transforms-3\d552f6a7ccd94a500b0ef15a133e6620\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] D:\android studio\android-gradle\caches\transforms-3\f6637771952336731f0e0751842691c1\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] D:\android studio\android-gradle\caches\transforms-3\f6637771952336731f0e0751842691c1\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.flutter.flutter.petro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.flutter.flutter.petro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#android.permission.CAMERA
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:22:22-62
uses-feature#android.hardware.camera
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
uses-feature#android.hardware.camera.front
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
uses-feature#android.hardware.camera.autofocus
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
uses-feature#android.hardware.camera.flash
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
uses-feature#android.hardware.screen.landscape
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
uses-feature#android.hardware.wifi
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
activity#com.journeyapps.barcodescanner.CaptureActivity
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
	android:screenOrientation
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
	android:clearTaskOnLaunch
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
	android:stateNotNeeded
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
	android:windowSoftInputMode
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
	android:theme
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
