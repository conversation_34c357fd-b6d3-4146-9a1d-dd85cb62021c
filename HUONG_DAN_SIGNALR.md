# Hướng dẫn khắc phục lỗi SignalR trong ứng dụng Flutter

## Vấn đề

Khi kết nối đến máy chủ SignalR, ứng dụng gặp lỗi sau:
```
Error joining native group: PlatformException(INVOKE_ERROR, There was a bad response from the server., null, null)
```

Lỗi này xảy ra khi:
1. Kết nối ban đầu đến máy chủ SignalR thành công (có connectionId)
2. Nhưng khi cố gắng tham gia vào một nhóm (join group), máy chủ trả về lỗi

## Nguyên nhân

Nguyên nhân chính của lỗi này là do tên phương thức không khớp với những gì máy chủ mong đợi. SignalR rất nhạy cảm với chữ hoa/thường và tên phương thức phải chính xác.

Trong trường hợ<PERSON> nà<PERSON>, chúng ta đang sử dụng:
- `joinGroup` hoặc `JoinGroup` để tham gia vào một nhóm
- `sendMessage` hoặc `SendMessage` để gửi tin nhắn

Nhưng máy chủ có thể mong đợi các tên phương thức khác như:
- `Join` thay vì `joinGroup`
- `Send` thay vì `sendMessage`

## Giải pháp

Chúng tôi đã thực hiện các thay đổi sau để khắc phục vấn đề:

### 1. Cập nhật tên phương thức trong mã Swift

Trong file `SignalRClient.swift`, chúng tôi đã thay đổi cách xử lý tên phương thức:

```swift
// Trước
switch method {
case "JoinGroup":
    serverMethod = "joinGroup"
case "LeaveGroup":
    serverMethod = "leaveGroup"
default:
    serverMethod = method
}

// Sau
switch method {
case "JoinGroup", "joinGroup":
    serverMethod = "Join"
case "LeaveGroup", "leaveGroup":
    serverMethod = "Leave"
case "sendMessage", "SendMessage":
    serverMethod = "Send"
default:
    serverMethod = method
}
```

### 2. Cập nhật tên phương thức trong mã Dart

Trong file `native_signalr_service.dart`, chúng tôi đã cập nhật tên phương thức:

```dart
// Trước
final String _sendMessageEvent = 'sendMessage';
final String _joinGroupEvent = 'joinGroup';
final String _leaveGroupEvent = 'leaveGroup';

// Sau
final String _sendMessageEvent = 'Send';
final String _joinGroupEvent = 'Join';
final String _leaveGroupEvent = 'Leave';
```

### 3. Cải thiện xử lý tin nhắn

Chúng tôi đã cải thiện cách xử lý tin nhắn để hỗ trợ nhiều định dạng tin nhắn khác nhau:

- Hỗ trợ cả `broadcastMessage` và `UpdateSeat` làm tên phương thức
- Hỗ trợ nhiều định dạng tham số khác nhau
- Thêm log chi tiết để dễ dàng chẩn đoán vấn đề

### 4. Thêm độ trễ nhỏ sau khi gọi phương thức

Chúng tôi đã thêm một khoảng thời gian chờ nhỏ (0.5 giây) sau khi gọi phương thức để đảm bảo máy chủ có đủ thời gian xử lý trước khi chúng ta tiếp tục.

## Cách kiểm tra

1. Chạy ứng dụng và mở màn hình chọn ghế
2. Kiểm tra log để xem liệu kết nối SignalR có thành công không
3. Kiểm tra xem việc tham gia vào nhóm có thành công không
4. Thử chọn một ghế và xem liệu tin nhắn có được gửi thành công không

## Các vấn đề thường gặp và cách khắc phục

### 1. Lỗi "There was a bad response from the server"

**Nguyên nhân:**
- Tên phương thức không đúng
- Tham số không đúng định dạng
- Máy chủ không hỗ trợ phương thức đó

**Giải pháp:**
- Kiểm tra log để xem tên phương thức và tham số
- Thử các tên phương thức khác nhau (ví dụ: `Join` thay vì `joinGroup`)
- Kiểm tra mã nguồn iOS gốc để xem tên phương thức chính xác

### 2. Lỗi "Connection lost"

**Nguyên nhân:**
- Kết nối mạng không ổn định
- Máy chủ đóng kết nối
- Lỗi trong quá trình xử lý tin nhắn

**Giải pháp:**
- Kiểm tra kết nối mạng
- Thêm cơ chế tự động kết nối lại
- Kiểm tra log để xem lỗi cụ thể

### 3. Không nhận được tin nhắn

**Nguyên nhân:**
- Không tham gia vào nhóm thành công
- Định dạng tin nhắn không đúng
- Lỗi trong quá trình xử lý tin nhắn

**Giải pháp:**
- Kiểm tra xem việc tham gia vào nhóm có thành công không
- Kiểm tra log để xem tin nhắn nhận được
- Thử các định dạng tin nhắn khác nhau

## Tài liệu tham khảo

- [Tài liệu SignalR](https://docs.microsoft.com/en-us/aspnet/signalr/overview/guide-to-the-api/hubs-api-guide-server)
- [Tài liệu Flutter Platform Channels](https://docs.flutter.dev/platform-integration/platform-channels)
- [Tài liệu Swift](https://swift.org/documentation/)
