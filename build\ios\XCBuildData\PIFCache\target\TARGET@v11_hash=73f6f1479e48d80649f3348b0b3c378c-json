{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b16b4ba8f0ed09b8a6e3a84e08a60e3c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980ac11292d9aeff90080e82e9b621eaf0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869d395c152103c40730311be7ea0114f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98714841aed6413f1351bc5bbd6e7c765c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869d395c152103c40730311be7ea0114f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9857d2d2afca64da5f2cb2b8803556a3a3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d53920f6bb9c6cd26aff7a6864bf7ee", "guid": "bfdfe7dc352907fc980b868725387e982284047accee45c97fbf304b487ab650", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad6bf18229e832744abb5a02528b10eb", "guid": "bfdfe7dc352907fc980b868725387e984e7ae52ace37bf75c559997f613d9c0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98975f1c33c61329228fe4c24a1b4d7ced", "guid": "bfdfe7dc352907fc980b868725387e98ca98d164922652445333c5508e6805a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e3cb751b365a3b3667d6b395cc3b9c7", "guid": "bfdfe7dc352907fc980b868725387e987cc4f1f297b2ca067a708e2dc52ac9ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd99a9b71de4d32a6b60ca681914fe27", "guid": "bfdfe7dc352907fc980b868725387e9867f57f821b15aebc452c5381c1782d53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981745294302bd8dbade2659ea1dc7e596", "guid": "bfdfe7dc352907fc980b868725387e983a70fd4367aabfe7a8a62d3d00867e4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98093be9cdaf7d51383e5844ab0e70cf0b", "guid": "bfdfe7dc352907fc980b868725387e987ee86d720cff41c3c4f39c0755966d4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988804dc54a41d69db9d170bbc9de1dadd", "guid": "bfdfe7dc352907fc980b868725387e98220a4176bf571085f0b630071fbab814", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a25e367826e569944d158be8976434a", "guid": "bfdfe7dc352907fc980b868725387e98e419cb542866a664f307b452264ce988", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e41fdc7f7a68b743b059a712d6865fb1", "guid": "bfdfe7dc352907fc980b868725387e98994a12868f1fc9991244b8bea7518b48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e79edb587aa52f88351eb81e5e83241c", "guid": "bfdfe7dc352907fc980b868725387e98a27cfc3d3740f6bfed68fb42c417b11b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac678f3e6f592321499baf09e79328f", "guid": "bfdfe7dc352907fc980b868725387e981b041e5ba447852ad99159e5f0eac232", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d58fec9509bbf3a51d3a61e6b0f65ae4", "guid": "bfdfe7dc352907fc980b868725387e988c4362b974eaff1b5fc265c4c83248a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a610fb8cbbe859bfe72917b0458e9fd6", "guid": "bfdfe7dc352907fc980b868725387e98c47e05ccae131a0140a0157d8030fc75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d652cc11eeb03e3a43be449d8805ff93", "guid": "bfdfe7dc352907fc980b868725387e98d9e664bab760fe99000322ecbaf1b7fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d3e3b6e575266569f426c1360d74388", "guid": "bfdfe7dc352907fc980b868725387e98e2065abaa27acbea0b5e490ae137b3c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1ec791ed7abed03dc58be6bc71c136d", "guid": "bfdfe7dc352907fc980b868725387e980270d8a19ccc1d0391e6e3e1825f8f6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98406cda6d441b8b4c8ff4a3a07480625e", "guid": "bfdfe7dc352907fc980b868725387e98b4bc5dd25ee29337b2b31905a7307324", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809793d1de90794e1916dc72a5b0f906a", "guid": "bfdfe7dc352907fc980b868725387e98d63c9930aa44cb2f5ce4094f758f7324", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf228ae5b11b6d5356631dfdb724e170", "guid": "bfdfe7dc352907fc980b868725387e9876dfc9dc887cf6e6d4bcc8d88b072a94", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98606e33b459ba949c2f1849ffc8f1b1d7", "guid": "bfdfe7dc352907fc980b868725387e98af89f64385434e3d69ea987991c7a3ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846f24d44a9f174450eecf69f9e6bfe99", "guid": "bfdfe7dc352907fc980b868725387e98ec618e0f2ad82a2d6db9eb04365c10af", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e57557468f7fa48dec0cb71963d5350e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9880e100b0e9f25cf042bba483018f5734", "guid": "bfdfe7dc352907fc980b868725387e98b9ca74fa0579cfa2500ea2dc42edf045"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be329f38a4e9976e5012735753203f1a", "guid": "bfdfe7dc352907fc980b868725387e98d77970279e019e78852b672c01918aac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981db1caea4d773e83c6f8468b4f652e28", "guid": "bfdfe7dc352907fc980b868725387e98d933f7854b5bc2d97c183ce765615cca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828f27715e15c3a5556fe2ef7af739e59", "guid": "bfdfe7dc352907fc980b868725387e982804c5e1be52f870060ee1842cccec94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988985c0cf982c5ff2f3fb7e315cdffde1", "guid": "bfdfe7dc352907fc980b868725387e98750ba46816975a3e248d6fe4c7fc8837"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e558dc319689d34de6fb7e556005ccb", "guid": "bfdfe7dc352907fc980b868725387e984698e960485ee48a577958a4a10d38a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98703da7ab1dc27f80aa6d3418bc1ceb99", "guid": "bfdfe7dc352907fc980b868725387e98cc4363c7094680a7773ae0b23530c429"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863ebdbcffc05590c2e394b684ca2ef9d", "guid": "bfdfe7dc352907fc980b868725387e981fe47bc62bb421a9e353587fb92d6774"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab7b3ce6e6fd9d43441167a742d44bf7", "guid": "bfdfe7dc352907fc980b868725387e9863f754459b27145ff86decf5f02cd78b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98706815006e46a34e6cb84fbea72bebae", "guid": "bfdfe7dc352907fc980b868725387e98669d60f8a1f5599f483650664a9d4607"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b991c240bff6ba243c7c125dbe4f349", "guid": "bfdfe7dc352907fc980b868725387e98bcc297a2f44a20588feb4acce7779405"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981406d57d7ee3b53bb73f90854fb1947d", "guid": "bfdfe7dc352907fc980b868725387e989995faa88299d30fa80b4e4c5d0b1177"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0c99d1a274cab404e9f22c9c2d82045", "guid": "bfdfe7dc352907fc980b868725387e9844e2b0d53ba4fd681fec45a9767e9881"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3cbfe4eb0646bf194533147c8f503f1", "guid": "bfdfe7dc352907fc980b868725387e98b8ee5f752d83dc0aa61ab23ffe9e43ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98121b876462c1cf096619b713efd60560", "guid": "bfdfe7dc352907fc980b868725387e98c58f62740d87240881f56ce960a7b48e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae1a14fe0ff4a1176643d024250c38ff", "guid": "bfdfe7dc352907fc980b868725387e98d9010684662c40afa0ea65546c2006a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872a9a600442db4c096c170d78308936e", "guid": "bfdfe7dc352907fc980b868725387e98c6c871e1d1eb7e49631ff9d042091c46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98193a30850e16b828394f1981e18c96a7", "guid": "bfdfe7dc352907fc980b868725387e98935b57be9756ede441a235011122ebe8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbd4e666c4c3d02d41193c60fbb5a678", "guid": "bfdfe7dc352907fc980b868725387e98f71bc58b8a08a7505dd5454aae99253c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989deba1804d5af4a2c90c184de67d410e", "guid": "bfdfe7dc352907fc980b868725387e9859033a68558bc1e03f96ca175771b0d2"}], "guid": "bfdfe7dc352907fc980b868725387e9887ad6689d34978197d1a4e5f237caee4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e986ed7d720ba25d4ec50b0af18348c633c"}], "guid": "bfdfe7dc352907fc980b868725387e983081fc959c11556c7c8a908e6bc100e0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f4992a54d9699a7575f571c7018b1e4f", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98ff5401441808c00c7f17907ccada57c2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}