<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/color_toolbar"
    tools:context="vn.zenity.betacineplex.view.HomeActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/contentLayout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/llBottomBar"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <LinearLayout
            android:id="@+id/llBottomBar"
            android:layout_width="match_parent"
            android:layout_height="70dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="#fdfdfd"
            android:gravity="center"
            android:orientation="horizontal"
            tools:ignore="DisableBaselineAlignment">

            <LinearLayout
                android:id="@+id/llHomeFilm"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:tag="@string/menu_home"
                android:orientation="vertical">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivHomeFilm"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:scaleType="center"
                    app:srcCompat="@drawable/ic_home_film"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvHomeFilm"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:lineSpacingExtra="1.2dp"
                    android:lines="2"
                    android:text="@string/shows_by_film"
                    android:textColor="@drawable/draw_main_bottom_text"
                    android:textSize="12sp"
                    app:fontFamily="@font/sanspro_regular" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llHomeCinema"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:tag="@string/menu_beta_cinema"
                android:orientation="vertical">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivHomeCinema"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:scaleType="center"
                    app:srcCompat="@drawable/ic_home_cinema"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvHomeCinema"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:lineSpacingExtra="1.2dp"
                    android:lines="2"
                    android:text="@string/show_by_cinema"
                    android:textColor="@drawable/draw_main_bottom_text"
                    android:textSize="12sp"
                    app:fontFamily="@font/sanspro_regular" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llHomeVoucher"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:tag="@string/menu_voucher"
                android:orientation="vertical">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivHomeVoucher"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    app:srcCompat="@drawable/ic_home_voucher"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvHomeVoucher"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal|top"
                    android:lines="2"
                    android:text="@string/voucher"
                    android:textColor="@drawable/draw_main_bottom_text"
                    android:textSize="12sp"
                    app:fontFamily="@font/sanspro_regular" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llHomeGift"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:tag="@string/menu_news"
                android:orientation="vertical">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivHomeGift"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_home_gift"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvHomeGift"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal|top"
                    android:lines="2"
                    android:text="@string/promotion"
                    android:textColor="@drawable/draw_main_bottom_text"
                    android:textSize="12sp"
                    app:fontFamily="@font/sanspro_regular" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llHomeMore"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:tag="@string/menu_setting"
                android:orientation="vertical">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivHomeMore"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    app:srcCompat="@drawable/ic_home_more"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvHomeMore"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal|top"
                    android:lines="2"
                    android:text="@string/more"
                    android:textColor="@drawable/draw_main_bottom_text"
                    android:textSize="12sp"
                    app:fontFamily="@font/sanspro_regular" />
            </LinearLayout>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
