package vn.zenity.betacineplex.model

import androidx.room.Ignore
import com.google.gson.annotations.Expose
import vn.zenity.betacineplex.helper.extension.toDate
import java.util.*

/**
 * Created by tinhvv on 4/14/18.
 */
class ShowModel {
    var ShowId: String? = null
    var CinemaId: String? = null
    var ScreenId: String? = null
    var FilmId: String? = null
    var StartTime: String? = null
    var EndTime: String? = null
    var CleanupTime: Int? = null
    var TrailerTime: Int? = null
    var FimlDuration: Int? = null
    var PricardId: String? = null
    var Status: Boolean? = null
    var Order: Int? = null
    var CreatedOnDate: String? = null
    var CreatedByUserId: String? = null
    var LastModifiedOnDate: String? = null
    var LastModifiedByUserId: String? = null
    var TotalSeat: Int? = null
    var SeatSolded: Int? = null
    var DateShow: String? = null
    var StartWeek: String? = null
    var Day: Int? = null
    var IsApprove: Int? = null

    var FilmFormat: String? = null
    var ScreenClass: String? = null
    var ScreenDesc: String? = null
    var ScreenTitle: String? = null
    var ScreenImageUrl: String? = null
    var IsShowScreenIntro: Boolean? = null


    @Transient
    @Expose
    private var StartDate: Date? = null

    @Transient
    @Expose
    private var EndDate: Date? = null

    var TimeToLock: Int = 0

    fun getIsShowScreenIntro(): Boolean {
        return IsShowScreenIntro ?: false
    }

    fun getStartDate(): Date? {
        if (StartDate != null) return StartDate
        if (StartTime == null) return null
        return StartTime?.toDate()
    }

    fun getEndDate(): Date? {
        if (EndDate != null) return EndDate
        if (EndTime == null) return null
        return EndTime?.toDate()
    }

    fun isMorning(): Boolean {
        return getStartDate()?.hours ?: 0 < 12
    }
}