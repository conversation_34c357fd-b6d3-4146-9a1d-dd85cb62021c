<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:descendantFocusability="beforeDescendants"
    android:fitsSystemWindows="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <View
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/statusBarHeight"
        android:background="@drawable/color_toolbar"/>

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/book_by_film"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_small"
            app:srcCompat="@drawable/ic_menubuger_white" />
    </LinearLayout>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:tabIndicatorColor="@color/textDark"
        app:tabSelectedTextColor="@color/textDark"
        app:layout_constraintTop_toBottomOf="@+id/action_bar"
        app:tabTextColor="@color/textGray"
        app:tabTextAppearance="@style/TextTabLayoutCustom"
        app:tabMode="fixed">
        <com.google.android.material.tabs.TabItem
            android:id="@+id/tabDeals"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/now_showing"/>

        <com.google.android.material.tabs.TabItem
            android:id="@+id/tabNews"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/sneak_show"/>
    </com.google.android.material.tabs.TabLayout>
    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout"
        app:layout_constraintBottom_toBottomOf="parent"/>

</LinearLayout>
