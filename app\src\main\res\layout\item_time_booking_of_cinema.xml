<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/viewItemTimeRoot"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingLeft="12dp"
    android:paddingRight="12dp"
    android:background="@color/white"
    android:layout_marginLeft="8dp"
    android:layout_marginRight="8dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTypeFilm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:fontFamily="@font/oswald_bold"
        android:textSize="14sp"
        app:textAllCaps="true"
        android:layout_marginTop="26dp"
        android:textColor="@color/text494c62"
        tools:text="3D Phụ Đề"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <vn.zenity.betacineplex.helper.view.BetaHorizScrollview
        android:id="@+id/morningScroll"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTypeFilm"
        android:layout_marginTop="12dp"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/listTimeMorning"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
        </LinearLayout>
    </vn.zenity.betacineplex.helper.view.BetaHorizScrollview>

    <View
        android:id="@+id/bottomLine"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:alpha="0.2"
        android:background="@color/text494c62"
        android:layout_marginTop="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/morningScroll"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>