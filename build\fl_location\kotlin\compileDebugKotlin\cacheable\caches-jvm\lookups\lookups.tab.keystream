  Manifest android  ACCESS_BACKGROUND_LOCATION android.Manifest.permission  ACCESS_COARSE_LOCATION android.Manifest.permission  ACCESS_FINE_LOCATION android.Manifest.permission  SuppressLint android.annotation  Activity android.app  	RESULT_OK android.app.Activity  $shouldShowRequestPermissionRationale android.app.Activity  Context android.app.Service  #LocationServicesStatusIntentService android.app.Service  LocationServicesStatusWatcher android.app.Service  LocationServicesUtils android.app.Service  checkLocationServicesStatus android.app.Service  enqueueWork android.app.Service  java android.app.Service  with android.app.Service  BroadcastReceiver android.content  Context android.content  Intent android.content  IntentFilter android.content  IntentSender android.content  SharedPreferences android.content  LocationManager !android.content.BroadcastReceiver  #LocationServicesStatusIntentService !android.content.BroadcastReceiver  enqueueWork !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  Context android.content.Context  IntentFilter android.content.Context  LOCATION_SERVICE android.content.Context  "LOCATION_SERVICES_STATUS_PREFS_KEY android.content.Context  #LOCATION_SERVICES_STATUS_PREFS_NAME android.content.Context  LocationManager android.content.Context  #LocationServicesStatusIntentService android.content.Context  LocationServicesStatusReceiver android.content.Context  LocationServicesStatusWatcher android.content.Context  LocationServicesUtils android.content.Context  MODE_PRIVATE android.content.Context  broadcastReceiver android.content.Context  checkLocationServicesStatus android.content.Context  enqueueWork android.content.Context  getSharedPreferences android.content.Context  getSystemService android.content.Context  java android.content.Context  packageManager android.content.Context  packageName android.content.Context  ,registerLocationServicesStatusIntentReceiver android.content.Context  registerReceiver android.content.Context  &registerSharedPreferenceChangeListener android.content.Context  .unregisterLocationServicesStatusIntentReceiver android.content.Context  unregisterReceiver android.content.Context  (unregisterSharedPreferenceChangeListener android.content.Context  with android.content.Context  Context android.content.ContextWrapper  #LocationServicesStatusIntentService android.content.ContextWrapper  LocationServicesStatusWatcher android.content.ContextWrapper  LocationServicesUtils android.content.ContextWrapper  applicationContext android.content.ContextWrapper  checkLocationServicesStatus android.content.ContextWrapper  enqueueWork android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  java android.content.ContextWrapper  with android.content.ContextWrapper  action android.content.Intent  setClass android.content.Intent  SendIntentException android.content.IntentSender   OnSharedPreferenceChangeListener !android.content.SharedPreferences  edit !android.content.SharedPreferences  	getString !android.content.SharedPreferences  (registerOnSharedPreferenceChangeListener !android.content.SharedPreferences  *unregisterOnSharedPreferenceChangeListener !android.content.SharedPreferences  commit (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  remove (android.content.SharedPreferences.Editor  PackageInfo android.content.pm  PackageManager android.content.pm  requestedPermissions android.content.pm.PackageInfo  GET_PERMISSIONS !android.content.pm.PackageManager  PERMISSION_GRANTED !android.content.pm.PackageManager  getPackageInfo !android.content.pm.PackageManager  Location android.location  LocationManager android.location  accuracy android.location.Location  altitude android.location.Location  bearing android.location.Location  isMock android.location.Location  latitude android.location.Location  	longitude android.location.Location  speed android.location.Location  speedAccuracyMetersPerSecond android.location.Location  time android.location.Location  GPS_PROVIDER  android.location.LocationManager  NETWORK_PROVIDER  android.location.LocationManager  PROVIDERS_CHANGED_ACTION  android.location.LocationManager  isProviderEnabled  android.location.LocationManager  Build 
android.os  Looper 
android.os  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  
getMainLooper android.os.Looper  Log android.util  e android.util.Log  ActivityCompat androidx.core.app  JobIntentService androidx.core.app  requestPermissions  androidx.core.app.ActivityCompat  Context "androidx.core.app.JobIntentService  #LocationServicesStatusIntentService "androidx.core.app.JobIntentService  LocationServicesStatusWatcher "androidx.core.app.JobIntentService  LocationServicesUtils "androidx.core.app.JobIntentService  checkLocationServicesStatus "androidx.core.app.JobIntentService  enqueueWork "androidx.core.app.JobIntentService  java "androidx.core.app.JobIntentService  with "androidx.core.app.JobIntentService  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  ApiException !com.google.android.gms.common.api  ResolvableApiException !com.google.android.gms.common.api  
statusCode .com.google.android.gms.common.api.ApiException  RESOLUTION_REQUIRED 3com.google.android.gms.common.api.CommonStatusCodes  startResolutionForResult 8com.google.android.gms.common.api.ResolvableApiException  
statusCode 8com.google.android.gms.common.api.ResolvableApiException  Activity com.google.android.gms.location  ApiException com.google.android.gms.location  Boolean com.google.android.gms.location  Build com.google.android.gms.location  Context com.google.android.gms.location  DEFAULT_LOCATION_INTERVAL com.google.android.gms.location  Double com.google.android.gms.location  
ErrorCodes com.google.android.gms.location  FusedLocationProviderClient com.google.android.gms.location  Int com.google.android.gms.location  IntentSender com.google.android.gms.location  LocationCallback com.google.android.gms.location  LocationData com.google.android.gms.location  LocationDataCallback com.google.android.gms.location  LocationDataProvider com.google.android.gms.location  LocationRequest com.google.android.gms.location  LocationResult com.google.android.gms.location  LocationServices com.google.android.gms.location  LocationSettings com.google.android.gms.location  LocationSettingsRequest com.google.android.gms.location  LocationSettingsStatusCodes com.google.android.gms.location  Log com.google.android.gms.location  Looper com.google.android.gms.location  Priority com.google.android.gms.location  REQUEST_CHECK_SETTINGS com.google.android.gms.location  ResolvableApiException com.google.android.gms.location  SettingsClient com.google.android.gms.location  TAG com.google.android.gms.location  apply com.google.android.gms.location  java com.google.android.gms.location  removeLocationUpdates ;com.google.android.gms.location.FusedLocationProviderClient  requestLocationUpdates ;com.google.android.gms.location.FusedLocationProviderClient  SendIntentException ,com.google.android.gms.location.IntentSender  Boolean 0com.google.android.gms.location.LocationCallback  Build 0com.google.android.gms.location.LocationCallback  Double 0com.google.android.gms.location.LocationCallback  LocationData 0com.google.android.gms.location.LocationCallback  Builder /com.google.android.gms.location.LocationRequest  apply 7com.google.android.gms.location.LocationRequest.Builder  build 7com.google.android.gms.location.LocationRequest.Builder  setMinUpdateDistanceMeters 7com.google.android.gms.location.LocationRequest.Builder  setMinUpdateIntervalMillis 7com.google.android.gms.location.LocationRequest.Builder  lastLocation .com.google.android.gms.location.LocationResult  getFusedLocationProviderClient 0com.google.android.gms.location.LocationServices  getSettingsClient 0com.google.android.gms.location.LocationServices  Builder 7com.google.android.gms.location.LocationSettingsRequest  addLocationRequest ?com.google.android.gms.location.LocationSettingsRequest.Builder  build ?com.google.android.gms.location.LocationSettingsRequest.Builder  RESOLUTION_REQUIRED ;com.google.android.gms.location.LocationSettingsStatusCodes  SETTINGS_CHANGE_UNAVAILABLE ;com.google.android.gms.location.LocationSettingsStatusCodes   PRIORITY_BALANCED_POWER_ACCURACY (com.google.android.gms.location.Priority  PRIORITY_HIGH_ACCURACY (com.google.android.gms.location.Priority  PRIORITY_LOW_POWER (com.google.android.gms.location.Priority  checkLocationSettings .com.google.android.gms.location.SettingsClient  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  Activity com.pravera.fl_location  
ActivityAware com.pravera.fl_location  ActivityPluginBinding com.pravera.fl_location  Any com.pravera.fl_location  BinaryMessenger com.pravera.fl_location  Context com.pravera.fl_location  
ErrorCodes com.pravera.fl_location  ErrorHandleUtils com.pravera.fl_location  EventChannel com.pravera.fl_location  FlLocationPlugin com.pravera.fl_location  FlLocationPluginChannel com.pravera.fl_location  
FlutterPlugin com.pravera.fl_location  Int com.pravera.fl_location  LocationData com.pravera.fl_location  LocationDataCallback com.pravera.fl_location  LocationDataProviderManager com.pravera.fl_location  LocationPermission com.pravera.fl_location  LocationPermissionCallback com.pravera.fl_location  LocationPermissionManager com.pravera.fl_location  LocationServicesStatus com.pravera.fl_location  'LocationServicesStatusStreamHandlerImpl com.pravera.fl_location  LocationServicesStatusWatcher com.pravera.fl_location  LocationServicesUtils com.pravera.fl_location  LocationSettings com.pravera.fl_location  LocationStreamHandlerImpl com.pravera.fl_location  Map com.pravera.fl_location  
MethodCall com.pravera.fl_location  MethodCallHandlerImpl com.pravera.fl_location  
MethodChannel com.pravera.fl_location  ServiceProvider com.pravera.fl_location  checkLocationServicesStatus com.pravera.fl_location  fromMap com.pravera.fl_location  handleMethodCallError com.pravera.fl_location  handleStreamError com.pravera.fl_location  
isInitialized com.pravera.fl_location  	EventSink $com.pravera.fl_location.EventChannel  
StreamHandler $com.pravera.fl_location.EventChannel  LocationDataProviderManager (com.pravera.fl_location.FlLocationPlugin  LocationPermissionManager (com.pravera.fl_location.FlLocationPlugin  'LocationServicesStatusStreamHandlerImpl (com.pravera.fl_location.FlLocationPlugin  LocationServicesStatusWatcher (com.pravera.fl_location.FlLocationPlugin  LocationStreamHandlerImpl (com.pravera.fl_location.FlLocationPlugin  MethodCallHandlerImpl (com.pravera.fl_location.FlLocationPlugin  activityBinding (com.pravera.fl_location.FlLocationPlugin  
isInitialized (com.pravera.fl_location.FlLocationPlugin  locationDataProviderManager (com.pravera.fl_location.FlLocationPlugin  locationPermissionManager (com.pravera.fl_location.FlLocationPlugin  #locationServicesStatusStreamHandler (com.pravera.fl_location.FlLocationPlugin  locationServicesStatusWatcher (com.pravera.fl_location.FlLocationPlugin  locationStreamHandler (com.pravera.fl_location.FlLocationPlugin  methodCallHandler (com.pravera.fl_location.FlLocationPlugin  onAttachedToActivity (com.pravera.fl_location.FlLocationPlugin  onDetachedFromActivity (com.pravera.fl_location.FlLocationPlugin  FlutterPluginBinding %com.pravera.fl_location.FlutterPlugin  EventChannel ?com.pravera.fl_location.LocationServicesStatusStreamHandlerImpl  activity ?com.pravera.fl_location.LocationServicesStatusStreamHandlerImpl  channel ?com.pravera.fl_location.LocationServicesStatusStreamHandlerImpl  context ?com.pravera.fl_location.LocationServicesStatusStreamHandlerImpl  disposeChannel ?com.pravera.fl_location.LocationServicesStatusStreamHandlerImpl  initChannel ?com.pravera.fl_location.LocationServicesStatusStreamHandlerImpl  
isInitialized ?com.pravera.fl_location.LocationServicesStatusStreamHandlerImpl  serviceProvider ?com.pravera.fl_location.LocationServicesStatusStreamHandlerImpl  setActivity ?com.pravera.fl_location.LocationServicesStatusStreamHandlerImpl  ErrorHandleUtils 1com.pravera.fl_location.LocationStreamHandlerImpl  EventChannel 1com.pravera.fl_location.LocationStreamHandlerImpl  LocationSettings 1com.pravera.fl_location.LocationStreamHandlerImpl  activity 1com.pravera.fl_location.LocationStreamHandlerImpl  channel 1com.pravera.fl_location.LocationStreamHandlerImpl  disposeChannel 1com.pravera.fl_location.LocationStreamHandlerImpl  fromMap 1com.pravera.fl_location.LocationStreamHandlerImpl  handleStreamError 1com.pravera.fl_location.LocationStreamHandlerImpl  initChannel 1com.pravera.fl_location.LocationStreamHandlerImpl  
isInitialized 1com.pravera.fl_location.LocationStreamHandlerImpl  locationDataProviderHashCode 1com.pravera.fl_location.LocationStreamHandlerImpl  serviceProvider 1com.pravera.fl_location.LocationStreamHandlerImpl  setActivity 1com.pravera.fl_location.LocationStreamHandlerImpl  
ErrorCodes -com.pravera.fl_location.MethodCallHandlerImpl  ErrorHandleUtils -com.pravera.fl_location.MethodCallHandlerImpl  LocationServicesStatus -com.pravera.fl_location.MethodCallHandlerImpl  LocationServicesUtils -com.pravera.fl_location.MethodCallHandlerImpl  LocationSettings -com.pravera.fl_location.MethodCallHandlerImpl  
MethodChannel -com.pravera.fl_location.MethodCallHandlerImpl  activity -com.pravera.fl_location.MethodCallHandlerImpl  channel -com.pravera.fl_location.MethodCallHandlerImpl  checkLocationServicesStatus -com.pravera.fl_location.MethodCallHandlerImpl  context -com.pravera.fl_location.MethodCallHandlerImpl  disposeChannel -com.pravera.fl_location.MethodCallHandlerImpl  fromMap -com.pravera.fl_location.MethodCallHandlerImpl  handleMethodCallError -com.pravera.fl_location.MethodCallHandlerImpl  initChannel -com.pravera.fl_location.MethodCallHandlerImpl  
isInitialized -com.pravera.fl_location.MethodCallHandlerImpl  serviceProvider -com.pravera.fl_location.MethodCallHandlerImpl  setActivity -com.pravera.fl_location.MethodCallHandlerImpl  MethodCallHandler %com.pravera.fl_location.MethodChannel  Result %com.pravera.fl_location.MethodChannel  ACTIVITY_NOT_ATTACHED com.pravera.fl_location.errors  
ErrorCodes com.pravera.fl_location.errors  %LOCATION_PERMISSION_REQUEST_CANCELLED com.pravera.fl_location.errors  LOCATION_SERVICES_NOT_AVAILABLE com.pravera.fl_location.errors  LOCATION_SETTINGS_CHANGE_FAILED com.pravera.fl_location.errors  String com.pravera.fl_location.errors  ACTIVITY_NOT_ATTACHED )com.pravera.fl_location.errors.ErrorCodes  %LOCATION_PERMISSION_REQUEST_CANCELLED )com.pravera.fl_location.errors.ErrorCodes  LOCATION_SERVICES_NOT_AVAILABLE )com.pravera.fl_location.errors.ErrorCodes  LOCATION_SETTINGS_CHANGE_FAILED )com.pravera.fl_location.errors.ErrorCodes  message )com.pravera.fl_location.errors.ErrorCodes  toString )com.pravera.fl_location.errors.ErrorCodes  Any com.pravera.fl_location.models  BALANCED com.pravera.fl_location.models  BEST com.pravera.fl_location.models  Boolean com.pravera.fl_location.models  Double com.pravera.fl_location.models  Float com.pravera.fl_location.models  HIGH com.pravera.fl_location.models  Int com.pravera.fl_location.models  LOW com.pravera.fl_location.models  LocationAccuracy com.pravera.fl_location.models  LocationData com.pravera.fl_location.models  LocationPermission com.pravera.fl_location.models  LocationServicesStatus com.pravera.fl_location.models  LocationSettings com.pravera.fl_location.models  Long com.pravera.fl_location.models  Map com.pravera.fl_location.models  
NAVIGATION com.pravera.fl_location.models  
POWER_SAVE com.pravera.fl_location.models  Priority com.pravera.fl_location.models  String com.pravera.fl_location.models  	fromIndex com.pravera.fl_location.models  get com.pravera.fl_location.models  mutableMapOf com.pravera.fl_location.models  set com.pravera.fl_location.models  
toFloatOrNull com.pravera.fl_location.models  toIntOrNull com.pravera.fl_location.models  toLongOrNull com.pravera.fl_location.models  toString com.pravera.fl_location.models  BALANCED /com.pravera.fl_location.models.LocationAccuracy  BEST /com.pravera.fl_location.models.LocationAccuracy  	Companion /com.pravera.fl_location.models.LocationAccuracy  HIGH /com.pravera.fl_location.models.LocationAccuracy  Int /com.pravera.fl_location.models.LocationAccuracy  LOW /com.pravera.fl_location.models.LocationAccuracy  LocationAccuracy /com.pravera.fl_location.models.LocationAccuracy  
NAVIGATION /com.pravera.fl_location.models.LocationAccuracy  
POWER_SAVE /com.pravera.fl_location.models.LocationAccuracy  Priority /com.pravera.fl_location.models.LocationAccuracy  	fromIndex /com.pravera.fl_location.models.LocationAccuracy  ordinal /com.pravera.fl_location.models.LocationAccuracy  
toPriority /com.pravera.fl_location.models.LocationAccuracy  BALANCED 9com.pravera.fl_location.models.LocationAccuracy.Companion  BEST 9com.pravera.fl_location.models.LocationAccuracy.Companion  HIGH 9com.pravera.fl_location.models.LocationAccuracy.Companion  LOW 9com.pravera.fl_location.models.LocationAccuracy.Companion  
NAVIGATION 9com.pravera.fl_location.models.LocationAccuracy.Companion  
POWER_SAVE 9com.pravera.fl_location.models.LocationAccuracy.Companion  Priority 9com.pravera.fl_location.models.LocationAccuracy.Companion  	fromIndex 9com.pravera.fl_location.models.LocationAccuracy.Companion  accuracy +com.pravera.fl_location.models.LocationData  altitude +com.pravera.fl_location.models.LocationData  heading +com.pravera.fl_location.models.LocationData  isMock +com.pravera.fl_location.models.LocationData  latitude +com.pravera.fl_location.models.LocationData  	longitude +com.pravera.fl_location.models.LocationData  millisecondsSinceEpoch +com.pravera.fl_location.models.LocationData  mutableMapOf +com.pravera.fl_location.models.LocationData  set +com.pravera.fl_location.models.LocationData  speed +com.pravera.fl_location.models.LocationData  
speedAccuracy +com.pravera.fl_location.models.LocationData  toJson +com.pravera.fl_location.models.LocationData  ALWAYS 1com.pravera.fl_location.models.LocationPermission  DENIED 1com.pravera.fl_location.models.LocationPermission  DENIED_FOREVER 1com.pravera.fl_location.models.LocationPermission  WHILE_IN_USE 1com.pravera.fl_location.models.LocationPermission  ordinal 1com.pravera.fl_location.models.LocationPermission  toString 1com.pravera.fl_location.models.LocationPermission  valueOf 1com.pravera.fl_location.models.LocationPermission  DISABLED 5com.pravera.fl_location.models.LocationServicesStatus  ENABLED 5com.pravera.fl_location.models.LocationServicesStatus  ordinal 5com.pravera.fl_location.models.LocationServicesStatus  toString 5com.pravera.fl_location.models.LocationServicesStatus  valueOf 5com.pravera.fl_location.models.LocationServicesStatus  	Companion /com.pravera.fl_location.models.LocationSettings  Float /com.pravera.fl_location.models.LocationSettings  LocationAccuracy /com.pravera.fl_location.models.LocationSettings  LocationSettings /com.pravera.fl_location.models.LocationSettings  Long /com.pravera.fl_location.models.LocationSettings  Map /com.pravera.fl_location.models.LocationSettings  accuracy /com.pravera.fl_location.models.LocationSettings  distanceFilter /com.pravera.fl_location.models.LocationSettings  	fromIndex /com.pravera.fl_location.models.LocationSettings  fromMap /com.pravera.fl_location.models.LocationSettings  get /com.pravera.fl_location.models.LocationSettings  interval /com.pravera.fl_location.models.LocationSettings  
toFloatOrNull /com.pravera.fl_location.models.LocationSettings  toIntOrNull /com.pravera.fl_location.models.LocationSettings  toLongOrNull /com.pravera.fl_location.models.LocationSettings  toString /com.pravera.fl_location.models.LocationSettings  LocationAccuracy 9com.pravera.fl_location.models.LocationSettings.Companion  LocationSettings 9com.pravera.fl_location.models.LocationSettings.Companion  	fromIndex 9com.pravera.fl_location.models.LocationSettings.Companion  fromMap 9com.pravera.fl_location.models.LocationSettings.Companion  get 9com.pravera.fl_location.models.LocationSettings.Companion  
toFloatOrNull 9com.pravera.fl_location.models.LocationSettings.Companion  toIntOrNull 9com.pravera.fl_location.models.LocationSettings.Companion  toLongOrNull 9com.pravera.fl_location.models.LocationSettings.Companion  toString 9com.pravera.fl_location.models.LocationSettings.Companion  Activity com.pravera.fl_location.service  
ActivityAware com.pravera.fl_location.service  ActivityCompat com.pravera.fl_location.service  ActivityPluginBinding com.pravera.fl_location.service  ApiException com.pravera.fl_location.service  Array com.pravera.fl_location.service  'BACKGROUND_LOCATION_PERMISSION_REQ_CODE com.pravera.fl_location.service  Boolean com.pravera.fl_location.service  BroadcastReceiver com.pravera.fl_location.service  Build com.pravera.fl_location.service  Context com.pravera.fl_location.service  
ContextCompat com.pravera.fl_location.service  DEFAULT_LOCATION_INTERVAL com.pravera.fl_location.service  Double com.pravera.fl_location.service  
ErrorCodes com.pravera.fl_location.service  
FlutterPlugin com.pravera.fl_location.service  Int com.pravera.fl_location.service  IntArray com.pravera.fl_location.service  Intent com.pravera.fl_location.service  IntentFilter com.pravera.fl_location.service  IntentSender com.pravera.fl_location.service  JobIntentService com.pravera.fl_location.service  LOCATION_PERMISSION_REQ_CODE com.pravera.fl_location.service  "LOCATION_SERVICES_STATUS_PREFS_KEY com.pravera.fl_location.service  #LOCATION_SERVICES_STATUS_PREFS_NAME com.pravera.fl_location.service  LocationCallback com.pravera.fl_location.service  LocationData com.pravera.fl_location.service  LocationDataCallback com.pravera.fl_location.service  LocationDataProvider com.pravera.fl_location.service  LocationDataProviderManager com.pravera.fl_location.service  LocationManager com.pravera.fl_location.service  LocationPermission com.pravera.fl_location.service  LocationPermissionCallback com.pravera.fl_location.service  LocationPermissionManager com.pravera.fl_location.service  LocationRequest com.pravera.fl_location.service  LocationResult com.pravera.fl_location.service  LocationServices com.pravera.fl_location.service  LocationServicesStatus com.pravera.fl_location.service  #LocationServicesStatusIntentService com.pravera.fl_location.service  LocationServicesStatusReceiver com.pravera.fl_location.service  'LocationServicesStatusStreamHandlerImpl com.pravera.fl_location.service  LocationServicesStatusWatcher com.pravera.fl_location.service  LocationServicesUtils com.pravera.fl_location.service  LocationSettings com.pravera.fl_location.service  LocationSettingsRequest com.pravera.fl_location.service  LocationSettingsStatusCodes com.pravera.fl_location.service  LocationStreamHandlerImpl com.pravera.fl_location.service  Log com.pravera.fl_location.service  Looper com.pravera.fl_location.service  Manifest com.pravera.fl_location.service  MethodCallHandlerImpl com.pravera.fl_location.service  !PREV_PERMISSION_STATUS_PREFS_NAME com.pravera.fl_location.service  PackageManager com.pravera.fl_location.service  PluginRegistry com.pravera.fl_location.service  REQUEST_CHECK_SETTINGS com.pravera.fl_location.service  ResolvableApiException com.pravera.fl_location.service  ServiceProvider com.pravera.fl_location.service  SharedPreferences com.pravera.fl_location.service  String com.pravera.fl_location.service  SuppressLint com.pravera.fl_location.service  TAG com.pravera.fl_location.service  Unit com.pravera.fl_location.service  any com.pravera.fl_location.service  apply com.pravera.fl_location.service  arrayOf com.pravera.fl_location.service  broadcastReceiver com.pravera.fl_location.service  checkLocationServicesStatus com.pravera.fl_location.service  enqueueWork com.pravera.fl_location.service  indexOf com.pravera.fl_location.service  isEmpty com.pravera.fl_location.service  
isInitialized com.pravera.fl_location.service  iterator com.pravera.fl_location.service  java com.pravera.fl_location.service  let com.pravera.fl_location.service  mutableMapOf com.pravera.fl_location.service  set com.pravera.fl_location.service  stopLocationUpdates com.pravera.fl_location.service  with com.pravera.fl_location.service  FlutterPluginBinding -com.pravera.fl_location.service.FlutterPlugin  SendIntentException ,com.pravera.fl_location.service.IntentSender  onError 4com.pravera.fl_location.service.LocationDataCallback  onUpdate 4com.pravera.fl_location.service.LocationDataCallback  Activity 4com.pravera.fl_location.service.LocationDataProvider  ApiException 4com.pravera.fl_location.service.LocationDataProvider  Boolean 4com.pravera.fl_location.service.LocationDataProvider  Build 4com.pravera.fl_location.service.LocationDataProvider  	Companion 4com.pravera.fl_location.service.LocationDataProvider  Context 4com.pravera.fl_location.service.LocationDataProvider  DEFAULT_LOCATION_INTERVAL 4com.pravera.fl_location.service.LocationDataProvider  Double 4com.pravera.fl_location.service.LocationDataProvider  
ErrorCodes 4com.pravera.fl_location.service.LocationDataProvider  Int 4com.pravera.fl_location.service.LocationDataProvider  IntentSender 4com.pravera.fl_location.service.LocationDataProvider  LocationCallback 4com.pravera.fl_location.service.LocationDataProvider  LocationData 4com.pravera.fl_location.service.LocationDataProvider  LocationDataCallback 4com.pravera.fl_location.service.LocationDataProvider  LocationDataProvider 4com.pravera.fl_location.service.LocationDataProvider  LocationRequest 4com.pravera.fl_location.service.LocationDataProvider  LocationResult 4com.pravera.fl_location.service.LocationDataProvider  LocationServices 4com.pravera.fl_location.service.LocationDataProvider  LocationSettings 4com.pravera.fl_location.service.LocationDataProvider  LocationSettingsRequest 4com.pravera.fl_location.service.LocationDataProvider  LocationSettingsStatusCodes 4com.pravera.fl_location.service.LocationDataProvider  Log 4com.pravera.fl_location.service.LocationDataProvider  Looper 4com.pravera.fl_location.service.LocationDataProvider  REQUEST_CHECK_SETTINGS 4com.pravera.fl_location.service.LocationDataProvider  ResolvableApiException 4com.pravera.fl_location.service.LocationDataProvider  TAG 4com.pravera.fl_location.service.LocationDataProvider  activity 4com.pravera.fl_location.service.LocationDataProvider  apply 4com.pravera.fl_location.service.LocationDataProvider  callback 4com.pravera.fl_location.service.LocationDataProvider  ,checkLocationSettingsAndStartLocationUpdates 4com.pravera.fl_location.service.LocationDataProvider  context 4com.pravera.fl_location.service.LocationDataProvider  createLocationCallback 4com.pravera.fl_location.service.LocationDataProvider  createLocationRequest 4com.pravera.fl_location.service.LocationDataProvider  hashCode 4com.pravera.fl_location.service.LocationDataProvider  isRunningLocationUpdates 4com.pravera.fl_location.service.LocationDataProvider  java 4com.pravera.fl_location.service.LocationDataProvider  let 4com.pravera.fl_location.service.LocationDataProvider  locationCallback 4com.pravera.fl_location.service.LocationDataProvider  locationProvider 4com.pravera.fl_location.service.LocationDataProvider  locationRequest 4com.pravera.fl_location.service.LocationDataProvider  onActivityResult 4com.pravera.fl_location.service.LocationDataProvider  requestLocationUpdates 4com.pravera.fl_location.service.LocationDataProvider  setActivity 4com.pravera.fl_location.service.LocationDataProvider  settings 4com.pravera.fl_location.service.LocationDataProvider  startLocationUpdates 4com.pravera.fl_location.service.LocationDataProvider  stopLocationUpdates 4com.pravera.fl_location.service.LocationDataProvider  Activity >com.pravera.fl_location.service.LocationDataProvider.Companion  Build >com.pravera.fl_location.service.LocationDataProvider.Companion  DEFAULT_LOCATION_INTERVAL >com.pravera.fl_location.service.LocationDataProvider.Companion  
ErrorCodes >com.pravera.fl_location.service.LocationDataProvider.Companion  LocationData >com.pravera.fl_location.service.LocationDataProvider.Companion  LocationDataProvider >com.pravera.fl_location.service.LocationDataProvider.Companion  LocationRequest >com.pravera.fl_location.service.LocationDataProvider.Companion  LocationServices >com.pravera.fl_location.service.LocationDataProvider.Companion  LocationSettingsRequest >com.pravera.fl_location.service.LocationDataProvider.Companion  LocationSettingsStatusCodes >com.pravera.fl_location.service.LocationDataProvider.Companion  Log >com.pravera.fl_location.service.LocationDataProvider.Companion  Looper >com.pravera.fl_location.service.LocationDataProvider.Companion  REQUEST_CHECK_SETTINGS >com.pravera.fl_location.service.LocationDataProvider.Companion  TAG >com.pravera.fl_location.service.LocationDataProvider.Companion  apply >com.pravera.fl_location.service.LocationDataProvider.Companion  java >com.pravera.fl_location.service.LocationDataProvider.Companion  SendIntentException Acom.pravera.fl_location.service.LocationDataProvider.IntentSender  LocationDataProvider ;com.pravera.fl_location.service.LocationDataProviderManager  buildLocationDataProvider ;com.pravera.fl_location.service.LocationDataProviderManager  context ;com.pravera.fl_location.service.LocationDataProviderManager  getLocation ;com.pravera.fl_location.service.LocationDataProviderManager  iterator ;com.pravera.fl_location.service.LocationDataProviderManager  let ;com.pravera.fl_location.service.LocationDataProviderManager  mutableMapOf ;com.pravera.fl_location.service.LocationDataProviderManager  	providers ;com.pravera.fl_location.service.LocationDataProviderManager  requestLocationUpdates ;com.pravera.fl_location.service.LocationDataProviderManager  set ;com.pravera.fl_location.service.LocationDataProviderManager  setActivity ;com.pravera.fl_location.service.LocationDataProviderManager  stopAllLocationUpdates ;com.pravera.fl_location.service.LocationDataProviderManager  stopLocationUpdates ;com.pravera.fl_location.service.LocationDataProviderManager  onError :com.pravera.fl_location.service.LocationPermissionCallback  onResult :com.pravera.fl_location.service.LocationPermissionCallback  Activity 9com.pravera.fl_location.service.LocationPermissionManager  ActivityCompat 9com.pravera.fl_location.service.LocationPermissionManager  Array 9com.pravera.fl_location.service.LocationPermissionManager  'BACKGROUND_LOCATION_PERMISSION_REQ_CODE 9com.pravera.fl_location.service.LocationPermissionManager  Boolean 9com.pravera.fl_location.service.LocationPermissionManager  Build 9com.pravera.fl_location.service.LocationPermissionManager  Context 9com.pravera.fl_location.service.LocationPermissionManager  
ContextCompat 9com.pravera.fl_location.service.LocationPermissionManager  
ErrorCodes 9com.pravera.fl_location.service.LocationPermissionManager  Int 9com.pravera.fl_location.service.LocationPermissionManager  IntArray 9com.pravera.fl_location.service.LocationPermissionManager  LOCATION_PERMISSION_REQ_CODE 9com.pravera.fl_location.service.LocationPermissionManager  LocationPermission 9com.pravera.fl_location.service.LocationPermissionManager  LocationPermissionCallback 9com.pravera.fl_location.service.LocationPermissionManager  Manifest 9com.pravera.fl_location.service.LocationPermissionManager  !PREV_PERMISSION_STATUS_PREFS_NAME 9com.pravera.fl_location.service.LocationPermissionManager  PackageManager 9com.pravera.fl_location.service.LocationPermissionManager  String 9com.pravera.fl_location.service.LocationPermissionManager  SuppressLint 9com.pravera.fl_location.service.LocationPermissionManager  activity 9com.pravera.fl_location.service.LocationPermissionManager  any 9com.pravera.fl_location.service.LocationPermissionManager  arrayOf 9com.pravera.fl_location.service.LocationPermissionManager  callback 9com.pravera.fl_location.service.LocationPermissionManager  checkLocationPermission 9com.pravera.fl_location.service.LocationPermissionManager  context 9com.pravera.fl_location.service.LocationPermissionManager  disposeResources 9com.pravera.fl_location.service.LocationPermissionManager  getLocationPermission 9com.pravera.fl_location.service.LocationPermissionManager  getPrevPermissionStatus 9com.pravera.fl_location.service.LocationPermissionManager  hasPermissionInManifest 9com.pravera.fl_location.service.LocationPermissionManager  indexOf 9com.pravera.fl_location.service.LocationPermissionManager  isEmpty 9com.pravera.fl_location.service.LocationPermissionManager  isPermissionGranted 9com.pravera.fl_location.service.LocationPermissionManager  requestLocationPermission 9com.pravera.fl_location.service.LocationPermissionManager  setPrevPermissionStatus 9com.pravera.fl_location.service.LocationPermissionManager  with 9com.pravera.fl_location.service.LocationPermissionManager  ActivityCompat Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  'BACKGROUND_LOCATION_PERMISSION_REQ_CODE Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  Build Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  Context Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  
ContextCompat Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  
ErrorCodes Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  LOCATION_PERMISSION_REQ_CODE Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  LocationPermission Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  Manifest Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  !PREV_PERMISSION_STATUS_PREFS_NAME Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  PackageManager Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  any Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  arrayOf Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  indexOf Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  isEmpty Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  with Ccom.pravera.fl_location.service.LocationPermissionManager.Companion  	Companion Ccom.pravera.fl_location.service.LocationServicesStatusIntentService  Context Ccom.pravera.fl_location.service.LocationServicesStatusIntentService  Intent Ccom.pravera.fl_location.service.LocationServicesStatusIntentService  JOB_ID Ccom.pravera.fl_location.service.LocationServicesStatusIntentService  #LocationServicesStatusIntentService Ccom.pravera.fl_location.service.LocationServicesStatusIntentService  LocationServicesStatusWatcher Ccom.pravera.fl_location.service.LocationServicesStatusIntentService  LocationServicesUtils Ccom.pravera.fl_location.service.LocationServicesStatusIntentService  applicationContext Ccom.pravera.fl_location.service.LocationServicesStatusIntentService  checkLocationServicesStatus Ccom.pravera.fl_location.service.LocationServicesStatusIntentService  enqueueWork Ccom.pravera.fl_location.service.LocationServicesStatusIntentService  getSharedPreferences Ccom.pravera.fl_location.service.LocationServicesStatusIntentService  java Ccom.pravera.fl_location.service.LocationServicesStatusIntentService  with Ccom.pravera.fl_location.service.LocationServicesStatusIntentService  Context Mcom.pravera.fl_location.service.LocationServicesStatusIntentService.Companion  JOB_ID Mcom.pravera.fl_location.service.LocationServicesStatusIntentService.Companion  #LocationServicesStatusIntentService Mcom.pravera.fl_location.service.LocationServicesStatusIntentService.Companion  LocationServicesStatusWatcher Mcom.pravera.fl_location.service.LocationServicesStatusIntentService.Companion  LocationServicesUtils Mcom.pravera.fl_location.service.LocationServicesStatusIntentService.Companion  checkLocationServicesStatus Mcom.pravera.fl_location.service.LocationServicesStatusIntentService.Companion  enqueueWork Mcom.pravera.fl_location.service.LocationServicesStatusIntentService.Companion  java Mcom.pravera.fl_location.service.LocationServicesStatusIntentService.Companion  with Mcom.pravera.fl_location.service.LocationServicesStatusIntentService.Companion  LocationManager >com.pravera.fl_location.service.LocationServicesStatusReceiver  #LocationServicesStatusIntentService >com.pravera.fl_location.service.LocationServicesStatusReceiver  enqueueWork >com.pravera.fl_location.service.LocationServicesStatusReceiver  java >com.pravera.fl_location.service.LocationServicesStatusReceiver  	Companion =com.pravera.fl_location.service.LocationServicesStatusWatcher  Context =com.pravera.fl_location.service.LocationServicesStatusWatcher  IntentFilter =com.pravera.fl_location.service.LocationServicesStatusWatcher  "LOCATION_SERVICES_STATUS_PREFS_KEY =com.pravera.fl_location.service.LocationServicesStatusWatcher  #LOCATION_SERVICES_STATUS_PREFS_NAME =com.pravera.fl_location.service.LocationServicesStatusWatcher  LocationManager =com.pravera.fl_location.service.LocationServicesStatusWatcher  LocationServicesStatus =com.pravera.fl_location.service.LocationServicesStatusWatcher  LocationServicesStatusReceiver =com.pravera.fl_location.service.LocationServicesStatusWatcher  SharedPreferences =com.pravera.fl_location.service.LocationServicesStatusWatcher  String =com.pravera.fl_location.service.LocationServicesStatusWatcher  Unit =com.pravera.fl_location.service.LocationServicesStatusWatcher  broadcastReceiver =com.pravera.fl_location.service.LocationServicesStatusWatcher  onChangedCallback =com.pravera.fl_location.service.LocationServicesStatusWatcher  ,registerLocationServicesStatusIntentReceiver =com.pravera.fl_location.service.LocationServicesStatusWatcher  &registerSharedPreferenceChangeListener =com.pravera.fl_location.service.LocationServicesStatusWatcher  start =com.pravera.fl_location.service.LocationServicesStatusWatcher  stop =com.pravera.fl_location.service.LocationServicesStatusWatcher  .unregisterLocationServicesStatusIntentReceiver =com.pravera.fl_location.service.LocationServicesStatusWatcher  (unregisterSharedPreferenceChangeListener =com.pravera.fl_location.service.LocationServicesStatusWatcher  with =com.pravera.fl_location.service.LocationServicesStatusWatcher  Context Gcom.pravera.fl_location.service.LocationServicesStatusWatcher.Companion  IntentFilter Gcom.pravera.fl_location.service.LocationServicesStatusWatcher.Companion  "LOCATION_SERVICES_STATUS_PREFS_KEY Gcom.pravera.fl_location.service.LocationServicesStatusWatcher.Companion  #LOCATION_SERVICES_STATUS_PREFS_NAME Gcom.pravera.fl_location.service.LocationServicesStatusWatcher.Companion  LocationManager Gcom.pravera.fl_location.service.LocationServicesStatusWatcher.Companion  LocationServicesStatus Gcom.pravera.fl_location.service.LocationServicesStatusWatcher.Companion  LocationServicesStatusReceiver Gcom.pravera.fl_location.service.LocationServicesStatusWatcher.Companion  broadcastReceiver Gcom.pravera.fl_location.service.LocationServicesStatusWatcher.Companion  with Gcom.pravera.fl_location.service.LocationServicesStatusWatcher.Companion  ActivityResultListener .com.pravera.fl_location.service.PluginRegistry   RequestPermissionsResultListener .com.pravera.fl_location.service.PluginRegistry  getLocationDataProviderManager /com.pravera.fl_location.service.ServiceProvider  getLocationPermissionManager /com.pravera.fl_location.service.ServiceProvider   getLocationServicesStatusWatcher /com.pravera.fl_location.service.ServiceProvider   OnSharedPreferenceChangeListener 1com.pravera.fl_location.service.SharedPreferences  Context com.pravera.fl_location.utils  
ErrorCodes com.pravera.fl_location.utils  ErrorHandleUtils com.pravera.fl_location.utils  EventChannel com.pravera.fl_location.utils  LocationManager com.pravera.fl_location.utils  LocationServicesStatus com.pravera.fl_location.utils  LocationServicesUtils com.pravera.fl_location.utils  
MethodChannel com.pravera.fl_location.utils  	Companion .com.pravera.fl_location.utils.ErrorHandleUtils  
ErrorCodes .com.pravera.fl_location.utils.ErrorHandleUtils  EventChannel .com.pravera.fl_location.utils.ErrorHandleUtils  
MethodChannel .com.pravera.fl_location.utils.ErrorHandleUtils  handleMethodCallError .com.pravera.fl_location.utils.ErrorHandleUtils  handleStreamError .com.pravera.fl_location.utils.ErrorHandleUtils  handleMethodCallError 8com.pravera.fl_location.utils.ErrorHandleUtils.Companion  handleStreamError 8com.pravera.fl_location.utils.ErrorHandleUtils.Companion  	EventSink ;com.pravera.fl_location.utils.ErrorHandleUtils.EventChannel  Result <com.pravera.fl_location.utils.ErrorHandleUtils.MethodChannel  	EventSink *com.pravera.fl_location.utils.EventChannel  	Companion 3com.pravera.fl_location.utils.LocationServicesUtils  Context 3com.pravera.fl_location.utils.LocationServicesUtils  LocationManager 3com.pravera.fl_location.utils.LocationServicesUtils  LocationServicesStatus 3com.pravera.fl_location.utils.LocationServicesUtils  checkLocationServicesStatus 3com.pravera.fl_location.utils.LocationServicesUtils  Context =com.pravera.fl_location.utils.LocationServicesUtils.Companion  LocationManager =com.pravera.fl_location.utils.LocationServicesUtils.Companion  LocationServicesStatus =com.pravera.fl_location.utils.LocationServicesUtils.Companion  checkLocationServicesStatus =com.pravera.fl_location.utils.LocationServicesUtils.Companion  Result +com.pravera.fl_location.utils.MethodChannel  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  addActivityResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  #addRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  removeActivityResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  &removeRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  error /io.flutter.plugin.common.EventChannel.EventSink  success /io.flutter.plugin.common.EventChannel.EventSink  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  ActivityResultListener 'io.flutter.plugin.common.PluginRegistry   RequestPermissionsResultListener 'io.flutter.plugin.common.PluginRegistry  Class 	java.lang  	Exception 	java.lang  
simpleName java.lang.Class  Array kotlin  Enum kotlin  	Function1 kotlin  IntArray kotlin  Nothing kotlin  apply kotlin  arrayOf kotlin  
isInitialized kotlin  let kotlin  toString kotlin  with kotlin  hashCode 
kotlin.Any  toString 
kotlin.Any  indexOf kotlin.Array  not kotlin.Boolean  ACTIVITY_NOT_ATTACHED kotlin.Enum  BALANCED kotlin.Enum  BEST kotlin.Enum  	Companion kotlin.Enum  HIGH kotlin.Enum  Int kotlin.Enum  %LOCATION_PERMISSION_REQUEST_CANCELLED kotlin.Enum  LOCATION_SERVICES_NOT_AVAILABLE kotlin.Enum  LOCATION_SETTINGS_CHANGE_FAILED kotlin.Enum  LOW kotlin.Enum  LocationAccuracy kotlin.Enum  
NAVIGATION kotlin.Enum  
POWER_SAVE kotlin.Enum  Priority kotlin.Enum  String kotlin.Enum  ACTIVITY_NOT_ATTACHED kotlin.Enum.Companion  BALANCED kotlin.Enum.Companion  BEST kotlin.Enum.Companion  HIGH kotlin.Enum.Companion  %LOCATION_PERMISSION_REQUEST_CANCELLED kotlin.Enum.Companion  LOCATION_SERVICES_NOT_AVAILABLE kotlin.Enum.Companion  LOCATION_SETTINGS_CHANGE_FAILED kotlin.Enum.Companion  LOW kotlin.Enum.Companion  
NAVIGATION kotlin.Enum.Companion  
POWER_SAVE kotlin.Enum.Companion  Priority kotlin.Enum.Companion  toDouble kotlin.Float  invoke kotlin.Function1  	compareTo 
kotlin.Int  get kotlin.IntArray  isEmpty kotlin.IntArray  toDouble kotlin.Long  
toFloatOrNull 
kotlin.String  toIntOrNull 
kotlin.String  toLongOrNull 
kotlin.String  ByteIterator kotlin.collections  CharIterator kotlin.collections  Iterator kotlin.collections  Map kotlin.collections  MutableCollection kotlin.collections  MutableIterator kotlin.collections  
MutableMap kotlin.collections  any kotlin.collections  get kotlin.collections  indexOf kotlin.collections  isEmpty kotlin.collections  iterator kotlin.collections  mutableMapOf kotlin.collections  set kotlin.collections  toString kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  Entry kotlin.collections.Map  get kotlin.collections.Map  iterator $kotlin.collections.MutableCollection  iterator "kotlin.collections.MutableIterator  MutableEntry kotlin.collections.MutableMap  clear kotlin.collections.MutableMap  get kotlin.collections.MutableMap  remove kotlin.collections.MutableMap  set kotlin.collections.MutableMap  values kotlin.collections.MutableMap  iterator 	kotlin.io  java 
kotlin.jvm  KMutableProperty0 kotlin.reflect  java kotlin.reflect.KClass  
isInitialized  kotlin.reflect.KMutableProperty0  any kotlin.sequences  indexOf kotlin.sequences  iterator kotlin.sequences  
MatchGroup kotlin.text  any kotlin.text  get kotlin.text  indexOf kotlin.text  isEmpty kotlin.text  iterator kotlin.text  set kotlin.text  
toFloatOrNull kotlin.text  toIntOrNull kotlin.text  toLongOrNull kotlin.text  toString kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  