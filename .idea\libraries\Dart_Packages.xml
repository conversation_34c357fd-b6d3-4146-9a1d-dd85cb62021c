<component name="libraryTable">
  <library name="Dart Packages" type="DartPackagesLibraryType">
    <properties>
      <option name="packageNameToDirsMap">
        <entry key="_flutterfire_internals">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.40/lib" />
            </list>
          </value>
        </entry>
        <entry key="archive">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/archive-3.6.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="args">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/args-2.6.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="async">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/async-2.11.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="audio_session">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/audio_session-0.1.23/lib" />
            </list>
          </value>
        </entry>
        <entry key="barcode">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/barcode-2.2.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="bidi">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/bidi-2.0.12/lib" />
            </list>
          </value>
        </entry>
        <entry key="bloc">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/bloc-8.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="boolean_selector">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/boolean_selector-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/cached_network_image-3.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/cached_network_image_web-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="cart_stepper">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/cart_stepper-4.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="characters">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/characters-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="chewie">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/chewie-1.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="clock">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/clock-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="collection">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/collection-1.19.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="convert">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/convert-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cross_file">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="crypto">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/crypto-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="csslib">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/csslib-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cupertino_icons">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="dbus">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/dbus-0.7.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/device_info_plus-10.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="dots_indicator">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/dots_indicator-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="dotted_border">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/dotted_border-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="dropdown_button2">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/dropdown_button2-2.3.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="easy_localization">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/easy_localization-3.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="easy_logger">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/easy_logger-0.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="equatable">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/equatable-2.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="excel">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/excel-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="extended_image">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/extended_image-8.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="extended_image_library">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/extended_image_library-4.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="fake_async">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fake_async-1.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="faker">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/faker-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="ffi">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/ffi-2.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="file">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/file-7.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_linux">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_macos">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+3/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_core">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_core-3.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_core_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_core_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_core_web-2.17.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_messaging">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_messaging-15.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_messaging_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.42/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_messaging_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_messaging_web-3.8.12/lib" />
            </list>
          </value>
        </entry>
        <entry key="fixnum">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fixnum-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="fl_location">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fl_location-4.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="fl_location_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fl_location_platform_interface-5.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="fl_location_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fl_location_web-4.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_bloc">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_bloc-8.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_cache_manager">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_contacts">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_contacts-1.1.9+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_dotenv">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_driver">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_driver/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_hooks">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_hooks-0.20.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_html">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_html-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview-6.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_internal_annotations">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_ios">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_macos">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_web-1.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_keyboard_visibility">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_keyboard_visibility_linux">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_keyboard_visibility_macos">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_keyboard_visibility_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_platform_interface-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_keyboard_visibility_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_keyboard_visibility_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_lints">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_lints-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_localizations">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_localizations/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_plugin_android_lifecycle">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.24/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_slidable">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_slidable-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_svg">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_svg-2.0.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_test">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_web_plugins">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_web_plugins/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_widget_from_html">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_widget_from_html-0.15.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_widget_from_html_core">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_widget_from_html_core-0.15.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="fuchsia_remote_debug_protocol">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/fuchsia_remote_debug_protocol/lib" />
            </list>
          </value>
        </entry>
        <entry key="fwfh_cached_network_image">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fwfh_cached_network_image-0.14.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="fwfh_chewie">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fwfh_chewie-0.14.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="fwfh_just_audio">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fwfh_just_audio-0.15.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="fwfh_svg">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fwfh_svg-0.8.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="fwfh_url_launcher">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fwfh_url_launcher-0.9.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="fwfh_webview">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fwfh_webview-0.15.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="geocoding">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geocoding-2.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="geocoding_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geocoding_android-3.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="geocoding_ios">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geocoding_ios-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="geocoding_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="geolocator">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geolocator-9.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="geolocator_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geolocator_android-4.6.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="geolocator_apple">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geolocator_apple-2.3.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="geolocator_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="geolocator_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geolocator_web-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="geolocator_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geolocator_windows-0.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="go_router">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/go_router-14.6.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_maps">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps-8.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_maps_flutter">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter-2.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_maps_flutter_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_android-2.14.11/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_maps_flutter_ios">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_maps_flutter_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.9.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_maps_flutter_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_web-0.5.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="html">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/html-0.15.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="http">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/http-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_client_helper">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/http_client_helper-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_parser">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/http_parser-4.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="image">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image-4.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_gallery_saver">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_gallery_saver-2.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+18/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_for_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_ios">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_linux">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_macos">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="integration_test">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/integration_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="intl">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/intl-0.19.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="introduction_screen">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/introduction_screen-3.1.14/lib" />
            </list>
          </value>
        </entry>
        <entry key="js">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/js-0.6.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="just_audio">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/just_audio-0.9.42/lib" />
            </list>
          </value>
        </entry>
        <entry key="just_audio_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/just_audio_platform_interface-4.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="just_audio_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/just_audio_web-0.4.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/leak_tracker-10.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_flutter_testing">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_testing">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="lints">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/lints-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="list_counter">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/list_counter-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="logging">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/logging-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="lottie">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/lottie-3.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="mask_text_input_formatter">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/mask_text_input_formatter-2.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="matcher">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/matcher-0.12.16+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_color_utilities">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="meta">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/meta-1.15.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="mime">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/mime-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="nested">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/nested-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="octo_image">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/octo_image-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="open_filex">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/open_filex-4.6.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/package_info_plus-8.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-3.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path-1.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_drawing">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_drawing-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_parsing">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_parsing-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider-2.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_android-2.2.15/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_foundation">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_linux">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="pdf">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/pdf-3.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler-11.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler_android-12.0.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_apple">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_html">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="petitparser">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/petitparser-6.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="pinput">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/pinput-5.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="platform">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/platform-3.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="plugin_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="pool">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/pool-1.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="process">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/process-5.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="provider">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/provider-6.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="qr">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/qr-3.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="qr_code_scanner">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/qr_code_scanner-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="qr_flutter">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/qr_flutter-4.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="rxdart">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/rxdart-0.28.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sanitize_html">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sanitize_html-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences-2.3.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_foundation">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_linux">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shelf-1.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="signalr_core">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/signalr_core-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="sky_engine">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/bin/cache/pkg/sky_engine/lib" />
            </list>
          </value>
        </entry>
        <entry key="smooth_page_indicator">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_span">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/source_span-1.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sprintf">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sprintf-7.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sqflite-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sqflite_android-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_common">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_darwin">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sse">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sse-4.1.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="sse_channel">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sse_channel-0.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="stack_trace">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/stack_trace-1.12.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_channel">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/stream_channel-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_transform">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/stream_transform-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="string_scanner">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/string_scanner-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sync_http">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sync_http-0.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_flutter_charts">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_flutter_core">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_flutter_datepicker">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_datepicker-26.2.14/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_flutter_pdf">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_pdf-26.2.14/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_flutter_pdfviewer">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_pdfviewer-26.2.14/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_flutter_signaturepad">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_signaturepad-26.2.14/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_pdfviewer_macos">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_pdfviewer_macos-26.2.14/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_pdfviewer_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_pdfviewer_platform_interface-26.2.14/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_pdfviewer_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_pdfviewer_web-26.2.14/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_pdfviewer_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_pdfviewer_windows-26.2.14/lib" />
            </list>
          </value>
        </entry>
        <entry key="synchronized">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/synchronized-3.3.0+3/lib" />
            </list>
          </value>
        </entry>
        <entry key="term_glyph">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/term_glyph-1.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="test_api">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/test_api-0.7.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="timeline_tile">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/timeline_tile-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="tuple">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/tuple-2.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="typed_data">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/typed_data-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="universal_platform">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/universal_platform-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher-6.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_android-6.3.14/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_ios">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_linux">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_macos">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_web-2.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="uuid">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/uuid-4.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/vector_graphics-1.1.15/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_codec">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/vector_graphics_codec-1.1.12/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_compiler">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_math">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/vector_math-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player-2.9.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player_android-2.7.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_avfoundation">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player_avfoundation-2.6.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player_platform_interface-6.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player_web-2.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="vm_service">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/vm_service-14.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="wakelock_plus">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/wakelock_plus-1.2.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="wakelock_plus_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/web-0.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket_channel">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/web_socket_channel-2.4.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="webdriver">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/webdriver-3.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/webview_flutter-4.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/webview_flutter_android-4.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/webview_flutter_platform_interface-2.11.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter_wkwebview">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/webview_flutter_wkwebview-3.20.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/win32-5.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32_registry">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/win32_registry-1.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="xdg_directories">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xml">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/xml-6.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="youtube_player_flutter">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/youtube_player_flutter-9.0.4/lib" />
            </list>
          </value>
        </entry>
      </option>
    </properties>
    <CLASSES>
      <root url="file://$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/bin/cache/pkg/sky_engine/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_driver/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_localizations/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_test/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/flutter_web_plugins/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/fuchsia_remote_debug_protocol/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter SDK/flutter_windows_3.27.1-stable/flutter/packages/integration_test/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.40/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/archive-3.6.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/args-2.6.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/async-2.11.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/audio_session-0.1.23/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/barcode-2.2.8/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/bidi-2.0.12/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/bloc-8.1.4/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/boolean_selector-2.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/cached_network_image-3.4.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/cached_network_image_web-1.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/cart_stepper-4.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/characters-1.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/chewie-1.10.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/clock-1.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/collection-1.19.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/convert-3.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/crypto-3.0.6/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/csslib-1.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/dbus-0.7.10/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/device_info_plus-10.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/dots_indicator-2.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/dotted_border-2.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/dropdown_button2-2.3.9/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/easy_localization-3.0.7/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/easy_logger-0.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/equatable-2.0.7/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/excel-2.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/extended_image-8.3.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/extended_image_library-4.0.5/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fake_async-1.3.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/faker-2.2.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/ffi-2.1.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/file-7.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_core-3.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_core_web-2.17.5/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_messaging-15.0.4/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.42/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/firebase_messaging_web-3.8.12/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fixnum-1.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fl_location-4.4.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fl_location_platform_interface-5.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fl_location_web-4.2.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_bloc-8.1.6/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_contacts-1.1.9+2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_hooks-0.20.5/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_html-3.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview-6.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_inappwebview_web-1.0.8/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_platform_interface-2.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_lints-4.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.24/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_slidable-3.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_svg-2.0.16/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_widget_from_html-0.15.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/flutter_widget_from_html_core-0.15.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fwfh_cached_network_image-0.14.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fwfh_chewie-0.14.8/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fwfh_just_audio-0.15.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fwfh_svg-0.8.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fwfh_url_launcher-0.9.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/fwfh_webview-0.15.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geocoding-2.2.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geocoding_android-3.3.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geocoding_ios-2.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geolocator-9.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geolocator_android-4.6.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geolocator_apple-2.3.13/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geolocator_web-2.2.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/geolocator_windows-0.1.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/go_router-14.6.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps-8.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter-2.10.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_android-2.14.11/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.9.5/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/google_maps_flutter_web-0.5.10/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/html-0.15.5/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/http-1.2.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/http_client_helper-3.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/http_parser-4.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image-4.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_gallery_saver-2.0.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker-1.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+18/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/intl-0.19.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/introduction_screen-3.1.14/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/js-0.6.7/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/just_audio-0.9.42/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/just_audio_platform_interface-4.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/just_audio_web-0.4.13/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/leak_tracker-10.0.7/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.8/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/lints-4.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/list_counter-1.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/logging-1.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/lottie-3.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/mask_text_input_formatter-2.9.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/matcher-0.12.16+1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/meta-1.15.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/mime-2.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/nested-1.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/octo_image-2.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/open_filex-4.6.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/package_info_plus-8.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-3.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path-1.9.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_drawing-1.0.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_parsing-1.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider-2.1.5/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_android-2.2.15/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/pdf-3.11.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler-11.3.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler_android-12.0.13/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.5/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.2.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/petitparser-6.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/pinput-5.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/platform-3.1.5/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/pool-1.5.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/process-5.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/provider-6.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/qr-3.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/qr_code_scanner-1.0.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/qr_flutter-4.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/rxdart-0.28.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sanitize_html-2.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences-2.3.4/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/shelf-1.4.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/signalr_core-1.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/source_span-1.10.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sprintf-7.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sqflite-2.4.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sqflite_android-2.4.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sse-4.1.7/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sse_channel-0.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/stack_trace-1.12.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/stream_channel-2.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/stream_transform-2.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/string_scanner-1.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/sync_http-0.3.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_charts-26.2.14/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_core-26.2.14/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_datepicker-26.2.14/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_pdf-26.2.14/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_pdfviewer-26.2.14/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_flutter_signaturepad-26.2.14/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_pdfviewer_macos-26.2.14/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_pdfviewer_platform_interface-26.2.14/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_pdfviewer_web-26.2.14/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/syncfusion_pdfviewer_windows-26.2.14/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/synchronized-3.3.0+3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/term_glyph-1.2.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/test_api-0.7.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/timeline_tile-2.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/tuple-2.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/typed_data-1.4.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/universal_platform-1.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher-6.3.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_android-6.3.14/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_web-2.3.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/uuid-4.5.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/vector_graphics-1.1.15/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/vector_graphics_codec-1.1.12/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/vector_graphics_compiler-1.1.16/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/vector_math-2.1.4/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player-2.9.5/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player_android-2.7.16/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player_avfoundation-2.6.5/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player_platform_interface-6.2.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/video_player_web-2.3.3/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/vm_service-14.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/wakelock_plus-1.2.10/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.2/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/web-0.5.1/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/web_socket_channel-2.4.5/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/webdriver-3.0.4/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/webview_flutter-4.10.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/webview_flutter_android-4.2.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/webview_flutter_platform_interface-2.11.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/webview_flutter_wkwebview-3.20.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/win32-5.9.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/win32_registry-1.1.5/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/xml-6.5.0/lib" />
      <root url="file://$PROJECT_DIR$/../../flutter_pub/Cache/hosted/pub.dev/youtube_player_flutter-9.0.4/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>