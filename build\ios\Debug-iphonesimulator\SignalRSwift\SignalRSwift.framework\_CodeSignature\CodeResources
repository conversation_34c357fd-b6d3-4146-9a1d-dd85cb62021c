<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/SignalRSwift-Swift.h</key>
		<data>
		Rif7RQ5MRPOG1t3OHEVyKPA6il0=
		</data>
		<key>Headers/SignalRSwift-umbrella.h</key>
		<data>
		8mHz7ntLnGEc9+jG5lKXGSMcA6g=
		</data>
		<key>Info.plist</key>
		<data>
		AVHtyeYjry1IXWjMdm/CRIkqVzc=
		</data>
		<key>Modules/SignalRSwift.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		kfx0rnZ4XJBTLZilCgfLr3xo1hw=
		</data>
		<key>Modules/SignalRSwift.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		R61WqKmjTR22WUHlMKo9iToodPA=
		</data>
		<key>Modules/SignalRSwift.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/SignalRSwift.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		89Uk+/khXli4uR8jL/QmO4OIEIE=
		</data>
		<key>Modules/SignalRSwift.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		y0ImBK5MPR7qxJAywndzPGApTDg=
		</data>
		<key>Modules/SignalRSwift.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/SignalRSwift.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		56833H2T4t3KE09mfENRbwANK6Q=
		</data>
		<key>Modules/SignalRSwift.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		fVKnDFeUyo6P/Fp3Drc6GZYB950=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		9Ott6MjJwDBZTzz6i7ZRvq89mP4=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/SignalRSwift-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			Rif7RQ5MRPOG1t3OHEVyKPA6il0=
			</data>
			<key>hash2</key>
			<data>
			XZdFu4DPmBALQ6r+umYISjk6pCf04vY0NUngJFJjISU=
			</data>
		</dict>
		<key>Headers/SignalRSwift-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			8mHz7ntLnGEc9+jG5lKXGSMcA6g=
			</data>
			<key>hash2</key>
			<data>
			61jq9TBmkZkkttNex4+ddNx+h3Dx+4qB/OCzLS6P1w4=
			</data>
		</dict>
		<key>Modules/SignalRSwift.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			kfx0rnZ4XJBTLZilCgfLr3xo1hw=
			</data>
			<key>hash2</key>
			<data>
			FohSr2oRfFOFv5ezSjed5TFc1UhvWcBzDjbBmP300hc=
			</data>
		</dict>
		<key>Modules/SignalRSwift.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			R61WqKmjTR22WUHlMKo9iToodPA=
			</data>
			<key>hash2</key>
			<data>
			VpCFVnQsExue3KyTPoGotpiQwCVokJyIwdo+JJh8qRo=
			</data>
		</dict>
		<key>Modules/SignalRSwift.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/SignalRSwift.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			89Uk+/khXli4uR8jL/QmO4OIEIE=
			</data>
			<key>hash2</key>
			<data>
			DgH1Gc4Nx71VS7yWhdqdFqvzqlT5sBlJgkUiy/nPpGI=
			</data>
		</dict>
		<key>Modules/SignalRSwift.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			y0ImBK5MPR7qxJAywndzPGApTDg=
			</data>
			<key>hash2</key>
			<data>
			oL6PjGTQhoGdeFZLPSNlY7oVbZ88bkObaT8MN3K63P4=
			</data>
		</dict>
		<key>Modules/SignalRSwift.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/SignalRSwift.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			56833H2T4t3KE09mfENRbwANK6Q=
			</data>
			<key>hash2</key>
			<data>
			vV2EFJHAUGEijOza+GbGBq3qUdpaKIbqbk2QMEX9xk0=
			</data>
		</dict>
		<key>Modules/SignalRSwift.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			fVKnDFeUyo6P/Fp3Drc6GZYB950=
			</data>
			<key>hash2</key>
			<data>
			mt3nr1tT3swyB/G70et2FRN1F/HSJiDe4QRRStujZ28=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			9Ott6MjJwDBZTzz6i7ZRvq89mP4=
			</data>
			<key>hash2</key>
			<data>
			miCIxZfcE16cFCzcerstyAqIcMqQDZTN7bEI8JN2mKo=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
