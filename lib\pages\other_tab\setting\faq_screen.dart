import 'package:flutter/material.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../constants/index.dart';
import '../../../core/index.dart';
import 'faq_detail_screen.dart';

class FAQScreen extends StatefulWidget {
  const FAQScreen({super.key});

  @override
  State<FAQScreen> createState() => FAQScreenState();
}

class FAQScreenState extends State<FAQScreen> {
  bool _isLoading = true;
  List<FAQTopic> _faqTopics = [];
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchFAQTopics();
  }

  Future<void> _fetchFAQTopics() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Get language preference from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final isEnglish = prefs.getBool('isEnglish') ?? false;
      final lang = isEnglish ? 'en' : 'vi';

      final result = await RepositoryProvider.of<Api>(context).other.getFAQTopics(
        lang: lang,
      );

      if (result != null ) {
        final List<dynamic> topicsData = result.data['content'] ?? [];
        setState(() {
          _faqTopics = topicsData.map((item) => FAQTopic.fromJson(item)).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
          _error = 'Failed to load FAQ topics';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
        title: 'FAQ',
        titleStyle: const TextStyle(color: Colors.white, fontSize: CFontSize.xl2),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(_error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchFAQTopics,
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    if (_faqTopics.isEmpty) {
      return const Center(child: Text('Không có câu hỏi thường gặp'));
    }

    return ListView.separated(
      itemCount: _faqTopics.length,
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final topic = _faqTopics[index];
        return _buildTopicItem(topic);
      },
    );
  }

  Widget _buildTopicItem(FAQTopic topic) {
    return ListTile(
      title: Text(
        topic.title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          fontFamily: 'SourceSansPro'
        ),
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => FAQDetailScreen(
              topicId: topic.id,
              topicTitle: topic.title,
            ),
          ),
        );
      },
    );
  }
}

class FAQTopic {
  final String id;
  final String title;
  final String content;

  FAQTopic({
    required this.id,
    required this.title,
    this.content = '',
  });

  factory FAQTopic.fromJson(Map<String, dynamic> json) {
    return FAQTopic(
      id: json['FeedbackThreadId'] ?? '',
      title: json['Title'] ?? '',
      content: json['Content'] ?? '',
    );
  }
}
