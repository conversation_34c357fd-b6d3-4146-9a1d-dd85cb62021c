# WebView Implementation Comparison: Android vs Flutter

## Overview
This document compares the WebView implementation between the native Android repository and the Flutter repository to identify differences and ensure consistency.

## 1. WebView Configuration

### Android Repository (BookingPaymentFragment.kt)
```kotlin
// WebView Settings
webView?.settings?.javaScriptEnabled = true
webView.settings.loadWithOverviewMode = true
webView.settings.useWideViewPort = true
webView?.settings?.domStorageEnabled = true
webView.scrollBarStyle = WebView.SCROLLBARS_OUTSIDE_OVERLAY
webView.isScrollbarFadingEnabled = false

// JavaScript Interface
webView.addJavascriptInterface(this@BookingPaymentFragment, "androidkit")

// Load HTML
webView?.loadDataWithBaseURL("${BuildConfig.BASE_URL}Booking", content, "text/html", "utf-8", null)
```

### Flutter Repository (webview_payment.dart)
```dart
// WebView Settings
_webViewController = WebViewController.fromPlatformCreationParams(params)
  ..setJavaScriptMode(JavaScriptMode.unrestricted)
  ..setBackgroundColor(const Color(0x00000000))
  ..enableZoom(false)
  ..setUserAgent('Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36')

// JavaScript Channel
..addJavaScriptChannel('Flutter', onMessageReceived: (JavaScriptMessage message) {
  _handleJavaScriptMessage(message.message);
})

// Load HTML
..loadHtmlString(widget.htmlData, baseUrl: widget.baseUrl);
```

### ❌ **Differences Found:**
1. **JavaScript Interface Name**: Android uses `"androidkit"`, Flutter uses `"Flutter"`
2. **User Agent**: Flutter sets custom user agent, Android uses default
3. **Zoom**: Flutter disables zoom, Android doesn't specify
4. **DOM Storage**: Android explicitly enables DOM storage, Flutter doesn't

## 2. JavaScript Data Injection

### Android Repository
```kotlin
// Booking Info Injection (onPageFinished)
val jsGetBookingInfo = "var bookingIf = {};" +
    " bookingIf.FilmName = \"${showTimeModel?.FilmName ?: ""}\";\n" +
    "    bookingIf.FilmInfo = \"${showTimeModel?.FilmFormatName ?: ""} | ${filmModel?.FilmGenreName} |${filmModel?.Duration ?: 0} ${R.string.minute.getString()}\";\n" +
    "    bookingIf.CinemaName = \"${showTimeModel?.TenRap ?: ""}\";\n" +
    "    bookingIf.DateShow = \"${showTimeModel?.NgayChieu?.dateConvertFormat(...)}\";\n" +
    "    bookingIf.ShowTime = \"${showTimeModel?.GioChieu?.dateConvertFormat(...)}\";\n" +
    "    bookingIf.Combo = \"$filmCombo\";\n" +
    "    bookingIf.TotalMoney = $totalPrice;\n" +
    "    bookingIf.Screen = \"${showTimeModel?.Screen?.Code ?: ""}\";\n" +
    "    bookingIf.FilmPoster = \"${filmModel?.MainPosterUrl?.toImageUrl() ?: ""}\";" +
    "getBookingInfo(bookingIf);"

// Customer Info Injection
val jsUser = "var cusI = {};" +
    "cusI.customerId = '${Global.share().user?.AccountId ?: ""}';\n" +
    "    cusI.customerCard = '${Global.share().user?.CardNumber ?: ""}';" +
    "getCustomerInfo(cusI);"
```

### Flutter Repository
```dart
// Booking Info Injection (_loadBookingInfo)
final method = "getBookingInfo({'FilmName': '${film.getName()}', "
    "'FilmInfo': '${film.getName()} | ${film.FilmGenreName ?? ""} | ${film.Duration ?? 0} phút', "
    "'CinemaName': '${listSeat.tenRap ?? ""}', "
    "'DateShow': '$dateStr', "
    "'ShowTime': '$timeStr', "
    "'Combo': '$combo', "
    "'TotalMoney': '${_formatCurrency(widget.totalPrice ?? 0)}', "
    "'Screen': '${listSeat.phongChieu ?? ""}', "
    "'FilmPoster': '${ApiService.baseUrlImage}${film.MainPosterUrl ?? ""}', "
    "'FilmFormatCode': '${listSeat.filmFormatCode ?? ""}'});";

// Customer Info Injection
final customerInfoMethod = "getCustomerInfo({'customerId': '${user.accountId ?? ""}', 'customerCard': '${user.cardNumber ?? ""}'});";
```

### ❌ **Differences Found:**
1. **Variable Names**: Android uses `bookingIf`, Flutter uses direct object
2. **Data Format**: Android uses separate variable assignment, Flutter uses direct object notation
3. **Currency Format**: Android uses raw number, Flutter uses formatted currency
4. **Film Info Format**: Different concatenation patterns
5. **Property Names**: Some property names differ (e.g., `TenRap` vs `tenRap`)

## 3. URL Handling & Payment Methods

### Android Repository
```kotlin
// Payment Method Detection
if (request.url?.host?.contains(Constant.AirPay.fullHost) == true) {
    getAirPayOrderId(request.url.toString())
    presenter.trackingConfirm("airpay")
    openLink(request.url.toString())
    return true
}
if (request.url?.host?.contains(Constant.Momo.fullHost) == true) {
    presenter.trackingConfirm("momo")
    openLink(request.url.toString())
    return true
}
// Similar for ZaloPay, NoiDia, QuocTe
```

### Flutter Repository
```dart
// Payment Method Detection
if (url.contains('airpay.vn') && await canLaunchUrl(Uri.parse(url))) {
  widget.onPaymentMethodSelected('airpay');
  _trackPayment('confirm');
  _launchExternalUrl(url);
  return NavigationDecision.prevent;
} else if (url.contains('payment.momo') && await canLaunchUrl(Uri.parse(url))) {
  widget.onPaymentMethodSelected('momo');
  _trackPayment('confirm');
  _launchExternalUrl(url);
  return NavigationDecision.prevent;
}
// Similar for ZaloPay, NoiDia, QuocTe
```

### ✅ **Similarities:**
- Both handle the same payment methods (AirPay, MoMo, ZaloPay, NoiDia, QuocTe)
- Both track payment confirmation
- Both launch external URLs for payment

### ❌ **Differences Found:**
1. **URL Detection**: Android uses `Constant` values, Flutter uses hardcoded strings
2. **Method Names**: Android uses `trackingConfirm()`, Flutter uses `_trackPayment('confirm')`
3. **External Launch**: Android uses `openLink()`, Flutter uses `_launchExternalUrl()`

## 4. JavaScript Message Handling

### Android Repository
```kotlin
@JavascriptInterface
fun postMessage(value: String?) {
    when (value?.replace("\"", "")) {
        "policy" -> openFragment(PaymentPolicyFragment())
        "payment_success" -> {
            presenter.trackingPaySuccess()
            showPaymentSuccess()
        }
        "awaiting_payment" -> showPaymentAwaiting()
        "payment_failed" -> {
            presenter.trackingPayFail("payment_failed", getString(R.string.payment_failed))
            showNotice(getString(R.string.payment_failed))
        }
        "booking_seat_failed" -> {
            presenter.trackingPayFail("booking_seat_failed", getString(R.string.payment_failed))
            showNotice(getString(R.string.payment_failed))
        }
        "timeout" -> (activity as? HomeActivity)?.checkCountDown()
    }
}
```

### Flutter Repository
```dart
void _handleJavaScriptMessage(String message) {
  try {
    // Try to parse as JSON first
    final jsonData = jsonDecode(message);
    final type = jsonData['type'];

    if (type == 'payment_success' || type == 'payment_susccess') {
      _trackPayment('success');
      _getTransactionId();
    } else if (type == 'payment_failed') {
      final String errorMsg = jsonData['message'] ?? 'Thanh toán thất bại';
      _trackPayment('fail', jsonData['code'], errorMsg);
      widget.onPaymentFailed(errorMsg);
    }
    // ... other cases
  } catch (e) {
    // Handle as string
    if (message == 'payment_success') {
      _trackPayment('success');
      _getTransactionId();
    } else if (message == 'payment_failed') {
      widget.onPaymentFailed('Thanh toán thất bại');
    }
    // ... other cases
  }
}
```

### ❌ **Differences Found:**
1. **Message Format**: Android expects simple strings, Flutter handles both JSON and strings
2. **Method Names**: Different callback method names
3. **Error Handling**: Flutter has more sophisticated error handling
4. **Timeout Handling**: Android has timeout handling, Flutter doesn't

## 5. Back Button Handling

### Android Repository
```kotlin
private fun getCurrentType(handler: ((String) -> Unit)? = null) {
    webView.evaluateJavascript("screenType") { value ->
        when (value?.replace("\"", "")) {
            "voucher", "coupon", "beta-point" -> loadJs("backToMain();")
            "payment" -> super.back()
            else -> {
                if (webView?.canGoBack() == true) {
                    webView?.goBack()
                } else {
                    super.back()
                }
            }
        }
    }
}
```

### Flutter Repository
```dart
Future<bool> _handleBackPress() async {
  try {
    final screenType = await _webViewController.runJavaScriptReturningResult("screenType");
    final screenTypeStr = screenType.toString().replaceAll('"', '');

    if (screenTypeStr == "voucher" || screenTypeStr == "coupon" || screenTypeStr == "beta-point") {
      await _webViewController.runJavaScript("backToMain();");
      return false;
    }

    if (await _webViewController.canGoBack()) {
      _webViewController.goBack();
      return false;
    } else {
      // Show confirmation dialog
      bool shouldPop = false;
      await UDialog().showConfirm(
        title: 'Hủy thanh toán',
        text: 'Bạn có chắc chắn muốn hủy thanh toán không?',
        // ... dialog configuration
      );
      return shouldPop;
    }
  } catch (e) {
    return true;
  }
}
```

### ❌ **Differences Found:**
1. **Confirmation Dialog**: Flutter shows confirmation dialog, Android doesn't
2. **Error Handling**: Flutter has try-catch, Android doesn't
3. **Return Logic**: Different return value handling

## Summary of Key Differences

### 🔴 **Critical Differences:**
1. **JavaScript Interface Name**: `"androidkit"` vs `"Flutter"`
2. **Data Injection Format**: Variable assignment vs direct object
3. **Message Handling**: Simple strings vs JSON + strings
4. **Back Button**: No confirmation vs confirmation dialog

### 🟡 **Minor Differences:**
1. **WebView Settings**: Some settings missing in Flutter
2. **User Agent**: Custom vs default
3. **Property Names**: Case differences
4. **Method Names**: Different naming conventions

## 6. Transaction Status Checking

### Android Repository
```kotlin
// AirPay Status Check
private fun checkAirPayOrderStatus(orderId: String?) {
    orderId?.let {
        loadJs("checkShopeePayTransactionStatus('$it');")
    }
}

// MoMo Status Check (via BroadcastReceiver)
if (intent.action == Constant.INTENT_FILTER_MOMO) {
    val orderId = intent.getStringExtra(Constant.Momo.orderId)
    val resultCode = intent.getStringExtra(Constant.Momo.resultCode)
    // ... other parameters
    loadJs("checkMomoTransactionStatus('$orderId', '$resultCode', ...);")
}

// ZaloPay Status Check (via BroadcastReceiver)
if (intent.action == Constant.INTENT_FILTER_ZALOPAY) {
    val appTransId = intent.getStringExtra(Constant.ZaloPay.appTransId)
    // ... other parameters
    loadJs("checkZaloPayTransactionStatus('$appId', '$appTransId', ...);")
}
```

### Flutter Repository
```dart
// Transaction ID Retrieval
void _getTransactionId() {
  _webViewController
      .runJavaScriptReturningResult("getTransactionId();")
      .then((result) {
    if (result is String) {
      widget.onPaymentSuccess(result);
    } else {
      widget.onPaymentSuccess(null);
    }
  }).catchError((error) {
    widget.onPaymentSuccess(null);
  });
}

// No equivalent BroadcastReceiver mechanism
// Payment status checking is handled differently
```

### ❌ **Major Difference:**
- **Android**: Uses BroadcastReceiver for real-time payment status updates from external apps
- **Flutter**: Relies on WebView JavaScript callbacks only
- **Android**: Has specific status checking methods for each payment provider
- **Flutter**: Generic transaction ID retrieval

## 7. WebView Lifecycle Management

### Android Repository
```kotlin
override fun onResume() {
    checkAirPayOrderStatus(airPayOrderId)
    super.onResume()
}

override fun onDestroyView() {
    super.onDestroyView()
    activity?.unregisterReceiver(receiver)
    webView?.destroy()
}

// BroadcastReceiver Registration
val intentFilter = IntentFilter()
intentFilter.addAction(Constant.INTENT_FILTER_MOMO)
activity?.registerReceiver(receiver, intentFilter)
```

### Flutter Repository
```dart
@override
void initState() {
  super.initState();
  _initWebView();
}

@override
void dispose() {
  // No specific WebView cleanup
  super.dispose();
}

// No BroadcastReceiver equivalent
```

### ❌ **Critical Missing Features in Flutter:**
1. **No BroadcastReceiver**: Cannot receive payment status from external apps
2. **No onResume handling**: Cannot check payment status when returning from external apps
3. **No WebView cleanup**: Potential memory leaks
4. **No receiver unregistration**: No cleanup of system resources

## 8. Console Logging & Debugging

### Android Repository
```kotlin
inner class BetaChromeClient : WebChromeClient() {
    override fun onConsoleMessage(consoleMessage: ConsoleMessage): Boolean {
        logD(consoleMessage.message())
        return super.onConsoleMessage(consoleMessage)
    }

    override fun onJsAlert(view: WebView?, url: String?, message: String?, result: JsResult?): Boolean {
        result?.cancel()
        showNotice(message ?: return true)
        return true
    }
}
```

### Flutter Repository
```dart
// No equivalent console message handling
// JavaScript errors are not captured or logged
// No JavaScript alert/confirm handling
```

### ❌ **Missing Debug Features in Flutter:**
1. **No console message capture**: Cannot see JavaScript console.log messages
2. **No JavaScript alert handling**: JavaScript alerts may not work properly
3. **No error logging**: JavaScript errors are not captured

## 9. Payment Method Constants

### Android Repository
```kotlin
// Well-defined constants
object Constant {
    object AirPay {
        const val fullHost = "airpay.vn"
        const val orderId = "order_id"
    }
    object Momo {
        const val fullHost = "payment.momo"
        const val orderId = "orderId"
        const val resultCode = "resultCode"
        // ... other fields
    }
    object ZaloPay {
        const val fullHost = "gateway.zalopay.vn"
        const val domain = "zalopay"
        // ... other fields
    }
}
```

### Flutter Repository
```dart
// Hardcoded strings scattered throughout code
if (url.contains('airpay.vn')) { ... }
if (url.contains('payment.momo')) { ... }
if (url.contains('gateway.zalopay.vn')) { ... }
```

### ❌ **Code Quality Issues in Flutter:**
1. **No centralized constants**: Hardcoded strings everywhere
2. **Maintenance difficulty**: Changes require multiple file updates
3. **Error prone**: Typos in URLs can break functionality

## Summary of Critical Issues

### 🔴 **High Priority Fixes Needed:**

1. **BroadcastReceiver Equivalent**
   - Flutter needs a mechanism to receive payment status from external apps
   - Consider using platform channels or app lifecycle listeners

2. **JavaScript Interface Alignment**
   - Change from `"Flutter"` to `"androidkit"` for consistency
   - Ensure same method signatures

3. **Data Injection Format**
   - Use Android's variable assignment pattern instead of direct object
   - Ensure property names match exactly

4. **WebView Settings**
   - Add DOM storage enablement
   - Add proper WebView cleanup in dispose()

5. **Console Logging**
   - Implement JavaScript console message capture
   - Add JavaScript alert/confirm handling

### 🟡 **Medium Priority Improvements:**

1. **Constants Management**
   - Create centralized constants file
   - Replace hardcoded strings

2. **Error Handling**
   - Add comprehensive error handling like Android
   - Implement timeout mechanisms

3. **Back Button Behavior**
   - Consider removing confirmation dialog to match Android
   - Or add confirmation to Android for consistency

### 🟢 **Low Priority Enhancements:**

1. **User Agent Alignment**
   - Decide on standard user agent across platforms

2. **Method Naming**
   - Standardize method names between platforms

### ✅ **Action Items:**
1. **Immediate**: Fix JavaScript interface name and data injection format
2. **Short-term**: Implement BroadcastReceiver equivalent using platform channels
3. **Medium-term**: Add missing WebView settings and console logging
4. **Long-term**: Refactor constants and standardize error handling
