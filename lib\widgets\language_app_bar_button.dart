import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../service/language_service.dart';

/// A language switcher button for the app bar
class LanguageAppBarButton extends StatefulWidget {
  final VoidCallback? onLanguageChanged;
  final bool showDialog;

  const LanguageAppBarButton({
    Key? key,
    this.onLanguageChanged,
    this.showDialog = true,
  }) : super(key: key);

  @override
  State<LanguageAppBarButton> createState() => _LanguageAppBarButtonState();
}

class _LanguageAppBarButtonState extends State<LanguageAppBarButton> {
  final LanguageService _languageService = LanguageService();
  bool _isEnglish = false;

  @override
  void initState() {
    super.initState();
    _checkCurrentLanguage();
  }

  Future<void> _checkCurrentLanguage() async {
    final isEnglish = await _languageService.isEnglish();
    if (mounted) {
      setState(() {
        _isEnglish = isEnglish;
      });
    }
  }

  Future<void> _toggleLanguage() async {
    final newLanguage = _isEnglish ? 'vi' : 'en';
    await _languageService.setLanguage(context, newLanguage);
    
    if (mounted) {
      setState(() {
        _isEnglish = !_isEnglish;
      });
    }
    
    // Call the callback if provided
    if (widget.onLanguageChanged != null) {
      widget.onLanguageChanged!();
    }

    // Show a dialog to confirm the language change
    if (widget.showDialog && mounted) {
      _showLanguageChangedDialog();
    }
  }

  void _showLanguageChangedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Setting.Language'.tr()),
        content: Text(
          _isEnglish
              ? 'Ngôn ngữ đã được chuyển sang Tiếng Việt'
              : 'Language has been changed to English',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Bt.OK'.tr()),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.language),
      tooltip: 'Setting.Language'.tr(),
      onPressed: _toggleLanguage,
    );
  }
}

/// A language switcher button with a flag icon
class LanguageFlagButton extends StatefulWidget {
  final VoidCallback? onLanguageChanged;
  final bool showDialog;
  final double size;

  const LanguageFlagButton({
    Key? key,
    this.onLanguageChanged,
    this.showDialog = true,
    this.size = 24.0,
  }) : super(key: key);

  @override
  State<LanguageFlagButton> createState() => _LanguageFlagButtonState();
}

class _LanguageFlagButtonState extends State<LanguageFlagButton> {
  final LanguageService _languageService = LanguageService();
  bool _isEnglish = false;

  @override
  void initState() {
    super.initState();
    _checkCurrentLanguage();
  }

  Future<void> _checkCurrentLanguage() async {
    final isEnglish = await _languageService.isEnglish();
    if (mounted) {
      setState(() {
        _isEnglish = isEnglish;
      });
    }
  }

  Future<void> _toggleLanguage() async {
    final newLanguage = _isEnglish ? 'vi' : 'en';
    await _languageService.setLanguage(context, newLanguage);
    
    if (mounted) {
      setState(() {
        _isEnglish = !_isEnglish;
      });
    }
    
    // Call the callback if provided
    if (widget.onLanguageChanged != null) {
      widget.onLanguageChanged!();
    }

    // Show a dialog to confirm the language change
    if (widget.showDialog && mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Setting.Language'.tr()),
          content: Text(
            _isEnglish
                ? 'Ngôn ngữ đã được chuyển sang Tiếng Việt'
                : 'Language has been changed to English',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Bt.OK'.tr()),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Image.asset(
        _isEnglish ? 'assets/images/flag_vi.png' : 'assets/images/flag_en.png',
        width: widget.size,
        height: widget.size,
      ),
      tooltip: 'Setting.Language'.tr(),
      onPressed: _toggleLanguage,
    );
  }
}
