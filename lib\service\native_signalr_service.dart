import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants/index.dart';

/// A callback type for SignalR hub method invocations
typedef SignalRMethodCallback = void Function(List<dynamic> arguments);

/// A service that provides SignalR functionality using native Swift implementation
class NativeSignalRService {
  // Method channel for communicating with the native side
  static const MethodChannel _methodChannel =
      MethodChannel('com.betacinemas.signalr/methods');

  // Event channel for receiving events from the native side
  static const EventChannel _eventChannel =
      EventChannel('com.betacinemas.signalr/events');

  // Stream controller for SignalR events
  final StreamController<SignalREvent> _eventStreamController =
      StreamController<SignalREvent>.broadcast();

  /// Xử lý URL SignalR giống như trong iOS
  ///
  /// [url] URL cơ sở
  /// [useDefault] Nếu true, thêm "/signalr" vào cuối URL
  ///
  /// Tương đương với hàm getUrl trong iOS:
  /// ```swift
  /// static func getUrl(url: String, useDefault: Bool) -> String {
  ///     let urlResult = url.hasSuffix("/") ? url : url.appending("/")
  ///     return useDefault ? urlResult.appending("signalr") : urlResult
  /// }
  /// ```
  static String getUrl(String url, {bool useDefault = true}) {
    final urlResult = url.endsWith("/") ? url : "$url/";
    return useDefault ? "${urlResult}signalr" : urlResult;
  }

  // Stream of SignalR events
  Stream<SignalREvent> get events => _eventStreamController.stream;

  // Map of registered method handlers
  final Map<String, List<SignalRMethodCallback>> _methodHandlers = {};

  // Connection ID of the current SignalR connection
  String? _connectionId;

  // Whether the SignalR connection is currently connected
  bool _isConnected = false;

  // Singleton instance
  static final NativeSignalRService _instance = NativeSignalRService._internal();

  // Factory constructor to return the singleton instance
  factory NativeSignalRService() => _instance;

  // Private constructor for singleton pattern
  NativeSignalRService._internal() {
    if (Platform.isIOS) {
      // Set up the event channel to listen for events from the native side
      try {
        _eventChannel.receiveBroadcastStream().listen(
          _handleEvent,
          onError: (error) {
            debugPrint('Error from event channel: $error');
            _eventStreamController.add(
              SignalRErrorEvent(
                errorCode: 'EVENT_CHANNEL_ERROR',
                errorMessage: error.toString(),
              ),
            );
          },
        );
        debugPrint('Event channel set up successfully');
      } catch (e) {
        debugPrint('Error setting up event channel: $e');
      }
    }
  }

  /// Get the connection ID of the current SignalR connection
  String? get connectionId => _connectionId;

  /// Get whether the SignalR connection is currently connected
  bool get isConnected => _isConnected;

  /// Connect to a SignalR hub
  ///
  /// [url] The URL of the SignalR hub
  /// [headers] Optional headers to include in the connection
  /// [useDefaultUrl] Nếu true, thêm "/signalr" vào cuối URL (mặc định: true)
  Future<void> connect(String url, {Map<String, String>? headers, bool useDefaultUrl = true}) async {
    if (!Platform.isIOS) {
      debugPrint('Native SignalR is only supported on iOS');
      throw PlatformException(
        code: 'UNSUPPORTED_PLATFORM',
        message: 'Native SignalR is only supported on iOS',
      );
    }

    // Xử lý URL giống như trong iOS
    final processedUrl = getUrl(url, useDefault: useDefaultUrl);
    debugPrint('Connecting to SignalR hub at $processedUrl');

    try {
      // Check if the method channel is available
      final isMethodChannelAvailable = await _isMethodChannelAvailable();
      if (!isMethodChannelAvailable) {
        debugPrint('Method channel is not available');
        throw PlatformException(
          code: 'METHOD_CHANNEL_UNAVAILABLE',
          message: 'Method channel is not available. Make sure the native plugin is properly registered.',
        );
      }

      // Invoke the connect method on the native side
      final result = await _methodChannel.invokeMethod<Map<dynamic, dynamic>>(
        'connect',
        {
          'url': processedUrl,
          'headers': headers ?? {},
        },
      );

      debugPrint('Connect result: $result');

      if (result != null) {
        _connectionId = result['connectionId'] as String?;
        _isConnected = true;
        _eventStreamController.add(
          SignalRConnectionEvent(
            connected: true,
            connectionId: _connectionId,
          ),
        );
        debugPrint('Connected to SignalR hub with ID: $_connectionId');
      } else {
        debugPrint('Connect result is null');
        throw PlatformException(
          code: 'CONNECT_FAILED',
          message: 'Failed to connect to SignalR hub',
        );
      }
    } on PlatformException catch (e) {
      debugPrint('Platform exception during connect: ${e.code} - ${e.message}');
      _isConnected = false;
      _connectionId = null;
      _eventStreamController.add(
        SignalRErrorEvent(
          errorCode: e.code,
          errorMessage: e.message ?? 'Unknown error',
        ),
      );
      rethrow;
    } catch (e) {
      debugPrint('Error during connect: $e');
      _isConnected = false;
      _connectionId = null;
      _eventStreamController.add(
        SignalRErrorEvent(
          errorCode: 'CONNECT_ERROR',
          errorMessage: e.toString(),
        ),
      );
      rethrow;
    }
  }

  /// Check if the method channel is available
  Future<bool> _isMethodChannelAvailable() async {
    try {
      // Try to invoke a simple method to check if the channel is available
      await _methodChannel.invokeMethod<bool>('isConnected');
      return true;
    } catch (e) {
      debugPrint('Method channel check failed: $e');
      return false;
    }
  }

  /// Disconnect from the SignalR hub
  Future<void> disconnect() async {
    if (!Platform.isIOS) {
      return;
    }

    await _methodChannel.invokeMethod<void>('disconnect');
    _isConnected = false;
    _connectionId = null;
    _methodHandlers.clear();
    _eventStreamController.add(
      SignalRConnectionEvent(
        connected: false,
        connectionId: null,
      ),
    );
  }

  /// Invoke a method on the SignalR hub
  ///
  /// [method] The name of the method to invoke
  /// [arguments] Optional arguments to pass to the method
  Future<dynamic> invoke(String method, {List<dynamic>? arguments}) async {
    if (!Platform.isIOS) {
      throw PlatformException(
        code: 'UNSUPPORTED_PLATFORM',
        message: 'Native SignalR is only supported on iOS',
      );
    }

    if (!_isConnected) {
      throw PlatformException(
        code: 'NOT_CONNECTED',
        message: 'SignalR is not connected',
      );
    }

    try {
      return await _methodChannel.invokeMethod<dynamic>(
        'invoke',
        {
          'method': method,
          'arguments': arguments ?? [],
        },
      );
    } on PlatformException catch (e) {
      _eventStreamController.add(
        SignalRErrorEvent(
          errorCode: e.code,
          errorMessage: e.message ?? 'Unknown error',
        ),
      );
      rethrow;
    }
  }

  /// Register a callback for a SignalR hub method
  ///
  /// [method] The name of the method to register for
  /// [callback] The callback to invoke when the method is called
  Future<void> on(String method, SignalRMethodCallback callback) async {
    if (!Platform.isIOS) {
      return;
    }

    // Add the callback to the list of handlers for this method
    if (!_methodHandlers.containsKey(method)) {
      _methodHandlers[method] = [];

      // Register the method with the native side
      await _methodChannel.invokeMethod<void>(
        'on',
        {'method': method},
      );
    }

    _methodHandlers[method]!.add(callback);
  }

  /// Unregister a callback for a SignalR hub method
  ///
  /// [method] The name of the method to unregister
  /// [callback] The callback to unregister (if null, all callbacks for the method will be unregistered)
  Future<void> off(String method, {SignalRMethodCallback? callback}) async {
    if (!Platform.isIOS) {
      return;
    }

    if (callback == null) {
      // Remove all callbacks for this method
      _methodHandlers.remove(method);

      // Unregister the method with the native side
      await _methodChannel.invokeMethod<void>(
        'off',
        {'method': method},
      );
    } else if (_methodHandlers.containsKey(method)) {
      // Remove the specific callback
      _methodHandlers[method]!.remove(callback);

      // If there are no more callbacks for this method, unregister it with the native side
      if (_methodHandlers[method]!.isEmpty) {
        _methodHandlers.remove(method);

        await _methodChannel.invokeMethod<void>(
          'off',
          {'method': method},
        );
      }
    }
  }

  /// Handle events from the native side
  void _handleEvent(dynamic event) {
    debugPrint('🔵 NativeSignalR: Received event: $event');

    if (event is! Map) {
      debugPrint('🟡 NativeSignalR: Event is not a Map, ignoring');
      return;
    }

    final eventName = event['event'] as String?;
    debugPrint('🔵 NativeSignalR: Event name: $eventName');

    if (eventName == 'connectionState') {
      final connected = event['connected'] as bool? ?? false;
      final connectionId = event['connectionId'] as String?;

      debugPrint('🔵 NativeSignalR: Connection state changed - connected: $connected, id: $connectionId');

      _isConnected = connected;
      _connectionId = connectionId;

      _eventStreamController.add(
        SignalRConnectionEvent(
          connected: connected,
          connectionId: connectionId,
        ),
      );
    } else if (eventName == 'hubMethod') {
      final method = event['method'] as String?;
      final arguments = event['arguments'] as List<dynamic>?;

      debugPrint('🔵 NativeSignalR: Hub method called - method: $method, args: $arguments');
      debugPrint('🔵 NativeSignalR: Available handlers: ${_methodHandlers.keys}');

      if (method != null && _methodHandlers.containsKey(method)) {
        debugPrint('🔵 NativeSignalR: Calling ${_methodHandlers[method]!.length} handlers for method: $method');
        for (final handler in _methodHandlers[method]!) {
          try {
            handler(arguments ?? []);
            debugPrint('✅ NativeSignalR: Handler called successfully');
          } catch (e) {
            debugPrint('❌ NativeSignalR: Error calling handler: $e');
          }
        }
      } else {
        debugPrint('🟡 NativeSignalR: No handlers found for method: $method');
      }

      _eventStreamController.add(
        SignalRMethodEvent(
          method: method ?? '',
          arguments: arguments ?? [],
        ),
      );
    } else {
      debugPrint('🟡 NativeSignalR: Unknown event name: $eventName');
    }
  }

  /// Dispose of the service
  void dispose() {
    _eventStreamController.close();
  }
}

/// Base class for SignalR events
abstract class SignalREvent {}

/// Event for SignalR connection changes
class SignalRConnectionEvent extends SignalREvent {
  final bool connected;
  final String? connectionId;

  SignalRConnectionEvent({
    required this.connected,
    this.connectionId,
  });
}

/// Event for SignalR method invocations
class SignalRMethodEvent extends SignalREvent {
  final String method;
  final List<dynamic> arguments;

  SignalRMethodEvent({
    required this.method,
    required this.arguments,
  });
}

/// Event for SignalR errors
class SignalRErrorEvent extends SignalREvent {
  final String errorCode;
  final String errorMessage;

  SignalRErrorEvent({
    required this.errorCode,
    required this.errorMessage,
  });
}
