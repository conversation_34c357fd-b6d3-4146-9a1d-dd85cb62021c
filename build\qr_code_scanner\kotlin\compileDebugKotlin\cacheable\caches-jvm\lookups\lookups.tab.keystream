  Manifest android  CAMERA android.Manifest.permission  SuppressLint android.annotation  Activity android.app  Application android.app  UnRegisterLifecycleCallback android.app.Activity  Unit android.app.Activity  application android.app.Activity  registerLifecycleCallbacks android.app.Activity  requestPermissions android.app.Activity  ActivityLifecycleCallbacks android.app.Application  "registerActivityLifecycleCallbacks android.app.Application  $unregisterActivityLifecycleCallbacks android.app.Application  Context android.content  packageManager android.content.Context  	resources android.content.Context  PackageManager android.content.pm  FEATURE_CAMERA !android.content.pm.PackageManager  FEATURE_CAMERA_FLASH !android.content.pm.PackageManager  FEATURE_CAMERA_FRONT !android.content.pm.PackageManager  PERMISSION_GRANTED !android.content.pm.PackageManager  hasSystemFeature !android.content.pm.PackageManager  displayMetrics android.content.res.Resources  Rect android.graphics  bottom android.graphics.Rect  	intersect android.graphics.Rect  top android.graphics.Rect  
CameraInfo android.hardware.Camera  CAMERA_FACING_BACK "android.hardware.Camera.CameraInfo  CAMERA_FACING_FRONT "android.hardware.Camera.CameraInfo  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  AttributeSet android.util  density android.util.DisplayMetrics  View android.view  BOTTOM_OFFSET_NOT_SET_VALUE android.view.View  Rect android.view.View  Size android.view.View  minusAssign android.view.View  BOTTOM_OFFSET_NOT_SET_VALUE android.view.ViewGroup  Rect android.view.ViewGroup  Size android.view.ViewGroup  minusAssign android.view.ViewGroup  NonNull androidx.annotation  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  
BarcodeFormat com.google.zxing  ResultPoint com.google.zxing  name com.google.zxing.BarcodeFormat  values com.google.zxing.BarcodeFormat  BarcodeCallback com.journeyapps.barcodescanner  
BarcodeResult com.journeyapps.barcodescanner  BarcodeView com.journeyapps.barcodescanner  DefaultDecoderFactory com.journeyapps.barcodescanner  Size com.journeyapps.barcodescanner  
barcodeFormat ,com.journeyapps.barcodescanner.BarcodeResult  rawBytes ,com.journeyapps.barcodescanner.BarcodeResult  text ,com.journeyapps.barcodescanner.BarcodeResult  BOTTOM_OFFSET_NOT_SET_VALUE *com.journeyapps.barcodescanner.BarcodeView  Rect *com.journeyapps.barcodescanner.BarcodeView  Size *com.journeyapps.barcodescanner.BarcodeView  calculateFramingRect *com.journeyapps.barcodescanner.BarcodeView  decodeContinuous *com.journeyapps.barcodescanner.BarcodeView  decoderFactory *com.journeyapps.barcodescanner.BarcodeView  minusAssign *com.journeyapps.barcodescanner.BarcodeView  pause *com.journeyapps.barcodescanner.BarcodeView  stopDecoding *com.journeyapps.barcodescanner.BarcodeView  BOTTOM_OFFSET_NOT_SET_VALUE ,com.journeyapps.barcodescanner.CameraPreview  Rect ,com.journeyapps.barcodescanner.CameraPreview  Size ,com.journeyapps.barcodescanner.CameraPreview  calculateFramingRect ,com.journeyapps.barcodescanner.CameraPreview  cameraSettings ,com.journeyapps.barcodescanner.CameraPreview  framingRectSize ,com.journeyapps.barcodescanner.CameraPreview  isPreviewActive ,com.journeyapps.barcodescanner.CameraPreview  minusAssign ,com.journeyapps.barcodescanner.CameraPreview  resume ,com.journeyapps.barcodescanner.CameraPreview  setTorch ,com.journeyapps.barcodescanner.CameraPreview  CameraSettings %com.journeyapps.barcodescanner.camera  isScanInverted 4com.journeyapps.barcodescanner.camera.CameraSettings  requestedCameraId 4com.journeyapps.barcodescanner.camera.CameraSettings  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  platformViewRegistry Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  #addRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  &removeRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  StandardMessageCodec io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  invokeMethod &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result   RequestPermissionsResultListener 'io.flutter.plugin.common.PluginRegistry  INSTANCE -io.flutter.plugin.common.StandardMessageCodec  PlatformView io.flutter.plugin.platform  PlatformViewFactory io.flutter.plugin.platform  PlatformViewRegistry io.flutter.plugin.platform  Any .io.flutter.plugin.platform.PlatformViewFactory  HashMap .io.flutter.plugin.platform.PlatformViewFactory  QRView .io.flutter.plugin.platform.PlatformViewFactory  StandardMessageCodec .io.flutter.plugin.platform.PlatformViewFactory  String .io.flutter.plugin.platform.PlatformViewFactory  Suppress .io.flutter.plugin.platform.PlatformViewFactory  requireNotNull .io.flutter.plugin.platform.PlatformViewFactory  registerViewFactory /io.flutter.plugin.platform.PlatformViewRegistry  	Exception 	java.lang  message java.lang.Exception  HashMap 	java.util  get java.util.HashMap  Array kotlin  	Function0 kotlin  	Function1 kotlin  IntArray kotlin  Nothing kotlin  Pair kotlin  Result kotlin  Suppress kotlin  also kotlin  arrayOf kotlin  map kotlin  requireNotNull kotlin  to kotlin  with kotlin  get kotlin.Array  not kotlin.Boolean  context 
kotlin.Double  convertDpToPixels 
kotlin.Double  times 
kotlin.Double  toInt 
kotlin.Double  invoke kotlin.Function0  	compareTo 
kotlin.Int  minus 
kotlin.Int  minusAssign 
kotlin.Int  plus 
kotlin.Int  firstOrNull kotlin.IntArray  to 
kotlin.String  message kotlin.Throwable  
Collection kotlin.collections  List kotlin.collections  Map kotlin.collections  Set kotlin.collections  	emptyList kotlin.collections  firstOrNull kotlin.collections  map kotlin.collections  mapOf kotlin.collections  minusAssign kotlin.collections  orEmpty kotlin.collections  contains kotlin.collections.List  isEmpty kotlin.collections.List  map kotlin.collections.List  orEmpty kotlin.collections.List  firstOrNull 
kotlin.ranges  Sequence kotlin.sequences  firstOrNull kotlin.sequences  map kotlin.sequences  orEmpty kotlin.sequences  firstOrNull kotlin.text  map kotlin.text  orEmpty kotlin.text  Activity net.touchcapture.qr.flutterqr  
ActivityAware net.touchcapture.qr.flutterqr  ActivityPluginBinding net.touchcapture.qr.flutterqr  Any net.touchcapture.qr.flutterqr  Application net.touchcapture.qr.flutterqr  Array net.touchcapture.qr.flutterqr  AttributeSet net.touchcapture.qr.flutterqr  BOTTOM_OFFSET_NOT_SET_VALUE net.touchcapture.qr.flutterqr  BarcodeCallback net.touchcapture.qr.flutterqr  
BarcodeFormat net.touchcapture.qr.flutterqr  
BarcodeResult net.touchcapture.qr.flutterqr  BarcodeView net.touchcapture.qr.flutterqr  BinaryMessenger net.touchcapture.qr.flutterqr  Boolean net.touchcapture.qr.flutterqr  Build net.touchcapture.qr.flutterqr  Bundle net.touchcapture.qr.flutterqr   CHANNEL_METHOD_ON_PERMISSION_SET net.touchcapture.qr.flutterqr  CHANNEL_METHOD_ON_RECOGNIZE_QR net.touchcapture.qr.flutterqr  
CameraInfo net.touchcapture.qr.flutterqr  Context net.touchcapture.qr.flutterqr  
ContextCompat net.touchcapture.qr.flutterqr  CustomFramingRectBarcodeView net.touchcapture.qr.flutterqr  DefaultDecoderFactory net.touchcapture.qr.flutterqr  Double net.touchcapture.qr.flutterqr  ERROR_CODE_NOT_SET net.touchcapture.qr.flutterqr  ERROR_MESSAGE_FLASH_NOT_FOUND net.touchcapture.qr.flutterqr  ERROR_MESSAGE_NOT_SET net.touchcapture.qr.flutterqr  	Exception net.touchcapture.qr.flutterqr  
FlutterPlugin net.touchcapture.qr.flutterqr  FlutterQrPlugin net.touchcapture.qr.flutterqr  HashMap net.touchcapture.qr.flutterqr  Int net.touchcapture.qr.flutterqr  IntArray net.touchcapture.qr.flutterqr  List net.touchcapture.qr.flutterqr  Manifest net.touchcapture.qr.flutterqr  
MethodCall net.touchcapture.qr.flutterqr  
MethodChannel net.touchcapture.qr.flutterqr  NonNull net.touchcapture.qr.flutterqr  PARAMS_CAMERA_FACING net.touchcapture.qr.flutterqr  PackageManager net.touchcapture.qr.flutterqr  PlatformView net.touchcapture.qr.flutterqr  PlatformViewFactory net.touchcapture.qr.flutterqr  PluginRegistry net.touchcapture.qr.flutterqr  QRView net.touchcapture.qr.flutterqr  
QRViewFactory net.touchcapture.qr.flutterqr  QrShared net.touchcapture.qr.flutterqr  Rect net.touchcapture.qr.flutterqr  ResultPoint net.touchcapture.qr.flutterqr  Size net.touchcapture.qr.flutterqr  StandardMessageCodec net.touchcapture.qr.flutterqr  String net.touchcapture.qr.flutterqr  Suppress net.touchcapture.qr.flutterqr  SuppressLint net.touchcapture.qr.flutterqr  UnRegisterLifecycleCallback net.touchcapture.qr.flutterqr  Unit net.touchcapture.qr.flutterqr  VIEW_TYPE_ID net.touchcapture.qr.flutterqr  View net.touchcapture.qr.flutterqr  also net.touchcapture.qr.flutterqr  arrayOf net.touchcapture.qr.flutterqr  channel net.touchcapture.qr.flutterqr  context net.touchcapture.qr.flutterqr  	emptyList net.touchcapture.qr.flutterqr  firstOrNull net.touchcapture.qr.flutterqr  map net.touchcapture.qr.flutterqr  mapOf net.touchcapture.qr.flutterqr  minusAssign net.touchcapture.qr.flutterqr  orEmpty net.touchcapture.qr.flutterqr  registerLifecycleCallbacks net.touchcapture.qr.flutterqr  requireNotNull net.touchcapture.qr.flutterqr  to net.touchcapture.qr.flutterqr  with net.touchcapture.qr.flutterqr  ActivityLifecycleCallbacks )net.touchcapture.qr.flutterqr.Application  AttributeSet :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  BOTTOM_OFFSET_NOT_SET_VALUE :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  Context :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  Int :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  Rect :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  Size :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  also :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  bottomOffset :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  cameraSettings :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  decodeContinuous :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  decoderFactory :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  framingRectSize :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  isPreviewActive :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  minusAssign :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  pause :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  resume :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  setFramingRect :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  setTorch :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  stopDecoding :net.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView  BOTTOM_OFFSET_NOT_SET_VALUE Dnet.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView.Companion  Rect Dnet.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView.Companion  Size Dnet.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView.Companion  minusAssign Dnet.touchcapture.qr.flutterqr.CustomFramingRectBarcodeView.Companion  FlutterPluginBinding +net.touchcapture.qr.flutterqr.FlutterPlugin  ActivityPluginBinding -net.touchcapture.qr.flutterqr.FlutterQrPlugin  
FlutterPlugin -net.touchcapture.qr.flutterqr.FlutterQrPlugin  NonNull -net.touchcapture.qr.flutterqr.FlutterQrPlugin  
QRViewFactory -net.touchcapture.qr.flutterqr.FlutterQrPlugin  QrShared -net.touchcapture.qr.flutterqr.FlutterQrPlugin  VIEW_TYPE_ID -net.touchcapture.qr.flutterqr.FlutterQrPlugin  
QRViewFactory 7net.touchcapture.qr.flutterqr.FlutterQrPlugin.Companion  QrShared 7net.touchcapture.qr.flutterqr.FlutterQrPlugin.Companion  VIEW_TYPE_ID 7net.touchcapture.qr.flutterqr.FlutterQrPlugin.Companion  FlutterPluginBinding ;net.touchcapture.qr.flutterqr.FlutterQrPlugin.FlutterPlugin  MethodCallHandler +net.touchcapture.qr.flutterqr.MethodChannel  Result +net.touchcapture.qr.flutterqr.MethodChannel   RequestPermissionsResultListener ,net.touchcapture.qr.flutterqr.PluginRegistry  Any $net.touchcapture.qr.flutterqr.QRView  Array $net.touchcapture.qr.flutterqr.QRView  BarcodeCallback $net.touchcapture.qr.flutterqr.QRView  
BarcodeFormat $net.touchcapture.qr.flutterqr.QRView  
BarcodeResult $net.touchcapture.qr.flutterqr.QRView  BinaryMessenger $net.touchcapture.qr.flutterqr.QRView  Boolean $net.touchcapture.qr.flutterqr.QRView  Build $net.touchcapture.qr.flutterqr.QRView   CHANNEL_METHOD_ON_PERMISSION_SET $net.touchcapture.qr.flutterqr.QRView  CHANNEL_METHOD_ON_RECOGNIZE_QR $net.touchcapture.qr.flutterqr.QRView  
CameraInfo $net.touchcapture.qr.flutterqr.QRView  Context $net.touchcapture.qr.flutterqr.QRView  
ContextCompat $net.touchcapture.qr.flutterqr.QRView  CustomFramingRectBarcodeView $net.touchcapture.qr.flutterqr.QRView  DefaultDecoderFactory $net.touchcapture.qr.flutterqr.QRView  Double $net.touchcapture.qr.flutterqr.QRView  ERROR_CODE_NOT_SET $net.touchcapture.qr.flutterqr.QRView  ERROR_MESSAGE_FLASH_NOT_FOUND $net.touchcapture.qr.flutterqr.QRView  ERROR_MESSAGE_NOT_SET $net.touchcapture.qr.flutterqr.QRView  	Exception $net.touchcapture.qr.flutterqr.QRView  HashMap $net.touchcapture.qr.flutterqr.QRView  Int $net.touchcapture.qr.flutterqr.QRView  IntArray $net.touchcapture.qr.flutterqr.QRView  List $net.touchcapture.qr.flutterqr.QRView  Manifest $net.touchcapture.qr.flutterqr.QRView  
MethodCall $net.touchcapture.qr.flutterqr.QRView  
MethodChannel $net.touchcapture.qr.flutterqr.QRView  PARAMS_CAMERA_FACING $net.touchcapture.qr.flutterqr.QRView  PackageManager $net.touchcapture.qr.flutterqr.QRView  QrShared $net.touchcapture.qr.flutterqr.QRView  ResultPoint $net.touchcapture.qr.flutterqr.QRView  String $net.touchcapture.qr.flutterqr.QRView  Suppress $net.touchcapture.qr.flutterqr.QRView  SuppressLint $net.touchcapture.qr.flutterqr.QRView  UnRegisterLifecycleCallback $net.touchcapture.qr.flutterqr.QRView  Unit $net.touchcapture.qr.flutterqr.QRView  View $net.touchcapture.qr.flutterqr.QRView  also $net.touchcapture.qr.flutterqr.QRView  arrayOf $net.touchcapture.qr.flutterqr.QRView  barCodeViewNotSet $net.touchcapture.qr.flutterqr.QRView  barcodeView $net.touchcapture.qr.flutterqr.QRView  cameraRequestCode $net.touchcapture.qr.flutterqr.QRView  changeScanArea $net.touchcapture.qr.flutterqr.QRView  channel $net.touchcapture.qr.flutterqr.QRView  checkAndRequestPermission $net.touchcapture.qr.flutterqr.QRView  context $net.touchcapture.qr.flutterqr.QRView  convertDpToPixels $net.touchcapture.qr.flutterqr.QRView  	emptyList $net.touchcapture.qr.flutterqr.QRView  firstOrNull $net.touchcapture.qr.flutterqr.QRView  
flipCamera $net.touchcapture.qr.flutterqr.QRView  getAllowedBarcodeTypes $net.touchcapture.qr.flutterqr.QRView  
getCameraInfo $net.touchcapture.qr.flutterqr.QRView  getFlashInfo $net.touchcapture.qr.flutterqr.QRView  getSystemFeatures $net.touchcapture.qr.flutterqr.QRView  
hasBackCamera $net.touchcapture.qr.flutterqr.QRView  hasCameraPermission $net.touchcapture.qr.flutterqr.QRView  hasFlash $net.touchcapture.qr.flutterqr.QRView  hasFrontCamera $net.touchcapture.qr.flutterqr.QRView  hasSystemFeature $net.touchcapture.qr.flutterqr.QRView  id $net.touchcapture.qr.flutterqr.QRView  initBarCodeView $net.touchcapture.qr.flutterqr.QRView  isPaused $net.touchcapture.qr.flutterqr.QRView  	isTorchOn $net.touchcapture.qr.flutterqr.QRView  map $net.touchcapture.qr.flutterqr.QRView  mapOf $net.touchcapture.qr.flutterqr.QRView  orEmpty $net.touchcapture.qr.flutterqr.QRView  params $net.touchcapture.qr.flutterqr.QRView  pauseCamera $net.touchcapture.qr.flutterqr.QRView  registerLifecycleCallbacks $net.touchcapture.qr.flutterqr.QRView  requireNotNull $net.touchcapture.qr.flutterqr.QRView  resumeCamera $net.touchcapture.qr.flutterqr.QRView  
setInvertScan $net.touchcapture.qr.flutterqr.QRView  setScanAreaSize $net.touchcapture.qr.flutterqr.QRView  	startScan $net.touchcapture.qr.flutterqr.QRView  stopScan $net.touchcapture.qr.flutterqr.QRView  to $net.touchcapture.qr.flutterqr.QRView  toggleFlash $net.touchcapture.qr.flutterqr.QRView  unRegisterLifecycleCallback $net.touchcapture.qr.flutterqr.QRView  with $net.touchcapture.qr.flutterqr.QRView  
BarcodeFormat .net.touchcapture.qr.flutterqr.QRView.Companion  Build .net.touchcapture.qr.flutterqr.QRView.Companion   CHANNEL_METHOD_ON_PERMISSION_SET .net.touchcapture.qr.flutterqr.QRView.Companion  CHANNEL_METHOD_ON_RECOGNIZE_QR .net.touchcapture.qr.flutterqr.QRView.Companion  
CameraInfo .net.touchcapture.qr.flutterqr.QRView.Companion  
ContextCompat .net.touchcapture.qr.flutterqr.QRView.Companion  CustomFramingRectBarcodeView .net.touchcapture.qr.flutterqr.QRView.Companion  DefaultDecoderFactory .net.touchcapture.qr.flutterqr.QRView.Companion  ERROR_CODE_NOT_SET .net.touchcapture.qr.flutterqr.QRView.Companion  ERROR_MESSAGE_FLASH_NOT_FOUND .net.touchcapture.qr.flutterqr.QRView.Companion  ERROR_MESSAGE_NOT_SET .net.touchcapture.qr.flutterqr.QRView.Companion  Manifest .net.touchcapture.qr.flutterqr.QRView.Companion  
MethodChannel .net.touchcapture.qr.flutterqr.QRView.Companion  PARAMS_CAMERA_FACING .net.touchcapture.qr.flutterqr.QRView.Companion  PackageManager .net.touchcapture.qr.flutterqr.QRView.Companion  QrShared .net.touchcapture.qr.flutterqr.QRView.Companion  Unit .net.touchcapture.qr.flutterqr.QRView.Companion  also .net.touchcapture.qr.flutterqr.QRView.Companion  arrayOf .net.touchcapture.qr.flutterqr.QRView.Companion  channel .net.touchcapture.qr.flutterqr.QRView.Companion  context .net.touchcapture.qr.flutterqr.QRView.Companion  	emptyList .net.touchcapture.qr.flutterqr.QRView.Companion  firstOrNull .net.touchcapture.qr.flutterqr.QRView.Companion  map .net.touchcapture.qr.flutterqr.QRView.Companion  mapOf .net.touchcapture.qr.flutterqr.QRView.Companion  orEmpty .net.touchcapture.qr.flutterqr.QRView.Companion  registerLifecycleCallbacks .net.touchcapture.qr.flutterqr.QRView.Companion  requireNotNull .net.touchcapture.qr.flutterqr.QRView.Companion  to .net.touchcapture.qr.flutterqr.QRView.Companion  with .net.touchcapture.qr.flutterqr.QRView.Companion  Result 2net.touchcapture.qr.flutterqr.QRView.MethodChannel  QRView +net.touchcapture.qr.flutterqr.QRViewFactory  	messenger +net.touchcapture.qr.flutterqr.QRViewFactory  requireNotNull +net.touchcapture.qr.flutterqr.QRViewFactory  CAMERA_REQUEST_ID &net.touchcapture.qr.flutterqr.QrShared  activity &net.touchcapture.qr.flutterqr.QrShared  binding &net.touchcapture.qr.flutterqr.QrShared  application 9net.touchcapture.qr.flutterqr.UnRegisterLifecycleCallback  callback 9net.touchcapture.qr.flutterqr.UnRegisterLifecycleCallback  invoke 9net.touchcapture.qr.flutterqr.UnRegisterLifecycleCallback                                                                                                                                                                                                                                                                                                                                                          