var customerId = '';
var customerCard = '';
var airpayOrderNo = '';

var i = 0, count = 0, j = 0, k = 0, c = 0; var quantity = 0;

var bookingInfor = {};
bookingInfor.FilmFormatCode = '2d';

var paymentInfor = {};
paymentInfor.BetaPoint = {};
paymentInfor.BetaPoint.money = 0;
paymentInfor.BetaPoint.point = 0;
paymentInfor.Vouchers = {};
paymentInfor.Coupons = {};
paymentInfor.PaymentCardType = 'vn';
paymentInfor.TotalDiscount = 0;
paymentInfor.VoucherDiscountMoney = 0;
paymentInfor.VoucherPaymentValidate = {};
paymentInfor.VoucherPaymentDetail = {};

var customerInfor = {};

var FirstLoad = {};
FirstLoad.Coupon = false;
FirstLoad.Voucher = false;
FirstLoad.Point = false;

var screenType = "payment";
//chon loai hinh giam gia
var ChoosedDiscount = function (type) {
    $('#' + type).show();
    $('.content').hide();
    screenType = type;
    if (type === "voucher") {
        if (!FirstLoad.Voucher) {
            getVoucherCoupon(customerId, "Voucher");
            FirstLoad.Voucher = false;
        }
        $("title").text("Beta Voucher");

    } else if (type === "coupon") {
        if (!FirstLoad.Coupon) {
            getVoucherCoupon(customerId, "Coupon");
            FirstLoad.Coupon = true;
        }

        $("title").text("Beta Coupon");
    } else if (type === "beta-point") {
        if (!FirstLoad.Point) {
            getPoint(customerId);
            FirstLoad.Point = true;
        }
        $("title").text("Điểm Beta");
    }
    $('#' + type).scrollTop(300);
};

var voucherRequestModel = {};

var listVoucher = [];
//lay danh sach voucher - coupon
var getVoucherCoupon = function (customerId, cardType) {
    count = 0;

    voucherRequestModel = {};
    voucherRequestModel.ShowId = bookingInfor.ShowId;
    voucherRequestModel.TotalMoneyInvoice = bookingInfor.TotalMoney;

    voucherRequestModel.ListItem = [];
    voucherRequestModel.ListCombo = [];
    for (i = 0; i < bookingInfor.ComboSelected.length; i++) {
        if (bookingInfor.ComboSelected[i].IsTicketPackage === false) {
            for (j = 0; j < bookingInfor.ComboSelected[i].Quantity; j++) {
                voucherRequestModel.ListCombo.push({
                    Id: count++,
                    ObjectId: bookingInfor.ComboSelected[i].ComboPackageId,
                    SeatIndex: bookingInfor.ComboSelected[i].SeatIndex,
                    Type: 3,
                    CostPrice: bookingInfor.ComboSelected[i].TotalPriceInCombo,
                    SalePrice: bookingInfor.ComboSelected[i].TotalPriceInCombo
                });
            }
        }
    }

    voucherRequestModel.ListPackage = [];
    var listTicketInPack = [];
    for (i = 0; i < bookingInfor.ComboSelected.length; i++) {
        if (bookingInfor.ComboSelected[i].IsTicketPackage === true) {
            for (k = 0; k < bookingInfor.ComboSelected[i].Quantity; k++) {
                for (j = 0; j < bookingInfor.ComboSelected[i].ItemInCombos.length; j++) {
                    if (bookingInfor.ComboSelected[i].ItemInCombos[j].IsTicketType === true) {
                        if (listTicketInPack[bookingInfor.ComboSelected[i].ItemInCombos[j].Item_Id] === null
                            || listTicketInPack[bookingInfor.ComboSelected[i].ItemInCombos[j].Item_Id] === undefined
                        ) {
                            listTicketInPack[bookingInfor.ComboSelected[i].ItemInCombos[j].Item_Id] = 1;
                        } else {
                            listTicketInPack[bookingInfor.ComboSelected[i].ItemInCombos[j].Item_Id] += 1;
                        }
                    }
                }
                voucherRequestModel.ListPackage.push({
                    Id: count++,
                    ObjectId: bookingInfor.ComboSelected[i].ComboPackageId,
                    SeatIndex: bookingInfor.ComboSelected[i].SeatIndex,
                    Type: 2,
                    CostPrice: bookingInfor.ComboSelected[i].TotalPriceInCombo,
                    SalePrice: bookingInfor.ComboSelected[i].TotalPriceInCombo
                });
            }

        }
    }

    voucherRequestModel.ListTicket = [];
    var listTicketCount = [];
    for (i = 0; i < bookingInfor.seats.length; i++) {
        var tiketId = bookingInfor.seats[i].TicketTypeId;
        if (listTicketCount[tiketId] === null
            || listTicketCount[tiketId] === undefined
        ) {
            listTicketCount[tiketId] = 1;
        } else {
            listTicketCount[tiketId] += 1;
        }

        if (
            listTicketInPack[tiketId] !== undefined
            && listTicketInPack[tiketId] !== null
            && listTicketInPack[tiketId] !== 0
            && listTicketCount[tiketId] <= listTicketInPack[tiketId]
        ) {
            continue;
        }

        voucherRequestModel.ListTicket.push({
            Id: count++,
            ObjectId: bookingInfor.seats[i].TicketTypeId,
            SeatIndex: bookingInfor.seats[i].SeatIndex,
            Type: 1,
            CostPrice: bookingInfor.seats[i].Price,
            SalePrice: bookingInfor.seats[i].Price
        });
    }

    //console.log("voucherRequestModel");
    //console.log(voucherRequestModel);

    //var test = {};
    //test.UserId = customerId;
    //test.NumberCard = customerCard;
    //test.PaymentInfo = paymentInfor;
    //test.BookingInfo = bookingInfor;
    //test.CustomerInfo = customerInfor;
    //console.log(test);

    listVoucher = [];
    $('#voucher-items').html('');
    document.getElementById("voucher-items-loading").style.display = "block";
    jQuery.ajax({
        type: 'POST',
        url: "/api/v2/erp/voucher-payment",
        data: voucherRequestModel,
        headers: {
            'Authorization': authoToken,
            'X-User': customerId
        },
        success: function (data, textStatus, jqXHR) {
            //console.log(data);
            //console.log(textStatus);
            //console.log(jqXHR);
            listVoucher = data.Data;
            if (cardType === 'Voucher') {
                $('#voucher-items').html('');
                document.getElementById("voucher-items-loading").style.display = "none";
            } else if (cardType === 'Coupon') {
                $('#coupon-items').html('');
            }
            if (cardType === "Voucher") {
                customerInfor.Vouchers = data.Data;
            } else if (cardType === "Coupon") {
                customerInfor.Coupons = data.Data;
            }
            if (data.Status === 1) {
                appendHtmlToVoucherCoupon(cardType);
            } else {
                toastAlert(data.Message);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(jqXHR);
            console.log(textStatus);
            console.log(errorThrown);
            if (cardType === 'Voucher') {
                $('#voucher-items').html('');
            } else if (cardType === 'Coupon') {
                $('#coupon-items').html('');
            }
            //getVoucherCoupon(customerId, cardType);
        }
    });
    //$.ajax("/api/v1/erp/voucher?customerId=" + customerId + "&cardTypeName=" + cardType, function (data, status) {


    //});
};

//lay danh sach voucher - coupon
var getPoint = function (customerId) {
    $(".image-loading-full-screen").show();
    jQuery.ajax({
        type: 'GET',
        url: "/api/v1/erp/accounts/" + customerId + "/points",
        headers: {
            'Authorization': authoToken,
            'X-User': customerId
        },
        success: function (data, textStatus, jqXHR) {
            //console.log(data);
            //console.log(textStatus);
            //console.log(jqXHR);
            customerInfor.point = data.Data;
            $(".image-loading-full-screen").hide();
            //console.log(data);
            if (data.Status === 1) {
                $(".point-value").text(customerInfor.point.AvailablePoint);

            } else {
                toastAlert(data.Message);
            }

        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(jqXHR);
            console.log(textStatus);
            console.log(errorThrown);
            $(".image-loading-full-screen").hide();
            //getVoucherCoupon(customerId, cardType);
        }
    });
    //$.ajax("/api/v1/erp/accounts/" + customerId + "/points", function (data, status) {


    //});
};

// dang ky voucher - coupon
var registerVoucherCoupon = function (voucherCode, pinCode, cardType, customerId, customerCard) {
    jQuery.ajax({
        type: 'PUT',
        url: '/api/v1/erp/voucher',
        headers: {
            'Authorization': authoToken,
            'X-User': customerId
        },
        data: {
            CardTypeName: cardType,
            PinCode: pinCode,
            VoucherCode: voucherCode,
            CustomerId: customerId,
            CustomerCard: customerCard
        },
        success: function (data, textStatus, jqXHR) {
            console.log(data);
            console.log(textStatus);
            console.log(jqXHR);
            if (data.Status === 1) {
                getVoucherCoupon(customerId, cardType);
            } else if (data.Status === -1) {
                toastAlert(data.Message);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(jqXHR);
            console.log(textStatus);
            console.log(errorThrown);
            //getVoucherCoupon(customerId, cardType);
        }
    });
};

//dang ky coupon
var registerCoupon = function () {
    var couponCode = $("input#coupon-code").val();
    var couponPIN = $("input#coupon-pin").val();
    if (isEmpty(couponCode) || isEmpty(couponPIN)) {
        return;
    }
    registerVoucherCoupon(couponCode, couponPIN, "Coupon", customerId, customerCard);
};

//dang ky voucher
var registerVoucher = function () {
    var voucherCode = $("input#voucher-code").val();
    var voucherPIN = $("input#voucher-pin").val();
    if (isEmpty(voucherCode) || isEmpty(voucherPIN)) {
        return;
    }
    registerVoucherCoupon(voucherCode, voucherPIN, "Voucher", customerId, customerCard);
};

//hien thi voucher - coupon lên
var appendHtmlToVoucherCoupon = function (cardType) {
    var html = generateVoucherHtml(listVoucher);
    if (cardType === 'Voucher') {
        $('#voucher-items').html(html);
    } else if (cardType === 'Coupon') {
        $('#coupon-items').html(html);
    }
};

//gen html voucher-coupon
var generateVoucherHtml = function (listVoucherCoupon) {
    var html = "";
    if (listVoucherCoupon === null || listVoucherCoupon === undefined) {
        return html;
    }
    for (i = 0; i < listVoucherCoupon.length; i++) {
        var vc = listVoucherCoupon[i];
        html += '<div class="panel panel-voucher-info">';
        html += '<div class="item-in-panel">';
        html += '<div class="item-title cinema-title">Tên voucher</div>';
        html += '<div class="item-value cinema-name">' + vc.VoucherPackageName + '</div>';
        html += '</div>';
        html += '<div class="item-in-panel">';
        html += '<div class="item-title date-show-title">Mã voucher</div>';
        html += '<div class="item-value date-show-name">' + vc.VoucherCode + '</div>';
        html += '</div>';
        html += '<div class="item-in-panel">';
        html += '<div class="item-title time-title">Thời hạn sử dụng</div>';
        html += '<div class="item-value time-name">' + vc.ExpirationDateFormat + '</div>';
        html += '</div>';
        html += '<div class="img-checkbox" onclick="slectedVoucher(this,\'' + vc.VoucherId + '\',\'' + vc.VoucherCode + '\')"></div>';
        html += '</div>';
    }
    return html;
};

//check string empty
function isEmpty(val) {
    return val === undefined || val === null || val.length <= 0 ? true : false;
}

//thong bao toast
var toastAlert = function (message) {
    alert(message);
};

//chon voucher
var listCouponVoucherSelected = [];
var slectedVoucher = function (event, voucherId, voucherCode) {

    var isAdd = $(event).hasClass('selected');
    //remove voucher-coupon trong danh sach da chon
    if (isAdd) {
        for (i = 0; i < listCouponVoucherSelected.length; i++) {
            if (listCouponVoucherSelected[i].VoucherId === voucherId) {
                listCouponVoucherSelected.splice(i, 1);
            }
        }
        $(event).removeClass('selected');
    } else {
        for (i = 0; i < listVoucher.length; i++) {
            if (listVoucher[i].VoucherId === voucherId) {
                var voucherSelected = JSON.parse(JSON.stringify(listVoucher[i]));
                voucherSelected = {};
                voucherSelected.VoucherId = voucherId;
                voucherSelected.VoucherCode = voucherCode;
                voucherSelected.CardTypeName = "Voucher";
                listCouponVoucherSelected.push(voucherSelected);
            }
        }
        $(event).addClass('selected');
    }
};

//confirm chon voucher
var confirmCouponVoucher = function (cardType) {
    var listVoucher = [];
    var listCoupon = [];
    var listVoucherCode = [];
    //console.log(listCouponVoucherSelected);
    for (i = 0; i < listCouponVoucherSelected.length; i++) {
        if (listCouponVoucherSelected[i].CardTypeName === "Voucher") {
            listVoucher.push(listCouponVoucherSelected[i]);
            listVoucherCode.push(listCouponVoucherSelected[i].VoucherCode);
        } else if (listCouponVoucherSelected[i].CardTypeName === "Coupon") {
            listCoupon.push(listCouponVoucherSelected[i]);
        }

    }

    voucherRequestModel.ListVoucherCode = listVoucherCode;
    paymentInfor.Vouchers = [];

    resetVoucher();

    jQuery.ajax({
        type: 'POST',
        url: "/api/v2/erp/voucher/validate-voucher",
        data: voucherRequestModel,
        headers: {
            'Authorization': authoToken,
            'X-User': customerId
        },
        success: function (data, textStatus, jqXHR) {
            //console.log(data);
            //console.log(textStatus);
            //console.log(jqXHR);
            //console.log(data.Data);
            if (data.Status === 1) {
                for (i = 0; i < data.Data.ListVoucher.length; i++) {
                    paymentInfor.Vouchers.push({
                        Voucher_Card_Id: data.Data.ListVoucher[i].VoucherId,
                        VoucherCode: data.Data.ListVoucher[i].VoucherCode,
                        UnitPrice: data.Data.ListVoucher[i].VoucherPaymentValue
                    });
                }
                paymentInfor.VoucherDiscountMoney = data.Data.TotalMoneyDiscount;
                paymentInfor.TotalDiscount += data.Data.TotalMoneyDiscount;
                //toastAlert(data.Message);

                paymentInfor.VoucherPaymentValidate = voucherRequestModel;
                paymentInfor.VoucherPaymentDetail = data.Data;

                //console.log(paymentInfor);

                if (paymentInfor.VoucherDiscountMoney !== undefined && paymentInfor.VoucherDiscountMoney >= 0) {
                    $('.beta-voucher-value').text(paymentInfor.VoucherDiscountMoney);
                    $('.beta-voucher-value').attr('data-money', paymentInfor.VoucherDiscountMoney);
                }

                var t = checkSelectDiscount();
                if (t) {
                    changeScreen("#content");
                    $('.coupon-discount').text(ConvertToCurrency(paymentInfor.TotalDiscount.toString()) + 'đ');
                    $('.money-need-pay').text(ConvertToCurrency(paymentInfor.TotalMoneyNeedPay.toString()) + 'đ');
                    if (paymentInfor.TotalDiscount !== 0) {
                        toastAlert('Bạn đã được giảm ' + ConvertToCurrency(paymentInfor.VoucherDiscountMoney.toString()) + 'đ');
                    }
                } else {
                    paymentInfor = JSON.parse(JSON.stringify(PrePaymentInfor));
                }
            } else {
                toastAlert(data.Message);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(jqXHR);
            console.log(textStatus);
            console.log(errorThrown);
            toastAlert("Có lỗi xảy ra, vui lòng liên hệ Fanpage Beta để biết thêm chi tiết.");
        }
    });


    //if (cardType === "Voucher") {
    //    paymentInfor.Vouchers = JSON.parse(JSON.stringify(listVoucher));
    //} else if (cardType === "Coupon") {
    //    paymentInfor.Coupons = JSON.parse(JSON.stringify(listCoupon));
    //}

    //var PrePaymentInfor = JSON.parse(JSON.stringify(paymentInfor));
    //if (cardType === "Voucher") {
    //    paymentInfor.Vouchers = JSON.parse(JSON.stringify(listCouponVoucherSelected));
    //} else if (cardType === "Coupon") {
    //    paymentInfor.Coupons = JSON.parse(JSON.stringify(listCouponVoucherSelected));
    //}



};

//Reset lại voucher đã chọn
var resetVoucher = function () {
    listVoucher = [];
    listCouponVoucherSelected = [];

    paymentInfor.Vouchers = [];
    paymentInfor.TotalDiscount -= paymentInfor.VoucherDiscountMoney;
    paymentInfor.VoucherDiscountMoney = 0;
    paymentInfor.VoucherPaymentValidate = {};
    paymentInfor.VoucherPaymentDetail = {};

    if (paymentInfor.VoucherDiscountMoney !== undefined && paymentInfor.VoucherDiscountMoney >= 0) {
        $('.beta-voucher-value').text(paymentInfor.VoucherDiscountMoney);
        $('.beta-voucher-value').attr('data-money', paymentInfor.VoucherDiscountMoney);
    }

    var t = checkSelectDiscount();
    if (t) {
        changeScreen("#content");
        $('.coupon-discount').text(ConvertToCurrency(paymentInfor.TotalDiscount.toString()) + 'đ');
        $('.money-need-pay').text(ConvertToCurrency(paymentInfor.TotalMoneyNeedPay.toString()) + 'đ');
    } else {
        paymentInfor = JSON.parse(JSON.stringify(PrePaymentInfor));
    }
};

var confirmDoiDiem = function () {
    if (paymentInfor.BetaPoint !== undefined) {
        if (paymentInfor.BetaPoint.point !== undefined && paymentInfor.BetaPoint.point >= 0) {
            $('.beta-point-value').text(paymentInfor.BetaPoint.point);
            $('.beta-point-value').attr('data-money', paymentInfor.BetaPoint.money);
        }
        var t = checkSelectDiscount();
        if (t) {
            //changeScreen("#content");
            $('.coupon-discount').text(ConvertToCurrency(paymentInfor.TotalDiscount.toString()) + 'đ');
            $('.money-need-pay').text(ConvertToCurrency(paymentInfor.TotalMoneyNeedPay.toString()) + 'đ');
        }
    }

    changeScreen("#content");
};

//thay doi hien thi man hinh
var changeScreen = function (screenId) {
    $("#content").hide();
    $("#voucher").hide();
    $("#coupon").hide();
    $("#beta-point").hide();
    var title = "";
    if (screenId === "#content") {
        title = "Thanh toán";
        screenType = "payment";
    }
    if (screenId === "#voucher") {
        title = "Beta Voucher";
        screenType = "voucher";
    }
    if (screenId === "#coupon") {
        title = "Beta Coupon";
        screenType = "coupon";
    }
    if (screenId === "#beta-point") {
        title = "Điểm Beta";
        screenType = "beta-point";
    }
    $("title").text(title);
    $(screenId).show();
};

//chuyen diem sang tien
var convertBetaPointToMoney = function (point) {
    var money = 0;
    if (point > customerInfor.point.AvailablePoint || point < 0) {
        return money;
    }
    money = point * customerInfor.point.SpendingPointUnit;
    return money;
};

//thay doi so diem beta-point
var changeBetaPoint = function (event) {
    var p = $(event).val();
    var point = TryParseInt(p, 0);

    if (point > customerInfor.point.AvailablePoint) {
        $(event).val(customerInfor.point.AvailablePoint);
        point = customerInfor.point.AvailablePoint;
    }
    if (point < 0) {
        $(event).val(0);
        point = 0;
    }

    var money = convertBetaPointToMoney(point);
    var textMoney = ConvertToCurrency(money.toString());
    $('.point-money').html(" = " + textMoney + "đ");
    paymentInfor.BetaPoint = { point: point, money: money };

};

function TryParseInt(str, defaultValue) {
    var retValue = defaultValue;
    if (str !== null) {
        if (str.length > 0) {
            if (!isNaN(str)) {
                retValue = parseInt(str);
            }
        }
    }
    return retValue;
}

bookingInfor.ComboSelected = [];
//tang so luong combo
var increaseQuantityCombo = function (comboId) {
    //console.log(listCombo);
    //console.log(comboId);

    var isAdd = false;
    var preSelected = JSON.parse(JSON.stringify(bookingInfor.ComboSelected));
    for (c = 0; c < bookingInfor.ComboSelected.length; c++) {
        if (comboId === bookingInfor.ComboSelected[c].ComboPackageId) {
            bookingInfor.ComboSelected[c].Quantity += 1;
            isAdd = true;
        }
    }
    if (!isAdd) {
        var currentCombo = {};
        for (i = 0; i < listCombo.length; i++) {
            if (listCombo[i].ComboPackageId === comboId) {
                currentCombo = listCombo[i];
            }
        }
        currentCombo.Quantity = 1;
        bookingInfor.ComboSelected.push(currentCombo);
    }
    if (checkQuantityCombo()) {
        quantity = $(".item-outer-pannel[data-combo-id='" + comboId + "'] .combo-quantity").attr("data-combo-quantity");
        quantity = parseInt(quantity);
        quantity += 1;
        $(".item-outer-pannel[data-combo-id='" + comboId + "'] .combo-quantity").text(quantity);
        $(".item-outer-pannel[data-combo-id='" + comboId + "'] .combo-quantity").attr("data-combo-quantity", quantity);

    } else {
        bookingInfor.ComboSelected = JSON.parse(JSON.stringify(preSelected));
        toastAlert("Combo/package kèm vé hết lượt chọn");
    }
    var totalMoney = 0;
    //for (c = 0; c < bookingInfor.ComboSelected.length; c++) {
    //    totalMoney += bookingInfor.ComboSelected[c].Quantity * bookingInfor.ComboSelected[c].TotalPriceInCombo;
    //}

    var totalMoneyWithoutTicket = 0;
    for (c = 0; c < bookingInfor.ComboSelected.length; c++) {
        totalMoney += bookingInfor.ComboSelected[c].Quantity * bookingInfor.ComboSelected[c].TotalPriceInCombo;
        var comboMoneyWithoutTicket = 0;
        for (var j = 0; j < bookingInfor.ComboSelected[c].ItemInCombos.length; j++) {
            if (bookingInfor.ComboSelected[c].ItemInCombos[j].IsTicketType !== true) {
                comboMoneyWithoutTicket += bookingInfor.ComboSelected[c].ItemInCombos[j].PriceInCombo;
            }
        }
        totalMoneyWithoutTicket += bookingInfor.ComboSelected[c].Quantity * comboMoneyWithoutTicket;

    }
    var textTotalMoney = ConvertToCurrency(totalMoney.toString()) + 'đ';
    $('.combo-total-money-value').text(textTotalMoney);
    totalMoneySeat = 0;
    for (i = 0; i < bookingInfor.seats.length; i++) {
        var isInCombo = false;
        for (c = 0; c < ticketTypeInCombos.length; c++) {
            if (ticketTypeInCombos[c].NewQuantity === undefined) {
                ticketTypeInCombos[c].NewQuantity = ticketTypeInCombos[c].Quantity;
            }

            if (bookingInfor.seats[i].TicketTypeId === ticketTypeInCombos[c].TicketTypeId && ticketTypeInCombos[c].NewQuantity > 0) {
                bookingInfor.seats[i].NewPrice = 0;
                bookingInfor.seats[i].ComboId = ticketTypeInCombos[c].ComboPackageId;
                ticketTypeInCombos[c].NewQuantity -= 1;
                isInCombo = true;
                break;
            }
        }
        if (!isInCombo) {
            bookingInfor.seats[i].NewPrice = bookingInfor.seats[i].Price;
            bookingInfor.seats[i].ComboId = undefined;
        }
        totalMoneySeat += bookingInfor.seats[i].NewPrice;
    }
    totalMoneySeat += totalMoney;
    bookingInfor.TotalMoney = totalMoneySeat;
    bookingInfor.ComboMoney = totalMoney;
    bookingInfor.TotalMoneyWithoutTicket = totalMoneyWithoutTicket;
    var textTotalMoneySeat = ConvertToCurrency(totalMoneySeat.toString()) + 'đ';
    $(".total-money-name").text(textTotalMoneySeat);

    paymentInfor.TotalMoneyNeedPay = bookingInfor.TotalMoney - paymentInfor.TotalDiscount;
    $('.money-need-pay').text(ConvertToCurrency(paymentInfor.TotalMoneyNeedPay.toString()) + 'đ');

    resetVoucher();

};

//giam so luong combo
var decreaseQuantityCombo = function (comboId) {
    //console.log(listCombo);
    //console.log(comboId);

    var isAdd = false;

    for (c = 0; c < bookingInfor.ComboSelected.length; c++) {
        if (comboId === bookingInfor.ComboSelected[c].ComboPackageId) {
            if (bookingInfor.ComboSelected[c].Quantity > 0)
                bookingInfor.ComboSelected[c].Quantity -= 1;
        }
    }
    quantity = $(".item-outer-pannel[data-combo-id='" + comboId + "'] .combo-quantity").attr("data-combo-quantity");
    quantity = parseInt(quantity);
    quantity -= 1;
    if (quantity >= 0) {

        $(".item-outer-pannel[data-combo-id='" + comboId + "'] .combo-quantity").text(quantity);
        $(".item-outer-pannel[data-combo-id='" + comboId + "'] .combo-quantity").attr("data-combo-quantity", quantity);
    }
    var totalMoney = 0;
    var totalMoneyWithoutTicket = 0;
    for (c = 0; c < bookingInfor.ComboSelected.length; c++) {
        totalMoney += bookingInfor.ComboSelected[c].Quantity * bookingInfor.ComboSelected[c].TotalPriceInCombo;
        var comboMoneyWithoutTicket = 0;
        for (var j = 0; j < bookingInfor.ComboSelected[c].ItemInCombos.length; j++) {
            if (bookingInfor.ComboSelected[c].ItemInCombos[j].IsTicketType !== true) {
                comboMoneyWithoutTicket += bookingInfor.ComboSelected[c].ItemInCombos[j].PriceInCombo;
            }
        }
        totalMoneyWithoutTicket += bookingInfor.ComboSelected[c].Quantity * comboMoneyWithoutTicket;

    }
    var textTotalMoney = ConvertToCurrency(totalMoney.toString()) + 'đ';
    $('.combo-total-money-value').text(textTotalMoney);
    checkQuantityCombo();
    totalMoneySeat = 0;
    for (i = 0; i < bookingInfor.seats.length; i++) {
        var isInCombo = false;
        for (c = 0; c < ticketTypeInCombos.length; c++) {
            if (ticketTypeInCombos[c].NewQuantity === undefined) {
                ticketTypeInCombos[c].NewQuantity = ticketTypeInCombos[c].Quantity;
            }
            if (bookingInfor.seats[i].TicketTypeId === ticketTypeInCombos[c].TicketTypeId && ticketTypeInCombos[c].NewQuantity > 0) {
                bookingInfor.seats[i].NewPrice = 0;
                bookingInfor.seats[i].ComboId = ticketTypeInCombos[c].ComboPackageId;
                ticketTypeInCombos[c].NewQuantity -= 1;
                isInCombo = true;
            }
        }
        if (!isInCombo) {
            bookingInfor.seats[i].NewPrice = bookingInfor.seats[i].Price;
            bookingInfor.seats[i].ComboId = undefined;
        }
        totalMoneySeat += bookingInfor.seats[i].NewPrice;
    }
    totalMoneySeat += totalMoney;
    var textTotalMoneySeat = ConvertToCurrency(totalMoneySeat.toString()) + 'đ';
    $(".total-money-name").text(textTotalMoneySeat);

    paymentInfor.TotalMoneyNeedPay = bookingInfor.TotalMoney - paymentInfor.TotalDiscount;
    $('.money-need-pay').text(ConvertToCurrency(paymentInfor.TotalMoneyNeedPay.toString()) + 'đ');

    bookingInfor.TotalMoney = totalMoneySeat;
    bookingInfor.ComboMoney = totalMoney;
    bookingInfor.TotalMoneyWithoutTicket = totalMoneyWithoutTicket;

    resetVoucher();
};

//lay tickettype và số lượng của tickettype
var ticketTypeInBooking = [];
var totalMoneySeat = 0;
var getTicketTypeInBooking = function () {

    for (i = 0; i < bookingInfor.seats.length; i++) {
        var isAdd = false;
        for (c = 0; c < ticketTypeInBooking.length; c++) {
            if (bookingInfor.seats[i].TicketTypeId === ticketTypeInBooking[c].TicketTypeId) {
                ticketTypeInBooking[c].Quantity += 1;
                isAdd = true;
            }
        }
        if (!isAdd) {
            var currentTicket = {};
            currentTicket.Quantity = 1;
            currentTicket.TicketTypeId = bookingInfor.seats[i].TicketTypeId;

            ticketTypeInBooking.push(currentTicket);
        }
        bookingInfor.seats[i].NewPrice = bookingInfor.seats[i].Price;
        totalMoneySeat += bookingInfor.seats[i].NewPrice;
    }
    bookingInfor.TotalMoney = totalMoneySeat;
    bookingInfor.ComboMoney = 0;
    bookingInfor.TotalMoneyWithoutTicket = 0;
    var textTotalMoneySeat = ConvertToCurrency(totalMoneySeat.toString()) + 'đ';
    $(".total-money-name").text(textTotalMoneySeat);

    paymentInfor.TotalMoneyNeedPay = bookingInfor.TotalMoney - paymentInfor.TotalDiscount;
    $('.money-need-pay').text(ConvertToCurrency(paymentInfor.TotalMoneyNeedPay.toString()) + 'đ');
};

//check so luong combo 
var ticketTypeInCombos = [];
var checkQuantityCombo = function () {
    ticketTypeInCombos = [];
    for (i = 0; i < bookingInfor.ComboSelected.length; i++) {
        for (j = 0; j < bookingInfor.ComboSelected[i].ItemInCombos.length; j++) {

            var itemInCombo = bookingInfor.ComboSelected[i].ItemInCombos[j];
            if (itemInCombo.IsTicketType) {
                var isAdd = false;
                for (var k = 0; k < ticketTypeInCombos.length; k++) {
                    if (ticketTypeInCombos[k].TicketTypeId === bookingInfor.ComboSelected[i].ItemInCombos[j].Item_Id
                        && ticketTypeInCombos[k].ComboPackageId === bookingInfor.ComboSelected[i].ComboPackageId) {
                        ticketTypeInCombos[k].Quantity += bookingInfor.ComboSelected[i].Quantity * itemInCombo.Quantity;
                        isAdd = true;
                    }
                }
                if (!isAdd) {
                    quantity = bookingInfor.ComboSelected[i].Quantity * itemInCombo.Quantity;
                    var ticketId = bookingInfor.ComboSelected[i].ItemInCombos[j].Item_Id;
                    var comboId = bookingInfor.ComboSelected[i].ComboPackageId;
                    ticketTypeInCombos.push({ TicketTypeId: ticketId, Quantity: quantity, ComboPackageId: comboId });
                }
                bookingInfor.ComboSelected[i].HasTicket = true;
            }
        }
    }
    for (i = 0; i < ticketTypeInBooking.length; i++) {
        quantity = 0;
        for (j = 0; j < ticketTypeInCombos.length; j++) {
            if (ticketTypeInBooking[i].TicketTypeId === ticketTypeInCombos[j].TicketTypeId) {
                quantity += ticketTypeInCombos[j].Quantity;
            }
        }
        if (ticketTypeInBooking[i].Quantity < quantity) {
            return false;
        }
    }
    return true;
};

var ConvertToCurrency = function (num) {
    num = num.replace(/,/g, "");
    return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
};
var discountMoney = 0;
var discountTicket = 0;
var ticketFimlFormat = "";
var isAddVoucher = false;
var s = 0;
var checkSelectDiscount = function () {
    discountMoney = 0;
    discountTicket = 0;
    //kiem tra voucher
    //for (i = 0; i < paymentInfor.Vouchers.length; i++) {
    //    if (paymentInfor.Vouchers[i].ItemType === "hang-hoa-ve") {
    //        discountMoney += paymentInfor.Vouchers[i].UnitPrice;
    //    } else {
    //        ticketFimlFormat = 've-' + bookingInfor.FilmFormatCode.toLowerCase();
    //        if (paymentInfor.Vouchers[i].ItemType === ticketFimlFormat) {
    //            isAddVoucher = false;
    //            for (s = 0; s < bookingInfor.seats.length; s++) {
    //                if (bookingInfor.seats[s].Voucher === undefined && bookingInfor.seats[s].Coupon === undefined) {
    //                    bookingInfor.seats[s].Voucher === paymentInfor.Vouchers[i];
    //                    if (bookingInfor.seats[s].ComboId != undefined) {
    //                        for (var k = 0; k < bookingInfor.ComboSelected.length; k++) {
    //                            if (bookingInfor.ComboSelected[k].ComboPackageId === bookingInfor.seats[s].ComboId) {
    //                                for (var l = 0; l < bookingInfor.ComboSelected[k].ItemInCombos.length; l++) {
    //                                    if (bookingInfor.ComboSelected[k].ItemInCombos[l].Item_Id === bookingInfor.seats[s].TicketTypeId) {
    //                                        discountTicket += bookingInfor.ComboSelected[k].ItemInCombos[l].PriceInCombo;
    //                                    }
    //                                }
    //                            }
    //                        }
    //                    } else {
    //                        discountTicket += bookingInfor.seats[s].Price;
    //                    }
    //                    isAddVoucher = true;
    //                    break;
    //                }
    //            }
    //            if (!isAddVoucher) {
    //                toastAlert("Không còn vé nào để sử dụng Voucher: " + paymentInfor.Vouchers[i].VoucherName + " - " + paymentInfor.Vouchers[i].VoucherCode);
    //                return false;
    //            }
    //        } else {
    //            toastAlert("Voucher: " + paymentInfor.Vouchers[i].VoucherName + " - " + paymentInfor.Vouchers[i].VoucherCode + " Không sử dụng được cho phim này");
    //            return false;
    //        }
    //    }
    //}

    //paymentInfor.VoucherDiscountMoney = discountMoney;
    paymentInfor.VoucherDiscountTicket = discountTicket;

    discountMoney = 0;
    discountTicket = 0;
    // kiem tra coupon
    //for (i = 0; i < paymentInfor.Coupons.length; i++) {
    //    if (paymentInfor.Coupons[i].ItemType === "hang-hoa-ve") {
    //        discountMoney += paymentInfor.Coupons[i].UnitPrice;
    //    } else {
    //        ticketFimlFormat = 've-' + bookingInfor.FilmFormatCode.toLowerCase();
    //        if (paymentInfor.Coupons[i].ItemType === ticketFimlFormat) {
    //            isAddVoucher = false;
    //            for (s = 0; s < bookingInfor.seats.length; s++) {
    //                if (bookingInfor.seats[s].Coupon === undefined && bookingInfor.seats[s].Coupon === undefined) {
    //                    bookingInfor.seats[s].Coupon === paymentInfor.Coupons[i];
    //                    if (bookingInfor.seats[s].ComboId !== undefined) {
    //                        for (var k = 0; k < bookingInfor.ComboSelected.length; k++) {
    //                            if (bookingInfor.ComboSelected[k].ComboPackageId === bookingInfor.seats[s].ComboId) {
    //                                for (var l = 0; l < bookingInfor.ComboSelected[k].ItemInCombos.length; l++) {
    //                                    if (bookingInfor.ComboSelected[k].ItemInCombos[l].Item_Id === bookingInfor.seats[s].TicketTypeId) {
    //                                        discountTicket += bookingInfor.ComboSelected[k].ItemInCombos[l].PriceInCombo;
    //                                    }
    //                                }
    //                            }
    //                        }
    //                    } else {
    //                        discountTicket += bookingInfor.seats[s].Price;
    //                    }
    //                    isAddVoucher = true;
    //                    break;
    //                }
    //            }
    //            if (!isAddVoucher) {
    //                toastAlert("Không còn vé nào để sử dụng Coupon: " + paymentInfor.Coupons[i].VoucherName + " - " + paymentInfor.Coupons[i].VoucherCode);
    //                return false;
    //            }
    //        } else {
    //            toastAlert("Voucher: " + paymentInfor.Coupons[i].VoucherName + " - " + paymentInfor.Coupons[i].VoucherCode + " Không sử dụng được cho phim này");
    //            return false;
    //        }
    //    }
    //}

    paymentInfor.CouponDiscountMoney = discountMoney;
    paymentInfor.CouponDiscountTicket = discountTicket;

    paymentInfor.TotalDiscountMoney = paymentInfor.CouponDiscountMoney + paymentInfor.VoucherDiscountMoney + paymentInfor.BetaPoint.money;
    paymentInfor.TotalDiscountTicket = paymentInfor.CouponDiscountTicket + paymentInfor.VoucherDiscountTicket;
    paymentInfor.TotalDiscount = paymentInfor.TotalDiscountTicket + paymentInfor.TotalDiscountMoney;

    paymentInfor.TotalDiscountMoney = paymentInfor.CouponDiscountMoney + paymentInfor.VoucherDiscountMoney;
    //if (paymentInfor.TotalDiscountMoney > bookingInfor.TotalMoneyWithoutTicket) {
    //    paymentInfor.TotalDiscountMoney = bookingInfor.TotalMoneyWithoutTicket;
    //}
    //paymentInfor.TotalDiscountMoney += paymentInfor.BetaPoint.money;
    paymentInfor.TotalDiscountTicket = paymentInfor.CouponDiscountTicket + paymentInfor.VoucherDiscountTicket;
    paymentInfor.TotalDiscount = paymentInfor.TotalDiscountTicket + paymentInfor.TotalDiscountMoney + paymentInfor.BetaPoint.money;

    if (bookingInfor.TotalMoney < paymentInfor.TotalDiscountTicket + paymentInfor.BetaPoint.money) {
        toastAlert("Số điểm Beta thanh toán lớn hơn tiền phải chi");
        return false;
    }
    if (paymentInfor.TotalDiscount > bookingInfor.TotalMoney) {
        paymentInfor.TotalDiscount = bookingInfor.TotalMoney;
    }

    paymentInfor.TotalMoneyNeedPay = bookingInfor.TotalMoney - paymentInfor.TotalDiscount;

    return true;

    //if ((bookingInfor.TotalMoneyWithoutTicket != undefined && bookingInfor.TotalMoneyWithoutTicket >= paymentInfor.TotalDiscountMoney)
    //    || (bookingInfor.TotalMoneyWithoutTicket === undefined && paymentInfor.TotalDiscountMoney === 0)) {
    //    return true;
    //} else {
    //    toastAlert("Tổng tiền được giảm lớn hơn tiền phải chi");
    //    return false;
    //}
};

var slectedPaymentCard = function (event, value) {
    $(".item-card").removeClass('selected');
    $(event).addClass('selected');
    paymentInfor.PaymentCardType = value;
};

var isBooking = false;
var thanhtoan = function () {
    if (isBooking) {
        toastAlert("Giao dịch đang được thực hiện, Nếu có lỗi xảy ra vui lòng quay về màn hình đặt vé và thực hiện lại giao dịch.");
        return;
    }
    isBooking = true;
    if (checkSelectDiscount()) {
        var paymentBooking = {};
        customerInfor.UserId = customerId;
        customerInfor.NumberCard = customerCard;
        paymentBooking.PaymentInfo = paymentInfor;
        paymentBooking.BookingInfo = bookingInfor;
        paymentBooking.CustomerInfo = customerInfor;
        checkSeatValid(paymentBooking);
    }
};
var checkSeatValid = function (paymentBooking) {
    var SeatIndexes = bookingInfor.seats.map(a => a.SeatIndex);
    var showId = bookingInfor.ShowId;
    var data = {};
    data.SeatIndexes = SeatIndexes;
    data.ShowId = showId;
    $(".image-loading-full-screen").show();
    $.post("/api/v1/erp/shows/check-seat-sales", data, function (result) {
        if (result.Status === 1) {
            var resultDataCheck = result.Data;
            $(".image-loading-full-screen").hide()
            var isSuccess = true;
            for (i = 0; i < resultDataCheck.length; i++) {
                if (resultDataCheck[i].SeatStatus >= 3) {
                    isSuccess = false;
                    var errorMesage = "Ghế " + getSeatName(bookingInfor.seats, resultDataCheck[i].SeatIndex) + " đã bị đặt trước.";
                    toastAlert(errorMesage);
                    return;
                }
            }
            if (isSuccess) {
                $(".image-loading-full-screen").show();
                $.post("/booking/create", paymentBooking, function (result) {
                    console.log(result);
                    $(".image-loading-full-screen").hide()
                    if (result.Status === 1) {
                        if (paymentInfor.PaymentCardType === 'airpay') {
                            //Gọi hàm nào đó lưu thông tin giao dịch của airpay
                            console.log(result.Data.AirpayOrderNo);
                        } else if (paymentInfor.PaymentCardType === 'momo') {
                            //Gọi hàm nào đó lưu thông tin giao dịch của momo
                            console.log(result.Data.MomoOrderNo);
                        } else if (paymentInfor.PaymentCardType === 'shopeepay') {
                            //Gọi hàm nào đó lưu thông tin giao dịch của shopeepay
                            console.log(result.Data.ShopeepayOrderNo);
                        } else if (paymentInfor.PaymentCardType === 'zalopay') {
                            //Gọi hàm nào đó lưu thông tin giao dịch của zalopay
                            console.log(result.Data.ZaloPayOrderNo);
                        }
                        window.location = result.Message;
                    } else if (result.Status === 0) {
                        window.location = result.Message;
                    }
                    else {
                        toastAlert(result.Message);
                    }
                });
            }
        }
        else {
            toastAlert(result.Message);
        }
    })
        .fail(function () {
            $(".image-loading-full-screen").hide();
            toastAlert("Xảy ra lỗi trong quá trình xử lý");
        });
};

var getSeatName = function (seats, seatIndex) {
    var result = seats.filter(s => s.SeatIndex === seatIndex);
    if (result.length > 0) {
        return result[0].SeatName;
    }
    return seatIndex.toString();
};
// ham goi ios
//JAVASCRIPT => MOBILE
//khi chuyen man hinh cac phuong thuc giam gia can cap nhat title 
//Khi thanh toan thanh cong
var title = "";
var backToMain = function (form) {
    //from = beta-point, voucher, coupon
    $(".image-loading-full-screen").hide();
    changeScreen("#content");
};

var checkAirpayTransactionStatus = function (airpayOrderNo) {
    if (isBooking) {
        $(".image-loading-full-screen").show();
        jQuery.ajax({
            type: 'GET',
            url: "/api/airpay/get-order-status?orderNo=" + airpayOrderNo,
            headers: {
                'Authorization': authoToken,
                'X-User': customerId
            },
            success: function (data, textStatus, jqXHR) {
                $(".image-loading-full-screen").hide();
                //console.log(data);
                if (data.Status === 1) {
                    if (data.Data === '100') {
                        //toastAlert(data.Message);
                        if (paymentInfor.PaymentCardType === 'airpay') {
                            //toastAlert("Đang thực hiện thanh toán Airpay");
                            if (window.androidkit) {
                                window.androidkit.postMessage("awaiting_payment");
                            } else {
                                window.webkit.messageHandlers["scriptHandler"].postMessage("awaiting_payment");
                            }
                        }
                    } else {
                        window.location = "/booking/thanhtoanairpay";
                    }
                } else {
                    toastAlert(data.Message);
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $(".image-loading-full-screen").hide();
                //getVoucherCoupon(customerId, cardType);
            }
        });
    }
    $(".image-loading-full-screen").hide();
};

var checkShopeePayTransactionStatus = function (referenceId) {
    if (isBooking) {
        $(".image-loading-full-screen").show();
        jQuery.ajax({
            type: 'GET',
            url: "/api/shopeepay-online/get-order-status?referenceId=" + referenceId,
            headers: {
                'Authorization': authoToken,
                'X-User': customerId
            },
            success: function (data, textStatus, jqXHR) {
                $(".image-loading-full-screen").hide();
                //console.log(data);
                if (data.Status === 1) {
                    if (data.Data.OrderStatusCode == '100' || data.Data.OrderStatusCode == '2') {
                        console.log('data.Data.OrderStatusCode = 100', 'false', paymentInfor.PaymentCardType);
                        if (paymentInfor.PaymentCardType === 'shopeepay') {
                            toastAlert(data.Message);
                            //toastAlert("Đang thực hiện thanh toán Shopeepay");
                            //if (window.androidkit) {
                            //    console.log('awaiting_payment_android');
                            //    window.androidkit.postMessage("awaiting_payment");
                            //} else {
                            //    console.log('awaiting_payment_ios');
                            //    window.webkit.messageHandlers["scriptHandler"].postMessage("awaiting_payment");
                            //}
                        }
                    } else if (data.Data.OrderStatusCode == '3') {
                        //window.location = "/booking/thanhtoanshopeepay";
                        console.log('data.Data.OrderStatusCode == 3', 'success');
                        //console.log('orderIdMobile', data.Data.OrderId);
                        if (window.androidkit) {
                            console.log('payment_success_android');
                            //window.androidkit.postMessage("payment_success", data.Data.TransactionId);
                            window.androidkit.postPaymentSuccess(data.Data.TransactionId);
                        } else {
                            console.log('payment_success_ios');
                            //window.webkit.messageHandlers["scriptHandler"].postMessage("payment_success");
                            window.location = "/booking/thanhtoanshopeepayonline";
                        }
                        //window.location = "/booking/thanhtoanshopeepay";
                    }
                } else {
                    console.log('status != 1');
                    toastAlert(data.Message);
                    if (window.androidkit) {
                        console.log('payment_failed');
                        window.androidkit.postMessage("payment_failed");
                    } else {
                        console.log('payment_failed');
                        window.webkit.messageHandlers["scriptHandler"].postMessage("payment_failed");
                    }
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                $(".image-loading-full-screen").hide();
                //getVoucherCoupon(customerId, cardType);
            }
        });
    }
    $(".image-loading-full-screen").hide();
};
  

var checkMomoTransactionStatus = function (orderId, resultCode, requestId, transId, message, responseTime, payType, extraData, partnerCode) {
    if (isBooking) {
        $(".image-loading-full-screen").show();
        let retryCount = 0;
        const maxRetries = 10;
        let retryTimeoutId = null;
        const currentSessionId = Date.now(); // Tạo session duy nhất cho chuỗi retry này
        let handledSessionId = null;
   
        function postFailedMessage() {
            if (handledSessionId === currentSessionId) return;
            handledSessionId = currentSessionId;
            clearTimeout(retryTimeoutId);                          
            $(".image-loading-full-screen").hide();
            if (window.androidkit) {
                window.androidkit.postMessage("payment_failed");
            } else {
                window.webkit.messageHandlers.logger.postMessage("failed");
                // window.webkit.messageHandlers["scriptHandler"].postMessage("payment_failed");
            }
        }

        function postSuccessMessage(transactionId) {
            if (handledSessionId === currentSessionId) return;
            handledSessionId = currentSessionId;
            clearTimeout(retryTimeoutId);
            $(".image-loading-full-screen").hide();
            if (window.androidkit) {
                window.androidkit.postPaymentSuccess(transactionId);
            } else {
                window.webkit.messageHandlers.logger.postMessage("success");
                // window.webkit.messageHandlers["scriptHandler"].postMessage("payment_success");
                window.location = "/booking/thanhtoanmomo";
            }
        }

        function callOrderStatus() {
            console.log('Initiating order status check...');
            jQuery.ajax({
                type: 'POST',
                url: "/api/momo/get-order-status",
                data: {
                    partnerCode: partnerCode,
                    orderId: orderId,
                    requestId: requestId,
                    transId: transId,
                    resultCode: resultCode,
                    message: message,
                    responseTime: responseTime,
                    payType: payType,
                    extraData: extraData
                },
                headers: {
                    'Authorization': authoToken,
                    'X-User': customerId
                },
                success: function (data) {
                    if (handledSessionId === currentSessionId) return;

                    console.log('data', data);

                    if (data.Status === 1) {
                        const statusCode = data.Data.OrderStatusCode;
                        
                        if (statusCode === '1000') {
                            retryCount++;
                            if (retryCount < maxRetries) {
                                console.log(`Retrying... Attempt ${retryCount}`);
                                // window.webkit.messageHandlers.logger.postMessage("retrying");
                                if(retryCount < 5)
                                {
                                    retryTimeoutId = setTimeout(callOrderStatus, 1000);
                                }
                                else{
                                    retryTimeoutId = setTimeout(callOrderStatus, 3000);
                                }
                                
                            } else {
                                // window.webkit.messageHandlers.logger.postMessage("max retrys reached");
                                console.log('Max retries reached. Failing.');
                                postFailedMessage();
                            }
                        } else if (statusCode === '0') {
                            // window.webkit.messageHandlers.logger.postMessage("successful");
                            console.log('Order successful.');
                            postSuccessMessage(data.Data.TransactionId);
                        } else {
                            // window.webkit.messageHandlers.logger.postMessage("unknown status code");
                            console.log(`Unknown status code: ${statusCode}. Failing.`);
                            postFailedMessage();
                        }
                    } else {
                        // window.webkit.messageHandlers.logger.postMessage("Status != 1");
                        console.log('Status != 1');
                        toastAlert(data.Message);
                        postFailedMessage();
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    if (handledSessionId === currentSessionId) return;
                    // window.webkit.messageHandlers.logger.postMessage("Error in ajax call");
                    console.log('Status != 1');
                    console.log(jqXHR, textStatus, errorThrown);
                    postFailedMessage();
                }
            });
        }

        callOrderStatus();
    }
};



var checkZaloPayTransactionStatus = function (appid, apptransid, pmcid, bankcode, amount, discountamount, status, checksum) {
    if (isBooking) {
        $(".image-loading-full-screen").show();
        jQuery.ajax({
            type: 'POST',
            url: "/api/zalopay/get-order-status",
            data: {
                appid: appid,
                apptransid: apptransid,
                pmcid: pmcid,
                bankcode: bankcode,
                bankcode: bankcode,
                amount: amount,
                discountamount: discountamount,
                status: status,
                checksum: checksum
            },
            headers: {
                'Authorization': authoToken,
                'X-User': customerId
            },
            success: function (data, textStatus, jqXHR) {
                $(".image-loading-full-screen").hide();
                console.log('data', data);
                if (data.Status === 1) {
                    console.log('status = 1');
                    if (data.Data.OrderStatusCode === '3') {
                        console.log('data.Data.OrderStatusCode = 3', 'false', paymentInfor.PaymentCardType);
                        //toastAlert(data.Message);
                        if (paymentInfor.PaymentCardType === 'zalopay') {
                            //toastAlert("Đang thực hiện thanh toán zalopay");
                            if (window.androidkit) {
                                console.log('awaiting_payment_android');
                                window.androidkit.postMessage("awaiting_payment");
                            } else {
                                console.log('awaiting_payment_ios');
                                window.webkit.messageHandlers["scriptHandler"].postMessage("awaiting_payment");
                            }
                        }
                    } else {
                        //window.location = "/booking/thanhtoanzalopay";
                        console.log('data.Data.OrderStatusCode != 3', 'success');
                        //console.log('orderIdMobile', data.Data.OrderId);
                        if (window.androidkit) {
                            console.log('payment_success_android');
                            //window.androidkit.postMessage("payment_success", data.Data.TransactionId);
                            window.androidkit.postPaymentSuccess(data.Data.TransactionId);
                        } else {
                            console.log('payment_success_ios');
                            //window.webkit.messageHandlers["scriptHandler"].postMessage("payment_success");
                            window.location = "/booking/thanhtoanzalopay";
                        }
                        //window.location = "/booking/thanhtoanzalopay";
                    }
                } else {
                    console.log('status != 1');
                    toastAlert(data.Message);
                    if (window.androidkit) {
                        console.log('payment_success_android');
                        window.androidkit.postMessage("payment_failed");
                    } else {
                        console.log('payment_success_ios');
                        window.webkit.messageHandlers["scriptHandler"].postMessage("payment_failed");
                    }
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(jqXHR, textStatus, errorThrown);
                $(".image-loading-full-screen").hide();
                //getVoucherCoupon(customerId, cardType);
            }
        });
    }
    $(".image-loading-full-screen").hide();
};

//get thong tin dat ve
var getBookingInfo = function (bookingInfo) {
    ////Test
    //bookingInfo = {};
    //bookingInfo.FilmName = "Lời nguyễn";
    //bookingInfo.FilmInfo = "2D Phụ đề | Hoạt hình | 120 phút";
    //bookingInfo.CinemaName = "Beta Cinemas mỹ đình";
    //bookingInfo.DateShow = "15/01/2020";
    //bookingInfo.ShowTime = "23:15";
    //bookingInfo.Combo = "STANDARD";
    //bookingInfo.TotalMoney = "150.000đ";
    //bookingInfo.Screen = "MD-P5";
    //bookingInfo.FilmPoster = "http://************:9034/media/images/2019/02/23/220px-toy-story-4-teaser-poster-101317-230219-32.jpg";
    //bookingInfo.FilmFormatCode = '2D PD';

    //bookingInfor.FilmFormatCode = bookingInfo.FilmFormatCode;
    $(".film-name").text(bookingInfo.FilmName);
    $(".film-info").text(bookingInfo.FilmInfo);
    $(".cinema-name-value").text(bookingInfo.CinemaName);
    $(".date-show-name-value").text(bookingInfo.DateShow);
    $(".time-name-value").text(bookingInfo.ShowTime);
    $(".screen-name-value").text(bookingInfo.Screen);
    $(".combo-name-value").text(bookingInfo.Combo);

    //$(".combo-name-value").text(bookingInfor.TotalMoney);
    var bg = "linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.58), rgb(255, 255, 255)), url('" + bookingInfo.FilmPoster + "')";
    $(".panel-film-info").css({ "background-image": bg });
};

//get thong tin khach hang
var getCustomerInfo = function (customer) {
    ////Test
    //customer = {};
    //customer.customerId = '74638165-AAD6-4F87-9571-891A922165FA';
    //customer.customerCard = '9001000000039048';
    if (customer) {
        customerId = customer.customerId;
        customerCard = customer.customerCard;
        customerInfor.customerId = customer.customerId;
        customerInfor.customerCard = customer.customerCard;
    }
};

getCustomerInfo();
getBookingInfo();

var getScreenType = function () {
    return screenType;
};



//MOBILE => JAVASCRIPT
//Khi mobile nhấn nút back
//khi thanh toan thanh cong

