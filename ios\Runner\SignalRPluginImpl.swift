import Foundation
import Flutter
import SwiftSignalRClient

class SignalRPluginImpl: NSObject, FlutterStreamHandler {
    private let signalRClient: SignalRClient
    private let methodChannel: FlutterMethodChannel
    private let eventChannel: FlutterEventChannel
    private var eventSink: FlutterEventSink?
    
    init(signalRClient: SignalRClient, methodChannel: FlutterMethodChannel, eventChannel: FlutterEventChannel) {
        self.signalRClient = signalRClient
        self.methodChannel = methodChannel
        self.eventChannel = eventChannel
        super.init()
    }
    
    func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "connect":
            handleConnect(call, result: result)
        case "disconnect":
            handleDisconnect(result: result)
        case "invoke":
            handleInvoke(call, result: result)
        case "on":
            handleOn(call, result: result)
        case "off":
            handleOff(call, result: result)
        case "getConnectionId":
            handleGetConnectionId(result: result)
        case "isConnected":
            handleIsConnected(result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func handleConnect(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let url = args["url"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "URL is required", details: nil))
            return
        }
        
        let headers = args["headers"] as? [String: String] ?? [:]
        
        signalRClient.connect(url: url, headers: headers) { success, error in
            if success {
                result(["success": true, "connectionId": self.signalRClient.getConnectionId() ?? ""])
            } else {
                result(FlutterError(code: "CONNECTION_ERROR", message: error ?? "Unknown error", details: nil))
            }
        }
    }
    
    private func handleDisconnect(result: @escaping FlutterResult) {
        signalRClient.disconnect()
        result(nil)
    }
    
    private func handleInvoke(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let method = args["method"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Method name is required", details: nil))
            return
        }
        
        let arguments = args["arguments"] as? [Any] ?? []
        
        signalRClient.invoke(method: method, arguments: arguments) { response, error in
            if let error = error {
                result(FlutterError(code: "INVOKE_ERROR", message: error, details: nil))
                return
            }
            
            result(response)
        }
    }
    
    private func handleOn(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let method = args["method"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Method name is required", details: nil))
            return
        }
        
        signalRClient.on(method: method) { arguments in
            self.sendEvent(eventName: "hubMethod", data: [
                "method": method,
                "arguments": arguments
            ])
        }
        
        result(nil)
    }
    
    private func handleOff(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let method = args["method"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Method name is required", details: nil))
            return
        }
        
        signalRClient.off(method: method)
        result(nil)
    }
    
    private func handleGetConnectionId(result: @escaping FlutterResult) {
        result(signalRClient.getConnectionId())
    }
    
    private func handleIsConnected(result: @escaping FlutterResult) {
        result(signalRClient.isConnectedStatus())
    }
    
    private func sendEvent(eventName: String, data: [String: Any]) {
        guard let eventSink = eventSink else {
            return
        }
        
        var eventData: [String: Any] = ["event": eventName]
        for (key, value) in data {
            eventData[key] = value
        }
        
        eventSink(eventData)
    }
    
    // MARK: - FlutterStreamHandler
    
    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        eventSink = events
        return nil
    }
    
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        eventSink = nil
        return nil
    }
}
