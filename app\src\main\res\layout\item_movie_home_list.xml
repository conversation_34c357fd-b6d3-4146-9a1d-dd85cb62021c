<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/itemMoveHomeRootView"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:paddingLeft="5dp"
    android:paddingRight="5dp"
    android:paddingBottom="10dp"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/itemMoveHomeCard"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:cardCornerRadius="5dp"
        app:cardElevation="1dp"
        android:layout_margin="0dp"
        app:cardBackgroundColor="@android:color/white"
        app:layout_constraintDimensionRatio="h, 228:360">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivFilmBanner"
            app:srcCompat="@drawable/test_event"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivAgeWarning"
            android:layout_width="35dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_normal"
            android:layout_marginTop="@dimen/margin_normal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:adjustViewBounds="true"
            app:srcCompat="@drawable/c_16"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivIsHot"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:scaleType="fitXY"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_gravity="right"
            app:srcCompat="@drawable/ic_hot"/>

    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDateStart"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="5dp"
        android:paddingLeft="@dimen/nav_header_vertical_spacing"
        android:paddingRight="@dimen/nav_header_vertical_spacing"
        tools:text="29-09-2019"
        tools:visibility="visible"
        android:visibility="gone"
        android:textColor="@color/text3fb7f9"
        android:textSize="14sp"
        app:fontFamily="@font/oswald_regular"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@+id/itemMoveHomeCard"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/nav_header_vertical_spacing"
        android:paddingRight="@dimen/nav_header_vertical_spacing"
        tools:text="Pacific Rim: Trỗi Dậy Pacific Rim: Trỗi Dậy Pacific Rim: Trỗi Dậy Pacific Rim: Trỗi Dậy"
        android:textColor="@color/text223849"
        android:textSize="14sp"
        android:singleLine="true"
        android:ellipsize="end"
        android:maxLines="1"
        app:layout_goneMarginTop="5dp"
        app:fontFamily="@font/oswald_regular"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@+id/tvDateStart"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/nav_header_vertical_spacing"
        android:paddingRight="@dimen/nav_header_vertical_spacing"
        tools:text="135 phút"
        android:textColor="@color/text223849"
        android:visibility="visible"
        android:gravity="center"
        android:textSize="14sp"
        android:alpha="0.55"
        app:fontFamily="@font/sanspro_regular"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivIsHot2"
        android:layout_width="41dp"
        android:layout_height="41dp"
        android:scaleType="fitXY"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:elevation="5dp"
        app:srcCompat="@drawable/ic_hot"/>

</androidx.constraintlayout.widget.ConstraintLayout>