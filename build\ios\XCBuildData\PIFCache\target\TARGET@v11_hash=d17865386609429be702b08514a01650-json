{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986f6182d9ced709f0b4d539057eec987a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SwiftSignalRClient", "PRODUCT_NAME": "SwiftSignalRClient", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98131aa6fd4aae40a40a79876b6f5af911", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98461bf9360538a5a244bf7412ffd1c855", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient.modulemap", "PRODUCT_MODULE_NAME": "SwiftSignalRClient", "PRODUCT_NAME": "SwiftSignalRClient", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e0503c58bbdea224a60def444a6b17e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98461bf9360538a5a244bf7412ffd1c855", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient.modulemap", "PRODUCT_MODULE_NAME": "SwiftSignalRClient", "PRODUCT_NAME": "SwiftSignalRClient", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9895eea8c6038850234501f5353f77c065", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c10fcd4c7e328e2cbc04b3b53ae622d3", "guid": "bfdfe7dc352907fc980b868725387e981e522d76f0d7c0b6529bb1ae37426315", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9842ea3ae8f60e955e633ea09d8401a108", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9870d6298bae25cfa3c9bd6b5c752f7d11", "guid": "bfdfe7dc352907fc980b868725387e98dbb615369c65ebe26b860a9055f1f3ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cb0a27082be93238747d46fc794c45f", "guid": "bfdfe7dc352907fc980b868725387e9861a0d2e162a8461b80b98e0f6bb85e1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb485dd6e843e5f469a542694da106aa", "guid": "bfdfe7dc352907fc980b868725387e98487d27b8ae16dae68b1d0ee014cd20f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c43d65af8017128d93a81345d36f55dc", "guid": "bfdfe7dc352907fc980b868725387e984cf624cd2f534acae29609e3c2c785ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dba0cd5c687148970161a59cad274778", "guid": "bfdfe7dc352907fc980b868725387e98f065971cfa0bf1eea0c5112c0f4cb381"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98819376420e7b442ba206a6221cda031c", "guid": "bfdfe7dc352907fc980b868725387e98212dd800466f89181f42017ebeaf4b51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840985f49240771bd7268fabce57bc343", "guid": "bfdfe7dc352907fc980b868725387e981fbd83e2f98be1ee99d04ad75ef3e249"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98803fa905e6bfdd50ca792186927ab21f", "guid": "bfdfe7dc352907fc980b868725387e98d83d4be79ad465952036e04eee368592"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830c62c9379cc7bd02a3800380b7c1939", "guid": "bfdfe7dc352907fc980b868725387e987b18f67da676332fb9453f626a9918f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb42fb768fb54c5450c77b472f9aab6a", "guid": "bfdfe7dc352907fc980b868725387e9805c21959ad87ca0d3a6443d4d296662d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98567baef68f64917f49e1c648de5e72ab", "guid": "bfdfe7dc352907fc980b868725387e981e3d4b1f2a4c65ab986e3a65ba6837a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a8bc92d898b4067bbdf5604936d44c1", "guid": "bfdfe7dc352907fc980b868725387e987ea31f1ea3e66ae42b2ca61772f108cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98966eaaefd6828b183f34ad44525c1d38", "guid": "bfdfe7dc352907fc980b868725387e982982c46609282aa6186c37db3c1f308b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887e68a14137fd689b49309c5020afb26", "guid": "bfdfe7dc352907fc980b868725387e98e26499e334dae3011c578186478d8f68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec5a1155857572ca4b0b3bd1355eab53", "guid": "bfdfe7dc352907fc980b868725387e98aa301573c7b629857f9e1be9f7a68e44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7cd90c7f7eae13a316cfa0ad58eed93", "guid": "bfdfe7dc352907fc980b868725387e98e9eab2a4d3ac6c9f127e84157a621aca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcfe6106c94d4279ecdffe9310c4230f", "guid": "bfdfe7dc352907fc980b868725387e9836cc662173b47dad84c9cdbb29e39e5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986453bbfcc3707336f15ff7913f76002c", "guid": "bfdfe7dc352907fc980b868725387e98cc7ab1a423c547ca193306a08e884453"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d4f4077ccf2bd5d05f1a9be3bb2d549", "guid": "bfdfe7dc352907fc980b868725387e98e95645f47613efccebfac81d8f0355aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893aa629102d7e2774f090cea7b108611", "guid": "bfdfe7dc352907fc980b868725387e982fb515bd18f6678f200032f55b4d52f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98308f081341d21a357828739f8fcfa111", "guid": "bfdfe7dc352907fc980b868725387e987e9c3cfe414a46e3e1670ac4b8cf188a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98833f318fc63b935f2335692554221699", "guid": "bfdfe7dc352907fc980b868725387e98b2f552981c6ee31beefcfeb5c2e7bc78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882499cd90f3763ee8076167e6d1e083f", "guid": "bfdfe7dc352907fc980b868725387e9824ae9bf564a316481b697c36c9bb1ad1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab597799ab3708ed744f9b747018f62a", "guid": "bfdfe7dc352907fc980b868725387e9881b19f41ccfcd36746e18c581eb36237"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824d6563af35508603d3cfa58006a3403", "guid": "bfdfe7dc352907fc980b868725387e989972a6d79e2dce63640e7a33331926fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98668db39ba7ad2da2b0600441a35dc518", "guid": "bfdfe7dc352907fc980b868725387e9887f5a9f251807b07c262c8d4017ec105"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ec36d1716b339feef80a9ca6e849baf", "guid": "bfdfe7dc352907fc980b868725387e988b104019a229a0ae6acebd9575cd3b42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98787caca8788792a07c754eb6b177d52e", "guid": "bfdfe7dc352907fc980b868725387e981675178f97b0159dc926b965c092ff4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fd00584b0c0eb6d97b8bf011dc63f24", "guid": "bfdfe7dc352907fc980b868725387e98a1c90a4a1af15d58d2345be109397528"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ec8ada4d419075a68bd0f62e403c380", "guid": "bfdfe7dc352907fc980b868725387e9866d479061b9d5c396881a5cfae10482f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a5ca24614d21b542f94656893fea0af", "guid": "bfdfe7dc352907fc980b868725387e989707ffa184dc499ee38aa56a0070fbff"}], "guid": "bfdfe7dc352907fc980b868725387e9896aa49f170f8ad3a9c031358c73c3e27", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98f07eb8b01901cac0d9fb6d27712d617d"}], "guid": "bfdfe7dc352907fc980b868725387e9898f08f700a47307b642072b8f18e6256", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9893a13ea77dae041dcd7f3706baf98dd8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e984091fa6cc3ef8bab223377c5869a38e7", "name": "SwiftSignalRClient", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98802cf383e0cf2c1ddeda423c8c89e6af", "name": "SwiftSignalRClient.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}