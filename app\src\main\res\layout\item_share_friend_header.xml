<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@color/grayBg">
    <LinearLayout android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/shape_white_radius">
        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:srcCompat="@drawable/ic_img_sharecode"
            android:adjustViewBounds="true"/>
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvNoticeVoucherPercent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:text="Với mỗi lần giới thiệu thành công, Beta tặng bạn
1 voucher  giảm giá 10% khi thực hiện giao dịch tại các rạp của Beta."
            android:textColor="@color/text1e1f28"
            android:layout_marginLeft="@dimen/activity_horizontal_margin"
            android:layout_marginRight="@dimen/activity_horizontal_margin"
            android:textSize="16sp"
            app:fontFamily="@font/sanspro_regular"
            android:gravity="center"
            android:layout_marginTop="32dp"
        />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvNoticeVoucher"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/share_invite_code_notice_benefit"
            android:textColor="@color/text1e1f28"
            android:layout_marginLeft="@dimen/activity_horizontal_margin"
            android:layout_marginRight="@dimen/activity_horizontal_margin"
            android:textSize="16sp"
            app:fontFamily="@font/sanspro_regular"
            android:gravity="center"
            android:layout_marginTop="8dp"
        />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvShareCode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:text="ABCCCF"
            android:textColor="@color/colorPrimaryDark"
            android:layout_marginLeft="@dimen/activity_horizontal_margin"
            android:layout_marginRight="@dimen/activity_horizontal_margin"
            android:textSize="32sp"
            app:fontFamily="@font/oswald_light"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:gravity="center"
            android:background="@drawable/border_colorapp_radius_dash"
            android:layout_marginTop="32dp"
        />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnShare"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="@string/share"
            android:layout_marginLeft="@dimen/activity_vertical_margin"
            android:layout_marginRight="@dimen/activity_vertical_margin"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            android:layout_marginBottom="@dimen/activity_vertical_margin"
            style="@style/ButtonOrange"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/llInputReferenceCode"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:layout_marginTop="@dimen/activity_vertical_margin"
        android:background="@drawable/shape_white_radius">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/input_invite_code_notice"
            android:textColor="@color/text1e1f28"
            android:layout_marginLeft="@dimen/activity_horizontal_margin"
            android:layout_marginRight="@dimen/activity_horizontal_margin"
            android:textSize="16sp"
            app:fontFamily="@font/sanspro_regular"
            android:gravity="center"
            android:layout_marginTop="24dp"
        />

        <LinearLayout android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            android:layout_marginBottom="@dimen/activity_vertical_margin"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edtInputCode"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:hint="@string/share_code"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:inputType="textCapCharacters"
                android:background="@drawable/border_gray_radius"
                android:layout_marginLeft="@dimen/activity_vertical_margin"
                android:layout_weight="1"/>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnConfirm"
                android:layout_width="120dp"
                android:layout_height="60dp"
                android:text="@string/confirm"
                android:layout_marginLeft="@dimen/activity_vertical_margin"
                android:layout_marginRight="@dimen/activity_vertical_margin"
                style="@style/ButtonPrimary"/>
        </LinearLayout>

    </LinearLayout>
</LinearLayout>