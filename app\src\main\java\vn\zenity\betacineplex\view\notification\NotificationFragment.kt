package vn.zenity.betacineplex.view.notification

import android.os.Bundle
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.LinearLayoutManager
import android.view.View
import android.view.ViewGroup
import com.thoughtbot.expandablerecyclerview.ExpandableRecyclerViewAdapter
import com.thoughtbot.expandablerecyclerview.models.ExpandableGroup
import com.thoughtbot.expandablerecyclerview.viewholders.ChildViewHolder
import com.thoughtbot.expandablerecyclerview.viewholders.GroupViewHolder
import kotlinx.android.synthetic.main.fragment_notification.*
import kotlinx.android.synthetic.main.item_content_notifi_in_list.view.*
import kotlinx.android.synthetic.main.item_title_notifi_in_list.view.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.model.Notification
import vn.zenity.betacineplex.view.HomeActivity
import vn.zenity.betacineplex.view.event.EventDetailFragment

/**
 * Created by Zenity.
 */

class NotificationFragment : BaseFragment(), NotificationContractor.View {
    private var numberUnread = 0
    override fun showListNotifications(notifes: List<Notification>) {
        activity?.runOnUiThread {
            numberUnread = notifes.count { it.isRead != true }
            (activity as? HomeActivity)?.numberNotification = numberUnread
            adapter = NotifiAdapter(notifes)
            recyclerView.adapter = adapter
        }
    }

    private val presenter = NotificationPresenter()
    private var adapter: NotifiAdapter? = null

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_notification
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.layoutManager = LinearLayoutManager(this.context)
        numberUnread = (activity as? HomeActivity)?.numberNotification ?: 0
        adapter = NotifiAdapter(listOf())
        presenter.getListNotification(1)
    }

    override fun onResume() {
        super.onResume()
        presenter.getListNotification(1)
    }

    override fun updateMenuNotifi(numberUnread: Int, isResumeUpdate: Boolean) {
        super.updateMenuNotifi(numberUnread, isResumeUpdate)
        if (isResumeUpdate) {
            presenter.getListNotification(1)
        }
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (isVisibleToUser) {
            presenter.getListNotification(1)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        adapter?.onSaveInstanceState(outState)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        adapter?.onRestoreInstanceState(savedInstanceState)
    }

    inner class NotifiAdapter(notifes: List<Notification>?) : ExpandableRecyclerViewAdapter<TitleHolder, ContentHolder>(notifes) {

        override fun onCreateGroupViewHolder(parent: ViewGroup, viewType: Int): TitleHolder {
            val view = parent.inflate(R.layout.item_title_notifi_in_list)
            return TitleHolder(view)
        }

        override fun onCreateChildViewHolder(parent: ViewGroup, viewType: Int): ContentHolder {
            val view = parent.inflate(R.layout.item_content_notifi_in_list)
            return ContentHolder(view)
        }

        override fun onBindChildViewHolder(holder: ContentHolder, flatPosition: Int, group: ExpandableGroup<*>, childIndex: Int) {
            holder.itemView.tvContent.text = (group as Notification).content
        }

        override fun onBindGroupViewHolder(holder: TitleHolder, flatPosition: Int, group: ExpandableGroup<*>) {
            val notification = group as Notification
            holder.itemView.viewUnread.visible(!notification.isRead)
            holder.itemView.tvTitle.text = notification.title
            holder.itemView.tvDate.text = notification.date?.dateConvertFormat(Constant.DateFormat.defaultFull, "dd/MM/yyyy , hh:mm")
            holder.itemView.ivDropdown?.rotation = -90f
            holder.itemView.setOnClickListener {
                holder.itemView.viewUnread.gone()
                if (!notification.isRead) {
                    presenter.readNotification(notification.Id, notification.ScreenCode)
                }
                if(notification.ScreenCode <= 0) {
                    openFragment(EventDetailFragment.getInstance(null, notification.newNotification, R.string.notification.getString()))
                } else {
                    provideAppLink("${notification.ScreenCode}" to (notification.RefId ?: ""))
                }
            }
        }

    }

    inner class TitleHolder(itemView: View) : GroupViewHolder(itemView)
    inner class ContentHolder(itemView: View) : ChildViewHolder(itemView)
}
