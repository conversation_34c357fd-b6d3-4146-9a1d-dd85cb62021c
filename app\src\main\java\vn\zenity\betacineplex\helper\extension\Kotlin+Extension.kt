package vn.zenity.betacineplex.helper.extension

import java.math.RoundingMode
import java.text.DecimalFormat
import java.util.*

inline fun <T> T.guard(block: T.() -> Unit): T {
    if (this == null) block(); return this
}

fun Long.toVNDCurrency(pFix: String = "đ") : String {
    val df = DecimalFormat("###,###")
    df.roundingMode = RoundingMode.FLOOR
    df.currency = Currency.getInstance(Locale("vi", "vn"))
    return try {
        df.format(this).replace(",", ".") + pFix
    } catch (_ : Exception) {
        ""
    }
}

fun Int.toVNDCurrency(pFix: String = "đ") : String {
    return this.toLong().toVNDCurrency(pFix)
}