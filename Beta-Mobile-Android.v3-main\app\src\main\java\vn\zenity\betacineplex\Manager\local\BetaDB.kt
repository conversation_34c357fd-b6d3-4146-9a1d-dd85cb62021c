package vn.zenity.betacineplex.Manager.local

import androidx.sqlite.db.SupportSQLiteDatabase
import androidx.room.Room
import androidx.room.migration.Migration
import vn.zenity.betacineplex.app.App

/**
 * Created by vinhdn on 04-Mar-18.
 */
class BetaDB {
    companion object {
        var instance: BetaDB? = null
        fun getInstance() : AppDatabase {
            if (instance == null) {
                instance = BetaDB()
            }
            return instance!!.appDatabase
        }
    }

    private var appDatabase: AppDatabase

    private val MIGRATION_1_2 = object : Migration(1, 2) {
        override fun migrate(database: SupportSQLiteDatabase) {
            database.execSQL("ALTER TABLE UserModel ADD COLUMN ClassName TEXT")
            database.execSQL("ALTER TABLE UserModel ADD COLUMN ClassCode TEXT")
            database.execSQL("ALTER TABLE UserModel ADD COLUMN QuantityOfVoucher INTEGER NOT NULL DEFAULT 0")
        }
    }

    private val MIGRATION_2_3 = object : Migration(2, 3) {
        override fun migrate(database: SupportSQLiteDatabase) {
            database.execSQL("ALTER TABLE UserModel ADD COLUMN AlmostExpiredPoint INTEGER NOT NULL DEFAULT 0")
            database.execSQL("ALTER TABLE UserModel ADD COLUMN AlmostExpiredPointDate TEXT")
        }
    }

    init {
        val context = App.shared()
        //Intiliaze the room database with database name
        appDatabase = Room.databaseBuilder(context, AppDatabase::class.java, "beta.sqlite")
                .addMigrations(MIGRATION_1_2)
                .addMigrations(MIGRATION_2_3)
                .allowMainThreadQueries()
                .build()
    }

    private fun getAppDatabase(): AppDatabase {
        return appDatabase
    }
}