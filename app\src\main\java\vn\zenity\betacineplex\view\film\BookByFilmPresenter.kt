package vn.zenity.betacineplex.view.film

import android.location.Location
import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.helper.extension.toCalendar
import vn.zenity.betacineplex.helper.extension.toStringFormat
import vn.zenity.betacineplex.model.FilmModel
import vn.zenity.betacineplex.model.GroupFilmBooking
import vn.zenity.betacineplex.model.GroupFilmBookingEntity
import java.lang.ref.WeakReference
import java.util.Calendar

/**
 * Created by Zenity.
 */

class BookByFilmPresenter : BookByFilmContractor.Presenter {

    private var disposable: Disposable? = null
    private var disposable1: Disposable? = null
    private var page: Int = 1
    private var groupFilmBookingEntity: GroupFilmBookingEntity? = null
    private var listMoveRapCurrent: ArrayList<GroupFilmBooking> = ArrayList()
    var showButtonLoadMore: Boolean = false

    override fun getTimeBooking(
        film: FilmModel,
        date: Calendar,
        location: Location?,
        isLoadMore: Boolean
    ) {
        if (isLoadMore) {
            this.view?.get()?.showLoadMore()
        } else {
            page = 1
            this.view?.get()?.showLoading()
        }
        disposable = APIClient.shared.filmAPI.getShowsV2WithLocation(
            film.FilmGroupId ?: "",
            date.toStringFormat(Constant.DateFormat.date) ?: return,
            location?.latitude ?: 0.0,
            location?.longitude ?: 0.0,
            page,
            5,
        ).map { data ->
            data.Data?.let {
                groupFilmBookingEntity = it
                page += 1
                it.Content.map { oldBook ->
                    GroupFilmBooking(
                        oldBook.CinemaId,
                        oldBook.CinemaName,
                        oldBook.CinemaName_F,
                        oldBook.Latitude,
                        oldBook.Longtitude,
                        oldBook.Distance,
                        oldBook.ListFilm

                    )
                } as ArrayList

            }
        }.applyOn().subscribe({ response ->
//            response?.sortBy { it.getDistanceToCurrentLocation(location) }
            response?.let {
                if (!isLoadMore) {
                    listMoveRapCurrent.clear()
                }
                listMoveRapCurrent.addAll(it)
                showButtonLoadMore =
                    listMoveRapCurrent.size < (groupFilmBookingEntity?.TotalElements ?: 0)
                this.view?.get()?.showTimeBooking(listMoveRapCurrent)
            }
            if (isLoadMore) {
                this.view?.get()?.hideLoadMore()
            } else {
                this.view?.get()?.hideLoading()
            }
        }, { error ->
            this.view?.get()?.showError(error.message ?: R.string.error_server.getString())
            if (isLoadMore) {
                this.view?.get()?.hideLoadMore()
            } else {
                this.view?.get()?.hideLoading()
            }
        })
    }

    override fun getShowDates(filmId: String) {
        this.view?.get()?.showLoading()
        disposable =
            APIClient.shared.filmAPI.getShowDatesV2(filmId).applyOn().subscribe({ response ->
                this.view?.get()?.hideLoading()
                response.Data?.let {
                    this.view?.get()?.showShowDates(it.map {
                        it.toCalendar() ?: Calendar.getInstance()
                    })
                }
            }, { error ->
                this.view?.get()
                    ?.showError(error.localizedMessage ?: R.string.error_server.getString())
                this.view?.get()?.hideLoading()
            })
    }

    override fun getFilmModel(filmId: String) {
        this.view?.get()?.showLoading()
        disposable1 = APIClient.shared.filmAPI.filmDetail(filmId).applyOn().subscribe({
            it.Data?.let {
                view?.get()?.showFilmModel(it)
            }
        }, {})
    }

    private var view: WeakReference<BookByFilmContractor.View?>? = null
    override fun attachView(view: BookByFilmContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        disposable1?.dispose()
        this.view?.clear()
        this.view = null
    }
}
