// Fix for WebView JavaScript errors
// This file contains the fixes for the JavaScript errors in webview_js.md

// 1. Fix getBookingInfo function to handle undefined parameter
var getBookingInfo = function (bookingInfo) {
    // Check if bookingInfo is provided and has required properties
    if (!bookingInfo) {
        console.log("getBookingInfo called without parameter - using default values");
        bookingInfo = {
            FilmName: "",
            FilmInfo: "",
            CinemaName: "",
            DateShow: "",
            ShowTime: "",
            Screen: "",
            Combo: "",
            FilmPoster: ""
        };
    }

    // Safely access properties with fallback values
    $(".film-name").text(bookingInfo.FilmName || "");
    $(".film-info").text(bookingInfo.FilmInfo || "");
    $(".cinema-name-value").text(bookingInfo.CinemaName || "");
    $(".date-show-name-value").text(bookingInfo.DateShow || "");
    $(".time-name-value").text(bookingInfo.ShowTime || "");
    $(".screen-name-value").text(bookingInfo.Screen || "");
    $(".combo-name-value").text(bookingInfo.Combo || "");

    // Safely handle background image
    if (bookingInfo.FilmPoster) {
        var bg = "linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.58), rgb(255, 255, 255)), url('" + bookingInfo.FilmPoster + "')";
        $(".panel-film-info").css({ "background-image": bg });
    }
};

// 2. Fix getCustomerInfo function to handle undefined parameter
var getCustomerInfo = function (customer) {
    // Check if customer is provided
    if (!customer) {
        console.log("getCustomerInfo called without parameter - using default values");
        customer = {
            customerId: "",
            customerCard: ""
        };
    }

    // Safely access properties
    customerId = customer.customerId || "";
    customerCard = customer.customerCard || "";
    customerInfor.customerId = customer.customerId || "";
    customerInfor.customerCard = customer.customerCard || "";
};

// 3. Add safe property access for bookingInfor
if (typeof bookingInfor === 'undefined') {
    var bookingInfor = {};
}

// Ensure bookingInfor has required properties
bookingInfor.FilmFormatCode = bookingInfor.FilmFormatCode || '2d';
bookingInfor.ShowId = bookingInfor.ShowId || '';
bookingInfor.TotalMoney = bookingInfor.TotalMoney || 0;
bookingInfor.seats = bookingInfor.seats || [];
bookingInfor.ComboSelected = bookingInfor.ComboSelected || [];

// 4. Add safe property access for other objects
if (typeof customerInfor === 'undefined') {
    var customerInfor = {};
}

if (typeof paymentInfor === 'undefined') {
    var paymentInfor = {};
    paymentInfor.BetaPoint = {};
    paymentInfor.BetaPoint.money = 0;
    paymentInfor.BetaPoint.point = 0;
    paymentInfor.Vouchers = {};
    paymentInfor.Coupons = {};
    paymentInfor.PaymentCardType = 'vn';
    paymentInfor.TotalDiscount = 0;
    paymentInfor.VoucherDiscountMoney = 0;
    paymentInfor.VoucherPaymentValidate = {};
    paymentInfor.VoucherPaymentDetail = {};
    paymentInfor.TotalMoneyNeedPay = 0;
}

// 5. Add error handling wrapper for all functions that access undefined properties
function safeAccess(obj, property, defaultValue) {
    try {
        return obj && obj[property] !== undefined ? obj[property] : defaultValue;
    } catch (e) {
        console.error("Error accessing property " + property + ":", e);
        return defaultValue;
    }
}

// 6. Override the problematic calls at the end of the file
// Instead of calling getBookingInfo() without parameters, call it safely
try {
    // Only call if the functions exist and we have data
    if (typeof getCustomerInfo === 'function') {
        getCustomerInfo();
    }
    
    if (typeof getBookingInfo === 'function') {
        // Don't call getBookingInfo() without parameters
        // It will be called by Flutter with proper data
        console.log("getBookingInfo function is ready - waiting for Flutter to call with data");
    }
} catch (e) {
    console.error("Error in initialization:", e);
}

// 7. Add console logging for debugging
console.log("WebView JavaScript fixes applied successfully");
console.log("bookingInfor initialized:", typeof bookingInfor);
console.log("customerInfor initialized:", typeof customerInfor);
console.log("paymentInfor initialized:", typeof paymentInfor);
