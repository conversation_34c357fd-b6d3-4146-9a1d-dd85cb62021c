import 'dart:convert';

import 'package:http/http.dart';

import '../../models/index.dart';
import '../index.dart';
import '../language_service.dart';

class SCinema {
  late String endpoint;
  late Map<String, String> headers;
  late Future<MApi?> Function({required Response result}) checkAuth;

  SCinema(this.endpoint, this.headers, this.checkAuth);

  Future<MApi?> getCinema({bool? isShowing/*, String? lang*/}) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();
    //
    // // Update language header
    // headers['language'] = language;
    // headers['x-language'] = language;

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/cites/cinemas',
        headers: headers,
        queryParameters: {},
      ),
    );
  }

  // Tương đương với case .cinemaShowDate(String)
  Future<MApi?> getShowDate({required String id/*, String? lang*/}) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();
    //
    // // Update language header
    // headers['language'] = language;
    // headers['x-language'] = language;

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/cinemas/$id/show-dates',
        headers: headers,
        queryParameters: {},
      ),
    );
  }

  Future<MApi?> getShow({required String id}) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();
    //
    // // Update language header
    // headers['language'] = language;
    // headers['x-language'] = language;

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v2/erp/films/$id/shows',
        headers: headers,
        queryParameters: {
          'dateShow': DateTime.now().toString(),
        },
      ),
    );
  }

  // Tương đương với case .cinemaFilmShow(String, String)
  Future<MApi?> getFilmShow({required String id, required String dateShow}) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();
    //
    // // Update language header
    // headers['language'] = language;
    // headers['x-language'] = language;

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v2/erp/cinemas/%7B$id%7D/shows',
        headers: headers,
        queryParameters: {'dateShow': dateShow},
      ),
    );
  }

  // This method is already defined above with a different URL
  // Keeping the v2 endpoint and updating the existing method

}
