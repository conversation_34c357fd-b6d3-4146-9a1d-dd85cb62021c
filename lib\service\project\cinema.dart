import 'dart:convert';

import 'package:http/http.dart';

import '../../models/index.dart';
import '../index.dart';
import '../language_service.dart';

class SCinema {
  late String endpoint;
  late Map<String, String> headers;
  late Future<MApi?> Function({required Response result}) checkAuth;

  SCinema(this.endpoint, this.headers, this.checkAuth);

  /// Get cinemas by city/province
  Future<MApi?> getCinema({bool? isShowing/*, String? lang*/}) async {
    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/cites/cinemas',
        headers: headers,
        queryParameters: {},
      ),
    );
  }

  /// Get all cinemas (for calculating nearest cinemas)
  /// Equivalent to CinemaAPI.listCinema in Android
  Future<MApi?> getAllCinemas() async {
    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/cinemas',
        headers: headers,
        queryParameters: {},
      ),
    );
  }

  /// Get cinema detail by ID
  /// Equivalent to CinemaAPI.getCinemaDetail in Android
  Future<MApi?> getCinemaDetail({required String cinemaId}) async {
    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/cinemas/$cinemaId',
        headers: headers,
        queryParameters: {},
      ),
    );
  }

  // Tương đương với case .cinemaShowDate(String)
  Future<MApi?> getShowDate({required String id/*, String? lang*/}) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();
    //
    // // Update language header
    // headers['language'] = language;
    // headers['x-language'] = language;

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/cinemas/$id/show-dates',
        headers: headers,
        queryParameters: {},
      ),
    );
  }

  Future<MApi?> getShow({required String id}) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();
    //
    // // Update language header
    // headers['language'] = language;
    // headers['x-language'] = language;

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v2/erp/films/$id/shows',
        headers: headers,
        queryParameters: {
          'dateShow': DateTime.now().toString(),
        },
      ),
    );
  }

  // Tương đương với case .cinemaFilmShow(String, String)
  Future<MApi?> getFilmShow({required String id, required String dateShow}) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();
    //
    // // Update language header
    // headers['language'] = language;
    // headers['x-language'] = language;

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v2/erp/cinemas/%7B$id%7D/shows',
        headers: headers,
        queryParameters: {'dateShow': dateShow},
      ),
    );
  }

 Future<MApi?> selectSeat({required String showId, required int seatIndex}) async {
    return checkAuth(
      result: await BaseHttp.post(
        url: '$endpoint/api/v1/erp/shows/$showId/seats/$seatIndex/select-seat',
        headers: {...headers,},
        queryParameters: {}, body: null,
      ),
    );
  }
 Future<MApi?> returnSeat({required String showId, required int seatIndex}) async {
    return checkAuth(
      result: await BaseHttp.post(
        url: '$endpoint/api/v1/erp/shows/$showId/seats/$seatIndex/return-seat',
        headers: headers,
        queryParameters: {}, body: null,
      ),
    );
  }
Future<MApi?> mySeat({required String showId}) async {
    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/shows/$showId/seats/my-seats',
        headers: headers,
        queryParameters: {}
      ),
    );
  }
}
