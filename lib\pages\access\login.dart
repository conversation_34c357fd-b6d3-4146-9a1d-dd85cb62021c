import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:device_info_plus/device_info_plus.dart';

import '../../utils/index.dart';
import '/constants/index.dart';
import '/core/index.dart';
import '/cubit/index.dart';
import '/models/index.dart';
import '/widgets/index.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  String? _deviceId;

  @override
  Widget build(BuildContext context) {
    // Set screen size for responsive design
    CSpace.setScreenSize(context);

    return Scaffold(
      appBar: appBar(title: 'Đăng nhập',titleColor: Colors.white),
      body: Stack(alignment: Alignment.topCenter, children: [
        // Background gradient and image
        Container(
          decoration: BoxDecoration(
            color: CColor.blue,
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                // Colors.white,
                CColor.blue.shade50,
                CColor.blue.shade300,
                CColor.blue.shade400,
                CColor.blue.shade500,
                CColor.blue
              ],
            ),
          ),
          // child: Opacity(
          //   opacity: 0.1,
          //   child: Image.asset('assets/images/login_background.png',
          //       fit: BoxFit.cover, height: CSpace.height, alignment: Alignment.center),
          // ),
        ),

        // Main content
        SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: CSpace.xl5),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height -
                      (MediaQuery.of(context).padding.top + kToolbarHeight),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    const VSpacer(CSpace.xl5),
                    const Text(
                      "Đăng nhập",
                      style: TextStyle(
                        fontSize: CFontSize.xl,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    // SizedBox(
                    //   height: CSpace.height * 0.15,
                    //   child: Image.asset('assets/images/logo.png'),
                    // ),

                    // Form section
                    Column(
                      children: [
                        // Form fields
                        WForm<MUser>(
                          key: _formKey,
                          list: _listFormItem,
                          onInit: (controllers) {
                            // Store controllers for validation
                          },
                        ),

                        // Error message if any
                        if (_errorMessage != null && _errorMessage!.isNotEmpty)
                          Container(
                            width: double.infinity,
                            margin: const EdgeInsets.only(bottom: CSpace.base),
                            padding: const EdgeInsets.all(CSpace.base),
                            decoration: BoxDecoration(
                              color: Colors.red.withAlpha(25),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              _errorMessage!,
                              style: const TextStyle(
                                color: Colors.red,
                                fontSize: CFontSize.sm,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),

                        // Remember me and Forgot password row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                CustomCheckbox(
                                  onChanged: (val) {
                                    setState(() {
                                      rememberMe = !rememberMe;
                                    });
                                  },
                                  isChecked: rememberMe,
                                  rightMargin: 10,
                                  iconColor: Colors.white,
                                  strokeColor: Colors.white,
                                ),
                                const Text(
                                  'Ghi nhớ đăng nhập',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: CFontSize.sm
                                  )
                                )
                              ],
                            ),
                            WButton(
                              type: TextButton,
                              onPressed: () => context.goNamed(CRoute.forgotPassword),
                              child: Text(
                                '${'pages.login.login.Forgot password'.tr()}?',
                                style: const TextStyle(
                                  fontSize: CFontSize.sm,
                                  color: Colors.white,
                                  decoration: TextDecoration.underline,
                                )
                              ),
                            ),
                          ],
                        ),

                        const VSpacer(CSpace.xl3),

                        // Login button with loading state
                        BlocConsumer<BlocC<MUser>, BlocS<MUser>>(
                          listenWhen: (previous, current) =>
                              current.status == AppStatus.success ||
                              current.status == AppStatus.fails,
                          listener: (context, state) {
                            setState(() {
                              _isLoading = false;
                            });

                            if (state.status == AppStatus.success) {
                              GoRouter.of(context).go(CRoute.home);
                            } else if (state.status == AppStatus.fails) {
                              setState(() {
                                _errorMessage = 'Login failed. Please try again.';
                              });
                            }
                          },
                          builder: (context, state) => SizedBox(
                            width: double.infinity,
                            height: 50,
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                textStyle: TextStyle(
                                  color: CColor.blue,
                                  fontSize: CFontSize.base,
                                  fontWeight: FontWeight.bold,
                                ),
                                backgroundColor: Colors.white,
                                foregroundColor: CColor.blue,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                elevation: 2,
                              ),
                              onPressed: _isLoading ? null : () => _login(context),
                              child: _isLoading
                                ? const SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                                    ),
                                  )
                                : Text('pages.login.login.Log in'.tr()),
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Register option
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: CSpace.xl3),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            "pages.login.register.Do you already have an account?".tr(),
                            style: TextStyle(
                              color: Colors.white.withAlpha(230),
                              fontSize: CFontSize.sm
                            )
                          ),
                          WButton(
                            type: TextButton,
                            onPressed: () => context.goNamed(CRoute.register),
                            child: const Text(
                              'Register',
                              style: TextStyle(
                                fontSize: CFontSize.sm,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                decoration: TextDecoration.underline,
                              )
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),

        // Loading overlay
        if (_isLoading)
          Container(
            color: Colors.black.withAlpha(75),
            child: const Center(
              child: WLoading(),
            ),
          ),
      ]),
    );
  }

  // Login function
  void _login(BuildContext context) {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // Validate form
    // if (_validateInputs()) {
      context.read<BlocC<MUser>>().submit(
        notification: false,
        format: MUser.fromJson,
        api: (value, page, size, sort) async {
          // Add device ID and remember me flag
          // value['rememberMe'] = rememberMe;
          // if (_deviceId != null) {
          //   value['deviceId'] = _deviceId;ád
          // }

          // Make API call
          return RepositoryProvider.of<Api>(context).auth.login(body: {...value, 'deviceId': _deviceId});
        },
        submit: (data) => context.read<AuthC>().save(data: data, context: context),
      );
    // } else {
    //   setState(() {
        _isLoading = false;
    //     _errorMessage = 'Please enter valid email and password';
    //   });
    // }
  }

  // Validate form inputs
  // bool _validateInputs() {
  //   // Basic validation
  //   if (_listFormItem.isEmpty) return false;
  //
  //   // Check if username and password are not empty
  //   final usernameItem = _listFormItem.firstWhere((item) => item.name == 'username', orElse: () => MFormItem());
  //   final passwordItem = _listFormItem.firstWhere((item) => item.name == 'password', orElse: () => MFormItem());
  //
  //   if (usernameItem.value == null || usernameItem.value.toString().isEmpty) {
  //     setState(() {
  //       _errorMessage = 'Email cannot be empty';
  //     });
  //     return false;
  //   }
  //
  //   if (passwordItem.value == null || passwordItem.value.toString().isEmpty) {
  //     setState(() {
  //       _errorMessage = 'Password cannot be empty';
  //     });
  //     return false;
  //   }
  //
  //   return true;
  // }

  @override
  void initState() {
    _init();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _getDeviceInfo();
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // Get device ID for API request
  Future<void> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      if (mounted) {
        if (Theme.of(context).platform == TargetPlatform.iOS) {
          final iosInfo = await deviceInfo.iosInfo;
          setState(() {
            _deviceId = iosInfo.identifierForVendor;
          });
        } else if (Theme.of(context).platform == TargetPlatform.android) {
          final androidInfo = await deviceInfo.androidInfo;
          setState(() {
            _deviceId = androidInfo.id;
          });
        }
      }
    } catch (e) {
      // If we can't get device ID, we'll proceed without it
      print('Error getting device ID: $e');
    }
  }

  bool rememberMe = false;
  List<MFormItem> _listFormItem = [];

  Future<void> _init() async {
    _listFormItem = [
      MFormItem(
        name: 'username',
        hintText: 'Nhập email',
        value: '',
        icon: 'assets/form/mail.svg',
        isWantBackgroundTran: true,
        onChange: (value, controllers) {
          setState(() {
            _errorMessage = null;
          });
        },
      ),
      MFormItem(
        name: 'password',
        value: '',
        hintText: 'pages.login.login.Password'.tr(),
        icon: 'assets/form/password.svg',
        isWantBackgroundTran: true,
        password: true,
        onChange: (value, controllers) {
          setState(() {
            _errorMessage = null;
          });
        },
      ),
    ];
  }
}
