<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/SwiftSignalRClient-Swift.h</key>
		<data>
		kz3JYLxvRVAy85MoTlQEsHXX7wQ=
		</data>
		<key>Headers/SwiftSignalRClient-umbrella.h</key>
		<data>
		GNqoF77vmGeGYEG4W49ViXi1I28=
		</data>
		<key>Info.plist</key>
		<data>
		EKm5GtBajm13NFYogfWa25OTIgg=
		</data>
		<key>Modules/SwiftSignalRClient.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		dFtW4o601YjbLC/GP62E6Pdotqk=
		</data>
		<key>Modules/SwiftSignalRClient.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		PQ8Fe5bjhr1bVkgYvvFWDrW++jU=
		</data>
		<key>Modules/SwiftSignalRClient.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/SwiftSignalRClient.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		zEFUT3pMfN/TxUmAEPz8/2Az+pY=
		</data>
		<key>Modules/SwiftSignalRClient.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		6zk8ozCY83kGQmGUvvD9fi8pSLM=
		</data>
		<key>Modules/SwiftSignalRClient.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/SwiftSignalRClient.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		cZDK9z66BFHJDKes8fb6Zo9UsQI=
		</data>
		<key>Modules/SwiftSignalRClient.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		gb6OQ9KBXDZec3lsxkxoKN3qsF8=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		4R+gxxl17keD2KxilB6SdUtstiU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/SwiftSignalRClient-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			1YLQ1fr03AY6sBqApocM3rxeAPMcZivYr24IA8h1nZs=
			</data>
		</dict>
		<key>Headers/SwiftSignalRClient-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			7UnpycF+pnxtAHR2UmS5vyLa4a3eJ5KonIpljkdcUdU=
			</data>
		</dict>
		<key>Modules/SwiftSignalRClient.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			2j1uwDm+1DIpoShhctATp1ArUJweyE5qF9PoVoUVT/Q=
			</data>
		</dict>
		<key>Modules/SwiftSignalRClient.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			U6/0GCPSycH0G0LRzJji736g0UvP/ZjqhgJKgmwpuXU=
			</data>
		</dict>
		<key>Modules/SwiftSignalRClient.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/SwiftSignalRClient.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			GwdSBMajIOvqSVrbjCFvwdCRdVDiPjJKoMrvs4tTw8A=
			</data>
		</dict>
		<key>Modules/SwiftSignalRClient.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			PmDUq2TKEz4m2kccXqMPXbCUw0ZjnTUngyL3OEgDvyk=
			</data>
		</dict>
		<key>Modules/SwiftSignalRClient.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/SwiftSignalRClient.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			i1Cuf4hhSSVuYi03Jg4tiATWd4PnFPGEhcF6rih4bJU=
			</data>
		</dict>
		<key>Modules/SwiftSignalRClient.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			wjbIktHm2qP8qBqKu+Z/huNKWD+BXTbcbhCaeY7vxaU=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			Q34ellrFGK2dtVY0mw+quexqd/22tnfThtOKSQO4eFY=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
