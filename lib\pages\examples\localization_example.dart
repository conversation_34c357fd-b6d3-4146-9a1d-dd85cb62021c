import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_app/service/language_service.dart';
import 'package:flutter_app/widgets/language_app_bar_button.dart';
import 'package:flutter_app/widgets/language_switcher.dart';

/// An example screen that demonstrates the use of localization
class LocalizationExampleScreen extends StatefulWidget {
  const LocalizationExampleScreen({Key? key}) : super(key: key);

  @override
  State<LocalizationExampleScreen> createState() => _LocalizationExampleScreenState();
}

class _LocalizationExampleScreenState extends State<LocalizationExampleScreen> {
  final LanguageService _languageService = LanguageService();
  bool _isEnglish = false;
  
  @override
  void initState() {
    super.initState();
    _checkCurrentLanguage();
  }
  
  Future<void> _checkCurrentLanguage() async {
    final isEnglish = await _languageService.isEnglish();
    if (mounted) {
      setState(() {
        _isEnglish = isEnglish;
      });
    }
  }
  
  void _refreshUI() {
    setState(() {});
    _checkCurrentLanguage();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Localization Example'.tr()),
        actions: [
          LanguageAppBarButton(
            onLanguageChanged: _refreshUI,
            showDialog: false,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              title: 'Current Language',
              content: _isEnglish ? 'English' : 'Tiếng Việt',
            ),
            const SizedBox(height: 24),
            
            _buildSection(
              title: 'Language Switchers',
              content: null,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('1. Language Switcher with Label:'),
                  const SizedBox(height: 8),
                  LanguageSwitcher(
                    onLanguageChanged: _refreshUI,
                  ),
                  
                  const SizedBox(height: 16),
                  const Text('2. Language Switcher without Label:'),
                  const SizedBox(height: 8),
                  LanguageSwitcher(
                    showLabel: false,
                    onLanguageChanged: _refreshUI,
                  ),
                  
                  const SizedBox(height: 16),
                  const Text('3. Language Dropdown:'),
                  const SizedBox(height: 8),
                  LanguageDropdown(
                    onChanged: (language) {
                      _refreshUI();
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  const Text('4. Language Flag Button:'),
                  const SizedBox(height: 8),
                  LanguageFlagButton(
                    onLanguageChanged: _refreshUI,
                    showDialog: false,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            _buildSection(
              title: 'Translated Text Examples',
              content: null,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTranslatedText('Bt.OK'),
                  _buildTranslatedText('Bt.Yes'),
                  _buildTranslatedText('Bt.Cancel'),
                  _buildTranslatedText('Bt.Confirm'),
                  _buildTranslatedText('Bt.Continue'),
                  _buildTranslatedText('Bt.TimeLeft'),
                  const SizedBox(height: 16),
                  _buildTranslatedText('Cinema.Screen'),
                  _buildTranslatedText('Cinema.SeatTypes'),
                  _buildTranslatedText('Cinema.Standard'),
                  _buildTranslatedText('Cinema.VIP'),
                  _buildTranslatedText('Cinema.Couple'),
                  _buildTranslatedText('Cinema.Premium'),
                  _buildTranslatedText('Cinema.Available'),
                  _buildTranslatedText('Cinema.Selected'),
                  _buildTranslatedText('Cinema.Sold'),
                  _buildTranslatedText('Cinema.Reserved'),
                  _buildTranslatedText('Cinema.SelectSeats'),
                  _buildTranslatedText('Cinema.SelectedSeats'),
                  _buildTranslatedText('Cinema.TotalPrice'),
                  _buildTranslatedText('Cinema.ProceedToPayment'),
                  _buildTranslatedText('Cinema.TimeLeft'),
                  _buildTranslatedText('Cinema.SeatSelection'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSection({
    required String title,
    String? content,
    Widget? child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 8),
        if (content != null)
          Text(
            content,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        if (child != null) child,
      ],
    );
  }
  
  Widget _buildTranslatedText(String key) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 180,
            child: Text(
              key,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const Text(': '),
          Text(key.tr()),
        ],
      ),
    );
  }
}
