{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9848cf260bf926ea827f13b7ee9fdd4607", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d4f89401b11e60a802a7dec7b6f000c3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc07b9acaa1f3e393fddd8bc11b9695e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f702a4774034fcdd5368785ef3256ca", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc07b9acaa1f3e393fddd8bc11b9695e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98271a39dcbe2a1aed5418b810f7b36fbd", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98350d11f114a2fdac601dca5aee0f8f63", "guid": "bfdfe7dc352907fc980b868725387e98e7e37a096a1edd2dad955be022e44816"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819382a498e9b14b2806b43b7cfec1bdc", "guid": "bfdfe7dc352907fc980b868725387e98fc52219cdafdb74a7ad28076128ab78e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e379b5806837710a7258bab619100f5", "guid": "bfdfe7dc352907fc980b868725387e986713bed9b7bb92027754556f10f44489"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed0d1828d86902a6e0de10922b43cf69", "guid": "bfdfe7dc352907fc980b868725387e98c974f1e4bc0e138aed7aa9ea2788e2d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e83314914c6b8afa6f3613092f0279f", "guid": "bfdfe7dc352907fc980b868725387e9837d9dc0c367945f0aa3e87c7b61eef5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98799caebeb6d36a64600eb6583a44bb83", "guid": "bfdfe7dc352907fc980b868725387e98cea956348866953106a74a79856e3261"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f989d5b3d1cf70907b0441f332171a0", "guid": "bfdfe7dc352907fc980b868725387e98b16d4bb5e2fbc279970f4e6bff7d2787"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828a1de62d8883a750bb6436d793c760b", "guid": "bfdfe7dc352907fc980b868725387e986393a813253d2f4db2cb2219d9888f86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989af64591bd1a9fafc148b870ab0a1678", "guid": "bfdfe7dc352907fc980b868725387e980540c962797c5ac14ece34a2185691b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982db125911d92d5e377c31bc39b025def", "guid": "bfdfe7dc352907fc980b868725387e9845bab2152ca8665e3a7ccceccdd64481"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1ab92fbbb0b309bdc7095e9fdf154cf", "guid": "bfdfe7dc352907fc980b868725387e98a1ccb56005d3f083238dd5f7491b7fe9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd93fbe14a3e87c998ff48dcea8291fd", "guid": "bfdfe7dc352907fc980b868725387e986eac0567722ec5e062f130e73a8b548b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98588f087c78360235717de755d5a5adb4", "guid": "bfdfe7dc352907fc980b868725387e9890deb2c13498d959a1953d22a61c0f28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eca88999367dd79d1ea38d6d6fd635e", "guid": "bfdfe7dc352907fc980b868725387e98cdba2b776fe5301a07f3b4d7dbe5ad01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8460e54545a79fae3b70bce0dbe8366", "guid": "bfdfe7dc352907fc980b868725387e9889f24a0fec238b570b6411a27f6b752c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5acc41df0a6a8feab7176afef069c3e", "guid": "bfdfe7dc352907fc980b868725387e98cff74ca1f5df296eb278258d83e2bfba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983eb7d8b86e6609e32d2db15dbd95467b", "guid": "bfdfe7dc352907fc980b868725387e9806b587d8470e8c0a2fd75e2f30090323", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fc7b71540293e592c8094aa1cb248a3", "guid": "bfdfe7dc352907fc980b868725387e98ae0ffc38ce20bc02dbd6d964b2eb37b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988250f303182da9c450f3c0c205f39a74", "guid": "bfdfe7dc352907fc980b868725387e9887fdb1322cfdd75600d858857bb5919f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986003d06536e2ba1ce44af39047199b5f", "guid": "bfdfe7dc352907fc980b868725387e98bd83788ba3500fd93fb10f74b49c0a0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8de8881f7f52bfb44af0ddb0d32855f", "guid": "bfdfe7dc352907fc980b868725387e987a7edd07567b05b8b3ba556988138633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860d9a4e4f312c49b49533e6919ad29d7", "guid": "bfdfe7dc352907fc980b868725387e98ccd4afcd278b0cdac3f54ebaa6b1b7c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a71762966b9f33b34a06ee71a378b9b", "guid": "bfdfe7dc352907fc980b868725387e98fe1d07d444967ec4fe458f6ef477e8ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd896f5d13d892b904ef21a672c9dc86", "guid": "bfdfe7dc352907fc980b868725387e9808e1a3acd1a81733066a75f898e7a8e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba13e70bbc25701eb4a75f9b899479cd", "guid": "bfdfe7dc352907fc980b868725387e98952320cc98080da822884a08c9d75159"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844cba3fd70474a5c3c181feafc072578", "guid": "bfdfe7dc352907fc980b868725387e9817d3b41b963cac816241d68a26f30cfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801ffd53c5512d830f16d117a7328c8bb", "guid": "bfdfe7dc352907fc980b868725387e98b2baf190298dd661986a4c227bd821c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b24da4b69e9123682b2361b112191bf", "guid": "bfdfe7dc352907fc980b868725387e98c1746a73b1fee6d7b4b7112946ba47f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f067f32b96acafa5cf582dd2a63d6bf", "guid": "bfdfe7dc352907fc980b868725387e98102b060301774013b3079efede694576"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ebaffe14c69988ec72cbc6b8ebb1b63", "guid": "bfdfe7dc352907fc980b868725387e98b2518b7ac6b1498a59312a271efa4454"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaff72d931c3ea5eb78344b603f0d491", "guid": "bfdfe7dc352907fc980b868725387e98c655e2b39a1d5fc9e39234efc6f14e41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bf1f448ca4e8dcddaa2c62c6dc6d08b", "guid": "bfdfe7dc352907fc980b868725387e98e37b89912b139edee6eefd44ba4380d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808b55ee28f8224d9c3d3750498ec3978", "guid": "bfdfe7dc352907fc980b868725387e986492cd70ab18ab73158e0762a49cf3ce"}], "guid": "bfdfe7dc352907fc980b868725387e983ac6ffbc79e2251257b7344d31d33e01", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9849f88c0a441ef21d5ed25032208d1e0c", "guid": "bfdfe7dc352907fc980b868725387e98247d12125e38a7965f9c3a780fd7e1d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b49ea4cef444205705d9d56c03a5cdd", "guid": "bfdfe7dc352907fc980b868725387e98a1cdd8fddfdb77ed658f1c130efcb2a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859c05c7500daf42f434dad9dcfd840d1", "guid": "bfdfe7dc352907fc980b868725387e98e5464a222d402c64212a091f7114a3d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885e0406679ad4ced99d1c3fc5b5811ea", "guid": "bfdfe7dc352907fc980b868725387e980751ad207747ee2ee60f46ec645add11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884dc4b05ac28450a3a380ae326549f67", "guid": "bfdfe7dc352907fc980b868725387e982cf1d16a4c726796c8211bfd95f5dbca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f59c0d4bc728be7314486e46c4e8a86", "guid": "bfdfe7dc352907fc980b868725387e982043fabf0f49938207ce59b3f120fdb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feb35e7b4057951d0558983d4685895c", "guid": "bfdfe7dc352907fc980b868725387e98de9006fc264da1502d4463ce45140064"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986daadedd4b2561388a2216db5d0e4cda", "guid": "bfdfe7dc352907fc980b868725387e98115d860a3f4e95186032d373084143dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2ad8a93389e9955ff9c22662e47fc78", "guid": "bfdfe7dc352907fc980b868725387e98e5ea5f5f96f2e098e11df05240f0d68b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff1f67096b7df987bdee35a4752a1f2", "guid": "bfdfe7dc352907fc980b868725387e98d0065ec2e6531be9a187662b68c21b2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989286821e2e62663b3a2811232544bae5", "guid": "bfdfe7dc352907fc980b868725387e98b5d477098555ee888cfdf6c2cbd7b80d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c935c88c4fd8a1970e12ed8d3161ee0", "guid": "bfdfe7dc352907fc980b868725387e98d146cd3ebb9a8862b4db744b716368f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c013b876d621d351388873dc34f603", "guid": "bfdfe7dc352907fc980b868725387e98cdf7d06a0ef01192435400c42d002738"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841e3512d9fff715a48b8a092d47653d0", "guid": "bfdfe7dc352907fc980b868725387e98f64b04e346f53290557ff32759ed8ad9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f426834f3995251edd43587bb12ba5e0", "guid": "bfdfe7dc352907fc980b868725387e98fa393c3c5921d46fa7b5349454b5c234"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988de8eefd2151a1753ee33c748e2299b8", "guid": "bfdfe7dc352907fc980b868725387e984034fd11a8ab062df010238e034d5f7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827941adf7b1d9a14f6d49e9a18fdabaa", "guid": "bfdfe7dc352907fc980b868725387e981ec061fac34cf8b9c63a0026fa802e4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98288705a47fe52578c3e9ced90f3826d3", "guid": "bfdfe7dc352907fc980b868725387e980a8a928484e9055d71229b720449c217"}], "guid": "bfdfe7dc352907fc980b868725387e98054b14c270fe618bc934a21096d37958", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9889bd587daab9f86468f71610691e2be6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "guid": "bfdfe7dc352907fc980b868725387e98ad86fc7c874abc2553020a622b1305c6"}], "guid": "bfdfe7dc352907fc980b868725387e98376e3def59283f0ab8f6b422c13e4b9a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983ba4d78c15db5f7411fae018cb028d1f", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9864e75e21c017b61ba057131219d8fe00", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}