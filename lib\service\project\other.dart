import 'dart:convert';

import 'package:http/http.dart';

import '../../models/index.dart';
import '../index.dart';
import '../language_service.dart';

class SOther {
  late String endpoint;
  late Map<String, String> headers;
  late Future<MApi?> Function({required Response result}) checkAuth;

  SOther(this.endpoint, this.headers, this.checkAuth);

  /// Get recruitment information
  /// Equivalent to Ecm.getRecruitment in Swift
  Future<MApi?> getRecruitment({
    int? pageSize,
    int? pageNumber,
    String? lang,
  }) async {
    final languageService = LanguageService();
    final language = lang ?? await languageService.getCurrentLanguage();

    String path = 'api/v1/ecm/tuyendung';
    path += '/$language';

    Map<String, dynamic> queryParameters = {};

    if (pageSize != null) {
      queryParameters['pageSize'] = pageSize.toString();
    }

    if (pageNumber != null) {
      queryParameters['pageNumber'] = pageNumber.toString();
    }

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/$path',
        headers: headers,
        queryParameters: queryParameters,
      ),
    );
  }

  /// Get FAQ topics
  /// Equivalent to Ecm.getTopic in Swift
  Future<MApi?> getFAQTopics({String? lang}) async {
    final languageService = LanguageService();
    final language = lang ?? await languageService.getCurrentLanguage();

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/ecm/topics/$language',
        headers: headers,
        queryParameters: {},
      ),
    );
  }

  /// Get FAQ information
  /// Equivalent to Ecm.getFAQs in Swift
  Future<MApi?> getFAQs({required String id}) async {
    return checkAuth(
      result: await BaseHttp.get(
      /// %7B là dấu {
        url: '$endpoint/api/v1/ecm/%7B$id%7D/faqs',
        headers: headers,
        queryParameters: {},
      ),
    );
  }

  /// Get policy information (Terms, Payment, Security, Company Info)
  /// Equivalent to Ecm.getTermId, getPaymentPolicyId, getSecurityId, getCompanyInfoId in Swift
  /// policyType should be one of:
  /// - Terms: 'mobile:app:dieukhoan'
  /// - Payment: 'mobile:app:dieukhoan-thanhtoan'
  /// - Security: 'mobile:app:dieukhoan-baomat'
  /// - Company Info: 'mobile:app:thongtin-congty'
  Future<MApi?> getPolicyId({required String policyType/*, String? lang*/}) async {
    // final languageService = LanguageService();
    // final language = lang ?? await languageService.getCurrentLanguage();

    // Append language to policy type
    // final fullPolicyType = '$policyType:$language';

    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/ecm/parameter',
        headers: headers,
        queryParameters: {'code': policyType},
      ),
    );
  }

  /// Get policy content
  /// Equivalent to Ecm.getNewWithId in Swift for policy content
  Future<MApi?> getPolicyContent({required String id}) async {
    return checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/ecm/%7B$id%7D/news',
        headers: headers,
        queryParameters: {
          // 'pageSize': '1',
          // 'pageNumber': '20',
        },
      ),
    );
  }
}
