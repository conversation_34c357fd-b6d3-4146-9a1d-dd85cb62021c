package vn.zenity.betacineplex.model.RequestModel

import vn.zenity.betacineplex.model.UserModel

/**
 * Created by tinhvv on 4/14/18.
 */
data class RegisterModel(var FullName: String = "",
                         var FirstName: String = "",
                         var LastName: String = "",
                         var Email: String= "",
                         var Password: String= "",
                         var PhoneOffice: String = "",
                         var PersonalId: String = "",
                         var BirthDate: String = "",
                         var Gender: String? = null,
                         var AddressCity: String? = null,
                         var AddressCityId: String? = null,
                         var AddressDistrict: String? = null,
                         var AddressDistrictId: String? = null,
                         var AddressStreet: String? = null,
                         var ReCaptchaToken : String? = null) {
    fun insertData(currentUser: UserModel) {
        this.FullName = currentUser.FullName ?: ""
        this.Email = currentUser.Email ?: ""
        this.PhoneOffice = currentUser.PhoneOffice ?: ""
        this.PersonalId = currentUser.PersonalId ?: ""
        this.BirthDate = currentUser.BirthDate ?: ""
        this.Gender = if(currentUser.Gender != null) "${currentUser.Gender}" else null
        this.AddressCity = currentUser.AddressCity
        this.AddressCityId = currentUser.AddressCityId
        this.AddressDistrict = currentUser.AddressDistrict
        this.AddressDistrictId = currentUser.AddressDistrictId
        this.AddressStreet = currentUser.AddressStreet
    }
}