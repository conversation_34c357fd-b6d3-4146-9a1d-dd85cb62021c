package vn.zenity.betacineplex.view.voucher

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

/**
 * Created by Vinh at 05/08/2019.
 */

class VoucherFreeDetailPresenter : VoucherFreeDetailContractor.Presenter {

    private var disposable: Disposable? = null

    override fun getVoucherDetail(id: String) {
        view?.get()?.showLoading()
        disposable = APIClient.shared.ecmAPI.getNewWithId(id).applyOn()
                .subscribe({
                    if (it.Data != null) {
                        view?.get()?.showVoucherDetail(it.Data!!)
                    } else {
                        view?.get()?.dataNull()
                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.hideLoading()
                })
    }

    override fun getCode(id: String) {
        view?.get()?.showLoading()
        disposable = APIClient.shared.ecmAPI.getVoucherCode(id).applyOn()
                .subscribe({
                    if (it.Data != null) {
                        view?.get()?.showCode(it.Data)
                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<VoucherFreeDetailContractor.View?>? = null
    override fun attachView(view: VoucherFreeDetailContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
