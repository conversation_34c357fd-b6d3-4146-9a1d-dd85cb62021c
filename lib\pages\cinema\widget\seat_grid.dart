import 'package:flutter/material.dart';
import 'package:flutter_app/pages/cinema/model/seat_model.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SeatGrid extends StatelessWidget {
  final List<List<SeatModel>> seatPositions;
  final List<SeatModel> selectedSeats;
  final Function(SeatModel) onSeatSelected;
  final Function(SeatModel) onSeatDeselected;

  const SeatGrid({
    super.key,
    required this.seatPositions,
    required this.selectedSeats,
    required this.onSeatSelected,
    required this.onSeatDeselected,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate the maximum number of seats in a row
    int maxSeatsInRow = 0;
    for (var row in seatPositions) {
      // Keep track of seats that have already been counted
      final Set<int> countedSeatIndices = {};
      int seatsCount = 0;

      for (var seat in row) {
        // Skip if this seat has already been counted as part of a couple seat
        if (seat.seatIndex != null && countedSeatIndices.contains(seat.seatIndex!)) {
          continue;
        }

        // Count couple seats as 2 spaces in width
        if (seat.seatTypeEnum == SeatType.COUPLE) {
          seatsCount += 2;
          // Mark both this seat and its couple seat as counted
          if (seat.seatIndex != null) countedSeatIndices.add(seat.seatIndex!);
          if (seat.coupleSeat?.seatIndex != null) countedSeatIndices.add(seat.coupleSeat!.seatIndex!);
        } else if (!seat.isWay && !seat.isBroken) {
          seatsCount += 1;
        }
      }
      maxSeatsInRow = seatsCount > maxSeatsInRow ? seatsCount : maxSeatsInRow;
    }

    // Calculate the effective number of seats per row to fit the screen
    // We're not using row labels, so we don't need to add 1
    // Use a higher multiplier to make seats smaller and fit the screen
    const double sizeMultiplier = 0.8; // Higher multiplier means more seats fit in a row
    final effectiveSeatsPerRow = (maxSeatsInRow * sizeMultiplier).ceil();

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate seat size based on available width
        final availableWidth = constraints.maxWidth;

        // Make seats larger by dividing by a smaller number
        final seatSize = (availableWidth / effectiveSeatsPerRow).floorToDouble();

        return SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 5), // Reduced top padding
                for (int col = 0; col < seatPositions.length; col++)
                  _buildRow(col, seatSize),
                const SizedBox(height: 5), // Reduced bottom padding
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRow(int col, double seatSize) {
    // Keep track of seats that have already been rendered
    final Set<int> renderedSeatIndices = {};

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 1), // Further reduced vertical padding
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Seats - no row label
            for (int row = 0; row < seatPositions[col].length; row++)
              _buildSeatWithTracking(seatPositions[col][row], seatSize, renderedSeatIndices),
          ],
        ),
      ),
    );
  }

  // Helper method to build a seat while tracking which seats have been rendered
  Widget _buildSeatWithTracking(SeatModel seat, double baseSeatSize, Set<int> renderedSeatIndices) {
    // Skip if this seat has already been rendered as part of a couple seat
    if (seat.seatIndex != null && renderedSeatIndices.contains(seat.seatIndex!)) {
      return const SizedBox.shrink(); // Return an empty widget
    }

    // If this is a couple seat, check if it has a linked couple seat
    if (seat.seatTypeEnum == SeatType.COUPLE) {
      if (seat.coupleSeat != null && seat.coupleSeat!.seatIndex != null) {
        // Mark both seats as rendered
        renderedSeatIndices.add(seat.seatIndex!);
        renderedSeatIndices.add(seat.coupleSeat!.seatIndex!);
      } else {
        // If no linked couple seat, check adjacent seats in the row
        // This is a fallback in case the linking process didn't work
        final rowIndex = _findRowIndex(seat);
        if (rowIndex != -1) {
          final row = seatPositions[rowIndex];
          final seatIndex = row.indexWhere((s) => s.seatIndex == seat.seatIndex);

          if (seatIndex != -1 && seatIndex < row.length - 1) {
            final nextSeat = row[seatIndex + 1];
            if (nextSeat.seatTypeEnum == SeatType.COUPLE &&
                !renderedSeatIndices.contains(nextSeat.seatIndex)) {
              // Mark both seats as rendered
              renderedSeatIndices.add(seat.seatIndex!);
              if (nextSeat.seatIndex != null) {
                renderedSeatIndices.add(nextSeat.seatIndex!);
              }

              // Temporarily link the seats for this rendering
              seat.coupleSeat = nextSeat;
              nextSeat.coupleSeat = seat;
            }
          }
        }
      }
    }

    return _buildSeat(seat, baseSeatSize);
  }

  // Find the row index for a given seat
  int _findRowIndex(SeatModel seat) {
    for (int i = 0; i < seatPositions.length; i++) {
      if (seatPositions[i].any((s) => s.seatIndex == seat.seatIndex)) {
        return i;
      }
    }
    return -1;
  }

  // Row label method removed as we're not showing row labels

  Widget _buildSeat(SeatModel seat, double baseSeatSize) {
    // Skip if it's a way or not a seat
    if (seat.isWay || seat.isBroken || seat.isNotUsed || seat.isEntranceExit) {
      return SizedBox(width: baseSeatSize, height: baseSeatSize);
      // return SizedBox.shrink(/*width: baseSeatSize, height: baseSeatSize*/);
    }

    // Determine if this seat is selected
    // For couple seats, check if either the main seat or the cou ple seat is selected
    final isSelected = selectedSeats.any((s) =>
      s.seatNumber == seat.seatNumber ||
      (seat.coupleSeat != null && s.seatNumber == seat.coupleSeat!.seatNumber));

    // Get seat image based on type and status
    String seatImage = _getSeatImage(seat, isSelected);

    // Determine seat width based on type
    // For couple seats, make them twice as wide to properly display the SVG
    // Use a slightly smaller multiplier for couple seats to prevent overflow
    double seatWidth = seat.seatTypeEnum == SeatType.COUPLE ? baseSeatSize * 1.8 : baseSeatSize;
    // Make seats slightly taller than they are wide for better appearance
    double seatHeight = baseSeatSize * 1.1; // Same height as width to save space

    return GestureDetector(
      onTap: () {
        if (seat.soldStatus == SeatSoldStatus.EMPTY && !isSelected) {
          onSeatSelected(seat);
        } else if (isSelected) {
          onSeatDeselected(seat);
        }
      },
      child: Container(
        width: seatWidth,
        height: seatHeight,
        margin: EdgeInsets.all(baseSeatSize * 0.04), // Reduced margin to fit more seats
        decoration: BoxDecoration(
          // Add a subtle shadow for depth
          boxShadow: isSelected ? [
            BoxShadow(
              color: Colors.black.withAlpha(20),
              blurRadius: 2,
              spreadRadius: 1,
              offset: const Offset(0, 1),
            ),
          ] : null,
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            SvgPicture.asset(
              seatImage,
              width: seatWidth,
              height: seatHeight,
              fit: BoxFit.contain, // Ensure the SVG fits properly
            ),
            Text(
              _getSeatLabel(seat),
              style: TextStyle(
                fontSize: baseSeatSize * 0.36, // Smaller font size
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.white : (seat.soldStatus == SeatSoldStatus.SOLD || seat.soldStatus == SeatSoldStatus.BOOKED) ? Colors.white :Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getSeatImage(SeatModel seat, bool isSelected) {
    // Base path for seat images
    const String basePath = 'assets/icon/cinema';

    // Default image paths for empty seats
    const String normalEmpty = '$basePath/ic_empty_normal_seat.svg';
    const String vipEmpty = '$basePath/ic_empty_vip_seat.svg';
    const String coupleEmpty = '$basePath/ic_empty_couple_seat.svg';

    // Selected seats (by current user)
    const String normalSelected = '$basePath/ic_select_normal_seat.svg';
    const String vipSelected = '$basePath/ic_select_vip_seat.svg';
    const String coupleSelected = '$basePath/ic_select_couple_seat.svg';

    // Seats in process of being selected (by other users)
    const String normalSold = '$basePath/ic_set_normal_seat.svg';
    const String vipSelecting = '$basePath/ic_process_vip_seat.svg';
    const String coupleSold = '$basePath/ic_set_couple_seat.svg';

    const String normalBooked = '$basePath/ic_set_normal_seat.svg';
    const String vipBooked = '$basePath/ic_set_vip_seat.svg';
    const String coupleBooked = '$basePath/ic_set_couple_seat.svg';

    // Sold seats (final state)
    const String normalSoldFinal = '$basePath/ic_sold_normal_seat.svg';
    const String vipSoldFinal = '$basePath/ic_sold_vip_seat.svg';
    const String coupleSoldFinal = '$basePath/ic_sold_couple_seat.svg';

    // Determine image based on seat type and status
    if (isSelected) {
      // User selected seats
      if (seat.seatTypeEnum == SeatType.NORMAL) return normalSelected;
      if (seat.seatTypeEnum == SeatType.VIP) return vipSelected;
      if (seat.seatTypeEnum == SeatType.COUPLE) return coupleSelected;
    }
    else if (seat.soldStatus == SeatSoldStatus.EMPTY) {
      // Empty seats
      if (seat.seatTypeEnum == SeatType.NORMAL) return normalEmpty;
      if (seat.seatTypeEnum == SeatType.VIP) return vipEmpty;
      if (seat.seatTypeEnum == SeatType.COUPLE) return coupleEmpty;
    }
    else if (seat.soldStatus == SeatSoldStatus.SELECTING ||
             seat.soldStatus == SeatSoldStatus.SELECTED) {
      // Seats being selected by others
      if (seat.seatTypeEnum == SeatType.NORMAL) return normalSold;
      if (seat.seatTypeEnum == SeatType.VIP) return vipSelecting;
      if (seat.seatTypeEnum == SeatType.COUPLE) return coupleSold;
    }  else if (seat.soldStatus == SeatSoldStatus.BOOKED) {
      // Seats being selected by others
      if (seat.seatTypeEnum == SeatType.NORMAL) return normalBooked;
      if (seat.seatTypeEnum == SeatType.VIP) return vipBooked;
      if (seat.seatTypeEnum == SeatType.COUPLE) return coupleBooked;
    }
    else {
      // Sold seats - use the new SVG files for sold seats
      if (seat.seatTypeEnum == SeatType.NORMAL) return normalSoldFinal;
      if (seat.seatTypeEnum == SeatType.VIP) return vipSoldFinal;
      if (seat.seatTypeEnum == SeatType.COUPLE) return coupleSoldFinal;
    }

    // Default fallback
    return normalEmpty;
  }

  String _getSeatLabel(SeatModel seat) {
    if (seat.seatNumber == null || seat.seatNumber!.isEmpty) return '';

    // For couple seats, show both seat numbers if available
    if (seat.seatTypeEnum == SeatType.COUPLE) {
      final String mainSeatNumber = seat.seatNumber!.length > 1
          ? seat.seatNumber!/*.substring(1)*/
          : seat.seatNumber!;

      // If we have a linked couple seat, use its number
      if (seat.coupleSeat != null && seat.coupleSeat!.seatNumber != null) {
        final coupleseatNumber = seat.coupleSeat!.seatNumber!.length > 1
            ? seat.coupleSeat!.seatNumber!
            : '';

        if (coupleseatNumber.isNotEmpty) {
          return '$mainSeatNumber \t $coupleseatNumber';
        }
      }
      // If no linked seat, try to infer the adjacent seat number
      else {
        // For couple seats without a linked seat, try to infer the adjacent seat number
        // Example: If this is seat A1, show "1-2"
        final seatNum = int.tryParse(mainSeatNumber);
        if (seatNum != null) {
          return '$mainSeatNumber-${seatNum + 1}';
        }
      }

      // If we couldn't determine a range, just show the seat number
      return mainSeatNumber;
    }

    // For regular seats, show just the number part (without the row letter)
    if (seat.seatNumber!.length > 1) {
      return seat.seatNumber!/*.substring(1)*/;
    }

    return seat.seatNumber!;
  }
}
