package vn.zenity.betacineplex.base

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.*
import android.view.inputmethod.InputMethodManager
import android.widget.FrameLayout
import android.widget.ProgressBar
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.Toolbar
import androidx.fragment.app.Fragment
import com.lcodecore.tkrefreshlayout.RefreshListenerAdapter
import com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout
import com.lcodecore.tkrefreshlayout.footer.LoadingView
import com.lcodecore.tkrefreshlayout.header.progresslayout.ProgressLayout
import kotlinx.android.synthetic.main.fragment_base.view.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.helper.view.PopupFragment
import vn.zenity.betacineplex.view.HomeActivity
import vn.zenity.betacineplex.view.auth.LoginFragment
import vn.zenity.betacineplex.view.cenima.CenimaDetailFragment
import vn.zenity.betacineplex.view.event.EventDetailFragment
import vn.zenity.betacineplex.view.film.BookByCinemaFragment
import vn.zenity.betacineplex.view.film.BookByFilmFragment
import vn.zenity.betacineplex.view.film.FilmDetailFragment
import vn.zenity.betacineplex.view.user.BookHistoryDetailFragment
import vn.zenity.betacineplex.view.user.VoucherFragment
import vn.zenity.betacineplex.view.user.point.BetaPointFragment
import vn.zenity.betacineplex.view.voucher.VoucherFreeDetailFragment


/**
 * Created by tranduc on 1/5/18.
 */
abstract class BaseFragment : Fragment(), IBaseView {

    open fun getPresenter(): IBasePresenter<IBaseView>? = null
    open val isTransfStatus = false
    open val isPaddingBottomBar = true

    open val isUsingViewBase = true
    protected open fun isShowToolbar(): Boolean = false
    protected open fun isShowToolbarIcon(): Boolean = false

    protected open var onMenuSelected: ((MenuItem) -> Unit)? = null

    private lateinit var mActivity: BaseActivity
    private lateinit var rootView: View
    protected var toolbar: Toolbar? = null

    open var canLoadmore: Boolean = false
    private var refreshView: TwinklingRefreshLayout? = null
    protected open val isRootFragment = false
    private var btnMenu: AppCompatImageView? = null

    fun showToolbar(isShow: Boolean) {
        if (isShow) {
            rootView.toolbar?.visible()
        } else {
            rootView.toolbar?.gone()
        }
    }

    protected open var mTitle: String = ""
    fun setTitle(title: String) {
        mTitle = title
        toolbar?.title = mTitle
    }

    protected abstract fun getLayoutRes(): Int

    protected open fun getMenu(): Int? {
        return null
    }

    open var progressBar: ProgressBar? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (activity !is BaseActivity) {
            Throwable("Activity no override BaseActivity")
        }
        mActivity = activity as BaseActivity
    }

    fun showBackButton() {
        rootView.toolbar?.setNavigationIcon(R.drawable.bi_ic_back_white)
        rootView.toolbar?.setNavigationOnClickListener {
            back()
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        if (!isUsingViewBase) {
            rootView = inflater.inflate(getLayoutRes(), container, false)
            return rootView
        }
        rootView = inflater.inflate(R.layout.fragment_base, container, false)
        if (!isShowToolbar()) {
            rootView.toolbar?.gone()
        }
//        if (isPaddingBottomBar)
//            rootView.setPadding(0, 0, 0, getNavigationBarHeight())
        if (!isShowToolbarIcon()) {
            rootView.toolbar?.navigationIcon = null
            rootView.toolbar?.toolbar?.title = ""
        }

        val contentView = inflater.inflate(getLayoutRes(), container, false)
        contentView?.let {
            rootView.contentLayout?.addChild(contentView)
            refreshView = contentView.bind(R.id.refreshView)
        }
        refreshView?.let {
            it.setHeaderView(ProgressLayout(it.context))
            it.setBottomView(LoadingView(it.context))
            it.setOnRefreshListener(object : RefreshListenerAdapter() {
                override fun onRefresh(refreshLayout: TwinklingRefreshLayout?) {
                    onRefresh()
                }

                override fun onLoadMore(refreshLayout: TwinklingRefreshLayout?) {
                    if (canLoadmore) {
                        onLoadmore()
                    } else {
                        refreshLayout?.finishLoadmore()
                    }
                }
            })
        }
        //Add new Framelayout
        if (isRootFragment) {
            context?.let { context ->
                val frameRoot = FrameLayout(context)
                frameRoot.id = R.id.container_popover
                (rootView as? ViewGroup)?.addView(frameRoot, -1, rootView.container.layoutParams)
            }
        }
        toolbar = rootView.bind(R.id.toolbar)
        progressBar = rootView.bind(R.id.progressBar)
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        activity?.let {
            view.setupHiddenKeyboard(it)
        }
        getPresenter()?.attachView(this)

        savedInstanceState?.let {
            mTitle = it.getString("title") ?: ""
        }

        reloadMenu()

        toolbar?.setOnMenuItemClickListener { item ->
            onMenuSelected?.invoke(item)
            return@setOnMenuItemClickListener true
        }
        toolbar?.title = mTitle

        view.findViewById<View>(R.id.btnBack)?.setOnClickListener {
            back()
        }

        view.findViewById<View>(R.id.btnMenu)?.setOnClickListener {
            showMenu()
        }
        btnMenu = view.findViewById(R.id.btnMenu)
        btnMenu?.gone()
        (activity as? HomeActivity)?.numberNotification?.let {
            updateMenuNotifi(it)
        }
    }

    protected fun showMenu() {
        (activity as? HomeActivity)?.openMenu()
    }



    protected fun reloadMenu() {
        toolbar?.menu?.clear()
        val menu = getMenu()
        menu?.let {
            toolbar?.inflateMenu(it)
        }
    }

    open fun updateMenuNotifi(numberUnread: Int, isResumeUpdate: Boolean = false) {
        if (numberUnread > 0) {
            btnMenu?.setImageResource(R.drawable.ic_menubuger_red)
        } else {
            btnMenu?.setImageResource(R.drawable.ic_menubuger_white)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putString("title", mTitle)
        super.onSaveInstanceState(outState)
    }

    protected fun provideAppLink(it: Pair<String, String>) {
        when(it.first) {
            "1" -> {
                val filmId = it.second
                openFragment(BookByFilmFragment.getInstance(filmId))
            }
            "2" -> {
                val cinemaId = it.second
                openFragment(CenimaDetailFragment.getInstance(cinemaId))
            }
            "3", "6" -> {
                val eventId = it.second
                openFragment(EventDetailFragment.getInstance(null, eventId = eventId))
            }
            "4" -> {
                val filmId = it.second
                openFragment(FilmDetailFragment.getInstance(filmId, true))
            }
            "5" -> {
                val cinemaId = it.second
                openFragment(BookByCinemaFragment.getInstance(null, cinemaId))
            }
            "7", "11" -> {
                val voucherId = it.second
                openFragment(VoucherFreeDetailFragment.getInstance(voucherId))
            }
            "8" -> {
                val voucherId = it.second
                openFragment(BookHistoryDetailFragment.getInstance(voucherId))
            }
            "9" -> {
                if (Global.share().isLogin) {
                    openFragment(VoucherFragment())
                } else {
                    openFragment(LoginFragment())
                }
            }
            "10" -> {
                openFragment(BetaPointFragment())
            }
        }
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (isVisibleToUser) {
            (activity as? HomeActivity)?.numberNotification?.let {
                updateMenuNotifi(it)
            }
        }
    }

    override fun showLoading(text: String?) {
        activity?.runOnUiThread {
            rootView.progressLayout?.visible()
        }
    }

    override fun hideLoading() {
        activity?.runOnUiThread {
            rootView.progressLayout?.gone()
        }
    }

    override fun showError(message: String, isToast: Boolean) {
        activity?.runOnUiThread {
            if (isToast) {
                toast(message)
            } else {
                showNotice(message)
            }
        }
    }

    override fun back() {
        if (isShowPopup()) {
            backInPopup()
        } else {
//            mActivity.onBackPressed()
            try {

                activity?.supportFragmentManager?.popBackStack()
            }catch (ex: IllegalStateException) {

            }
        }
        hideKeyboard()
    }

    fun hideKeyboard() {
        val view = activity?.currentFocus
        if (view != null) {
            val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
            imm?.hideSoftInputFromWindow(view.windowToken, 0)
        }
    }

    fun popBackStack(name: String, flags: Int, isFromActivity: Boolean = true) {
        if (isFromActivity) {
            mActivity.popBackStack(name, flags)
        } else {
            childFragmentManager.popBackStack(name, flags)
        }
    }

    fun popBackStackImmediate(name: String, flags: Int, isFromActivity: Boolean = true): Boolean {
        if (isFromActivity) {
            return mActivity.popBackStackImmediate(name, flags)
        } else {
            return childFragmentManager.popBackStackImmediate(name, flags)
        }
    }

    fun openFragment(fragment: BaseFragment, isAddToActivity: Boolean = true, addToBackStack: Boolean = true, name: String? = null) {
        hideKeyboard()
        if (isAddToActivity) {
            mActivity.openFragment(fragment, addToBackStack, name)
        } else {
            openChildFragment(fragment, addToBackStack, name)
        }
    }

    fun replaceFragment(fragment: BaseFragment, isAddToActivity: Boolean = true) {
        if (isAddToActivity) {
            mActivity.openFragment(fragment, false)
        } else {
            openChildFragment(fragment, false)
        }
    }

    fun openChildFragment(fragment: BaseFragment, addToBackStack: Boolean = true, name: String? = null) {
        if (!isRootFragment && parentFragment is BaseFragment) {
            (parentFragment as? BaseFragment)?.openChildFragment(fragment)
        } else {
            val transaction = childFragmentManager.beginTransaction()
                    .setCustomAnimations(R.anim.enter_from_right, R.anim.exit_to_left,
                            R.anim.enter_from_left, R.anim.exit_to_right)
            if (isRootFragment) {
                transaction.replace(R.id.container_popover, fragment)
            } else {
                transaction.replace(R.id.container, fragment)
            }
            if (addToBackStack) {
                transaction.addToBackStack(name)
            }
            transaction.commitAllowingStateLoss()
        }
    }

    override fun onLoadmore() {

    }

    override fun onRefresh() {

    }

    override fun onDestroyView() {
        super.onDestroyView()
        getPresenter()?.detachView()
    }

    protected fun isShowPopup(): Boolean {
        if (parentFragment == null) return false
        if (parentFragment is PopupFragment) {
            return true
        }
        return (parentFragment as? BaseFragment)?.isShowPopup() ?: false
    }

    protected fun backInPopup(): Boolean {
        if (parentFragment == null) return false
        if (parentFragment is PopupFragment) {
            (parentFragment as PopupFragment).back()
            return true
        }
        return (parentFragment as? BaseFragment)?.backInPopup() ?: false
    }

    protected fun dismissInPopup(): Boolean {
        if (parentFragment == null) return false
        if (parentFragment is PopupFragment) {
            (parentFragment as PopupFragment).dismissAllowingStateLoss()
            return true
        }
        return (parentFragment as? BaseFragment)?.dismissInPopup() ?: false
    }

    protected fun dismiss() {
        if (isShowPopup()) {
            dismissInPopup()
        } else {
            back()
        }
    }

    protected fun showNotice(message: String, handler: (() -> Unit)? = null) {
        activity?.runOnUiThread {
            context?.let {
                showConfirm(it, null, message.mapCode(), R.string.ok.getString(), rightButtonClickHandler = handler)
            }
        }
    }

    protected fun showNoticeWithCancelable(message: String, handler: (() -> Unit)? = null, cancelable: Boolean? = true) {
        activity?.runOnUiThread {
            context?.let {
                showConfirm(it, null, message.mapCode(), R.string.ok.getString(), rightButtonClickHandler = handler, cancelable = cancelable ?: true)
            }
        }
    }

    fun getStatusBarHeight(): Int {
        var result = 0
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = resources.getDimensionPixelSize(resourceId)
        }
        return result
    }

    private fun getNavigationBarHeight(): Int {
        val hasBackKey = KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_BACK)
        val hasHomeKey = KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_HOME)
        if (!hasBackKey || !hasHomeKey) return 0
        if (Build.VERSION.SDK_INT >= 21) {
            var result = 0
            val resourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android")
            if (resourceId > 0) {
                result = resources.getDimensionPixelSize(resourceId)
            }
            return result
        } else return 0
    }

    fun hasNavBar(): Boolean {
        val id = resources.getIdentifier("config_showNavigationBar", "bool", "android")
        return id > 0 && resources.getBoolean(id)
    }

    fun openLink(link: String) {
        try {
            startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(link)))
        } catch (_: android.content.ActivityNotFoundException) {

        }
    }
}