  Context android.content  packageManager android.content.Context  packageName android.content.Context  ApplicationInfo android.content.pm  InstallSourceInfo android.content.pm  PackageInfo android.content.pm  PackageManager android.content.pm  SigningInfo android.content.pm  	loadLabel "android.content.pm.ApplicationInfo  initiatingPackageName $android.content.pm.InstallSourceInfo  applicationInfo android.content.pm.PackageInfo  longVersionCode android.content.pm.PackageInfo  
signatures android.content.pm.PackageInfo  signingInfo android.content.pm.PackageInfo  versionCode android.content.pm.PackageInfo  versionName android.content.pm.PackageInfo  	loadLabel "android.content.pm.PackageItemInfo  GET_SIGNATURES !android.content.pm.PackageManager  GET_SIGNING_CERTIFICATES !android.content.pm.PackageManager  NameNotFoundException !android.content.pm.PackageManager  getInstallSourceInfo !android.content.pm.PackageManager  getInstallerPackageName !android.content.pm.PackageManager  getPackageInfo !android.content.pm.PackageManager  message 7android.content.pm.PackageManager.NameNotFoundException  toByteArray android.content.pm.Signature  apkContentsSigners android.content.pm.SigningInfo  hasMultipleSigners android.content.pm.SigningInfo  signingCertificateHistory android.content.pm.SigningInfo  Build 
android.os  SDK_INT android.os.Build.VERSION  P android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  Build %dev.fluttercommunity.plus.packageinfo  	ByteArray %dev.fluttercommunity.plus.packageinfo  CHANNEL_NAME %dev.fluttercommunity.plus.packageinfo  	CharArray %dev.fluttercommunity.plus.packageinfo  Context %dev.fluttercommunity.plus.packageinfo  
FlutterPlugin %dev.fluttercommunity.plus.packageinfo  FlutterPluginBinding %dev.fluttercommunity.plus.packageinfo  HashMap %dev.fluttercommunity.plus.packageinfo  Int %dev.fluttercommunity.plus.packageinfo  Long %dev.fluttercommunity.plus.packageinfo  
MessageDigest %dev.fluttercommunity.plus.packageinfo  
MethodCall %dev.fluttercommunity.plus.packageinfo  MethodCallHandler %dev.fluttercommunity.plus.packageinfo  
MethodChannel %dev.fluttercommunity.plus.packageinfo  NoSuchAlgorithmException %dev.fluttercommunity.plus.packageinfo  PackageInfo %dev.fluttercommunity.plus.packageinfo  PackageInfoPlugin %dev.fluttercommunity.plus.packageinfo  PackageManager %dev.fluttercommunity.plus.packageinfo  String %dev.fluttercommunity.plus.packageinfo  Suppress %dev.fluttercommunity.plus.packageinfo  Throws %dev.fluttercommunity.plus.packageinfo  also %dev.fluttercommunity.plus.packageinfo  applicationContext %dev.fluttercommunity.plus.packageinfo  apply %dev.fluttercommunity.plus.packageinfo  charArrayOf %dev.fluttercommunity.plus.packageinfo  first %dev.fluttercommunity.plus.packageinfo  getLongVersionCode %dev.fluttercommunity.plus.packageinfo  indices %dev.fluttercommunity.plus.packageinfo  invoke %dev.fluttercommunity.plus.packageinfo  
isNullOrEmpty %dev.fluttercommunity.plus.packageinfo  Result 3dev.fluttercommunity.plus.packageinfo.MethodChannel  Build 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  	ByteArray 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  CHANNEL_NAME 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  	CharArray 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  Context 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  FlutterPluginBinding 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  HashMap 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  Int 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  Long 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  
MessageDigest 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  
MethodCall 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  
MethodChannel 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  NoSuchAlgorithmException 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  PackageInfo 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  PackageManager 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  String 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  Suppress 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  Throws 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  also 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  applicationContext 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  apply 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  
bytesToHex 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  charArrayOf 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  first 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getBuildSignature 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getInstallerPackageName 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getLongVersionCode 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  indices 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  invoke 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  
isNullOrEmpty 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  
methodChannel 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  signatureToSha256 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  Build Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  CHANNEL_NAME Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  	CharArray Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  HashMap Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  
MessageDigest Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  
MethodChannel Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  NoSuchAlgorithmException Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  PackageManager Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  String Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  also Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  applicationContext Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  apply Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  charArrayOf Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  first Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  getLongVersionCode Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  indices Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  invoke Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  
isNullOrEmpty Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  Result Edev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.MethodChannel  NameNotFoundException Fdev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.PackageManager  NameNotFoundException 4dev.fluttercommunity.plus.packageinfo.PackageManager  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  
MessageDigest 
java.security  NoSuchAlgorithmException 
java.security  digest java.security.MessageDigest  getInstance java.security.MessageDigest  update java.security.MessageDigest  HashMap 	java.util  also java.util.HashMap  applicationContext java.util.HashMap  apply java.util.HashMap  getLongVersionCode java.util.HashMap  put java.util.HashMap  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  	Function1 kotlin  Nothing kotlin  String kotlin  Suppress kotlin  also kotlin  apply kotlin  charArrayOf kotlin  toString 
kotlin.Any  toInt kotlin.Byte  get kotlin.ByteArray  indices kotlin.ByteArray  size kotlin.ByteArray  get kotlin.CharArray  set kotlin.CharArray  toString kotlin.CharSequence  and 
kotlin.Int  	compareTo 
kotlin.Int  plus 
kotlin.Int  times 
kotlin.Int  toLong 
kotlin.Int  ushr 
kotlin.Int  toString kotlin.Long  	Companion 
kotlin.String  invoke 
kotlin.String  invoke kotlin.String.Companion  message kotlin.Throwable  IntIterator kotlin.collections  first kotlin.collections  indices kotlin.collections  
isNullOrEmpty kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  Throws 
kotlin.jvm  IntRange 
kotlin.ranges  first 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  first kotlin.sequences  String kotlin.text  first kotlin.text  indices kotlin.text  
isNullOrEmpty kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         