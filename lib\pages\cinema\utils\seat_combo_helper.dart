import 'package:flutter_app/pages/cinema/model/seat_model.dart';

/// Helper class để tạo combo string từ các ghế được chọn
/// Tương ứng với logic trong Android SelectChairFragment
class SeatComboHelper {
  /// Tạo combo string từ danh sách ghế được chọn
  /// 
  /// Logic tương ứng với Android:
  /// ```kotlin
  /// filmCombo = "${if (isHasDouble) ",DOUBLE" else ""}${if (isHasVIP) ",VIP" else ""}${if (isHasStandand) ",STANDARD" else ""}"
  /// if (filmCombo.isNotEmpty()) {
  ///     filmCombo = filmCombo.substring(1)
  /// }
  /// ```
  static String generateComboString(List<SeatModel> selectedSeats) {
    bool isHasStandard = false;
    bool isHasVIP = false;
    bool isHasDouble = false;

    // Ki<PERSON><PERSON> tra các loại ghế đã chọn
    for (var seat in selectedSeats) {
      if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
        isHasVIP = true;
      } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
        isHasDouble = true;
      } else {
        isHasStandard = true;
      }
    }

    // Tạo combo string theo thứ tự: DOUBLE, VIP, STANDARD (như Android)
    String filmCombo = "";
    if (isHasDouble) filmCombo += ",DOUBLE";
    if (isHasVIP) filmCombo += ",VIP";
    if (isHasStandard) filmCombo += ",STANDARD";

    // Loại bỏ dấu phẩy đầu tiên
    if (filmCombo.isNotEmpty) {
      filmCombo = filmCombo.substring(1);
    }

    return filmCombo;
  }

  /// Tạo string hiển thị ghế đã chọn
  /// Tương ứng với selectedSeat trong Android
  static String generateSelectedSeatString(List<SeatModel> selectedSeats) {
    String selectedSeat = "";
    for (int index = 0; index < selectedSeats.length; index++) {
      final seat = selectedSeats[index];
      selectedSeat += (seat.seatNumber ?? "");
      if (index < selectedSeats.length - 1) {
        selectedSeat += ", ";
      }
    }
    return selectedSeat;
  }

  /// Tính tổng giá tiền từ các ghế được chọn
  /// Tương ứng với totalPrice trong Android
  static int calculateTotalPrice(
    List<SeatModel> selectedSeats, {
    required int priceNormal,
    required int priceVIP,
    required int priceDouble,
  }) {
    int totalPrice = 0;

    for (var seat in selectedSeats) {
      // Kiểm tra nếu là ghế đôi và không hiển thị double (tương ứng với !seat.isShowDouble)
      if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
        // Logic cho ghế đôi - cần kiểm tra isShowDouble nếu có
        totalPrice += priceDouble;
      } else if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
        totalPrice += priceVIP;
      } else {
        totalPrice += priceNormal;
      }
    }

    return totalPrice;
  }

  /// Debug method để in thông tin ghế
  static void debugSeatInfo(List<SeatModel> selectedSeats) {
    print("=== Debug Seat Info ===");
    print("Total seats selected: ${selectedSeats.length}");
    
    for (int i = 0; i < selectedSeats.length; i++) {
      final seat = selectedSeats[i];
      String seatType = "STANDARD";
      
      if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
        seatType = "VIP";
      } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
        seatType = "DOUBLE";
      }
      
      print("Seat ${i + 1}: ${seat.seatNumber} - Type: $seatType");
    }
    
    final comboString = generateComboString(selectedSeats);
    final selectedSeatString = generateSelectedSeatString(selectedSeats);
    
    print("Combo string: '$comboString'");
    print("Selected seats: '$selectedSeatString'");
    print("======================");
  }

  /// Kiểm tra xem có ghế loại nào được chọn không
  static Map<String, bool> getSeatTypeFlags(List<SeatModel> selectedSeats) {
    bool isHasStandard = false;
    bool isHasVIP = false;
    bool isHasDouble = false;

    for (var seat in selectedSeats) {
      if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
        isHasVIP = true;
      } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
        isHasDouble = true;
      } else {
        isHasStandard = true;
      }
    }

    return {
      'hasStandard': isHasStandard,
      'hasVIP': isHasVIP,
      'hasDouble': isHasDouble,
    };
  }

  /// Tạo thông tin chi tiết về ghế để hiển thị
  static Map<String, dynamic> getSeatSummary(List<SeatModel> selectedSeats) {
    final flags = getSeatTypeFlags(selectedSeats);
    final comboString = generateComboString(selectedSeats);
    final selectedSeatString = generateSelectedSeatString(selectedSeats);

    int normalCount = 0;
    int vipCount = 0;
    int doubleCount = 0;

    for (var seat in selectedSeats) {
      if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
        vipCount++;
      } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
        doubleCount++;
      } else {
        normalCount++;
      }
    }

    return {
      'comboString': comboString,
      'selectedSeatString': selectedSeatString,
      'totalSeats': selectedSeats.length,
      'normalCount': normalCount,
      'vipCount': vipCount,
      'doubleCount': doubleCount,
      'flags': flags,
    };
  }
}
