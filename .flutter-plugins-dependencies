{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "audio_session", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audio_session-0.1.23\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-10.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "firebase_core", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging-15.0.4\\\\", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "fl_location", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\fl_location-4.4.2\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_contacts", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_contacts-1.1.9+2\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_inappwebview_ios", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_inappwebview_ios-1.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility-6.0.0\\\\", "native_build": true, "dependencies": []}, {"name": "geocoding_ios", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geocoding_ios-2.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "google_maps_flutter_ios", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_maps_flutter_ios-2.13.2\\\\", "native_build": true, "dependencies": []}, {"name": "image_gallery_saver", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_gallery_saver-2.0.3\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_ios", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_ios-0.8.12+1\\\\", "native_build": true, "dependencies": []}, {"name": "integration_test", "path": "D:\\\\flutter SDK\\\\flutter_windows_3.27.1-stable\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": []}, {"name": "just_audio", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\just_audio-0.9.42\\\\", "native_build": true, "dependencies": ["audio_session"]}, {"name": "open_filex", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\open_filex-4.6.0\\\\", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_apple-9.4.5\\\\", "native_build": true, "dependencies": []}, {"name": "qr_code_scanner", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\qr_code_scanner-1.0.1\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "syncfusion_flutter_pdfviewer", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\syncfusion_flutter_pdfviewer-26.2.14\\\\", "native_build": true, "dependencies": ["device_info_plus"]}, {"name": "url_launcher_ios", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_ios-6.3.2\\\\", "native_build": true, "dependencies": []}, {"name": "video_player_avfoundation", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_avfoundation-2.6.5\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.2.10\\\\", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "webview_flutter_wkwebview", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\webview_flutter_wkwebview-3.20.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "android": [{"name": "audio_session", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audio_session-0.1.23\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-10.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "firebase_core", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging-15.0.4\\\\", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "fl_location", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\fl_location-4.4.2\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_contacts", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_contacts-1.1.9+2\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_inappwebview_android", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_inappwebview_android-1.1.3\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility-6.0.0\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_plugin_android_lifecycle-2.0.24\\\\", "native_build": true, "dependencies": []}, {"name": "geocoding_android", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geocoding_android-3.3.1\\\\", "native_build": true, "dependencies": []}, {"name": "google_maps_flutter_android", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_maps_flutter_android-2.14.11\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_gallery_saver", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_gallery_saver-2.0.3\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_android", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_android-0.8.12+18\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "integration_test", "path": "D:\\\\flutter SDK\\\\flutter_windows_3.27.1-stable\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": []}, {"name": "just_audio", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\just_audio-0.9.42\\\\", "native_build": true, "dependencies": ["audio_session"]}, {"name": "open_filex", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\open_filex-4.6.0\\\\", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.15\\\\", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_android-12.0.13\\\\", "native_build": true, "dependencies": []}, {"name": "qr_code_scanner", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\qr_code_scanner-1.0.1\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.4.0\\\\", "native_build": true, "dependencies": []}, {"name": "sqflite_android", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_android-2.4.0\\\\", "native_build": true, "dependencies": []}, {"name": "syncfusion_flutter_pdfviewer", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\syncfusion_flutter_pdfviewer-26.2.14\\\\", "native_build": true, "dependencies": ["device_info_plus"]}, {"name": "url_launcher_android", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_android-6.3.14\\\\", "native_build": true, "dependencies": []}, {"name": "video_player_android", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_android-2.7.16\\\\", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.2.10\\\\", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "webview_flutter_android", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\webview_flutter_android-4.2.0\\\\", "native_build": true, "dependencies": []}], "macos": [{"name": "audio_session", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audio_session-0.1.23\\\\", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-10.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "file_selector_macos", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_macos-0.9.4+2\\\\", "native_build": true, "dependencies": []}, {"name": "firebase_core", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging-15.0.4\\\\", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_inappwebview_macos", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_inappwebview_macos-1.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility_macos", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_macos-1.0.0\\\\", "native_build": false, "dependencies": []}, {"name": "image_picker_macos", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_macos-0.2.1+1\\\\", "native_build": false, "dependencies": ["file_selector_macos"]}, {"name": "just_audio", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\just_audio-0.9.42\\\\", "native_build": true, "dependencies": ["audio_session"]}, {"name": "package_info_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.1.2\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "syncfusion_pdfviewer_macos", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\syncfusion_pdfviewer_macos-26.2.14\\\\", "native_build": true, "dependencies": []}, {"name": "url_launcher_macos", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_macos-3.2.2\\\\", "native_build": true, "dependencies": []}, {"name": "video_player_avfoundation", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_avfoundation-2.6.5\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.2.10\\\\", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "webview_flutter_wkwebview", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\webview_flutter_wkwebview-3.20.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "linux": [{"name": "device_info_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-10.1.2\\\\", "native_build": false, "dependencies": []}, {"name": "file_selector_linux", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_linux-0.9.3+2\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility_linux", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_linux-1.0.0\\\\", "native_build": false, "dependencies": []}, {"name": "image_picker_linux", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_linux-0.2.1+1\\\\", "native_build": false, "dependencies": ["file_selector_linux"]}, {"name": "package_info_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.1.2\\\\", "native_build": false, "dependencies": []}, {"name": "path_provider_linux", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": []}, {"name": "shared_preferences_linux", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "url_launcher_linux", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_linux-3.2.1\\\\", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.2.10\\\\", "native_build": false, "dependencies": ["package_info_plus"]}], "windows": [{"name": "device_info_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-10.1.2\\\\", "native_build": false, "dependencies": []}, {"name": "file_selector_windows", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_windows-0.9.3+3\\\\", "native_build": true, "dependencies": []}, {"name": "firebase_core", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-3.3.0\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_windows-1.0.0\\\\", "native_build": false, "dependencies": []}, {"name": "image_picker_windows", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_windows-0.2.1+1\\\\", "native_build": false, "dependencies": ["file_selector_windows"]}, {"name": "package_info_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.1.2\\\\", "native_build": false, "dependencies": []}, {"name": "path_provider_windows", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_windows-0.2.1\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_windows", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "syncfusion_pdfviewer_windows", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\syncfusion_pdfviewer_windows-26.2.14\\\\", "native_build": true, "dependencies": []}, {"name": "url_launcher_windows", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_windows-3.1.3\\\\", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.2.10\\\\", "native_build": false, "dependencies": ["package_info_plus"]}], "web": [{"name": "audio_session", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audio_session-0.1.23\\\\", "dependencies": []}, {"name": "device_info_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-10.1.2\\\\", "dependencies": []}, {"name": "firebase_core_web", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core_web-2.17.5\\\\", "dependencies": []}, {"name": "firebase_messaging_web", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging_web-3.8.12\\\\", "dependencies": ["firebase_core_web"]}, {"name": "fl_location_web", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\fl_location_web-4.2.0\\\\", "dependencies": []}, {"name": "flutter_inappwebview_web", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_inappwebview_web-1.0.8\\\\", "dependencies": []}, {"name": "flutter_keyboard_visibility_web", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_web-2.0.0\\\\", "dependencies": []}, {"name": "google_maps_flutter_web", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_maps_flutter_web-0.5.10\\\\", "dependencies": []}, {"name": "image_picker_for_web", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_for_web-3.0.6\\\\", "dependencies": []}, {"name": "just_audio_web", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\just_audio_web-0.4.13\\\\", "dependencies": []}, {"name": "package_info_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-8.1.2\\\\", "dependencies": []}, {"name": "permission_handler_html", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_html-0.1.3+5\\\\", "dependencies": []}, {"name": "shared_preferences_web", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.2\\\\", "dependencies": []}, {"name": "syncfusion_pdfviewer_web", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\syncfusion_pdfviewer_web-26.2.14\\\\", "dependencies": []}, {"name": "url_launcher_web", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_web-2.3.3\\\\", "dependencies": []}, {"name": "video_player_web", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_web-2.3.3\\\\", "dependencies": []}, {"name": "wakelock_plus", "path": "D:\\\\flutter_pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.2.10\\\\", "dependencies": ["package_info_plus"]}]}, "dependencyGraph": [{"name": "audio_session", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "fl_location", "dependencies": ["fl_location_web"]}, {"name": "fl_location_web", "dependencies": []}, {"name": "flutter_contacts", "dependencies": []}, {"name": "flutter_inappwebview", "dependencies": ["flutter_inappwebview_android", "flutter_inappwebview_ios", "flutter_inappwebview_macos", "flutter_inappwebview_web"]}, {"name": "flutter_inappwebview_android", "dependencies": []}, {"name": "flutter_inappwebview_ios", "dependencies": []}, {"name": "flutter_inappwebview_macos", "dependencies": []}, {"name": "flutter_inappwebview_web", "dependencies": []}, {"name": "flutter_keyboard_visibility", "dependencies": ["flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows"]}, {"name": "flutter_keyboard_visibility_linux", "dependencies": []}, {"name": "flutter_keyboard_visibility_macos", "dependencies": []}, {"name": "flutter_keyboard_visibility_web", "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "geocoding", "dependencies": ["geocoding_android", "geocoding_ios"]}, {"name": "geocoding_android", "dependencies": []}, {"name": "geocoding_ios", "dependencies": []}, {"name": "google_maps_flutter", "dependencies": ["google_maps_flutter_android", "google_maps_flutter_ios", "google_maps_flutter_web"]}, {"name": "google_maps_flutter_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "google_maps_flutter_ios", "dependencies": []}, {"name": "google_maps_flutter_web", "dependencies": []}, {"name": "image_gallery_saver", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "integration_test", "dependencies": []}, {"name": "just_audio", "dependencies": ["just_audio_web", "audio_session", "path_provider"]}, {"name": "just_audio_web", "dependencies": []}, {"name": "open_filex", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "qr_code_scanner", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "syncfusion_flutter_pdfviewer", "dependencies": ["device_info_plus", "syncfusion_pdfviewer_web", "syncfusion_pdfviewer_macos", "syncfusion_pdfviewer_windows", "url_launcher"]}, {"name": "syncfusion_pdfviewer_macos", "dependencies": []}, {"name": "syncfusion_pdfviewer_web", "dependencies": []}, {"name": "syncfusion_pdfviewer_windows", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "video_player", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "video_player_android", "dependencies": []}, {"name": "video_player_avfoundation", "dependencies": []}, {"name": "video_player_web", "dependencies": []}, {"name": "wakelock_plus", "dependencies": ["package_info_plus"]}, {"name": "webview_flutter", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter_android", "dependencies": []}, {"name": "webview_flutter_wkwebview", "dependencies": []}], "date_created": "2025-05-22 11:35:56.361276", "version": "3.27.1", "swift_package_manager_enabled": false}