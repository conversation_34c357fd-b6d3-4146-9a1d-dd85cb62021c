import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/pages/notification/widget/notifi_item.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../cubit/bloc.dart';
import '../../cubit/index.dart';
import '../../models/project/notification.dart';
import '../../utils/src/api.dart';
import '../../utils/src/convert_data.dart';
import '../../utils/src/dialogs.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

enum Option {
  first,
  second,
}

String str(Option option) {
  switch (option) {
    case Option.first:
      return 'Notification.MarkAllRead'.tr();
    case Option.second:
      return 'Notification.DeleteAll'.tr();
  }
}

class _NotificationPageState extends State<NotificationPage> {
  late final sNotification = RepositoryProvider
      .of<Api>(context)
      .notification;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return
            Scaffold(
              appBar: appBar(title: 'Notification.Title'.tr(), actions: [
                PopupMenuButton<Option>(
                  child: Icon(
                    Icons.more_horiz,
                    color: CColor.black,
                  ),
                  itemBuilder: (context) {
                    return [
                      for (final option in Option.values)
                        PopupMenuItem(
                          onTap: () {
                            if (option == Option.first) {
                              List<int> readIds = [];
                              for (var item in context
                                  .read<BlocC<MNotification>>()
                                  .state
                                  .data
                                  .content) {
                                if (!item.isRead!) {
                                  readIds.add(item.id!);
                                }
                              }
                              sNotification.markAsRead(body: {"notificationIdList": readIds}).then((response) {
                                if (response!.isSuccess) {
                                  context.read<BlocC<MNotification>>().setPage(
                                      page: 1,
                                      format: MNotification.fromJson,
                                      api: (filter, page, size, sort) =>
                                          sNotification.get(filter: filter, page: page, size: size));
                                }
                              });
                            } else {
                              UDialog().showConfirm(
                                title: "Notification.DeleteAllTitle".tr(),
                                body: Center(
                                    child: Text(
                                      "Notification.DeleteAllMessage".tr(),
                                      textAlign: TextAlign.center,
                                    )),
                                btnOkText: "Notification.Yes".tr(),
                                btnCancelText: "Notification.No".tr(),
                                btnCancelStyle: TextStyle(fontWeight: FontWeight.bold, color: CColor.blue),
                                btnOkOnPress: () async {
                                  sNotification.delete().then((response) {
                                    if (response!.isSuccess) {
                                      context.pop();
                                      context.read<BlocC<MNotification>>().setPage(
                                          page: 1,
                                          format: MNotification.fromJson,
                                          api: (filter, page, size, sort) =>
                                              sNotification.get(filter: filter, page: page, size: size));
                                    }
                                  });
                                },
                              );
                            }
                          },
                          value: option,
                          child: Text(str(option)),
                        ),
                    ];
                  },
                ),
                const SizedBox(width: 20.0)
              ]),
              body: Padding(
                  padding: const EdgeInsets.only(top: CSpace.base),
                  child: WList<MNotification>(
                    item: (data, int index) {
                      String? createdOnDate =
                      Convert.getGroupDate(context.read<BlocC<MNotification>>(), index, 'createdOnDate');
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (createdOnDate != null)
                            Container(
                              width: double.infinity,
                              color: const Color(0xFFF3F3F3),
                              child: Padding(
                                padding:
                                const EdgeInsets.only(left: CSpace.lg, bottom: CSpace.base, top: CSpace.base),
                                child: Text(
                                  createdOnDate,
                                  style: TextStyle(
                                      fontSize: CFontSize.base, color: CColor.black, fontWeight: FontWeight.bold),
                                ),
                              ),
                            ),
                          NotificationItem(
                              onTap: () {
                                sNotification.markAsRead(body: {
                                  "notificationIdList": [data.id]
                                }).then((response) {
                                  if (response!.isSuccess) {
                                    context.read<BlocC<MNotification>>().setPage(
                                        page: 1,
                                        format: MNotification.fromJson,
                                        api: (filter, page, size, sort) =>
                                            sNotification.get(filter: filter, page: page, size: size));
                                  }
                                  context.pushNamed(CRoute.orderDetail, queryParameters: {'id': data.orderId} );

                                });
                              },
                              notification: data,
                              onDelete: (_) =>
                                  UDialog().showConfirm(
                                    title: "Notification.DeleteTitle".tr(),
                                    body: Center(
                                        child: Text(
                                          "Notification.DeleteMessage".tr(),
                                          textAlign: TextAlign.center,
                                        )),
                                    btnOkText: "Notification.Yes".tr(),
                                    btnCancelText: "Notification.No".tr(),
                                    btnCancelStyle: TextStyle(fontWeight: FontWeight.bold, color: CColor.blue),
                                    btnOkOnPress: () async {
                                      sNotification
                                          .deleteById(id: data.id!)
                                          .then((response) {
                                        if (response!.isSuccess) {
                                          context.pop();
                                          context.read<BlocC<MNotification>>().setPage(
                                              page: 1,
                                              format: MNotification.fromJson,
                                              api: (filter, page, size, sort) =>
                                                  sNotification.get(filter: filter, page: page, size: size));
                                        }
                                      });
                                          },
                                  ))
                        ],
                      );
                    },
                    api: (filter, page, size, sort) => sNotification.get(filter: filter, page: page, size: size),
                    format: MNotification.fromJson,
                  )),
            );
  }
}

class TestData {
  List<MNotification>? today;
  List<MNotification>? previous;

  TestData({this.today, this.previous});
}
