["/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/Flutter.framework/Flutter", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/vm_snapshot_data", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/kernel_blob.bin", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/App", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/Info.plist", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/.env.production", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/.env.development", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/login_background.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/.DS_Store", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/<EMAIL>", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/petro_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/background.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/d4af518117996ffbab547753b36d7886.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/<EMAIL>", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/placeholder.jpeg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/avatar.jpeg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_sold_vip_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_selected_normal_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_empty_normal_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_selecting_couple_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_sold_normal_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_sold_couple_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_selecting_vip_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_empty_vip_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_empty_couple_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_selecting_normal_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_selected_vip_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/images/seats/ic_selected_couple_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/search.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/arrow-down.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/request_content.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/.DS_Store", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/otp-verification.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/arrow-right.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/close.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/copy.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/forgot-password.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/edit.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/arrow-up.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/sort.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/reset-password.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/upload/camera-2.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/upload/image.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/upload/camera.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/upload/video.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/upload/multiple-image.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/splash/0.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/splash/1.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/splash/2.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/share/gmail.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/share/zalo.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/share/facebook.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/txt.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/docx.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/gmail.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/zalo.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/doc.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/json.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/mail.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/vsdx.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/jpeg.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/csv.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/xlsx.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/facebook.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/pdf.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/phone.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/zalo-20px.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/share.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/jpg.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/gmail%20(1).svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/html.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/xls.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/webp.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/svgs/mime-type/png.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/form/mail.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/form/password.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/.DS_Store", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/c-18.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/c-16.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/c-13.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/p.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/.DS_Store", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/fill.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/<EMAIL>", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/opacity.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_sold_vip_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_empty_normal_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/.DS_Store", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_screen.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_set_normal_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_process_vip_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/btnBookingdetail.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_select_couple_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_sold_normal_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_set_vip_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_select_vip_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_sold_couple_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_select_normal_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_empty_vip_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_empty_couple_seat.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_empty_couple_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/icon/cinema/ic_set_couple_seat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/json/trail_loading.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/translations/en.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/translations/vi.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/fonts/PlusJakartaSans-VariableFont_wght.ttf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/assets/fonts/Oswald/Oswald-Light.ttf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/packages/flutter_inappwebview_web/assets/web/web_support.js", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/squiggly.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/strikethrough.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/highlight.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/underline.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/fonts/RobotoMono-Regular.ttf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/packages/wakelock_plus/assets/no_sleep.js", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/packages/youtube_player_flutter/assets/speedometer.webp", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.bin", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/FontManifest.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-acuaiinuatatdlcjiwvytyscxged/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/NOTICES.Z"]