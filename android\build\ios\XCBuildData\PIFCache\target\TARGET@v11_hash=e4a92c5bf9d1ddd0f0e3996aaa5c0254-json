{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bef1ff8f621f6797583fd7a266cabaa9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e006620c952d4ba85cf0f3fd4f8b794b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860ac90c6fca353627e6619ba88f48e41", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98281cbf150dda0202e56a0f65a000543b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860ac90c6fca353627e6619ba88f48e41", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987d038ecdaeac7ecd3045168a6bd90dfc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989dcd9ef1f7ca17e341ed497b8690e671", "guid": "bfdfe7dc352907fc980b868725387e98fb4540f9b15560432e6728241c59bddc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec1d6d5daf7d4c211cf680d0f4f58f54", "guid": "bfdfe7dc352907fc980b868725387e98be5756dba7602e963715be1702be729a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9f1c0c9254a2d0a21c07338d09ad423", "guid": "bfdfe7dc352907fc980b868725387e98af65bf4a16438be6f06081eff5a232f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812baf503e8cc0dceb71988bed761aae5", "guid": "bfdfe7dc352907fc980b868725387e98a06b0d99ba8cd9e80e87fb9b8eb82364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852825f934b8b078e03958fce4e48c3dc", "guid": "bfdfe7dc352907fc980b868725387e987a338159fbf8f3e91af52ef7514c9031"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856b36060a40eec7ae1df313219442e13", "guid": "bfdfe7dc352907fc980b868725387e9867406068032f51635295964a5ecbe573"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef216bf4cb151e4df2fcf5591cea668c", "guid": "bfdfe7dc352907fc980b868725387e9854ba1b917674522f6e330b8dc19e3329"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acda295cb0ca3a24826926ffba6e7760", "guid": "bfdfe7dc352907fc980b868725387e989db71549870f2ea16e5e993f6aade984", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb14ff3da0c8f0ddb1e36d53c43b0516", "guid": "bfdfe7dc352907fc980b868725387e98a15777f69cb0677c45410ebd3f4d504c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb1be11f6cb0ceed71f7d02d99fc30fa", "guid": "bfdfe7dc352907fc980b868725387e98297c332127da1fff610a8952b08afffa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f60b5bf858a503b42f8a0ad63a82bf7", "guid": "bfdfe7dc352907fc980b868725387e98d8e8eb676306aa03ce0cf4db5e234d41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d996935b57df21ecda36778ebd04ebc", "guid": "bfdfe7dc352907fc980b868725387e986ddf988299e133c7656f57df80ab31d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f01fc497de050bf7ff1cff7864e241b8", "guid": "bfdfe7dc352907fc980b868725387e981a5c2f9e2e42f6a10c91b4c30534bcac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818169e901e3ecd7111bd42f00d0689b3", "guid": "bfdfe7dc352907fc980b868725387e981607b10840b9403ebf7b5fc989f617aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e9c1e9ff28f85ae979f1333b4d3f231", "guid": "bfdfe7dc352907fc980b868725387e98cd37753be522b36dd758d939760f5c35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980288204af30b3a42d74b28cc9d033f8f", "guid": "bfdfe7dc352907fc980b868725387e9840583f18e46ebf0c0f6be4f95f66129b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1e4193456e4c867eb4aa0a0b49ef55e", "guid": "bfdfe7dc352907fc980b868725387e9825501e0c77c89caaaab9e48f3576d477", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988edcf8c533938259f1c995cf89c5b36d", "guid": "bfdfe7dc352907fc980b868725387e9851a541b5c9fadb643dce712e57c7009b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c0bdc2099aa6a096ef8f3861f78fab6", "guid": "bfdfe7dc352907fc980b868725387e9826b4a68f063bc8db37206cc64968087c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98becf15ea8bcbb0dc794a62a220b49135", "guid": "bfdfe7dc352907fc980b868725387e98977d2cc2f3b2180d115594059e110845"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98639ef74c59c329047255764c8ab5627d", "guid": "bfdfe7dc352907fc980b868725387e98923ac05b48592a7fa54838c1ac527844"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a463ea5328f33b634a489c13bed7e086", "guid": "bfdfe7dc352907fc980b868725387e984cf1d2e74c6310b2ae11eb119938efce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9e2e7d4948e9b776b5725c9b7650435", "guid": "bfdfe7dc352907fc980b868725387e98625ce5dbbc03bca63839c4d3f00a787b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6995843c0652854d73794f9cecbf78f", "guid": "bfdfe7dc352907fc980b868725387e98373a75fa342efe1bd1c1d229f548bbff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a6db0c6cb93c423dca46d2f320ee804", "guid": "bfdfe7dc352907fc980b868725387e98a25ef643c8410e1d83759283b736a8e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981040b13dcaa5125fdf0e4201b4cdada9", "guid": "bfdfe7dc352907fc980b868725387e98b66792fb04e94fcba10f6db5af302619"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4e2c6da99128a674224504b924acfa2", "guid": "bfdfe7dc352907fc980b868725387e982d852c41d19d30e02ae0701730d2f081"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e9cb0eec1686fb1b0b14cba23590271", "guid": "bfdfe7dc352907fc980b868725387e981dbc5b5f66c64680e3e97e27abe7818e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d2b43597c5e775f1ee62a872a4c1af2", "guid": "bfdfe7dc352907fc980b868725387e98381b81f8ec569c656bc11b1432f16155"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0679c6eba29810d10ba20a0db35675f", "guid": "bfdfe7dc352907fc980b868725387e9893539f93b05b37f97feaf14af108ef70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7584780407fa23f73d865859bcd55d4", "guid": "bfdfe7dc352907fc980b868725387e9899d416544ae5d29baefa8eca9bd1f72d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982addef68152165195072300fc602bd7a", "guid": "bfdfe7dc352907fc980b868725387e98351adb929a6c49b36af923576d6b44ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893c9141319efd42c7d0689a6a00a60c7", "guid": "bfdfe7dc352907fc980b868725387e98c798a9816622ea32cc3030acaffeed10"}], "guid": "bfdfe7dc352907fc980b868725387e98abfef00aabaeb3da4c50d10e61c0d0ed", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f3e48b51aaee67331cc0afec17993d6", "guid": "bfdfe7dc352907fc980b868725387e989dc804a5f8bf5c9bfb272b1236c07313"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a7451fc3fbd567288a6cb5a81950b9d", "guid": "bfdfe7dc352907fc980b868725387e982e2b20a7a5c3e3d5eda1370cc305562c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d322201c40105521ddec10903dc527a0", "guid": "bfdfe7dc352907fc980b868725387e983241ee2257a8c7e7257e876d244b0abd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9466b5691d16bfd7c405daf9128ac36", "guid": "bfdfe7dc352907fc980b868725387e9867b36bb252a7644381db0d0851acfbd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d658686f809a6b6210746c5ccd52dc59", "guid": "bfdfe7dc352907fc980b868725387e98469aefa823901563e0fcfb21f39618a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a0d320decf712f026cb9fd3e479e1dc", "guid": "bfdfe7dc352907fc980b868725387e98ee7be42e0ade3df7f6bf078d5a91abbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98455331d0cdfe8b1df2e0459370d276c0", "guid": "bfdfe7dc352907fc980b868725387e98e657c7e68d4fdda46dba12196002a72e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987833507aedc23f1bad88944da037712e", "guid": "bfdfe7dc352907fc980b868725387e985cdbf11f8d685dc24b1fddafc20d0754"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98473cbff1ccbadd61a51ee41bc0ebe27b", "guid": "bfdfe7dc352907fc980b868725387e98d1711909afa375310c081dd5df239489"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d7a0b537d7bb3246308b4911ba42bde", "guid": "bfdfe7dc352907fc980b868725387e98821955d2a526a69b36388ae98ef080aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895770072cee554a02c8f3b6a1be408f0", "guid": "bfdfe7dc352907fc980b868725387e98ef7bf2f375d996980d306dcccfcec99b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832ab913b2f2096ae94c8b27abd447acc", "guid": "bfdfe7dc352907fc980b868725387e987adac0246ae90064262c9fcfba69c5e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982eea806a021fffd0e0092a70c4e818e4", "guid": "bfdfe7dc352907fc980b868725387e985ce396252d4d76d1eb3fac84b44b8c73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae3f3602a65fc31052c6768a09a8ea7a", "guid": "bfdfe7dc352907fc980b868725387e9887e6a2e508803b78f97a0375815431fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8b90ef478cb64643d53e0a2c3825e97", "guid": "bfdfe7dc352907fc980b868725387e98cd222ef1506f01b3311225c02fbecc10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813b6f81cac1c2f5b179aaf9a87acb9f6", "guid": "bfdfe7dc352907fc980b868725387e98622342791044c08af9c1dd555e69802c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885683d2164def3b6325e6a18246f5f7f", "guid": "bfdfe7dc352907fc980b868725387e9814c419eb4df7b75b236b40cc742ba14d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816204c83d663b0e51f01f07dc6d10857", "guid": "bfdfe7dc352907fc980b868725387e983c1c0cc85b979a18d1622834493a0d98"}], "guid": "bfdfe7dc352907fc980b868725387e98775a876b91569d90fa6a54cd0f02d766", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9816df303e16b3ae9ab2f5b4d3d81a1c44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "guid": "bfdfe7dc352907fc980b868725387e981b4066ee9fc5d6f140253882767680c3"}], "guid": "bfdfe7dc352907fc980b868725387e9835181bd687eb6774a305c03fa1febf6a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ead7e8be34febc3c754d482caddb87d4", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98ec1377683dd8bf9e19199577c7754c30", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}