import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '/constants/index.dart';
import '/cubit/index.dart';
import '/service/language_service.dart';
import '/utils/index.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: Environment.fileName);
  // HttpOverrides.global = MyHttpOverrides();
  await EasyLocalization.ensureInitialized();

  // Get saved language preference
  final languageService = LanguageService();
  final savedLanguage = await languageService.getCurrentLanguage();

  runApp(EasyLocalization(
      supportedLocales: const [
        Locale('en'),
        Locale('vi'),
      ],
      path: 'assets/translations',
      fallbackLocale: const Locale('vi'),
      startLocale: Locale(savedLanguage), // Use saved language
      child: MyApp()));
}

class MyHttpOverrides extends HttpOverrides{
  @override
  HttpClient createHttpClient(SecurityContext? context){
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port)=> true;
  }
}

class MyApp extends StatelessWidget {
  MyApp({super.key});

  final Api _api = Api();

  @override
  Widget build(BuildContext context) {
    return RepositoryProvider.value(
        value: _api,
        child: MultiBlocProvider(
          providers: [
            BlocProvider<AuthC>(
              create: (BuildContext context) => AuthC(),
            ),
          ],
          child: MaterialApp.router(
            title: 'Petro',
            builder: (BuildContext context, Widget? child) => MediaQuery(
              data:
                  MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
              child: GestureDetector(
                onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
                child: child,
              ),
            ),

            debugShowCheckedModeBanner: false,
            localizationsDelegates: context.localizationDelegates,
            supportedLocales: context.supportedLocales,
            locale: context.locale,
            theme: ThemeData(
              useMaterial3: false,
              fontFamily: 'Oswald',
              dialogTheme: const DialogTheme(
                titleTextStyle: TextStyle(
                  fontFamily: 'SourceSansPro',
                  color: Colors.black,
                ),
                contentTextStyle: TextStyle(
                  fontFamily: 'SourceSansPro',
                  color: Colors.black,
                ),
              ),
              textTheme: const TextTheme(
                bodyLarge: TextStyle(
                  fontFamily: 'SourceSansPro',
                  // fontSize: CFontSize.sm
                ),
                bodyMedium: TextStyle(
                  fontSize: CFontSize.sm,
                  fontWeight: FontWeight.w400,
                  textBaseline: TextBaseline.alphabetic,
                  fontFamily: 'SourceSansPro',
                  height: 1.1,
                ),

                labelLarge: TextStyle(
                    fontSize: CFontSize.base, fontWeight: FontWeight.w400),
              ),
              scaffoldBackgroundColor: Colors.white,
              primarySwatch: CColor.blue,
              unselectedWidgetColor: CColor.blue,
              floatingActionButtonTheme: FloatingActionButtonThemeData(
                  backgroundColor: CColor.primary,
                  foregroundColor: Colors.white),
              elevatedButtonTheme:
                  ElevatedButtonThemeData(style: CStyle.button),
            ),
            routeInformationProvider: routes.routeInformationProvider,
            routeInformationParser: routes.routeInformationParser,
            routerDelegate: routes.routerDelegate,
          ),
        ));
  }
}
