package vn.zenity.betacineplex.view.user

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.CardModel

/**
 * Created by Zenity.
 */

interface MemberCardContractor {
    interface View : IBaseView {
        fun showMemberCard(cards: List<CardModel>)
    }

    interface Presenter : IBasePresenter<View> {
        fun getMemberCard(accountId: String)
    }
}
