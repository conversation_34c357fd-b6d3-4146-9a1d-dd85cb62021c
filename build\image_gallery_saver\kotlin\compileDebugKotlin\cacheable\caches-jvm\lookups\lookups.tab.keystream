  	TargetApi android.annotation  ContentResolver android.content  
ContentValues android.content  Context android.content  Intent android.content  insert android.content.ContentResolver  openOutputStream android.content.ContentResolver  Environment android.content.ContentValues  
MediaStore android.content.ContentValues  	TextUtils android.content.ContentValues  apply android.content.ContentValues  put android.content.ContentValues  contentResolver android.content.Context  
sendBroadcast android.content.Context  ACTION_MEDIA_SCANNER_SCAN_FILE android.content.Intent  data android.content.Intent  Bitmap android.graphics  
BitmapFactory android.graphics  CompressFormat android.graphics.Bitmap  compress android.graphics.Bitmap  recycle android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  decodeByteArray android.graphics.BitmapFactory  MediaScannerConnection 
android.media  Uri android.net  fromFile android.net.Uri  toString android.net.Uri  Build 
android.os  Environment 
android.os  SDK_INT android.os.Build.VERSION  Q android.os.Build.VERSION_CODES  DIRECTORY_MOVIES android.os.Environment  DIRECTORY_PICTURES android.os.Environment  !getExternalStoragePublicDirectory android.os.Environment  
MediaStore android.provider  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  	MIME_TYPE (android.provider.MediaStore.Images.Media  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  
RELATIVE_PATH (android.provider.MediaStore.MediaColumns  EXTERNAL_CONTENT_URI 'android.provider.MediaStore.Video.Media  	MIME_TYPE 'android.provider.MediaStore.Video.Media  	TextUtils android.text  isEmpty android.text.TextUtils  MimeTypeMap android.webkit  getMimeTypeFromExtension android.webkit.MimeTypeMap  getSingleton android.webkit.MimeTypeMap  NonNull androidx.annotation  Any com.example.imagegallerysaver  Bitmap com.example.imagegallerysaver  
BitmapFactory com.example.imagegallerysaver  Boolean com.example.imagegallerysaver  Build com.example.imagegallerysaver  	ByteArray com.example.imagegallerysaver  
ContentValues com.example.imagegallerysaver  Context com.example.imagegallerysaver  Environment com.example.imagegallerysaver  File com.example.imagegallerysaver  FileInputStream com.example.imagegallerysaver  
FlutterPlugin com.example.imagegallerysaver  HashMap com.example.imagegallerysaver  IOException com.example.imagegallerysaver  ImageGallerySaverPlugin com.example.imagegallerysaver  Int com.example.imagegallerysaver  Intent com.example.imagegallerysaver  
MediaStore com.example.imagegallerysaver  
MethodCall com.example.imagegallerysaver  MethodCallHandler com.example.imagegallerysaver  
MethodChannel com.example.imagegallerysaver  MimeTypeMap com.example.imagegallerysaver  NonNull com.example.imagegallerysaver  OutputStream com.example.imagegallerysaver  Result com.example.imagegallerysaver  SaveResultModel com.example.imagegallerysaver  String com.example.imagegallerysaver  System com.example.imagegallerysaver  	TextUtils com.example.imagegallerysaver  Unit com.example.imagegallerysaver  Uri com.example.imagegallerysaver  also com.example.imagegallerysaver  apply com.example.imagegallerysaver  	extension com.example.imagegallerysaver  
isNotEmpty com.example.imagegallerysaver  	lowercase com.example.imagegallerysaver  println com.example.imagegallerysaver  set com.example.imagegallerysaver  
startsWith com.example.imagegallerysaver  toString com.example.imagegallerysaver  FlutterPluginBinding +com.example.imagegallerysaver.FlutterPlugin  Bitmap 5com.example.imagegallerysaver.ImageGallerySaverPlugin  
BitmapFactory 5com.example.imagegallerysaver.ImageGallerySaverPlugin  Build 5com.example.imagegallerysaver.ImageGallerySaverPlugin  	ByteArray 5com.example.imagegallerysaver.ImageGallerySaverPlugin  
ContentValues 5com.example.imagegallerysaver.ImageGallerySaverPlugin  Environment 5com.example.imagegallerysaver.ImageGallerySaverPlugin  File 5com.example.imagegallerysaver.ImageGallerySaverPlugin  FileInputStream 5com.example.imagegallerysaver.ImageGallerySaverPlugin  Intent 5com.example.imagegallerysaver.ImageGallerySaverPlugin  
MediaStore 5com.example.imagegallerysaver.ImageGallerySaverPlugin  
MethodChannel 5com.example.imagegallerysaver.ImageGallerySaverPlugin  MimeTypeMap 5com.example.imagegallerysaver.ImageGallerySaverPlugin  SaveResultModel 5com.example.imagegallerysaver.ImageGallerySaverPlugin  System 5com.example.imagegallerysaver.ImageGallerySaverPlugin  	TextUtils 5com.example.imagegallerysaver.ImageGallerySaverPlugin  Uri 5com.example.imagegallerysaver.ImageGallerySaverPlugin  also 5com.example.imagegallerysaver.ImageGallerySaverPlugin  applicationContext 5com.example.imagegallerysaver.ImageGallerySaverPlugin  apply 5com.example.imagegallerysaver.ImageGallerySaverPlugin  	extension 5com.example.imagegallerysaver.ImageGallerySaverPlugin  generateUri 5com.example.imagegallerysaver.ImageGallerySaverPlugin  getMIMEType 5com.example.imagegallerysaver.ImageGallerySaverPlugin  
isNotEmpty 5com.example.imagegallerysaver.ImageGallerySaverPlugin  	lowercase 5com.example.imagegallerysaver.ImageGallerySaverPlugin  
methodChannel 5com.example.imagegallerysaver.ImageGallerySaverPlugin  println 5com.example.imagegallerysaver.ImageGallerySaverPlugin  saveFileToGallery 5com.example.imagegallerysaver.ImageGallerySaverPlugin  saveImageToGallery 5com.example.imagegallerysaver.ImageGallerySaverPlugin  
sendBroadcast 5com.example.imagegallerysaver.ImageGallerySaverPlugin  
startsWith 5com.example.imagegallerysaver.ImageGallerySaverPlugin  toString 5com.example.imagegallerysaver.ImageGallerySaverPlugin  HashMap -com.example.imagegallerysaver.SaveResultModel  errorMessage -com.example.imagegallerysaver.SaveResultModel  filePath -com.example.imagegallerysaver.SaveResultModel  	isSuccess -com.example.imagegallerysaver.SaveResultModel  set -com.example.imagegallerysaver.SaveResultModel  	toHashMap -com.example.imagegallerysaver.SaveResultModel  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  	Registrar 'io.flutter.plugin.common.PluginRegistry  File java.io  FileInputStream java.io  IOException java.io  OutputStream java.io  absolutePath java.io.File  apply java.io.File  exists java.io.File  	extension java.io.File  mkdir java.io.File  close java.io.FileInputStream  read java.io.FileInputStream  toString java.io.IOException  close java.io.OutputStream  flush java.io.OutputStream  write java.io.OutputStream  currentTimeMillis java.lang.System  HashMap 	java.util  set java.util.HashMap  	ByteArray kotlin  	Function1 kotlin  Nothing kotlin  also kotlin  apply kotlin  toString kotlin  toString 
kotlin.Any  not kotlin.Boolean  size kotlin.ByteArray  also 
kotlin.Int  	compareTo 
kotlin.Int  toString kotlin.Long  
isNotEmpty 
kotlin.String  	lowercase 
kotlin.String  
startsWith 
kotlin.String  
isNotEmpty kotlin.collections  set kotlin.collections  toString kotlin.collections  	extension 	kotlin.io  println 	kotlin.io  
startsWith 	kotlin.io  
isNotEmpty kotlin.text  	lowercase kotlin.text  set kotlin.text  
startsWith kotlin.text  toString kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          