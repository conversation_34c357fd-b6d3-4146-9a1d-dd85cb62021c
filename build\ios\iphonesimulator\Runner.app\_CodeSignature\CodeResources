<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<data>
		mnLbgBhrpRwdlXh4UKzYj73lYuA=
		</data>
		<key><EMAIL></key>
		<data>
		cA4FSwmkyWtEMiGw0gRRWpXnaKY=
		</data>
		<key>Assets.car</key>
		<data>
		Hsh4i5RFKP8NhyC1u3EAmXKxvdk=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		K04OlgvWE65g20r7W50TsVf42h0=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<data>
		ebfgq9oaod6EJJQtxJE54Zm59IE=
		</data>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		MDrKFvFWroTb0+KEbQShBcoBvo4=
		</data>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<data>
		nFC1waP0YzYOchnqa85lPwrC73s=
		</data>
		<key>Frameworks/Alamofire.framework/Alamofire</key>
		<data>
		SR87GPVhRQVu9lYIHbXBsQms0Ig=
		</data>
		<key>Frameworks/Alamofire.framework/Info.plist</key>
		<data>
		Zc9ZVcPOOSa84XE8zEJ7ob1sZ70=
		</data>
		<key>Frameworks/Alamofire.framework/_CodeSignature/CodeResources</key>
		<data>
		Oz80jEpK+BkB+c17zCsb7tM0mcQ=
		</data>
		<key>Frameworks/App.framework/App</key>
		<data>
		nUUZAg+diVQV+3Kb6SOFmGuyfu8=
		</data>
		<key>Frameworks/App.framework/Info.plist</key>
		<data>
		h5OB7aKzS5WR9SemvZAyN6FEkJs=
		</data>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<data>
		g3dwUULPdtLUqIAY48enicoV1yU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/.env.development</key>
		<data>
		6t7ahNv0VnCZZdgYTh7asykxp00=
		</data>
		<key>Frameworks/App.framework/flutter_assets/.env.production</key>
		<data>
		6t7ahNv0VnCZZdgYTh7asykxp00=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<data>
		sbUiVjBpdmy7SZIcI20tLVq9C0M=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<data>
		THvGK6UlAcy0X4y3Pb/4loUasPs=
		</data>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<data>
		WpZyrCzYezYxI6nbMWVoDEuUWgM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<data>
		73TEuyKJARu5ByGq3fF9XtlScy8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/c-13.png</key>
		<data>
		WFI93uNLA4bPdtKWtmNGsEvOq5Q=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/c-16.png</key>
		<data>
		66NUOs+20qPqeYd920qSoqshHpk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/c-18.png</key>
		<data>
		1vSOiuvafVpc0pAg+G22aZarX+s=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/Oswald/Oswald-Medium.ttf</key>
		<data>
		jlKkRNVOCvFx7Rz+rnNfZzdHdHU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/PlusJakartaSans-VariableFont_wght.ttf</key>
		<data>
		+Skrn7AXMYEf4SY17FRFqP7ZR14=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf</key>
		<data>
		F8JUdcA2n3+MhGKvnPEnpM9vEzI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/form/mail.svg</key>
		<data>
		OPDrFDUO9vk7MQ/XACfswj01SGg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/form/password.svg</key>
		<data>
		CScyA5g+kgx6xV7cNo2cHh17ECo=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/btnBookingdetail.svg</key>
		<data>
		kmiD/biz4hPQEl7nHsEvKnsI2J4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_couple_seat.png</key>
		<data>
		m5cmsU0N+mU48H+2tQ1GypcxfWM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_couple_seat.svg</key>
		<data>
		0318KBcKTj90XBj9ptw34HxYn7Y=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_normal_seat.svg</key>
		<data>
		FoJCqtqjSlf3t338MBXS4Pqr60M=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_vip_seat.svg</key>
		<data>
		mZMwAZo0CwjkiDq8aB/C97Lyvio=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_process_vip_seat.svg</key>
		<data>
		cthXGLMx8tFgnniooIvNRJVuveM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_screen.svg</key>
		<data>
		GkPP6BVNNMK03XfWVBMy+DE5jps=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_select_couple_seat.svg</key>
		<data>
		svrvwgUEOvti68C/oMiBi7yOnAs=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_select_normal_seat.svg</key>
		<data>
		79kKd+DELsx5G/DO91Mu0uXjxBQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_select_vip_seat.svg</key>
		<data>
		xaUChgu3YsCXyDYtrTpS8rL3G3g=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_set_couple_seat.svg</key>
		<data>
		MRXZcCyup1wN8D59NbzX41Iz3IA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_set_normal_seat.svg</key>
		<data>
		U607oDBM5cTTb+KWvEK+bow+d84=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_set_vip_seat.svg</key>
		<data>
		LToDX7E+wuI3XwD/cFswUZQP2XE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_sold_couple_seat.svg</key>
		<data>
		hk1mO9TY0yYLKcT4l7d7/jSYMLM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_sold_normal_seat.svg</key>
		<data>
		mSRgy6KtyY1nDMt+S9ZP3OWZ7oQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_sold_vip_seat.svg</key>
		<data>
		OhTS8GBkQLO+W/fbyQbwU8aztAc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<data>
		OlFMNoBcSmoMmDHI5YH0F5SINqA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/fill.png</key>
		<data>
		TMucFPEJp66zYOaYeTnQf2ZJo3s=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<data>
		2zTuJu+BzeWZKVhKxGbaJveVRJw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<data>
		nAYklsC//Nr4Ndo553khPjZMlZ8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<data>
		ui+kKVu9ZtIqMKa9sbDDRrbyUCw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/opacity.png</key>
		<data>
		vqz06+Fa4Up2+KUOBrilLOFkGJE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<data>
		F4bnx76w4IQXCUmFfhWShU6tZ/I=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<data>
		uhzdCvB95el72TYKcGwud2IufEk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<data>
		ub3PLho6bky1zGcJVnu+9po7ytE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<data>
		NnstazoJKI/cYgsjfFsKOfCgvgE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/<EMAIL></key>
		<data>
		eNnhhy3Lipdd5fsQLxEAoQMcf1M=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/avatar.jpeg</key>
		<data>
		Bdnmav2Jxo9GtowHm+2ZSu00WwE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/background.png</key>
		<data>
		v70ilJCjojyZ4Zeyjm1TnUNU+6k=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/d4af518117996ffbab547753b36d7886.png</key>
		<data>
		z80CKNSvUYEXmW/7q1R3U7NteIY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/<EMAIL></key>
		<data>
		CHo+km7JbF9F61jzIC+7XjVwV3c=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/login_background.png</key>
		<data>
		z80CKNSvUYEXmW/7q1R3U7NteIY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/logo.png</key>
		<data>
		SBDp0mWI+nc1j3iASoPewVvRAwg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/petro_logo.png</key>
		<data>
		gu7HnVVj9D7bkWwO37IoxsQGSu0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/placeholder.jpeg</key>
		<data>
		rgo33cTWM3ddn6gYMctb6bUHnaM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_empty_couple_seat.svg</key>
		<data>
		2U1yYLL3PSfv+011hCNyLTlQ3SQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_empty_normal_seat.svg</key>
		<data>
		qYAXwXm/ZuQAyGP8GxKYHvtOJHc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_empty_vip_seat.svg</key>
		<data>
		3fti5LmlzJlN4r/UmLtWVyPkveM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selected_couple_seat.svg</key>
		<data>
		GiWL1+ODbQrPmOm+cgtBSvwNVnk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selected_normal_seat.svg</key>
		<data>
		pOgbUNB32nsktYtSeWp9ofbC90g=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selected_vip_seat.svg</key>
		<data>
		pOgbUNB32nsktYtSeWp9ofbC90g=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selecting_couple_seat.svg</key>
		<data>
		K++tH+xm41iEjzjb8sRGpV2CF0c=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selecting_normal_seat.svg</key>
		<data>
		ZKAdAKiQe1mnR26SnaDOC6E7No8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selecting_vip_seat.svg</key>
		<data>
		ZKAdAKiQe1mnR26SnaDOC6E7No8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_sold_couple_seat.svg</key>
		<data>
		ZevPTbpzjDE0SBQ8S1Auv1G0+lM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_sold_normal_seat.svg</key>
		<data>
		/XOlIbudIfojKPefVQo/iDe6MvM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_sold_vip_seat.svg</key>
		<data>
		/XOlIbudIfojKPefVQo/iDe6MvM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/json/trail_loading.json</key>
		<data>
		jYFkeGvFBKUj1hLpc+pE1hxULyU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/p.png</key>
		<data>
		XSvsaES4eJQXFgQ/ozuDVPz52Xo=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/arrow-down.svg</key>
		<data>
		dl3zw/EscPC8msH/WYboMVMSvB4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/arrow-right.svg</key>
		<data>
		cHzkfvAe/UX5PR4cZbjBeLzav08=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/arrow-up.svg</key>
		<data>
		gevrHAjYvajBFkdwyUkcqkQnXaE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/close.svg</key>
		<data>
		a524gkt6lJUBhz02qiMrzOvq+tQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/copy.svg</key>
		<data>
		/IowwSSVfMkdlVHpSmNPI3xH8LE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/edit.svg</key>
		<data>
		hCZFNJovWPxg2RnWN8HPQxXL2kc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/forgot-password.svg</key>
		<data>
		brY2ImuMx53pYKvLKn6aV9DztrQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/csv.svg</key>
		<data>
		yYG7ib6UQTQyeVo3h940D0sH7eo=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/doc.svg</key>
		<data>
		IumpS0eVH3PpzywALE4Snf3n3nw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/docx.svg</key>
		<data>
		IumpS0eVH3PpzywALE4Snf3n3nw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/facebook.svg</key>
		<data>
		WiI+wNkB6gwW6jLOTRAfHmfuco8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/gmail%20(1).svg</key>
		<data>
		xrsLke/hOSYCtv2pTX2nARcnWY8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/gmail.svg</key>
		<data>
		JGuzVzuA1lgblwZH2I7wYVuYCEE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/html.svg</key>
		<data>
		6cBC+pp+GwgRRGJlAcaI+R0fwNE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/jpeg.svg</key>
		<data>
		xst66Wk8QtpkmMfo6U20y73Q7zA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/jpg.svg</key>
		<data>
		xst66Wk8QtpkmMfo6U20y73Q7zA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/json.svg</key>
		<data>
		A12G7NCNHkqmdLVEX/+7v8mPBdI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/mail.svg</key>
		<data>
		gToKTZodZ9v6cfTCkU2Qylx4YYg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/pdf.svg</key>
		<data>
		ZYpmvcvTmrsBWc4skNvd/kjFsVk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/phone.svg</key>
		<data>
		x9vyN0YbbJr61QVqjg3Q30pd4lE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/png.svg</key>
		<data>
		3z/fPEwEi8wcgAQ7QmIm7gSmaU0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/share.svg</key>
		<data>
		PNGTJvV6NLhr3UMouit1crNuktc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/txt.svg</key>
		<data>
		bX0oreMW7lipa6vkhwe6JGqKXXo=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/vsdx.svg</key>
		<data>
		+akIHf9wwnbQimy3LsG70Bl3/8o=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/webp.svg</key>
		<data>
		Jnh+uHbbMkHGpqv4kGtZUvxmqP8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/xls.svg</key>
		<data>
		U4MFbxE0f4fRPvS23bADdMM0SO0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/xlsx.svg</key>
		<data>
		U4MFbxE0f4fRPvS23bADdMM0SO0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/zalo-20px.svg</key>
		<data>
		pyiU6prK+vtTxv6BOLCzw3gSMSU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/zalo.svg</key>
		<data>
		bBvwKrXWv/RmsjdvtlEvcoLSGz8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/otp-verification.svg</key>
		<data>
		JgNdbpswsDDrqEaHRlnLYm8lliM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/request_content.svg</key>
		<data>
		NhtP0lLS89GEedzOR5vdqykb2cg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/reset-password.svg</key>
		<data>
		JMhTi/y3g6Ur498yZAZ29fex8y4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/search.svg</key>
		<data>
		yCkUHBa6qCNHa6eaCn5pCC2MIp0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/share/facebook.svg</key>
		<data>
		vnDqmrhluaxiF2Zpg+x7m89FBJo=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/share/gmail.svg</key>
		<data>
		BL9HhgyvcJ+IlDLQwGXMYn+uY5Y=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/share/zalo.svg</key>
		<data>
		0KTBfIJEUB6p+lc6hJzAsdOP2so=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/sort.svg</key>
		<data>
		YyPFLRIVEVj3FNUeGNIp9jsG/l0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/splash/0.svg</key>
		<data>
		yJZ0VUcV3zQSmlc25/uFo3kCEFU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/splash/1.svg</key>
		<data>
		glC2XRhdpZyEERq1+nHxAhg9RBQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/splash/2.svg</key>
		<data>
		GqnPXPDGtogZmWBaOwofhF3sKZM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/upload/camera-2.svg</key>
		<data>
		MQ1vKNUm9m2U8O3OaIdPMx6XeCo=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/upload/camera.svg</key>
		<data>
		hAUyruMF6AXRr+3WD5h17hp1hUY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/upload/image.svg</key>
		<data>
		Gpellsc1ZWbTFZrnUFxnhjaY2b8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/upload/multiple-image.svg</key>
		<data>
		v1vhs3oFzfMRs/0nFEQaTZ7i6YY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/upload/video.svg</key>
		<data>
		/PABC/xf5qnzltK6433ScY/RxiI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/translations/en.json</key>
		<data>
		hpPFUsUs625az+S02ATDgednEYY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/translations/vi.json</key>
		<data>
		yBgXVJVJajl3XsBMXxnfqHNgeew=
		</data>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<data>
		qWRCYkB2ANaCGuWJ6K6EbAqreNE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<data>
		yIODAntuVAtv75bLXDJsfiytgVI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Bvk+P1ykE1PGRdktwgwDyz6AOqM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css</key>
		<data>
		mBihw0DQ+fObskdvU/uJxi4GQ/4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html</key>
		<data>
		60RUAYYoUnETCwVvpgmbGYgxn8Q=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_inappwebview_web/assets/web/web_support.js</key>
		<data>
		+Boin1DXnB61JcE1yuCXqZRkWsc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/fonts/RobotoMono-Regular.ttf</key>
		<data>
		wloHLJYkCrj8KIth7fK7Ezqql10=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/highlight.png</key>
		<data>
		r8WwBQ+evCnGl7x3gU424p2NOUQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/squiggly.png</key>
		<data>
		08snBsdc665o4HaZwcBUSGUi3n4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/strikethrough.png</key>
		<data>
		72Ix7h+1yekr/NFApyVZ08SfB7A=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/underline.png</key>
		<data>
		hSFDTS+dWKAnd1+jRfvucwSSK5w=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/wakelock_plus/assets/no_sleep.js</key>
		<data>
		4X7PZ95hkgUE15GU2+5c1VKgHP0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/youtube_player_flutter/assets/speedometer.webp</key>
		<data>
		TDvhTCHVurikSmzXUIYz0DM62Dw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VoVnsu58hN7wrwazX0nyAopiB0Q=
		</data>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<data>
		dY+ispxF8H6eOleDiynDNAGe7Jg=
		</data>
		<key>Frameworks/FBLPromises.framework/FBLPromises</key>
		<data>
		MtPPRoQcvoIlaHMo8x4rBlsFKiQ=
		</data>
		<key>Frameworks/FBLPromises.framework/FBLPromises_Privacy.bundle/Info.plist</key>
		<data>
		+Ans7zFMGXsltWSBjxmJUWtxAkg=
		</data>
		<key>Frameworks/FBLPromises.framework/FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ZajnvEs/MYRS3X4TPLAhBWi8mc4=
		</data>
		<key>Frameworks/FBLPromises.framework/Info.plist</key>
		<data>
		6HbhGzhidPuQPaUdcKU5hZ5UmUs=
		</data>
		<key>Frameworks/FBLPromises.framework/_CodeSignature/CodeResources</key>
		<data>
		HW4HjzzV+ZS+Y+lWtW0fo8qihlM=
		</data>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore</key>
		<data>
		Zl1wU70o7fwtPIXT1K6Pgyuut00=
		</data>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/Info.plist</key>
		<data>
		Yk841+OEXRu1n2jXMtGXnuzs6j4=
		</data>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		sa2OhFlqdCIyz9oV7fUdDKWzFL0=
		</data>
		<key>Frameworks/FirebaseCore.framework/Info.plist</key>
		<data>
		c0vGFfAVCLhZtvndzfNEY0xJuKM=
		</data>
		<key>Frameworks/FirebaseCore.framework/_CodeSignature/CodeResources</key>
		<data>
		jHIi2MoQWpQa7mHO3fWsjIH5H90=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal</key>
		<data>
		44FXc0he4ReVLxqBomMkhxKDIPI=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/Info.plist</key>
		<data>
		ol4Ol5y50FASHnTNdocck9aFRIU=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ifoThrqbbqoLG4yjAruMQRaf0Dw=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/Info.plist</key>
		<data>
		rg7uE/vV2PsE2z99/9Z+/9MxQ0k=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/_CodeSignature/CodeResources</key>
		<data>
		8ibsyFbt8GXMjZiEGXmyRSRKWOA=
		</data>
		<key>Frameworks/FirebaseInstallations.framework/FirebaseInstallations</key>
		<data>
		ky33EMDGadUyh5+McHR1wb1LIL8=
		</data>
		<key>Frameworks/FirebaseInstallations.framework/FirebaseInstallations_Privacy.bundle/Info.plist</key>
		<data>
		XkZ1U3UgVpqiyH6sJ0GA8LtjMUU=
		</data>
		<key>Frameworks/FirebaseInstallations.framework/FirebaseInstallations_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		WXQUJr75eMRgiVnLGyf8Gr3uLUU=
		</data>
		<key>Frameworks/FirebaseInstallations.framework/Info.plist</key>
		<data>
		skcGgPvcGFcQO9n11IXDtI4WAyc=
		</data>
		<key>Frameworks/FirebaseInstallations.framework/_CodeSignature/CodeResources</key>
		<data>
		Ihah8VBxgvFmv8kOiFITP/2Rvb0=
		</data>
		<key>Frameworks/FirebaseMessaging.framework/FirebaseMessaging</key>
		<data>
		IZpfAy3wn4nw5StERkGaCDqMPAQ=
		</data>
		<key>Frameworks/FirebaseMessaging.framework/FirebaseMessaging_Privacy.bundle/Info.plist</key>
		<data>
		6wQZFKZZZbX2huz5BZuVqOwD9aw=
		</data>
		<key>Frameworks/FirebaseMessaging.framework/FirebaseMessaging_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		gwv2NsO/6s7ik6Px6eTTTvacWN4=
		</data>
		<key>Frameworks/FirebaseMessaging.framework/Info.plist</key>
		<data>
		pvfuHryTlfAbR2xEfP16LYgIyz8=
		</data>
		<key>Frameworks/FirebaseMessaging.framework/_CodeSignature/CodeResources</key>
		<data>
		cdJ4XMCQHaOoQoUkGXbaX9CR2O0=
		</data>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<data>
		92Gq40CyRHlPjRDnKAxA95XqgiU=
		</data>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<data>
		wTPJHICwW6wxY3b87ek7ITN5kJk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<data>
		zbvYFr9dywry0lMMrHuNOOaNgkY=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<data>
		ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<data>
		V/wkSSsyYdMoexF6wPrC3KgkL4g=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<data>
		vFsZXNqjflvqKqAzsIptQaTSJho=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<data>
		sUgX1PJzkvyinL5i7nS1ro/Kd5o=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<data>
		SpNs7IhIC7xP34Ej+LQCaEZkqik=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<data>
		AqVvCbPmgWMQKrRnib05Okrjbp0=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<data>
		bkw+DmHReHDg1PPcvmSjuLZrheA=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		UqnnVWwQEYYX56eu7lt6dpR3LIc=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<data>
		VjAwScWkWWSrDeetip3K4yhuwDU=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<data>
		crQ9782ULebLQfIR+MbBkjB7d+k=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<data>
		ocQVSiAiUMYfVtZIn48LpYTJA5w=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<data>
		EARXud6pHb7ZYP8eXPDnluMqcXk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		qWHw5VIWEa0NmJ1PMhD16nlfRKk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<data>
		31prWLso2k5PfMMSbf5hGl+VE6Y=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<data>
		jCyzqU4gqHz/aFs0jcK3vgjCvmM=
		</data>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<data>
		3prFwsmSRuufG8h7qgSRpNSURCc=
		</data>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<data>
		D+cqXttvC7E/uziGjFdqFabWd7A=
		</data>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		GpxcPg5h6d4rGcPRcTiymYNSASY=
		</data>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<data>
		ipm8hg7aB3LzsfShJfpNR0QQ4hw=
		</data>
		<key>Frameworks/GoogleDataTransport.framework/GoogleDataTransport</key>
		<data>
		ryhLX3pb1r3whfzacUH2v5EEeEk=
		</data>
		<key>Frameworks/GoogleDataTransport.framework/GoogleDataTransport_Privacy.bundle/Info.plist</key>
		<data>
		JESOitRxxu+UhjCxa8pkRUSllCY=
		</data>
		<key>Frameworks/GoogleDataTransport.framework/GoogleDataTransport_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		WXQUJr75eMRgiVnLGyf8Gr3uLUU=
		</data>
		<key>Frameworks/GoogleDataTransport.framework/Info.plist</key>
		<data>
		Zlq6bRubOnK1VIjrYIrja1HQyJ4=
		</data>
		<key>Frameworks/GoogleDataTransport.framework/_CodeSignature/CodeResources</key>
		<data>
		p7I6JZn9ErG+hSa/USJOIWrFWrQ=
		</data>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities</key>
		<data>
		qe8z5RYYYPmTRGoSTlYGSWMJ24Q=
		</data>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/Info.plist</key>
		<data>
		XcSp91YPfvmqTmTyB4M72UjUs+g=
		</data>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		TrRzpkxcmDwPf9xXhrQKnuRceyI=
		</data>
		<key>Frameworks/GoogleUtilities.framework/Info.plist</key>
		<data>
		QjEKNRkTJx9lCNmMHzvYWpTgg20=
		</data>
		<key>Frameworks/GoogleUtilities.framework/_CodeSignature/CodeResources</key>
		<data>
		xiqi0n7fctRef6nHPKPzsVE2nsE=
		</data>
		<key>Frameworks/MTBBarcodeScanner.framework/Info.plist</key>
		<data>
		mbQ2GR3lQI9ZEh2Foy7eTHWzgJo=
		</data>
		<key>Frameworks/MTBBarcodeScanner.framework/MTBBarcodeScanner</key>
		<data>
		6Hiv+xJC1UgZsiGFvbYRMolqZSs=
		</data>
		<key>Frameworks/MTBBarcodeScanner.framework/_CodeSignature/CodeResources</key>
		<data>
		3HYVcDGFimFrO4h7huZIjJnEZuo=
		</data>
		<key>Frameworks/OrderedSet.framework/Info.plist</key>
		<data>
		RGzv7YKgoPVDw6Kx7CN4HM/EY6E=
		</data>
		<key>Frameworks/OrderedSet.framework/OrderedSet</key>
		<data>
		rxLwxTabOnPbgK/5YbGKwYxJbu8=
		</data>
		<key>Frameworks/OrderedSet.framework/OrderedSet_privacy.bundle/Info.plist</key>
		<data>
		iZsc/fPEE9eGbskmJED5rbSzSYU=
		</data>
		<key>Frameworks/OrderedSet.framework/OrderedSet_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		AL1dh5ctObXBjoBiabSJ86M3HQs=
		</data>
		<key>Frameworks/OrderedSet.framework/_CodeSignature/CodeResources</key>
		<data>
		QUVsKb4pk0UJsPJ84sNuRKKzkn0=
		</data>
		<key>Frameworks/SignalRSwift.framework/Info.plist</key>
		<data>
		AVHtyeYjry1IXWjMdm/CRIkqVzc=
		</data>
		<key>Frameworks/SignalRSwift.framework/SignalRSwift</key>
		<data>
		rWtb2Sr+o6y1yqGUHpfehZgAQyA=
		</data>
		<key>Frameworks/SignalRSwift.framework/_CodeSignature/CodeResources</key>
		<data>
		MQaahoCk6B3HNDvpCge8vGwMlLg=
		</data>
		<key>Frameworks/Starscream.framework/Info.plist</key>
		<data>
		kw+XDEp3qcBkI4lwfyMQXQ4Cukc=
		</data>
		<key>Frameworks/Starscream.framework/Starscream</key>
		<data>
		WydICDrgfnIYrvrac2mTDlMe3O8=
		</data>
		<key>Frameworks/Starscream.framework/_CodeSignature/CodeResources</key>
		<data>
		c0z8JOX8tVrYgykBJbkXzCK9TaA=
		</data>
		<key>Frameworks/SwiftSignalRClient.framework/Info.plist</key>
		<data>
		EKm5GtBajm13NFYogfWa25OTIgg=
		</data>
		<key>Frameworks/SwiftSignalRClient.framework/SwiftSignalRClient</key>
		<data>
		F85OrAFoZCdh8/oQlJqlt7p4y8Y=
		</data>
		<key>Frameworks/SwiftSignalRClient.framework/_CodeSignature/CodeResources</key>
		<data>
		ttwnuK20QuFtnXW2HTrUcabfHYw=
		</data>
		<key>Frameworks/audio_session.framework/Info.plist</key>
		<data>
		rBSpAEYEDRYRICoqcbUjfZErZPQ=
		</data>
		<key>Frameworks/audio_session.framework/_CodeSignature/CodeResources</key>
		<data>
		YOUOjOzBs+ZEdZFCePGTC/W8gj4=
		</data>
		<key>Frameworks/audio_session.framework/audio_session</key>
		<data>
		u+qAUSwMKWcpwT67ltk6IYhJG8s=
		</data>
		<key>Frameworks/device_info_plus.framework/Info.plist</key>
		<data>
		IZUa3iLi7aLPrNMuixOiXSYR0u8=
		</data>
		<key>Frameworks/device_info_plus.framework/_CodeSignature/CodeResources</key>
		<data>
		pQ/1+muI71ZdyohGWeZRc4SGEe8=
		</data>
		<key>Frameworks/device_info_plus.framework/device_info_plus</key>
		<data>
		b5MNiuanxUx4kKgDCeE10T6Yi3c=
		</data>
		<key>Frameworks/device_info_plus.framework/device_info_plus_privacy.bundle/Info.plist</key>
		<data>
		GzQETmoiBHZXH5/tyamhsFlGmx0=
		</data>
		<key>Frameworks/device_info_plus.framework/device_info_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/fl_location.framework/Info.plist</key>
		<data>
		CAO9pkgFO3pXgZBuaO3tSD4b6I0=
		</data>
		<key>Frameworks/fl_location.framework/_CodeSignature/CodeResources</key>
		<data>
		wgM1ro2KQW1e1uNAqhKmgzn56vQ=
		</data>
		<key>Frameworks/fl_location.framework/fl_location</key>
		<data>
		a+pOAD2kDS3D9DqwWaaCI9kcMrI=
		</data>
		<key>Frameworks/flutter_contacts.framework/Info.plist</key>
		<data>
		LFhMqaNYWb1jnEk3/LSgW+5cOBs=
		</data>
		<key>Frameworks/flutter_contacts.framework/_CodeSignature/CodeResources</key>
		<data>
		DrbFIbTpTKBC1ZHv4TiFXwMuAz4=
		</data>
		<key>Frameworks/flutter_contacts.framework/flutter_contacts</key>
		<data>
		l+cfHWNXft0jCXch8Q87PfZSUfQ=
		</data>
		<key>Frameworks/flutter_inappwebview_ios.framework/Info.plist</key>
		<data>
		M0cLvqAxiECQUzpBQHEJeKpT5ts=
		</data>
		<key>Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/Info.plist</key>
		<data>
		Jy29WF5V3X3M6f6G20Q5Adf/5ME=
		</data>
		<key>Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/Myh-pL-l6f-view-1n8-5e-oxa.nib</key>
		<data>
		uty2L58SD2aV2D5N8JrNJ96V8kM=
		</data>
		<key>Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/navController.nib</key>
		<data>
		8HYgGJ4vmXC1b2szD6CyaRscSds=
		</data>
		<key>Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/viewController.nib</key>
		<data>
		bbutQch+sFOdJaKbclbFgQNLboU=
		</data>
		<key>Frameworks/flutter_inappwebview_ios.framework/_CodeSignature/CodeResources</key>
		<data>
		Hg7qoie/CI33VbSroaVG6w0TyWI=
		</data>
		<key>Frameworks/flutter_inappwebview_ios.framework/flutter_inappwebview_ios</key>
		<data>
		6bfR8FfWRP6ZnahYzmyC25Wp1AY=
		</data>
		<key>Frameworks/flutter_inappwebview_ios.framework/flutter_inappwebview_ios_privacy.bundle/Info.plist</key>
		<data>
		TQ4Zz6W2jmB2STVpUVP/AlIYRYI=
		</data>
		<key>Frameworks/flutter_inappwebview_ios.framework/flutter_inappwebview_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
		<key>Frameworks/flutter_keyboard_visibility.framework/Info.plist</key>
		<data>
		aGPz0esztPoy1/P5mK3JNtDmatw=
		</data>
		<key>Frameworks/flutter_keyboard_visibility.framework/_CodeSignature/CodeResources</key>
		<data>
		fTD3+6tpthoiASURiWE8X01tUiA=
		</data>
		<key>Frameworks/flutter_keyboard_visibility.framework/flutter_keyboard_visibility</key>
		<data>
		sCbkeXJYULu52ty00dADIAL7CWs=
		</data>
		<key>Frameworks/geocoding_ios.framework/Info.plist</key>
		<data>
		kq0hGQsE6iWlRr+526SVQsBb9oo=
		</data>
		<key>Frameworks/geocoding_ios.framework/_CodeSignature/CodeResources</key>
		<data>
		2/nPwhHUBb7D+1DkGMde+IQ06Ds=
		</data>
		<key>Frameworks/geocoding_ios.framework/geocoding_ios</key>
		<data>
		VGBKEkouSrgVq3rYWyT5jc3GHng=
		</data>
		<key>Frameworks/geolocator_apple.framework/Info.plist</key>
		<data>
		LQRO6SmpAVwqTnK7Aemi1+ArnJI=
		</data>
		<key>Frameworks/geolocator_apple.framework/_CodeSignature/CodeResources</key>
		<data>
		43dPXF5s/ljRk9iJZX3ZCUYQPfo=
		</data>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple</key>
		<data>
		rAYYM7diG1Ym2wWATsOqe6RiuHA=
		</data>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/Info.plist</key>
		<data>
		D+yj1OeiQGVr5I1YDVyNZwnMCaE=
		</data>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		KFiVi4mWKmBFkTjfe3H4jsOvLNM=
		</data>
		<key>Frameworks/image_gallery_saver.framework/Info.plist</key>
		<data>
		Cdq3QiDpOp/STK3gPSKM2wZ8/QE=
		</data>
		<key>Frameworks/image_gallery_saver.framework/_CodeSignature/CodeResources</key>
		<data>
		lTrfUMjJzOJ76QeUQEkIR4aXnGY=
		</data>
		<key>Frameworks/image_gallery_saver.framework/image_gallery_saver</key>
		<data>
		VsNQ5/7gh0oHBLZbHe+UV8X6bng=
		</data>
		<key>Frameworks/image_picker_ios.framework/Info.plist</key>
		<data>
		D6me4DiyQwBsh4jUwUG7F1ssPcM=
		</data>
		<key>Frameworks/image_picker_ios.framework/_CodeSignature/CodeResources</key>
		<data>
		urM+zto8SUAIxe9zQDitonXVInc=
		</data>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios</key>
		<data>
		u3E0ZtkNyvyY056KMhYOmp1myaI=
		</data>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/Info.plist</key>
		<data>
		HaSJ8UDYGYR2bhYs+CAwS3aGz3U=
		</data>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/integration_test.framework/Info.plist</key>
		<data>
		tGSX+BlEtyyO4CyLRWnVA1Nft1o=
		</data>
		<key>Frameworks/integration_test.framework/_CodeSignature/CodeResources</key>
		<data>
		9JqnXD5wsa/S9HsrtxreMIwNytM=
		</data>
		<key>Frameworks/integration_test.framework/integration_test</key>
		<data>
		nEQaqFd2Ft2znCHFU9Vo1YlG8XQ=
		</data>
		<key>Frameworks/just_audio.framework/Info.plist</key>
		<data>
		6Unf6kCQnfjL+YtsBhlX5sUq+F0=
		</data>
		<key>Frameworks/just_audio.framework/_CodeSignature/CodeResources</key>
		<data>
		3xTRR3NFO92s1mizql7IuxACuRc=
		</data>
		<key>Frameworks/just_audio.framework/just_audio</key>
		<data>
		1H/JNI8cErIqN9TLoSq793365ns=
		</data>
		<key>Frameworks/nanopb.framework/Info.plist</key>
		<data>
		odWZIi4JfJwOmCxQ7RUWnwWj2no=
		</data>
		<key>Frameworks/nanopb.framework/_CodeSignature/CodeResources</key>
		<data>
		Q7cYjgPq72CjKOVNGjtpVax0wjE=
		</data>
		<key>Frameworks/nanopb.framework/nanopb</key>
		<data>
		k3AWzUmBvet/5JNeWzfiK+GZ4dE=
		</data>
		<key>Frameworks/nanopb.framework/nanopb_Privacy.bundle/Info.plist</key>
		<data>
		PfdpzudxlS1Ct4/OiUKtf7M6Cvc=
		</data>
		<key>Frameworks/nanopb.framework/nanopb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		KY5lfwC2TvsgFj4wt7hkMmainbs=
		</data>
		<key>Frameworks/open_filex.framework/Info.plist</key>
		<data>
		mgH8MfB3h1UqMWpEYsu8iV40dVA=
		</data>
		<key>Frameworks/open_filex.framework/_CodeSignature/CodeResources</key>
		<data>
		Iquyb7WKPsxrTiFDe3NbTkTB8DE=
		</data>
		<key>Frameworks/open_filex.framework/open_filex</key>
		<data>
		4pP6iUoXh/HhRr+i8FoK00elrIY=
		</data>
		<key>Frameworks/package_info_plus.framework/Info.plist</key>
		<data>
		SMGh/TVs50I2dGlIfnr3UfUkvEQ=
		</data>
		<key>Frameworks/package_info_plus.framework/_CodeSignature/CodeResources</key>
		<data>
		vRluSUl6R+FSgG3VZDiawskzGow=
		</data>
		<key>Frameworks/package_info_plus.framework/package_info_plus</key>
		<data>
		6+3mMt2YlmqG8cVtrPePi1RWLII=
		</data>
		<key>Frameworks/package_info_plus.framework/package_info_plus_privacy.bundle/Info.plist</key>
		<data>
		sOwTs+hyNakmwHNZGDpLUYpY5D8=
		</data>
		<key>Frameworks/package_info_plus.framework/package_info_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<data>
		CKyfuFY8zO7HRAWweQ3Ck5cJi4s=
		</data>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		tNl3Jp6ll60/q8QCPkn+S2N7m4Q=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<data>
		ika170OjLtfNe+Mu8LFtt1Woaiw=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<data>
		ILap70NMXtWLK5HXVDre4uGjJAo=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/qr_code_scanner.framework/Info.plist</key>
		<data>
		MxVOdysVD/AAJhu/70483KHSZr0=
		</data>
		<key>Frameworks/qr_code_scanner.framework/_CodeSignature/CodeResources</key>
		<data>
		6x0t/Mgsw8aJNTuF6EsxBYMCFIg=
		</data>
		<key>Frameworks/qr_code_scanner.framework/qr_code_scanner</key>
		<data>
		qCqvtigpnZupxDB9phkWD5FEz8s=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<data>
		AudlsDTh1vU47hcY2LFe6jDx4o0=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		l8OHzHm+3sm/WnF8E/k9H24pBlM=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<data>
		VX7/nAg4aUdCPgD8z55Zr6SRNfA=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<data>
		vFe1TP1SJvCjBu42zdEhcvSNLbI=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6uLTlq7fgHHWA8emYDf4ImHC+AY=
		</data>
		<key>Frameworks/sqflite_darwin.framework/Info.plist</key>
		<data>
		IrTZU0EZ+uCjkxcuCvUP0xgP55E=
		</data>
		<key>Frameworks/sqflite_darwin.framework/_CodeSignature/CodeResources</key>
		<data>
		NxebLLSK76tuTSrOIIATVD2X3pM=
		</data>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin</key>
		<data>
		v4qf3aGj6cU9of3cLvHd1hA/Fto=
		</data>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/Info.plist</key>
		<data>
		zMPj7JyjDAX761/V5LE7mRu1NsQ=
		</data>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
		<key>Frameworks/syncfusion_flutter_pdfviewer.framework/Info.plist</key>
		<data>
		BEft/TPHD7tyB8aqd6V/U9GhhX8=
		</data>
		<key>Frameworks/syncfusion_flutter_pdfviewer.framework/_CodeSignature/CodeResources</key>
		<data>
		PNN3jrv9whFA+NNYKAM5Ioe6dBQ=
		</data>
		<key>Frameworks/syncfusion_flutter_pdfviewer.framework/syncfusion_flutter_pdfviewer</key>
		<data>
		8ghUizG6tTfuhzLKRJSfH1FhZq8=
		</data>
		<key>Frameworks/url_launcher_ios.framework/Info.plist</key>
		<data>
		mzd+PBFIv/iOTL5uRnLbW4lqEhg=
		</data>
		<key>Frameworks/url_launcher_ios.framework/_CodeSignature/CodeResources</key>
		<data>
		vv3zQZsz2tFF/QBwTJtaIPoyVSU=
		</data>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios</key>
		<data>
		IeTmSilIekAdyjVvoMun78iiSFM=
		</data>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/Info.plist</key>
		<data>
		AU6baO2Ek//QCWlLc+TEllu2keY=
		</data>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/video_player_avfoundation.framework/Info.plist</key>
		<data>
		DIXKPYQkF+eeewPtVwl+ZTSDI68=
		</data>
		<key>Frameworks/video_player_avfoundation.framework/_CodeSignature/CodeResources</key>
		<data>
		c8AaM3YprVDwaZD5YiS3G71xdv0=
		</data>
		<key>Frameworks/video_player_avfoundation.framework/video_player_avfoundation</key>
		<data>
		V9sHp7puL1CiyqWjdXOL1HgMl1k=
		</data>
		<key>Frameworks/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/Info.plist</key>
		<data>
		iO5cvbcHRoGIdAb6HUE2rPjOXhI=
		</data>
		<key>Frameworks/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/wakelock_plus.framework/Info.plist</key>
		<data>
		yvu1ySx+zn1MdwhuevSY6Kudb9g=
		</data>
		<key>Frameworks/wakelock_plus.framework/_CodeSignature/CodeResources</key>
		<data>
		EtE/I+obQvs87hFtdvOwA8P4sQE=
		</data>
		<key>Frameworks/wakelock_plus.framework/wakelock_plus</key>
		<data>
		0O9Vk/6YZwqKCl12X46W9phbl2c=
		</data>
		<key>Frameworks/wakelock_plus.framework/wakelock_plus_privacy.bundle/Info.plist</key>
		<data>
		GdMgLzmmxbRTOJO5bUiMF9lXE4s=
		</data>
		<key>Frameworks/wakelock_plus.framework/wakelock_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/webview_flutter_wkwebview.framework/Info.plist</key>
		<data>
		5rU0IKFWS0bSD1IJWCLOLA+kcyA=
		</data>
		<key>Frameworks/webview_flutter_wkwebview.framework/_CodeSignature/CodeResources</key>
		<data>
		hOIVgfj2oTmiu3n2l6byG4Ip+Lk=
		</data>
		<key>Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview</key>
		<data>
		9PeDMc+JXKgn2GpJC0GSMVoddGc=
		</data>
		<key>Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview_privacy.bundle/Info.plist</key>
		<data>
		a7vDn8pgBcylFzeVlBCrhlznzF0=
		</data>
		<key>Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/Assets.car</key>
		<data>
		gBy8o+lU7fq7hVq+9H4dj3a7CkE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/Storage.mom</key>
		<data>
		Oon9nPw9yDxNK5I2dkjliQ+q9CQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileProto.mom</key>
		<data>
		FFqyrwkOXS4RV4JcHko8WlrvxH4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileVersionID.mom</key>
		<data>
		f6iV64AmgVo/8iRBMSg6SVkz2TA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/VersionInfo.plist</key>
		<data>
		HTYqw3m8dIaBrYtkwin0OHV6t04=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Assets.car</key>
		<data>
		E8Ck3L4tjw/ILpwiFCEz4Y8szFo=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/DroidSansMerged-Regular.ttf</key>
		<data>
		IM7pUc3dgbBfKBC7eTUgbJOheGI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-1x.png</key>
		<data>
		KBiuGmPvZm6y3heWHurqjBMitJw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-2x.png</key>
		<data>
		/ZC7kLA33LLUaGoy4elYV+v1Pss=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-3x.png</key>
		<data>
		UPqwKRHKhiKSYvXhNjoHx17Nuzw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-1x.png</key>
		<data>
		mfB/MyxUmXFg9Vpp/U5VsQY7S1g=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-2x.png</key>
		<data>
		jjx4hEkp/WAC6uAe/KXdwGjm6CI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-3x.png</key>
		<data>
		71kfAzHiEMvBEzOv7EHvlQHjfuA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShaders.metallib</key>
		<data>
		cQgvL+PScbS4JqappS/31IdzRGw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShadersSim.metallib</key>
		<data>
		doo26zSGrtADDZtuyBYSBQqBGBE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-1x.png</key>
		<data>
		ey+dHScVUXPkJIjjHuWIHqMc+x4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-2x.png</key>
		<data>
		PiTZA77HRySkmvo2YD7uXS0sYS8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-3x.png</key>
		<data>
		P7DEu6rMJYoBQSGW31uStrq81F0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Info.plist</key>
		<data>
		kBcvy5EM0LZteAaG+mrPH06vsBI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Tharlon-Regular.ttf</key>
		<data>
		QKmhT0236O/x77A3aFViLI3U0RA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ar.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JbmKQ7Yi9nb9O+v/B5QrGMl5qbc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/az.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			09EM2+7axQpHwCjPWI4QcQo3dq8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_background.png</key>
		<data>
		58HUtPew0VfX28iT0hoZoqqV8R0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		6gBjAkHF3kLwzqmfNBlRZY9e/kA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		wI+5cU+Y/voatJ+IHCVJWAv6jQQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass.png</key>
		<data>
		DSedvNbNTixsQO+c0IlL1PkNCdY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		euWXSDkE7B75wvPAU1ssfZA9NaM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass_night.png</key>
		<data>
		d+Mu/JBZVfGIW30YjWEaWiC/sMA=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		lE3Jc6hIhQEfpuGBhw6EKFWV+Q8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_my_location.png</key>
		<data>
		1h5giTFrQI++3fVXazIMR/7gzoY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		xT8ttVKmlQQ7RypGMAzTOIjuhLs=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ca.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			yjTzffTeb5fDlSkw3rG/aYIOdgQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/cs.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bzTpYpYOsxUvdELEsNav7tg8Uqg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/da.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1WXJdseg4LVPpUrAIbaFXL0iRU4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/dav_one_way_16_256.png</key>
		<data>
		6ZZOqO9aCr59xWXdfdxHYMMTkEM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/de.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kXIJA6+wX/K9jf2Hujfjog3tcNo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/el.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ue+ybnKPpYbbRYlnUjLCJ23NmIA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IHYnabnDXvmCRBqRyQB2tV7WS6I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_AU.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5lJIfvDH2f7QNivaY24C1v3IMSA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_GB.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5lJIfvDH2f7QNivaY24C1v3IMSA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_IN.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5lJIfvDH2f7QNivaY24C1v3IMSA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aTt+26dsWZeYDS+XJp91dJW6XPs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_419.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6j8AZkLqElUzbBSleBGbbzJVv3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_MX.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6j8AZkLqElUzbBSleBGbbzJVv3o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QshaVMsla0C4pBe5s1r3/je8G6U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gxOrQTrBzVCR0KLf/2DjcghR/38=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr_CA.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8OFgRokj2aQkz5PAVj6yhWCkrb0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/he.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8zYc83A9d6OqMODdRjv+FTOB3qM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			EkVuUv6RtviRuGZAFmgCZQXC4Hc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Y4gd7MUWlUxVrC0S4jxqZw5Qhuo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hu.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3TKcyay/Qz4LthmS3cN81pR2yeY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hy.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			e9+43XmfNEHP9WJDxSc0geaYSac=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_32pt.png</key>
		<data>
		bFfwK83wBj2pyvoGrW0gsjjayaw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		hQkZK9WIJ30yrzBkesaTSfM5C1k=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		FCUpCQIg8t8Kle0t7ba+v99x9x8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_night_32pt.png</key>
		<data>
		//Qx3mc4+ewO3CjLc2Y1//23DEY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		KrTZRKDI7Kduyxjzb3s8GUaEn68=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		jAhA6BSLXPLryZQnXa1GUxjVKi4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle.png</key>
		<data>
		FczO+2H9ZOJL3KtoFkr7ZEbUEo0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		ik1Nng6iqN/+ruu7baKmTu3Cy/M=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_32pt.png</key>
		<data>
		IgSXn2hc/27HC0evGvhDhTXKIE4=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		byDfU2A4NkxQbzsuboUTK7iuvnI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		tHnPy6KqF69QBs6+b1B9h0FqGRU=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_large.png</key>
		<data>
		/WPXm9lH4rlc3x4vdnr7k2RHkEg=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		zbZAM7mPNNvouB2QcXsTxOCJtxQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		P08jpJQZdro2N5VhzgQnu+jsSZk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_border_waypoint_alert_32pt.png</key>
		<data>
		b4HAoXXQ9MHUkvo2sAUYv1olAFk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		aRQYYYNELohm737W3DQtx10B1Sw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		cuJK6gmSOjEhVI30xIeEIONVSTQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_32pt.png</key>
		<data>
		jNNswlCF0SH6V+5se0OWriTR7uQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		lUI1aCanVG7Yzr2hHEiHEha2VNQ=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		JWzMUhmhmba6Wxo9dCHPf5IQb8s=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_night_32pt.png</key>
		<data>
		8O78ScAfBRbkyJp9FhiadAejM50=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		ZHFlVPKPc/Zwb3ySc9nBC689uOU=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		B+VEFeaeFm2C6uIBDXzUldmMLZM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_32pt.png</key>
		<data>
		KhSMRxKx1AkoUpWWV+VBZqgpDsw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		nwdneM0KoilU07pSKqKyw4UeWxI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		GIkw+XPhxJ6mPgnW6nZLmostL4s=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_night_32pt.png</key>
		<data>
		yxcShW6PUH4L7SvAnPK6znnipCk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		YEJpYFlKpPtva6iPBIofJQGmrF8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		mpZTRyQo0MeDf8tFP5XVApaPBB0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_location_off.png</key>
		<data>
		xh3gGmRY86tJc5Lh3Nd6zBYqSbs=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		NTF65BOR+sJY4SRV1e2mgkz9kmc=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		rQpisYk5GDcvEAM1KX1zN7Tk0UE=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_qu_direction_mylocation.png</key>
		<data>
		v210WlF/V/X0MFX7rrbTkt6/gKg=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		+WqWJySO0WVHmdN9btErTRncON0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<data>
		Nda7w6LKUWLwuNgEGSa3UV+Gu4c=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/id.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Id2d1TYMOC3GPpiy92XGj//yFgI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/it.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			D4rPH9dJz1fTBQeKsIB6AYKE2oo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ja.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ak1jUzKd12Du7Cn/KaVBeSviYI4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ka.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LNvs+CzgutdzKwN0XZXq9t5F30k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ko.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vFpe6HKmx3Wty7ZISQtlzkcxpAc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lt.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0tSKlBt3yuxrW05GsNzqdnrT+h0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lv.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RCKBIodaq/BPm8R2wxxZI+BN2sc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ms.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DWV28d5M08cuv9DtzUysDXFbO+A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/my.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			MYlyD/zOjrnV2n2U91CPt24WJ0E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nb.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			wQ4U/Q+p5/zoTHZwv8VBf3uhAk0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nl.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			O5DfgysQJzzCd7j2eGrgqbwT+hk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pl.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pruyhhc1rJKou3MKILqUZ4Lrc70=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture.png</key>
		<data>
		z1kf4/sWpoPOzvXlf8K5f337H7o=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture_dim.png</key>
		<data>
		YoDHbGi2Kzr6KzaLWpIC+iHNW24=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fLIf4JvrU9JH3q7AGeGEUnwBnmo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_BR.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fLIf4JvrU9JH3q7AGeGEUnwBnmo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_PT.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nmztYu9MrcyhylrohbORXSff/NY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ro.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			UbpquNUw8Pb3AjWYytrJKXwBwmQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_1-1.png</key>
		<data>
		HG4lAzW99jq1X860ruCadRTP1JI=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_128-32.png</key>
		<data>
		Y9Cc3P9fnqQlvUq6hljpmps6Rfg=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_16-4.png</key>
		<data>
		RnyN/LbKvVpEJtWMNkWwn1v0jnY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_2-1.png</key>
		<data>
		liaTlaCHAiLcpdhS15/FoNQuex8=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_256-64.png</key>
		<data>
		TgH7/QavDGQI/skL1YE/6rzYH6I=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_32-8.png</key>
		<data>
		5lTmhJEnxGk7JCSt1o7hQvsaZE0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_4-1.png</key>
		<data>
		RMtreAjsSE5oStFTdN9QLiWrM14=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_64-16.png</key>
		<data>
		solZa4fFXHU4p61KSegryhqGfMM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_8-2.png</key>
		<data>
		IGEsKFv6icS08/TMkp+MormykLo=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ru.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ZQxxbOa9SqerIFcZxVCb7hwnVPc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sk.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nSlgSiTUs/Q8ntegpWOmKpS6OkI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sq.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			k5p4vwv3FB0n44izHXvxmLEu6hc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rF14tC1rzneYivKZXDGt+aRKOkQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sv.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			sJHCPhWQxAWGu4lVlTzB+DoIegc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sw.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3OrZzSbV0A8W1luKhYLPVH7i9ZQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/th.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NhXIJxUR+Rn3xqHWJfwZSswdydk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/tr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Un2Wg0+U5Up8KdhDqnHZOZA0W0M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uk.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xVreJku1CfVa61YyrPDj4xCzYAo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uz.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GpZL39kYI8efwM11Q2euuZPc9lY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/vi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9e+3O4rjHEfIQylawQkOstDGWFE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_CN.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mkxZfPLm1uPPz6PPyn9RctnE+0Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_HK.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0W3/Rg1FHTleO/XRiX2MgMmLgW0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_TW.lproj/GMSCore.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4KI0ZC3ZrAiaNpBlwgjmB9NaZnY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/Info.plist</key>
		<data>
		utU6qxazBradXD3t8ogCN0Q74wY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_left.png</key>
		<data>
		wEgufayRRGE/XJ0qEoTBxkKrlHk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		Nk+SqgFNjGYaDU/37+ZTq+lPIrk=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		PeAkYTqZ90NC+h3Mi5El7hzRBgw=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_right.png</key>
		<data>
		y8j2sPykvEUgCCWbqkMQajBUA70=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		szkKZfIOIr+5aD8GPzuDEHHmsBY=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		YSf405gjD1uaF7YDsam6q4yvNso=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/ic_error.png</key>
		<data>
		0CVIeN+d0Z/e6FHcEzk0GpRz9KM=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		BYvkcO3ien1dBURGTX7/iQ61684=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<data>
		4z+GVXIKMXonei9wATmOpaG2Yp0=
		</data>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/oss_licenses_maps.txt.gz</key>
		<data>
		S51HodwJD+rwAAsP///g/nP7YYA=
		</data>
		<key>GoogleMapsResources.bundle/Info.plist</key>
		<data>
		AV9eBTgl9w8HzWap7X1m/nCCYuA=
		</data>
		<key>GoogleService-Info.plist</key>
		<data>
		jPUFBw629vjuUeYNsdsaz2l/AcU=
		</data>
		<key>Info.plist</key>
		<data>
		jHaXb18o4XkD9WgGw+26w6osuZY=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Runner.debug.dylib</key>
		<data>
		jiUXGyuYsyiOjAVWm5pX+8ruCfM=
		</data>
		<key>__preview.dylib</key>
		<data>
		f6eqOwewtAuCWyg1eFFkwk4VVs4=
		</data>
		<key>firebase_messaging_Privacy.bundle/Info.plist</key>
		<data>
		Ko5S6xQUSzckhzb8bYubCamOCPk=
		</data>
		<key>firebase_messaging_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		5QHcDAMWkAxcOff8YuMxIzU8/EI=
		</data>
		<key>google_maps_flutter_ios_privacy.bundle/Info.plist</key>
		<data>
		CWEyX/ZiAJUEBewFqP67qUUssiA=
		</data>
		<key>google_maps_flutter_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		2883mGk+25yVMT1GYe05XI1vTMQ=
		</data>
		<key>permission_handler_apple_privacy.bundle/Info.plist</key>
		<data>
		0VExUntAFf49nrjgGJnrlTHtzv4=
		</data>
		<key>permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PgAJpgZlblxKbgx9eihlgflAQU8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Zb9VR5aeuJMnm/RgXM3cr4LUNi9UZgxKD7xAgkid0NI=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			qD1swlrP2bQrzHPsXIVGyWnejjfsVOCk8ER0IotyJt4=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			LsgOnUi2m6fsSNX+O6JFEgpl5pPbQY3kS/cIt7WJI1I=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			MLYS/rGlRDfjjHWDuWCirDt0zobokkwlMoJohZWe2go=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Uns6CC0d+doVz2gKYBh1LeEL6xV0WrIPjwTMGsT2a5o=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PpvapAjR62rl6Ym4E6hkTgpKmBICxTaQXeUqcpHmmqQ=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			y90o2JQjssm+7ysnziyWCNMNbGqdLnZ595pTgURE5T8=
			</data>
		</dict>
		<key>Frameworks/Alamofire.framework/Alamofire</key>
		<dict>
			<key>hash2</key>
			<data>
			UDrNPyhcqhDvW1xvNA4qXyosyPHqi7BlPeVK4ltTnsI=
			</data>
		</dict>
		<key>Frameworks/Alamofire.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			0I6hlsYMLvpIQ0YBjFTddV1RadukR32Sb21ZMocsNFY=
			</data>
		</dict>
		<key>Frameworks/Alamofire.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			xswlejcUwnIU74c5dFxieHA/oy2aI0e0JmIuJPfhvMU=
			</data>
		</dict>
		<key>Frameworks/App.framework/App</key>
		<dict>
			<key>hash2</key>
			<data>
			x+K8Vz3icNou498fkvT2xWd7SfIZ4vZAhph/sn3gqlo=
			</data>
		</dict>
		<key>Frameworks/App.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SES+IrctFtb2ATzloiQHKMcw7x/vnPQ6XFUZixhhSAI=
			</data>
		</dict>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			yySlj1Oaj4uSq1FRZZ+xeGlCUoDwe7FGut7l+TulA98=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/.env.development</key>
		<dict>
			<key>hash2</key>
			<data>
			InA1q8zKQz2uW/PdUTYN9v+kSlS+6vMHIdOkWPje5DM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/.env.production</key>
		<dict>
			<key>hash2</key>
			<data>
			InA1q8zKQz2uW/PdUTYN9v+kSlS+6vMHIdOkWPje5DM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			edFp6eM2WS3ihJ7mBEYRGxvu3XssM1AVLQrPGYFkkiM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			vN7gJVkELG4KEymPfv+8rgv6We5E+rmDh8taVMjvjIc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			CXQfna1qeabPxVFGpVa3/XGcKEXuf2U8Fz84+dLMRys=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			wqUiC3u8EWzdaDmMuKkboLscwg9eYrJ3dQhWN4E1w24=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/c-13.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0EYAljygujqEZQ7dUW+/C4wwUOwVgJOTy02CHciZjMI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/c-16.png</key>
		<dict>
			<key>hash2</key>
			<data>
			CIqBSjfOf2rcTIbyDxxH3clVItyS/cAmieroi3H+hZE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/c-18.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JU0YR0z7TQN9N5hGLXHBVr8KX6zzP02IytaM8U7/Yr8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/Oswald/Oswald-Medium.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			i+dvsHhIfTq+INjQaNitj8R70ltQSsex5mQjIf6rFJ8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/PlusJakartaSans-VariableFont_wght.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			5JP+4rZ67SfDn47qh2IJ/8cC3UdYuPrIaP+upgCrtXY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			cdEKhrTFSlqcDItGflOsZ9ee25bJVuTp9lpwdN+5mSo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/form/mail.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			XlvazJpNY/zd0/+w3vLeIAd7j8/i5VFl42RdKmFLzO8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/form/password.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			KCPiDQNUPWWEnBlZdDcfj/Hh1gljxupSWepyEQzxeU8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/btnBookingdetail.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			6cycMrYri+QoPO4cJJfUrTQcRzMiuIzyrs8ENjEGcv0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_couple_seat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			kwaqo3izcQs0EAtiL6JSbnj23Ces5TubADi9tUvm/hg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_couple_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			kh46gHhTnRFE3M4SzPpcpt2q+FSvHzUc5D9xqCNfx70=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_normal_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			TKrc60lHVUtOyyHGK2rMulDsIue8cVaAi0cymFA9nOU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_empty_vip_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			tSEQ0JH3r+PuTImwKIyNLiFT32vywO4pZMXEWrd79To=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_process_vip_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Cg1RsPxuDjPEfAaKFxDCTM4UGHzlBVC2aVtKBbMAUuQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_screen.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			XuAEEQooOVIpszC0hpAI9nuXrEAZhMv7vS42iq5+BVc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_select_couple_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			V8jprX5AavXWZ287a4HQIV2xAMIodgJ3zYThab9GNBs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_select_normal_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Jpncctxl7K4IMYRg0sUje2IENYm6xvZoVFL2cQsJo/w=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_select_vip_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			s6Gu1ZDtAF1vwwQt7lOa0WkrL7JotYlGeiDoL1TDrH0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_set_couple_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			uttZXRGtgwVXEJ1L3esZFthoIl+r3lCB2eMAV/BBgVQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_set_normal_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Rul0+CFZvuUqietOIjFFfoJuOHBrL8fnLwMPsxB/VnE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_set_vip_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Qo127wBDGayBhDeerKLdNiorwfJbO+7oH/kbXOxjk8Q=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_sold_couple_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			lMp5n3eHGrey/R3ypig1m1Ys8cFtJ31HMIbjJkubi9k=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_sold_normal_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			MB5whmDZNGje+MsYZVZfZKHoWr3XabciXGkfQGQrsIw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/cinema/ic_sold_vip_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			tYuIYFYiNhRYzhPa/4uzRklPoJL9si3EGgGl58r+qOE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			CnxrYLtrwJyRqQ1wAf6RJPgRryx4gkamNOiCxnhe2YE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/fill.png</key>
		<dict>
			<key>hash2</key>
			<data>
			vxn9hHvyoIKmsxD02ytwwLqqlEHgzYY3/s29QWPvjkM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			ZshDzjTnM+8WKz3Zwmt0mebrXIetU8eMAjml09BgHqc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			wYsw0HtkWblib3acQlgqndXCSYD1EQ6ZDNoMZIqnV2U=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			caFoB2LSh9lk3mPXEPENLtUyq3t3GUde3ggdK/ZSozE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/opacity.png</key>
		<dict>
			<key>hash2</key>
			<data>
			fXK2SGjY3oJosOX4gwRfC5FXk43aPN7vNJTZkGGlNSo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			ZXv1vhsLScc89nSiUZe2xuuD3FZ8uqUTSdejSP5W87w=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			zzsNajyjvl2bF28um90VpwytekAYM5W7a2V/ma6QEkU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			9zcQhl0gdwn5LZl62RzqHObCAsdDYrgObdeAW/NSAJs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icon/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			L622LWOfVBZ+PHwayiOqcroaT3wx89zPB8YkFWUfSmk=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			8WIt3nhNnTSLyiXmpkjX1iWKsVc1zi73W4pVlQoCfSU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/avatar.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			DQANrbvaVdsml57EuxopsPFur45zuiT1i7P3tbtS8mo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/background.png</key>
		<dict>
			<key>hash2</key>
			<data>
			V1fTws3RJOOz8wWPKSb6ApsWu8murywZYhYj0LQb3dk=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/d4af518117996ffbab547753b36d7886.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0Mz/EY/v5ZPdz1nq4RUS+Yqp4VdHy5p11vja1bGN+F0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			bt3g2cxZEvzIfHg5l0fcpr1/b5pIEPCfuXlveFZbsiQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/login_background.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0Mz/EY/v5ZPdz1nq4RUS+Yqp4VdHy5p11vja1bGN+F0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jD27jfbiYqXNCUWKelAUTN2jekX14xZiSy6R0x3PF5s=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/petro_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			l5Whs0juk/0qVDU9ttEe8YaNjREaDkzkdp1PII0gE+c=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/placeholder.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			rS5po+logiRXZHVAtvDNyqjRbJg/EpANuKhfWQFuiHE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_empty_couple_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			9kkgpaQY6N/fKi6XMiRbZjQlXpkRO3welOWSWntjDV8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_empty_normal_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			g0OnI413BOWznplwm4wniMC4ySfYXrOd7scCFTVDiJw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_empty_vip_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			fViFxXb0SdNlWQbuTDxJcbdGq+UbChTrh5zNWdW3DnM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selected_couple_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Vs60VsBwmqeMqciPP7ZAoOoqr6oo3/NZXPOAxnq5myk=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selected_normal_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			SvaQOKiqsnRykvSk9PzXI8N2NW2KHUJZLOuTn5A6HzU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selected_vip_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			SvaQOKiqsnRykvSk9PzXI8N2NW2KHUJZLOuTn5A6HzU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selecting_couple_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			bvSQEWswwnueT/Cv/dhZzi0Unlj0dvnP3HeRJFFYSro=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selecting_normal_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Ec3UsZomh4D91ivw3kh2WGIyBRPZ2zAyl6LPpeBCDMI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_selecting_vip_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Ec3UsZomh4D91ivw3kh2WGIyBRPZ2zAyl6LPpeBCDMI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_sold_couple_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			gR7FlRuV3R0gQv1E92MjggaUU0N+S1RQii0ZnQbKkfM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_sold_normal_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			pNl5jCocrcqZjas6hwiNPs5OhVxDlrZBYwEWminnn2Q=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/seats/ic_sold_vip_seat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			pNl5jCocrcqZjas6hwiNPs5OhVxDlrZBYwEWminnn2Q=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/json/trail_loading.json</key>
		<dict>
			<key>hash2</key>
			<data>
			petOMxPH7vtDfZgKxU8p0UteY0lIoBNKPmihqnh8jes=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/p.png</key>
		<dict>
			<key>hash2</key>
			<data>
			mhigMencyT6RNTw9F/KjN+h/7Hyh2GlVJbR5rlxV3O8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/arrow-down.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			3qHwT7AU1YKIkcczVJL4H6xPuUuketr/MRh5DYswoFc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/arrow-right.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			J9w03ahc3fJvBj8+6BG85uuBtvk9SJdSbYgOym6hDA8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/arrow-up.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			v8clyEel9U5Wp3UF4omys+3bhdgdqayhV2dASEPi35U=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/close.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			XfzJrbwiqyRFC7Cb9SkUGKWT/YayfBbbOYe7V+eOdz8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/copy.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			5X7ZHM92QIqWdy5rqUmcstwVIC/4/6/rlPHy0KgAQIc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/edit.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			LjSVCKeRGBhTYaq7SOXyzwwAbRo68WQ7YDUdJUl/vBM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/forgot-password.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			NIHJyYAtJ9whTj3RmFa1LstFrPRbDtwPRG5jZDxPK+o=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/csv.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			6R9G2fM3MKu50WRxpJQ9drvfy3aV+jklyT8rqoqxKpo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/doc.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			5kImH4JEVYRwS+E9VLj8P+XX69T8W/tv3L9K1FtRAlg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/docx.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			5kImH4JEVYRwS+E9VLj8P+XX69T8W/tv3L9K1FtRAlg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/facebook.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			n2+iD2aSb8R/spCZdSobnXJP0RZpw9ssAsZ09uX928I=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/gmail%20(1).svg</key>
		<dict>
			<key>hash2</key>
			<data>
			3LpifUgLSRbMBdYs1M/sy/esBfyu7Z/3MyDU9qum/GM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/gmail.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			gs10kbIH6Z9JTVqRXNHoHbc8Nskb6BU98d+EuOC6hlg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/html.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ImWSU2poobgaVLdVPas96aZ3T+7Ug3uSRU+IUk/yTEs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/jpeg.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Niqq7BmW6ep6EHKH5Npcijr7/nJlIRV/9OIeM7SmqqA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/jpg.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Niqq7BmW6ep6EHKH5Npcijr7/nJlIRV/9OIeM7SmqqA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/json.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ATrNAR752hUwUph81LZY2WacKd5mb7HkFDhUTp3HrNU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/mail.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			J5/hoZT49hlKBQwngMAoHoYSSkxGk6xG/iIaa+o8eiM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/pdf.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Y85KMRyk6Wie4os7ETOV74mkAY3gH+Tm/kesasjSzlA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/phone.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ym3QBbwueRjTAyDwUXdhB/9pCfelsFDmquVzi09dPjM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/png.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Ig+cG5JS3tkO/IETEGBsoICqrJgPsfrx9TNaXuTX1w8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/share.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			97cgLw8DNrkYpppKfDyhk+RvEB/8hWeUPRYFEd3Wdvg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/txt.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			s3zGgBz5r1ZrBcbdUoa2WtzO1fK/uZ2QjlrlbuNaz6M=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/vsdx.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			bd1H1mecN9akDjnqxeklB17IVVViqjq0hEOtYIZt4No=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/webp.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			FvkjUZPnXI55XZiW2l03AvMXyMCxWHWwASNdGf++tig=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/xls.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			aLI2eeZlTFXyw0JpMMR0g68br9bolwo8EzmU/T3VRd0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/xlsx.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			aLI2eeZlTFXyw0JpMMR0g68br9bolwo8EzmU/T3VRd0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/zalo-20px.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			QfacO1WYXfSDqC6HsZCh/GT696/xrYrFEKufyXcM6Wo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/mime-type/zalo.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			1Dr2SZDScL9D1G6P76UsbqOSTcmKL+fiO/p9wgbyW4M=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/otp-verification.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			eNIdEhcyUkCyBhM0uwDTxQTrOjqVdsXLLEt1ENOUtFI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/request_content.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			/SsihkA9/SN1+5b9lWTbHZC8U4HQNn1utKuofM8ZAGM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/reset-password.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			sHN+YS9hSNVwEFz9WTbvaT8y8HjqQvyg/S3r3wKE4uc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/search.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			hRDnwsP7bGz7IaxBrmpKL3gqyfx1YklLwKZTCQvRUF0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/share/facebook.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			a4OOCSaD3m0dRjsItfo71KyF9Z9tIwHfyyGIlLR2XN4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/share/gmail.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			w316xwaWt1flQLQQZaKK1/w6MXXfK5B4v3gd1p46cGg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/share/zalo.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			eQQzMApJKylRAarrcNOw2roQ4BMQChTlFyOq1C3sqTg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/sort.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			0eI6o9rHZoXjdGcMBf/JbEvpskyoKXqbgOac5dQIJ6E=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/splash/0.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			r1OkDOfHXonBAbr123aSyQvKvoW2HrdSFEixcAwiNbE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/splash/1.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			VWzFIo+/lrikO6rYHWdIA+/7u2Aiv7BO883tXilRqpQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/splash/2.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			itSbtKCOx8xTpm1P4iZFRlE3G/dqifdKYDRRs/o4UZ0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/upload/camera-2.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			diAq+szkGggnbS3iMj/SIS2C9tDU4Z1xCmUaVmgMevU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/upload/camera.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			9dQJc05laCb7tUNDrSjYeV09EgiluFfnLQ0SLR/LWt4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/upload/image.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			nOHMZ456a3bnHzRVV009g3SGfa72TisePhTnqHaq+K0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/upload/multiple-image.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			JFqYvm+4QecP+sRxmDDA8qxoDLPXY/xqTp43r2sSIS8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/svgs/upload/video.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			XrVG6jJk4zgT+eWWGxBo28HTcZk9NTjF+LssuXif85M=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/translations/en.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Iwz/GMqMloeDco3vVZR5XExU2/1+SEunZVd3TyPzaQ4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/translations/vi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZHVbB3ploGYNM8Vh87b3hsKTgq/rQ19s3wRw+w05x7g=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			SOJ5rERauzhaCV7eUD78eXv8seogEgSQP/xrVhwDCq8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			VqLj+eEjjEqok3J1bJxzVEbWMElD5fT1KbTE7MUxGZA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Z8RP6Rg7AC553ef2l34piGYcmj5KPF/OloeH79vtgjw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css</key>
		<dict>
			<key>hash2</key>
			<data>
			LlYyIL2FXhHyMNwnoNby/VuDmimW+G7cgCWqtF5vpcM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html</key>
		<dict>
			<key>hash2</key>
			<data>
			3HLPwfHSpQE7ud40+MrPXiblQtfXE/y+CbhltKrKbd8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_inappwebview_web/assets/web/web_support.js</key>
		<dict>
			<key>hash2</key>
			<data>
			5Qb5DKJ6eTh4GMnE+7V+lmKzYiQssG29GsIOz5XB9Xg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/fonts/RobotoMono-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			jZofpN/lpd7qeA9lE5lhQUJe94LXzFZl1NRZFG/Tpxw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/highlight.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7S9xpxKH0Xe8w1gpKG7EgrRm6yNwRonaEyX5rw0olao=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/squiggly.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7bgfUlMdMgbze9YmpXWeWXrdlJflb2QkWeJZNb3PpQs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/strikethrough.png</key>
		<dict>
			<key>hash2</key>
			<data>
			bbNUrnKxl2z24VaWk+oENMcdr9+GlbqUIYgDhGWtNC0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/syncfusion_flutter_pdfviewer/assets/underline.png</key>
		<dict>
			<key>hash2</key>
			<data>
			k5XgJGUcOFg9eqyiJGr0eAO2lgw62E3iNyghuKXMoE8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/wakelock_plus/assets/no_sleep.js</key>
		<dict>
			<key>hash2</key>
			<data>
			3OTu8LGXtkCtaqqyIo7h7n3M+L1ta13lSE3RvRZDCng=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/youtube_player_flutter/assets/speedometer.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			UyJsGpac7pIldiQobLwRFgmPJCSQLk0Ald1JuO/hCTg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			4PC1vOWKHlNOnW4dGwFfD4TPwBCllniPCsOy7+1X5co=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			pKsT4eop3vfHEYhT5SQiPgfEGr7URpfsdddh2h4sDdY=
			</data>
		</dict>
		<key>Frameworks/FBLPromises.framework/FBLPromises</key>
		<dict>
			<key>hash2</key>
			<data>
			zcoF6jOXyIWA/GzfK0Vycp3N+vtSELUz6tzwxQhSPzY=
			</data>
		</dict>
		<key>Frameworks/FBLPromises.framework/FBLPromises_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			vMkxpQWyhYkLnpIlMEBKDWMpOWjRzvLcVLzyIQ/nMI8=
			</data>
		</dict>
		<key>Frameworks/FBLPromises.framework/FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			dLDNcvwjwe8wLyLuJ1P2GBfNxa8P96fy0GMrUk+4rOo=
			</data>
		</dict>
		<key>Frameworks/FBLPromises.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			D4azDqcdcqaGCDBuxiX5jeSRH5REpstBpI1sGMCt3MQ=
			</data>
		</dict>
		<key>Frameworks/FBLPromises.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			l+f0PYsYk0vVC9uySzmeMq/fMwBJAP28XvW+IU5BRKk=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore</key>
		<dict>
			<key>hash2</key>
			<data>
			P+JJ1x9A3h74/CyatwcPaOm3dGoo6wv1FIAioUS/b7s=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			6GWZ7HIlW/+5ak54Bymg7ezLl2yNZi9hkyL4+Ew5ulE=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			EeMfX2tg6A69WQFUn85QQ/mvPmg/h0AilFAAtAUwbD8=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			l7qtYtIB5biV85WaaLW2FjNqcPXhr/dUZ/KSjO4Mo/U=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			orNh9fP2f8W4qvJEC65ALUPYjQabEaCY5L4RsuBiL74=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal</key>
		<dict>
			<key>hash2</key>
			<data>
			bVHumEWGzskyzzjgGNuPGLRZMFfh5nTMNRRmgp2PbyA=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			e1/LV/CRCtinCsxRRPAf0HmmktXUyvlLj29+Wjhs0FE=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			W3/peUI97ePgivwppC8A9ghiddxUioTCl3QjWGPu0+8=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			oDC22dPbU8gmNg4H8jaWxqbacvgynuj6i3cv9Jkl1eI=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			hxK7swFNiSpFZwMYeIHlkkvwXF6F7GM3Cuon8PTUe40=
			</data>
		</dict>
		<key>Frameworks/FirebaseInstallations.framework/FirebaseInstallations</key>
		<dict>
			<key>hash2</key>
			<data>
			89dk6kgx8IGdMaa3NluGe7iD7utBcgus5x2Q5AkDYWg=
			</data>
		</dict>
		<key>Frameworks/FirebaseInstallations.framework/FirebaseInstallations_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			JQek3qSWiEl/rhbQeNiFy2CEcxkQDsJXzgEz/djDc08=
			</data>
		</dict>
		<key>Frameworks/FirebaseInstallations.framework/FirebaseInstallations_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			z7s8T3ambVNpi66R9xEMAPIUjm5vE619MlkpCbwBDlE=
			</data>
		</dict>
		<key>Frameworks/FirebaseInstallations.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			+n2kprjB1ZvxKHYYH0Dv7YUE42xFcpvd2EZKSi1et40=
			</data>
		</dict>
		<key>Frameworks/FirebaseInstallations.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			gFMdOaf1sJ+FdOyBRVuEWydRj1uLOEUIxXll2bEHIbM=
			</data>
		</dict>
		<key>Frameworks/FirebaseMessaging.framework/FirebaseMessaging</key>
		<dict>
			<key>hash2</key>
			<data>
			fqz8+b/jAI0elEzFL9DBzyxMaUOnJqYYV+N7iDKTPTA=
			</data>
		</dict>
		<key>Frameworks/FirebaseMessaging.framework/FirebaseMessaging_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			uYpAJZajn1I44CNWxuvDNsOP2+R+8Wa4D1YVAYYqsXU=
			</data>
		</dict>
		<key>Frameworks/FirebaseMessaging.framework/FirebaseMessaging_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			mHYgljcqwn9eiIxzctUS8NP1zzKDxkAGBw9M3hpyoHE=
			</data>
		</dict>
		<key>Frameworks/FirebaseMessaging.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			m1tN4fV4Vjf5W4ouHTpq7U1VXhl63YiHs+OlGlvyCjQ=
			</data>
		</dict>
		<key>Frameworks/FirebaseMessaging.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			U4x6uCvr44JOHdSki9w9/U2ZDBz83S60hqhjHjsOFS0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<dict>
			<key>hash2</key>
			<data>
			oR2obqGs2BxvwW9nxAsbWlEtW0SjtTviUgOqnKVO++8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			auaf7wPxiASCYD2ACy1dfbMJvmONwFvSz1BWYAQrrSw=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o0iigVsmgwmtZfSv3X7hReDNYP5rXblslDnqq2s6UQc=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0h9+vK5K+r8moTsiGBfs6+TM9Qog089afHAy3gbcwDU=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash2</key>
			<data>
			U8q/0Ibt9q4O2HMsCdUwITtJdTx8Ljhlx+0aY83fH6s=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RAOC6nDhZdghbAzsIZgVeq6qPt+MUNTfm/vkUnhmZO4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SqzvIxqBXEJ3U9LJ32hCEXsrH2P16gumQ+gQx6Pdlf4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nmZjZpvFCXrygf4U9aPkNi8VcI7cL5AtA+CY5uUWIL0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4SLFSghL/5EFJPyLg7PNi9J/xpkVVfzro0VQiQHtrY=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4hl+kRU4PNNKdAHvYrliObXzSjRzow9Z18oOMRZIa0o=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HqbvCHqKWTzs5GjLAwupqEIYVi9yf5CrMdMe31EOwUA=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+PMn+5SDj2Vd6RU8CQIt/JYl3T+8Dhp7HImqAzocoNk=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JcpN4a9sv6xynlD3Ri611N5y+HoupUWp2hyrIXB/I8Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			bZglrALfpMnuQhwoXBVDJeyOMZMw5ECfjy4LIv1NWTE=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			X01Q2vfeAEvuHnZ4lX2sz4bxnqmm1OdMSwjJ56E23D8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			n5XX54YqS1a2btkmvW1iLSplRagn0ZhHJ4tDjVcdQhI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			kw/ZHaVuWZcWhSuySsVZ4ivoMUp8hcLXipsNNhegpaU=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			wSU3Ai74GJkae/7UGnbY1q6WL/vA5lEax2Kl0IRef3w=
			</data>
		</dict>
		<key>Frameworks/GoogleDataTransport.framework/GoogleDataTransport</key>
		<dict>
			<key>hash2</key>
			<data>
			9zf5yAPWPlQlxpvMfMXpApzIO7Bv+6Tqgjmec5bsjZ4=
			</data>
		</dict>
		<key>Frameworks/GoogleDataTransport.framework/GoogleDataTransport_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			1X8Eqq3/D0fuiaRd709CHn0pN8S/hPAkle0kv3UqSjc=
			</data>
		</dict>
		<key>Frameworks/GoogleDataTransport.framework/GoogleDataTransport_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			z7s8T3ambVNpi66R9xEMAPIUjm5vE619MlkpCbwBDlE=
			</data>
		</dict>
		<key>Frameworks/GoogleDataTransport.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			dxuDhkRZFccv09G4VIuuVA6ahWIdC6jATPTqpt9OnTE=
			</data>
		</dict>
		<key>Frameworks/GoogleDataTransport.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			zg3yXaGmbHlo1HIBmP92rlB120/FSHQ+9bFkTb6+JtA=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities</key>
		<dict>
			<key>hash2</key>
			<data>
			+ywdO3+k37lGL8E77zJXe0283CTj7M8Tf1383DR90eg=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			tE9xuhtkuoCGifRz+hLgE220oHHKhzeoQmyYjj8N8wg=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			jxZCM8XSFfsGzrh4STQCHd3X05qJ15Xla6XIMqsPs/c=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			zyN+1cWHS162rj6EuUUBzjugML678kgXUzlcblouQPU=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			pQC8JAvwbLWp6eTqXmtMppSHb95fMTUlFrNSRH6jgdA=
			</data>
		</dict>
		<key>Frameworks/MTBBarcodeScanner.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			YzXRD9rqJVSXdDcE/FSBz0kZrcNOM+7QaRGBBMC0/Ps=
			</data>
		</dict>
		<key>Frameworks/MTBBarcodeScanner.framework/MTBBarcodeScanner</key>
		<dict>
			<key>hash2</key>
			<data>
			uLZUP9bORf4H5a5RhPwbdfLDRqiPg5Gdo3EEcTp/mDo=
			</data>
		</dict>
		<key>Frameworks/MTBBarcodeScanner.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			EmgCwC1FR9IvJPgJYma0yKrDy/+odqwYKXIV6E+Vw60=
			</data>
		</dict>
		<key>Frameworks/OrderedSet.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			9yBWqXoy3dOvZ4sEndUKekw8mdJdN9PdvRW6uh+zAvY=
			</data>
		</dict>
		<key>Frameworks/OrderedSet.framework/OrderedSet</key>
		<dict>
			<key>hash2</key>
			<data>
			r2jBovkBPqydWHaA3gfREZ7tUQEYFJ8V5qrOqs1Twc4=
			</data>
		</dict>
		<key>Frameworks/OrderedSet.framework/OrderedSet_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			0Gxu+ubl1hpLEXpfpERiKmkhhnGVBCsfNKFwYjDBaWc=
			</data>
		</dict>
		<key>Frameworks/OrderedSet.framework/OrderedSet_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			WpuPwM3bECAbtHzCgEs/AExyUUdmItJb/E61TtRuEIQ=
			</data>
		</dict>
		<key>Frameworks/OrderedSet.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			TbpnEApMZSdwcoXw+kKl7xyXRf5Y9UD/T8G5KeiIsmo=
			</data>
		</dict>
		<key>Frameworks/SignalRSwift.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			IBp5Gqnt2JzRvKNWhN8QXtPFJlWczoh3Guu3qRMofQ8=
			</data>
		</dict>
		<key>Frameworks/SignalRSwift.framework/SignalRSwift</key>
		<dict>
			<key>hash2</key>
			<data>
			1EGIdz5blJhGfgnSyOwz8Jiyi2bloLHeiS0xWeIaxkU=
			</data>
		</dict>
		<key>Frameworks/SignalRSwift.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			zT2GF6ODmfkGtbIej9c1twMSuEU1rwM5l8o+OxvVLEY=
			</data>
		</dict>
		<key>Frameworks/Starscream.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			5czBrhbqKN0YJU47uAtsQXbx+anIGC8sayBZnGRryZo=
			</data>
		</dict>
		<key>Frameworks/Starscream.framework/Starscream</key>
		<dict>
			<key>hash2</key>
			<data>
			LQJCpuxf03nPe25gQQVY+/Ht+73mvNCSCxi6RGMGP34=
			</data>
		</dict>
		<key>Frameworks/Starscream.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			55SNbf346g2a01IaY+99JfpBGKQmuDbtoZun9bVc5Zc=
			</data>
		</dict>
		<key>Frameworks/SwiftSignalRClient.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			nrpRGvGurqOrcLFRs6oiEUq/ZMlYL6SDqlcmOiiGNfc=
			</data>
		</dict>
		<key>Frameworks/SwiftSignalRClient.framework/SwiftSignalRClient</key>
		<dict>
			<key>hash2</key>
			<data>
			HClPoonTJOEEOOv6Y+4pjQIioJwgAiuOkj86ftbMc4I=
			</data>
		</dict>
		<key>Frameworks/SwiftSignalRClient.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			1SYcrza+3tjZkx6OYz/akUsv+89kKJMH0FMo7Ed/iPg=
			</data>
		</dict>
		<key>Frameworks/audio_session.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			JlJi6ikajqKC3cDIx+htLU9AxZ1wFeyc6o4F/rYn4TM=
			</data>
		</dict>
		<key>Frameworks/audio_session.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ND4Fhy4R6s3QcpSaGTur2yKe7VgGblzIscdlVaKjmI8=
			</data>
		</dict>
		<key>Frameworks/audio_session.framework/audio_session</key>
		<dict>
			<key>hash2</key>
			<data>
			wQtjY3f9Sp9eVhjNRt4PV652F6HUnOoRxG/hTKC88zs=
			</data>
		</dict>
		<key>Frameworks/device_info_plus.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			duoucyXAib/SryeMBMxVFX5DKNH0x9j0zdT+xP21v10=
			</data>
		</dict>
		<key>Frameworks/device_info_plus.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			yZgvj0TtVeC0W+ukLXyYBuci/FgWrbovrkRQUHn/4Ec=
			</data>
		</dict>
		<key>Frameworks/device_info_plus.framework/device_info_plus</key>
		<dict>
			<key>hash2</key>
			<data>
			Bguh8X05sR5ukTwy4S0FVqXSi643iXxtIEA5984exqs=
			</data>
		</dict>
		<key>Frameworks/device_info_plus.framework/device_info_plus_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			itvLwQEXJKNxDYydjBSfHGoLrpm3oh3lpCc19CgToYs=
			</data>
		</dict>
		<key>Frameworks/device_info_plus.framework/device_info_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/fl_location.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			S/1BDtzVQQskOGCZwAf3nxw5POh0fII5O5VhCsjjvi4=
			</data>
		</dict>
		<key>Frameworks/fl_location.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			eAR+CCOek+cZ6/5GZjB7yCRyMw2KbP0QW9Aj66gGFtc=
			</data>
		</dict>
		<key>Frameworks/fl_location.framework/fl_location</key>
		<dict>
			<key>hash2</key>
			<data>
			bRqwew3nYOI4nyYXTBN6TYzAYOapT+q1My3WlpOrVCo=
			</data>
		</dict>
		<key>Frameworks/flutter_contacts.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			APDl4VVU6VQ07iKD0QuLe0fWTCgOzzHmpZM0Sp5mD1E=
			</data>
		</dict>
		<key>Frameworks/flutter_contacts.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			m24YASMsuiQStJNeHOvA1WfEr3UbSqbXSMJxY+BdeGw=
			</data>
		</dict>
		<key>Frameworks/flutter_contacts.framework/flutter_contacts</key>
		<dict>
			<key>hash2</key>
			<data>
			IHLeyg4v9n5+xoYdCDYcuqTeVumv64xKK4LPUkmyKcg=
			</data>
		</dict>
		<key>Frameworks/flutter_inappwebview_ios.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			MDoDqtDmweWHgpWbJ1Hn4C62u1TzmnidDO520mZMzVo=
			</data>
		</dict>
		<key>Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			KHCcIMSR/wfVkGHOiPdC9Uu240k7G+mdZF80IbA2mvI=
			</data>
		</dict>
		<key>Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/Myh-pL-l6f-view-1n8-5e-oxa.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			06d2t4A877H8Rt3w2KC6/tsFOJX8+EtEBLJ4gTDm0UQ=
			</data>
		</dict>
		<key>Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/navController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			/B70M+OFgq0ILOg7kND9sehGk5O7l/BT0QVwZJRtF7c=
			</data>
		</dict>
		<key>Frameworks/flutter_inappwebview_ios.framework/WebView.storyboardc/viewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			dXwSO+m/gyuEYdL+H9vjxjVQ1ri5O8dWQ1QFEir6TeA=
			</data>
		</dict>
		<key>Frameworks/flutter_inappwebview_ios.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			dHnUshIdDlD3OtlSG6IWA+ehbQyNdbTUq9MqyK8Facw=
			</data>
		</dict>
		<key>Frameworks/flutter_inappwebview_ios.framework/flutter_inappwebview_ios</key>
		<dict>
			<key>hash2</key>
			<data>
			zmAcKte4Cc0+eq9Y9UEUj/Z3tsuefv50O6BRmRY3I5k=
			</data>
		</dict>
		<key>Frameworks/flutter_inappwebview_ios.framework/flutter_inappwebview_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ab/XQY+LWs1Zj7C6z5qVxLmrslUjPJEc8hiA03SWTSU=
			</data>
		</dict>
		<key>Frameworks/flutter_inappwebview_ios.framework/flutter_inappwebview_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
		<key>Frameworks/flutter_keyboard_visibility.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			yQWTYGdUe6L4qzb6Ifaj7EJn0bg3EdSrTUHyzbyY9FQ=
			</data>
		</dict>
		<key>Frameworks/flutter_keyboard_visibility.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			+JoFM3QKgL2czNBn5zgz8DO5HETMySETDSXA7HpId8c=
			</data>
		</dict>
		<key>Frameworks/flutter_keyboard_visibility.framework/flutter_keyboard_visibility</key>
		<dict>
			<key>hash2</key>
			<data>
			ypmsr/PGvYCNsr7L5lqxG3uXT2twkemK1aCCE+f1Hqg=
			</data>
		</dict>
		<key>Frameworks/geocoding_ios.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			2KJOwD7mnN0UTC2SDPP5jMTxp8eJhTUAAG0R+AkAlSA=
			</data>
		</dict>
		<key>Frameworks/geocoding_ios.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			6z1wR9Kk7EyBPfWgedg4ulMB3LJeuQ6YancI5/8PyLU=
			</data>
		</dict>
		<key>Frameworks/geocoding_ios.framework/geocoding_ios</key>
		<dict>
			<key>hash2</key>
			<data>
			y8/xSNfPm9yPXUTqcduBJIcL5swLP9wTtcLsetrwWiM=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			1YbcjWdeY0MVkCve4LlGcENwTeMI3Wb4pAsMsk3mc3w=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			9N0t8hFhQgROsCe4etlETlQcotBVEon/yx/FXPx8KNs=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple</key>
		<dict>
			<key>hash2</key>
			<data>
			SgqvtZh5EtHD8v11f7ROrajFm7m5jmYm3qscQuWB0tI=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			uR/FckhqcDVxIJnzxNpwh+qvOcz4w4UhU3NzA630Sfw=
			</data>
		</dict>
		<key>Frameworks/geolocator_apple.framework/geolocator_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			p+c+xOFN/pYr5bFknUH/y01YGEb8+JJchzbtJ60mdTI=
			</data>
		</dict>
		<key>Frameworks/image_gallery_saver.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			neOkUoigtb5rmyPkMPbiCjyirNbDaxZMIU+ni2TpmYQ=
			</data>
		</dict>
		<key>Frameworks/image_gallery_saver.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			jbEdgc9sKTe1Dfh38TgSoQbbMsK9hDh8MGg2RSf/7r0=
			</data>
		</dict>
		<key>Frameworks/image_gallery_saver.framework/image_gallery_saver</key>
		<dict>
			<key>hash2</key>
			<data>
			DUSjHRfk2/RKHzbpGeOEmeolFFBLV7LUrrpfqNzTpYE=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			qTB1MhnXXjOwqxrJvH/OSIkP6hLjYId/5REnC0rLU44=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			DfGr4XHgrPCtNAWsnmUDsEjey25JsEVMCapyiUpNtY8=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios</key>
		<dict>
			<key>hash2</key>
			<data>
			K4JxHZjNPfMwIrICQCEpupCerlc7hetbwgOAIn33T6s=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			xSq53Z2BH4FG4n8cgkFI0ot6nQEXykuke/0UKVQTiL8=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/integration_test.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			2rpqs8ntMifzJQd3vuVNuRYqRDNvRJIJPVSaM6Ceq74=
			</data>
		</dict>
		<key>Frameworks/integration_test.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			SltQZUCpQErfjKVN1OM0/LQFXGM4C2nqjVZ3Z2ReGEQ=
			</data>
		</dict>
		<key>Frameworks/integration_test.framework/integration_test</key>
		<dict>
			<key>hash2</key>
			<data>
			joxJJnMwon7bS5LcAeNzjiT5KwUYmKdkNLSBk/amK1s=
			</data>
		</dict>
		<key>Frameworks/just_audio.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SYGCFvH6R7RTerVqw1B/RNiqkuszGv7K0JVqjZnyNe0=
			</data>
		</dict>
		<key>Frameworks/just_audio.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			KpnR92L/Y/50oCdY559ofAcLVPbg2R78OaZFEpwCMTc=
			</data>
		</dict>
		<key>Frameworks/just_audio.framework/just_audio</key>
		<dict>
			<key>hash2</key>
			<data>
			guyGRNx9XlHUfzdRHyOJxyhf07oLfNRiZzsUItcBpUU=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			tMxWuOG2YGadt+F18KkGdX7IIY+nGYUcECaDM1Cr2Dw=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			T5ohm/WJsAUqjdJQLJKcMkCy5rAmqeqJaNOJZvjksVo=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/nanopb</key>
		<dict>
			<key>hash2</key>
			<data>
			5MxBa7yQZBAyxSsZ4O/516L9g7O+Wj39uECQ+D+FteU=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/nanopb_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			z6JckSOf28Ak6ZaHiWPOwMNsF3N3z6FYCxu6UXWF2JY=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/nanopb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			cpujy9D0WMeM1h7fFzUO2v4ONMqG4xTsZMjLIszSG1Q=
			</data>
		</dict>
		<key>Frameworks/open_filex.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			IQAgwM5hvbiu1eoAx/76CTtYtz0NjaEkmiqRN078ylY=
			</data>
		</dict>
		<key>Frameworks/open_filex.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			9Cdw1ZafNOizHrDlr/67f8Kbd+aGPCxIqMq6QmY4HZ8=
			</data>
		</dict>
		<key>Frameworks/open_filex.framework/open_filex</key>
		<dict>
			<key>hash2</key>
			<data>
			ZowKy90/4F3LEuQwWnaRvGRoFeeBuAoj6/etHCheW64=
			</data>
		</dict>
		<key>Frameworks/package_info_plus.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			WS8JBZFvgr+OPUPSGuDuPdbcRNkXHNABIM9tvC5s5Gw=
			</data>
		</dict>
		<key>Frameworks/package_info_plus.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			WfwzPWLMGmcJpeCVrtiOPVWJ0xX67G9bjEE4zpEqCJU=
			</data>
		</dict>
		<key>Frameworks/package_info_plus.framework/package_info_plus</key>
		<dict>
			<key>hash2</key>
			<data>
			ArHOS3MI6TDxADGxZZJtyim5m3U9vhHdw3oegtK/1Yc=
			</data>
		</dict>
		<key>Frameworks/package_info_plus.framework/package_info_plus_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			nMCniRS0jjhBI6CfeAgB94gokTSQwXwz6PtCiMjoZlo=
			</data>
		</dict>
		<key>Frameworks/package_info_plus.framework/package_info_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			XMj6M1zlqJYUZEyyYdyv5cCX9I8RLZYbtaOXDNrouS8=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			XVALoObHcaKk53eStbKai/TUrdcCIQROTzZcXeMMXMM=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			SY8vkCNP/aext46cMj0S+sLWGHF3rhDGoDmeBW/tKq4=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			C0pxSzukpifKOSB8BGvx9uftbKBwq0nSyEkoyRblrvY=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/qr_code_scanner.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			/28DY5hBkKGPmCPu8MRmrLaYHBihJJCeqK2dLed6kx0=
			</data>
		</dict>
		<key>Frameworks/qr_code_scanner.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			lAzkNob7knDOKNWBXT6jOrlx11gI29oEr1lZ7Z3tQ/8=
			</data>
		</dict>
		<key>Frameworks/qr_code_scanner.framework/qr_code_scanner</key>
		<dict>
			<key>hash2</key>
			<data>
			YEAZ2+21PKFIt8L3R+Wrwkk5Q+T2Ehq4V7pE93gfVHs=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			TJRrTo68VWyfAuc8dIj0kDQpeUmIz255ZNA1GmKr8K8=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			37kaVtfP5P5O6ZL90nXqWSu2gI2eW/fsKbVjAvrr9Bk=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			liChhUi9bqlTIVFoCmRqxk6TI45GGWYrjeuAoA0IMgc=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SHonB4ewWm9j5vo/HNjCa+IcOQahxZaWxKSrcS+OxCQ=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BWQouTi9VwGKYAGdFcPB7i+nJ/I2h1mLu9k0hIsYCxo=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			V4e2XZt2zpW2Aas4hCDhFXwYobed5rUZD3bcQMP5fFc=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			dUb6ElH7AMkVmU+/ZIZ7x5y5RrYkzvy7u6LEhWoyRqA=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin</key>
		<dict>
			<key>hash2</key>
			<data>
			4selmNfcp1A/JOReyvdE0NDfutwP/qgAPZ4+/2XrIcI=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			FRGOEXulF4Uv4rKUhtn2JvjcrUjzJogP2pxVVU4vpo0=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
		<key>Frameworks/syncfusion_flutter_pdfviewer.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			rco+UnN6tX4mLrzm9aD4IQCm0XB0vnr5QcvGQtUjJng=
			</data>
		</dict>
		<key>Frameworks/syncfusion_flutter_pdfviewer.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			dPNfIwyrm9v9xqR++qUKYRXHQgxncXaq5MIm4wH7+pg=
			</data>
		</dict>
		<key>Frameworks/syncfusion_flutter_pdfviewer.framework/syncfusion_flutter_pdfviewer</key>
		<dict>
			<key>hash2</key>
			<data>
			82psM5UPRWnSstDyhyVPHWJG5WRiLdADgYG812UB9jo=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ZN8YYNTsYeb1QS7Di+X3k+8qO4ufwaOEU6hvP6Y2JHw=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			k/B217Lh+AHu6dE8Trlb2M6yRcaRZSeYRGjwNt7Zw+I=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios</key>
		<dict>
			<key>hash2</key>
			<data>
			esQf5id/RQ/2wlG7euhgLXLBF1SqOdXjHx7qZClPbfY=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			6cRKJne4EinqvtlFkBOaVAvoid3tFJyBVt+PKMDj8+g=
			</data>
		</dict>
		<key>Frameworks/url_launcher_ios.framework/url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/video_player_avfoundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			IdrBIIm2jZEZgor0Q66RQwUza99A0IUnQ99dqyrWi48=
			</data>
		</dict>
		<key>Frameworks/video_player_avfoundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			r2dHk0u9y5Tvb58OA/eZgC3g7ELWPhWT5UPq1r0k0I4=
			</data>
		</dict>
		<key>Frameworks/video_player_avfoundation.framework/video_player_avfoundation</key>
		<dict>
			<key>hash2</key>
			<data>
			BcBJtBn0Dv+qnVDBvZJBAZz33AAg3bkRNdJuAqNqUng=
			</data>
		</dict>
		<key>Frameworks/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			KTHRz5gHmMhoYeKhEmCTMM7cgYsov9FEbNWVcZPQOEY=
			</data>
		</dict>
		<key>Frameworks/video_player_avfoundation.framework/video_player_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/wakelock_plus.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			n8Un9XYoHOtIzOkUsEVOIDXcuoq8UcVyL/DlKq1tO2Y=
			</data>
		</dict>
		<key>Frameworks/wakelock_plus.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			n6UyWSAaa/F8+eAzbeGtEVtKysoiOGgDqM+cwEPdQ6c=
			</data>
		</dict>
		<key>Frameworks/wakelock_plus.framework/wakelock_plus</key>
		<dict>
			<key>hash2</key>
			<data>
			ii4USJu+l3VyekQQtW+HLofEJA34onqkgOQQbtO3ecc=
			</data>
		</dict>
		<key>Frameworks/wakelock_plus.framework/wakelock_plus_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			3RrTTjEGV6scK16tN7FhPYOLg1t9NBnezc8qd4Hc0VA=
			</data>
		</dict>
		<key>Frameworks/wakelock_plus.framework/wakelock_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/webview_flutter_wkwebview.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			V4tbXWs7MU5S3luF2tBY5j2m/Hd6WCEibBTLdC2P9mk=
			</data>
		</dict>
		<key>Frameworks/webview_flutter_wkwebview.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			o+CG3JLJS5SSbVx02IVYRUVtYQ92KX4FgM3xUlsGGPE=
			</data>
		</dict>
		<key>Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview</key>
		<dict>
			<key>hash2</key>
			<data>
			mUAn7dwK251niZYJzgqyk9M3gZ0FEkxNE5B8Nmxbx/o=
			</data>
		</dict>
		<key>Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			lDCXJOWlvhgO1jbBmzWsh7vAaV6o14zVCyaAZuqSxcY=
			</data>
		</dict>
		<key>Frameworks/webview_flutter_wkwebview.framework/webview_flutter_wkwebview_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			u/ze0fYPdR/4VYu0J+vM7e/U0Nlitf1uLLSrfYM0sVA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/Storage.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			Lx1844fBORK+6WxlCd5hy8b8Hx78DeEXncpstshaFcg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileProto.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			qUKKq8xAyT9yMLZPuZvJMLiK4Fm1awhmv3Ur6mn+dsw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileVersionID.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			4lEEfqa7KO9CdF4ONbnJpgJuHioXgKm80JgDPubEDO4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SdCHFcwUWcOn+6S2pVcKPWo5J1cOtjjALFOZ8D4dur0=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			cYagK+GGV27etFOXEItpW7uNaKAl2gQd59kxZrHuamU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/DroidSansMerged-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			oRgIxXbtc2TA+CQC4IYwuDWJiwC79rQTLdnyhCHh0Pg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-1x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6I9AZoWd/JQ8PV9b4FcAqf5GJT6iUEe7YeuyNGEkwfc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-2x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			35EHfeztT/7K9NKjZJGCUlauQ2HOF3PBQxkLgM2OGUw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-3x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			yGhIyrKtKLoQ2oJfnH4RrIxHQUQYODC5SL+bLjiNbxo=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-1x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jRfU5I+eBrG4NBhLV9IBsXXb0DJ9cXoHbCE4CN0QiUc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-2x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			S3sLYbDleAUGAjWIVe8mvnmB6ONkTxlmkd9BPlylOWs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-3x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0SyDaAgdNWt5XirK8fh9r/KPbbBqlWeaa1+p4aMYRvw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShaders.metallib</key>
		<dict>
			<key>hash2</key>
			<data>
			OHvYEVcftEvIaZzqtCyV0GTSm3rfeL6Nh7/2yNafnTQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShadersSim.metallib</key>
		<dict>
			<key>hash2</key>
			<data>
			xyuB0Ol44wtr43GIWv25F95LCrSMvCKPLdXm/iLUK/g=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-1x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			reMA3HPo0ZkLCelsd0yLOeO/Cq8A543Gc5rDKt348Rg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-2x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			GaMGqwoZ17o4/oQ+E5o3mcf4nZdU+fY2c6zouc+aHQw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-3x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			sg1gtlyHg36nwhRj66w/5buuQmGAtaveap9wGP1kQ98=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			LaWmbBXL6EOELT1JTPDqP5a3Jcj/zaZ/M4f7X/IJcJU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Tharlon-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			4HMV9v4N9IH0373fJ215YuoHOKrnkWwgbsN63C7LlNc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ar.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			DiOX80dqLJHNOFs8pK7D0mrw5UC1SlY+YVruxgh51u4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/az.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			5COu6rVzdMf7p7ab/pKliPYpZWshPzAQdPaeT+pDHyA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_background.png</key>
		<dict>
			<key>hash2</key>
			<data>
			VUfuf1zHkFbUsHfs0W2XgKZU1+OccZ1EFTBKqUPRvpA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			L3cYb4rSz5yv0DjIESC1J3/cb8P0Js0Ff9mVn459XmA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			UJlmp7f16dQ1TqC/a0uyZ8RGy7o/exUVKOFU8OsbbFU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass.png</key>
		<dict>
			<key>hash2</key>
			<data>
			fFQ2JJGO00f80s6uU4uleAHJ+R9BTG1nUEYfsfZY9Fo=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			KsU6yUyahIrTFyLyw00wG+zkDkxGGocVZMhX46e19Gs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass_night.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyxSDpdORHSyv61cmUzZqM8ndta0mAzjXNz1I0LMonI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			CriYgwzkN/I/cVy1eJf4HI6kfJaC2PT7qk3sqSPG60g=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_my_location.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qHyHEkW8JdVyAqojiLMeEvdbdu29vAMj/80KkzOxCoI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Lit0EakIiwZVnj6fbN0D3wgszTkSE9/rnfzmpGBwUps=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ca.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			M+yc0vsi2SqZThuot28Ol92alz0wj+256BMkt2i5mKs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/cs.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			oUajzNZqfSA69lkv+CsikGfg64oxk1xqfdZ+kLtraJk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/da.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			0akfBfrtBwmJYhF6g/WlRnYJE+CfUj8SuRDor4qsfwg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/dav_one_way_16_256.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7/vwjVbNju+XCu1xevYaVQ4/JUsqO/RCsRDlPNq7kaY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/de.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			DnRVfvU+UHFrNaIHAkx2603D8XNia2JPQ65WEYrT19I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/el.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1OWW2r+TcI04GAzLLa7dKGBUCWYyguNu5uwT0YYLKOA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fPpXDX73lW/RnZTi5eGVKOyMVlJ7aNEDXRbk9JbLlJ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_AU.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3dInCJ6Vxw6N9uReExxeVg5ZETji151akMx7S7eVpzI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_GB.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3dInCJ6Vxw6N9uReExxeVg5ZETji151akMx7S7eVpzI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_IN.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3dInCJ6Vxw6N9uReExxeVg5ZETji151akMx7S7eVpzI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			bNEqmxtX/c6TwMSQgglT0qxaaubbOsKvp6wRJZ+bwXM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_419.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CsEz4d4OQ3tyiePZw5djebxIUHOwxb3ylz7i+9+Bme4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_MX.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CsEz4d4OQ3tyiePZw5djebxIUHOwxb3ylz7i+9+Bme4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1VGyN5Sd2ufWo8WeIMlDBRKy04yW8ilMgDuFeomMRT8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Yf5Ylm4f57mHp+5F5hrpG++KQjhdLhw+uEI0m5pigbg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr_CA.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HP9Xs4hGscjnhKPQ5HkIYVYpoQNjnDz1Kg6thffHWNE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/he.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			R0mdqjWgHHVgSKu4xgu0z4U76Tiev9tHPyjO1Kf5WM0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			b53o9kQV4v0Fi7QrM96aCO0VC2+64dmsuGOxJBcW8Qw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			YOhL4Omy9LbQ178xmODPhZuGaE6d5QfkdYlhoiKN1c0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hu.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			51q+9WQFoOZ7mTbzQ5SbbBPVwUKi9upgAvK+mamOroI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hy.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			usfYnEtkJd+Wm0ov86GfzWOHNlFbjbTpUJwI11DYBLI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			XB10YfoFMMA/1hJ7RcpPwRn4FPISn4/GfcLt1yacpbk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			6Zoc/OxM+MGLTiTWiXfGiG/vQbaZu+4BpYdjwEWmO+k=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			32rNSQbMm/k11a9saRft5TUhSAEwkY3nI3izgMIVsWc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_night_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			tA6fq0SW033/kwgkIJ6B6sQ3DSvEwrztHLfbLzPMZJ8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			7w1goskgoHENDngEcZmMetZnbBY202XkAvwpkFUUGmc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			9NGwfkNI8H2ANbn1RB4cLWXlA/Nj4yeB5459a6Vr9Sc=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle.png</key>
		<dict>
			<key>hash2</key>
			<data>
			V2HcZbiTq9tAJFbLYxUnzbb0utxxO6kEVKiZsw0cr9c=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			l7DXZC/yFWAiJnn1y7b8MOf2wCeJgHDmWYdKB5lMTS8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			1TKLueEVV6y48hWYWBESJz23RavEBopB2CMUaHlutSg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			HcFbnY64QuHwGzcNE6SaFXzwaKhtLHJf4uQ9wDMEgGA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			GaTms7wugZAVzLWzjmz62i/7XEjgP6usP/nU20AYYP4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_large.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0TDx02zeOyLukli+xhvRlGoyfDPfmHWEXjHT3uzpDk8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			H8OXFdacv5KzG+qLR5RrChts7GKxClFlKqHyXNg/6qA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			z9EXEDKpCzvyZ7kGENSEXZznpcbyINaD1V8HSi9sgSs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_border_waypoint_alert_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			yBxDOtAyeWqzpC63ugLpJhxYcAscliU1BgZnNdEcxmI=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			0RtBxUvJfAOQCu0YN4P+NXKIgVGXshxUEVhfsd5RBek=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			ccFj0dMmtprxMVMeVoKpD1/Kb4WJxACoK30HziyMr+E=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			kE33BkYF1sZUQlzcuqab1WvHw2bKowuYmlzrbJpShF8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			1VhiJ1I7+yEdNt0CQMVPQt3sqbXWCS4N+l+OU4f+Wik=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			hl7Q3DhOytiS5PxACeNjtBEwRxXf7l8c3w6kbzMZtLY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_night_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jDxAgphhHwy2VvYUHX82RqRNnQDGP80tsgbIm4TM/Ik=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			OflLl1p4nwACrp977dRsNpKM9fZyFj0Yckli+0LJ7kE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			OiZcsS9CGROol2GoyCRvbD/1OnnO5Cr9tMmymx+gJuE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			OW3vSyC7N+/XTypt4QcbljeyrObk1gU5n9aHHmZk5oA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			4JsF2Fuy38x5T4fSQJsUzzGbcTca7TicSUHZ04JfcyE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			PfQ+g1OVrp2reKoxUY29V6sSev0XbPSVORza8Pa4s+4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_night_32pt.png</key>
		<dict>
			<key>hash2</key>
			<data>
			HgTUWKNNKDODh4K+sGwW4Ydlmqb22QS0xe1R27RpXcY=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			9i3bLmY8j1wPb8oYLkoXlfbtaxJSCwouObY+wUi4aUA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			RyZlVNbjaAqlDgKugE8FWdRwJhKx7ew7iQzSCsccclk=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_location_off.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JtnHExDZopxjHDdkxKvEUTYb16WTqGyOKzA5Al5e17k=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			yRv8itmfa+f7QkgZ03TsPrjc+YcMHHZz9Oxeuc5C+I4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Kswz6NBndW1QRhKZxc87C8BWIjBoKm5mPxHp6tqgvug=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_qu_direction_mylocation.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JtJ9rKoWknZ/eIujTeaSz/i9MKoY20wUUxxgY64wMz4=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			6PWtTGpNSDY/JmLM2KVXntVhoQhJKZUP/moGRbY3NMU=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			ILRnt9YuI4SFIzANE21lIdVzBhRC5hfsfl+AshH7LAg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/id.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WWuYhcGRPSEkS3jEe2ROs3IrNb25OFbcRQn2LuePONg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/it.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			rkCfvrRgS8xYZnlpwfzv1sXNTJokABiWUBd0xirTHLE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ja.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KZNFQNC20A2RPP90HaN7azncoRp8QY1jPsH4epCAjdE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ka.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			0AZlUe3I8Y9apquafluTQQuMl9rZQaABLCKi+iTsni4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ko.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			p0cQEikCmDDs8hdnPxIq24zJhIi+27Cd81errvm+3FI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lt.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			JXukFf88Wdzwa5tcV9HraZsGfM/F/70ZM1kltn6ihUU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lv.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			UGS+ER4ARYA7fjUPNlaOVKTsV0sTtmPSqnimetvKkWM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ms.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			R4DZjtyNufPl2/2KaI7QurSbK02iITj1Rt/8UiEXGqM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/my.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			vqFe1I+ykIJ6doyGy3Yuw0wrLvQoV3vNbO4YRXktYEA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nb.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			lhCLxiYfmP3IyTSA4DA/srdffj8SZNGQlu3iq5miA2g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nl.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xOM7UCAMDBnqYRGp7f+gdgw/C8gRFChnyoIXN6JLczc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pl.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CGCb9/AxCbiU3Cl38M8SKof+EVcE3ocGs0gw5tKewUo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+dg+/X/HQz7XU5A0YjxVVb409jFOvCba/h/Dk0xwgJg=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture_dim.png</key>
		<dict>
			<key>hash2</key>
			<data>
			utLkKWB1FOz3jZ0C25RC8IQUZKhnBiX4Zhqy+V9G1sM=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Ow721PuesGjAAoA8TA/11xralYhO3PlQYUK5+xrByfA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_BR.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Ow721PuesGjAAoA8TA/11xralYhO3PlQYUK5+xrByfA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_PT.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			k71LApZTJgdqcCCsIkJVYp3/UaN0UugujINtnjw1bAw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ro.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RmFE28UikTR3rPOs9e3hWiozh14McNOSuHrohzpDwFE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_1-1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			lzM7SlPvx18FzuOhp0AfctCjH872VUYhotrEmV/bjj8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_128-32.png</key>
		<dict>
			<key>hash2</key>
			<data>
			nxJMmc39cm6sjewdRFv7mVHN62HynJzISegXtemahqE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_16-4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			gBiXF2YDIq+dMhZNzmXGIviFDDKW65Wwd5MGxawE07s=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_2-1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			locpte2xHJxxYikj3vpYPAO4Fm0BJbKH3qAMUEHC6AQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_256-64.png</key>
		<dict>
			<key>hash2</key>
			<data>
			xWNHfxSxFwcTcbRx6ksenSsdQax+gxZj5zqKa4P5bWE=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_32-8.png</key>
		<dict>
			<key>hash2</key>
			<data>
			iBDn2L41GYIiiENv/p0efRgu7EM6L1eut92OA3dwsiM=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_4-1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ucj2pK8WExUWdTnnnvWIu9iE9PEOo0rSvhyP+qAhUe0=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_64-16.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7X62FR3qKLKgCi3mkwhoJfSYdhXVzy/pTp6j1wtdE1s=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_8-2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			pvfCkomVwJ0KZwclEtmUR11mSagaN9LrXrn58aC8Afw=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ru.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8F3iIfeZfkt3kc7zw4d4l5s9m2b9vFCn+p7G/68yjac=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sk.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			feymRx/Bi+chAP3Ey5Agr9Ft2cE7nqIhoQRcSalOf18=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sq.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xdPaO4Y0CzvRMTCC1q1H1VfThTHn2MaId+Q5GxGa3LA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xjWVk9Xxf4EPOS7zyMUmjLnnFVlTCeLpiM54ewmBeKQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sv.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1ORDxXYkUdGJcnE9Tv+nQG5zZQINeEtjvjr1fTJITGw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sw.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jTgCu5jFsQzYN22/wifVsX/RArJeiIrn85W+yYfIYW8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/th.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RC/EzqptnmbJr3KK7eF7JYvn5YMqHCwoT6JVZuSHC6g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/tr.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			k6qQBFkz41GpPIpKN8nbu0HCLy7uhiXBqPPKpgXMjNw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uk.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			2aiHx4UseFbXgwNRIMVD2sRio9L4fh13g8peWZiSKy4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uz.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			C0t80wQwgnkrQq5eb1UOQWiu6bG6l1375PCYAm8y6Ok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/vi.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			XDRpaLGcAXpWlyiHwm48D8P53TDZavyuwZxOqf+NyNM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_CN.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			sgjNca8LpMcjMVVQDTUR0Yb/VgUcg6dw+ALrOMRvuO8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_HK.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ieYiGeYETunrujyWGcmRClgFR/nkeEW6hpip167/vjY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_TW.lproj/GMSCore.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			eymXX+mzJGCbSuahiQXvbJtVXL/5XjaV+T5s0lYYFLc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			kMVj+72qX4tWZrrK+HdkNteEJQcI67CuY8B4J3VdxhM=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_left.png</key>
		<dict>
			<key>hash2</key>
			<data>
			esJZo0DQ/iA9fGTeZgnhHBaaXjKiwF0Q+D77afKUdtQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			85j4wRMjUhbJzw2Cj/dcdiSaIcvVJMiwY8pyr9uAvoQ=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			CC5oWJdSl7qbV3Nhcm9VlpgXL0MZNtSwnDkXPFFjOAs=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_right.png</key>
		<dict>
			<key>hash2</key>
			<data>
			af0tM2SZMSGEXPSOUwN7JZ7as/APH644Mlk2l4IWqms=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			2tW3XA24iExRY3gHrbutr+hAVYLNOk3LrSXZmHRjUIA=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			02zlKov8Ubrnz7aJ/JXa+qQ073+mO2gy2CgeYrDNF18=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/ic_error.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ZELicqEYSjWiwLH5Bpm+L59wifWIeso6rvF1uk5JPN8=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			g+br70qT4Wo8OHYO9GEnd5pkjYLVQsyw1W8T8+pd7Xo=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			l/lLCEplaZnTitN6/IXGuLZM5QXaxaSim9fc8XsRb3c=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/GoogleMaps.bundle/oss_licenses_maps.txt.gz</key>
		<dict>
			<key>hash2</key>
			<data>
			9EipuF8wrGWtoayZvMCl2L2s2vseMUYkupC8WgxxM5A=
			</data>
		</dict>
		<key>GoogleMapsResources.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			8hye6V7u3QWuHyedfHgANwabP5iXNsDvQHa5WJe9qlE=
			</data>
		</dict>
		<key>GoogleService-Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			sRgRW0L9AtBvn36qEKOU+AN+fT7OIkxWxigLivh/q8I=
			</data>
		</dict>
		<key>Runner.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Eq4QKvRzQzvhTolwj3FwNHbyoaAioRm7qTgD/ocu2oE=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			8P9tlXBzfv+T+Uh+gbdrbyDZCG85NTJTieJoGBpFi3c=
			</data>
		</dict>
		<key>firebase_messaging_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			sKaCSvT1D+oEro8Aa8taQpe1RFh7qHX6b4wW+koBgi8=
			</data>
		</dict>
		<key>firebase_messaging_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			oK3NMh7+Ic6+Dw+FOAGDNQibtWRRyIKnN6CN9yNPxrs=
			</data>
		</dict>
		<key>google_maps_flutter_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			27dAa8PYvC1cMoCIjPzFx5f7qJ3K1yHyDDyMC3U6m/4=
			</data>
		</dict>
		<key>google_maps_flutter_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			puoWk/6MP69ytJdvlbT3CeVa2vnstAliuu/Zl9LBVPc=
			</data>
		</dict>
		<key>permission_handler_apple_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			gmRXuXzjj55Aujg1EOQEQwJjZjYfn7EM1tDKLbCribE=
			</data>
		</dict>
		<key>permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ETZWiZY6EZHpaiLgs59i8FuG0NJKvoBAXBpc7vCamxs=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
