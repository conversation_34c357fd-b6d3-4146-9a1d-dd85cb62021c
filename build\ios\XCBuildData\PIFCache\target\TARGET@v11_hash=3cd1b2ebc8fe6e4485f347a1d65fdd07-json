{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c9209d2cf9afded48ce7e031d6434637", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6da72224a0002e2c8d4ad6a4140a101", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a91c9bb8ba64aeca165e9176bf75678", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cab3693a69eb1c553c791758183685f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a91c9bb8ba64aeca165e9176bf75678", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Work/FlutterSDK/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f480631d57f584ae82569893436b3d19", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9881ca46a3a7d99a82bc912f0c98549182", "guid": "bfdfe7dc352907fc980b868725387e98faa2d77c0ef7eb87f7f8421e51c1b40a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e3dac75ef5081293a3cce12f493338", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f11f857a4a70874bdd43699059350940", "guid": "bfdfe7dc352907fc980b868725387e989ddcd810c76cbf7aec141f26dad24364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980851fc303d76d38577fc386236ac7a3a", "guid": "bfdfe7dc352907fc980b868725387e983d3aa80a6c5cbf097e325437129a91c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819b699776c0368df035695147a588521", "guid": "bfdfe7dc352907fc980b868725387e98f99ec19d48dbaa08f6553308aa7031f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980591cfc425f84d4616eda497a616a6aa", "guid": "bfdfe7dc352907fc980b868725387e98247033ab0f796f1ffbbaca852872d5d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829ed28056afe436305b4aef5f5818ff3", "guid": "bfdfe7dc352907fc980b868725387e9830bd737ead5f45851717a657179fa678"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c5d3e32aad854929984285dea2f004d", "guid": "bfdfe7dc352907fc980b868725387e9828d1d33ac05cf75f06c018daf397107d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbbcbd2f0e6d0893442bc25f3e7fde6a", "guid": "bfdfe7dc352907fc980b868725387e9817432cce7e2f1827cf404d6f3d9190a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802170036ba1f067869fb3fdebdef5e0a", "guid": "bfdfe7dc352907fc980b868725387e984d6ff360bc0349ea53be30bad692c64f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a22181a2c843675cc7ac2112c3fd9ee", "guid": "bfdfe7dc352907fc980b868725387e985a9155407c6638782577f820e040e103"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db978a7322a8f046585ffdc7b795aeb2", "guid": "bfdfe7dc352907fc980b868725387e98cc7a049f807f094314597d137d97a3d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c708722fdbb1cceea51f75a4f1e61fa5", "guid": "bfdfe7dc352907fc980b868725387e9873ed2a0c4ce9cc656a49a9d37e60ee4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888ac2489715cdaa2b93c81c328ccdc3a", "guid": "bfdfe7dc352907fc980b868725387e987f1b930bfa25dd1bb7f0899fc406033e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f16be1443d3500ea9bf463e6dcd82f65", "guid": "bfdfe7dc352907fc980b868725387e98a6a8146888d34a3af99d3d4e0a020d6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b43e0df79bec9c20c032afae281889d", "guid": "bfdfe7dc352907fc980b868725387e987baadfbc0ece175d0f34ddfbe79602c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c59aa6cef8da6e67dfa37ef45f7bde75", "guid": "bfdfe7dc352907fc980b868725387e984c080abe8282843f4f3c73f9d3b89e1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982753f5aebf7131d2a3bd208f2b77ca08", "guid": "bfdfe7dc352907fc980b868725387e988ffaac1d374b47afbc634e36475d4bf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982efbf67b9ad635f3424c64a3296cfd21", "guid": "bfdfe7dc352907fc980b868725387e98815df83a235eb25771d65af7ce7117dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d2bb4e9b4767fdbf6e3f941a0350755", "guid": "bfdfe7dc352907fc980b868725387e98c4be2836973ae279ec244ba0230d2761"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851bd3e4b19bf3be52614fe7701e2e0e3", "guid": "bfdfe7dc352907fc980b868725387e98f74105977628bfbb25df762696c029c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980442f65bf36f3ff581a3787850dea56c", "guid": "bfdfe7dc352907fc980b868725387e989bf75970df78e6655591659920fa67a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98355c7a401d1ae1c5e1edcff25719bf16", "guid": "bfdfe7dc352907fc980b868725387e98e99a830203b0c41193b054b54864b7a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa5f247394a3e43117490b3ed80192b8", "guid": "bfdfe7dc352907fc980b868725387e9807ca1cd59b7788b851677fa78a3f2ecb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2fdbee5d34f37cd9004fd22adc4fd88", "guid": "bfdfe7dc352907fc980b868725387e981a49fe0f4a410c0a0af5ae4b4e382d9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eda95f2aec627ec18f0324c3899e3cfa", "guid": "bfdfe7dc352907fc980b868725387e9845ca48584721e5cd79c0445f541f849a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee00786dab3e8ad86a32660a76df3f97", "guid": "bfdfe7dc352907fc980b868725387e98824d5b67d6cfffb58b5bdc05b4544ccd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e43947f0a5a03666eec9be2413aeb094", "guid": "bfdfe7dc352907fc980b868725387e984e231bb1424b23e751254b87285b9022"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1a3fc480be5cb0ad673301a3a939dac", "guid": "bfdfe7dc352907fc980b868725387e9814d7aaca5ecf38cec7e37c889d74531e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827320310653cfa4235a611e167f15aa4", "guid": "bfdfe7dc352907fc980b868725387e98d6df19a19ac459f2695b59a8c25054d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb69500b6c76a63b8e871c6e211a8119", "guid": "bfdfe7dc352907fc980b868725387e98218de96c89ea1f5a9a989c9955cafe99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988039e302085feab3a69dc4738992182b", "guid": "bfdfe7dc352907fc980b868725387e986818668b296e5a2bf10f395d49708362"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab9ff805f51e911d35e3b72e02e0cd8a", "guid": "bfdfe7dc352907fc980b868725387e987b23ba6e14791ab6f25bf323575d3b12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98605736344de7ea65309c0abfb233c780", "guid": "bfdfe7dc352907fc980b868725387e9812813a51ce530301549e7b6291e02858"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1e6d742262cac201c160f4cfe1816e5", "guid": "bfdfe7dc352907fc980b868725387e9831d475c83e9918d6276133f441419524"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d7c6354a7174c8948d45007d77cb4b9", "guid": "bfdfe7dc352907fc980b868725387e98756bf29cf3086eab92af862ab0eb863f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98238a52452f3fa723c9ae7810b2cb8b83", "guid": "bfdfe7dc352907fc980b868725387e98ef93c52d121a2a22092944e8a96c25fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870546c4fb7a8848f37be96a7f996a99f", "guid": "bfdfe7dc352907fc980b868725387e980fbafc9b22a1d47c6246fd0f1d7c4891"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831874ce15e42bdad8ff185cd351f6b32", "guid": "bfdfe7dc352907fc980b868725387e9832f3e4176ea5d30d6ad315dc9196195b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0d64796216a231e05b71e61767edb57", "guid": "bfdfe7dc352907fc980b868725387e98ae456f51999333f90513c6a3f1a753d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838460f0e355f9d28fb759e1ce63ac2a6", "guid": "bfdfe7dc352907fc980b868725387e98d259083229b4eed28de4a364a94222ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986358917cb7174c65cd0b1536b612a194", "guid": "bfdfe7dc352907fc980b868725387e9893b3683171b867962d830e7c7ac29163"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d007ded7143c19e5e8a0a1c91a23146", "guid": "bfdfe7dc352907fc980b868725387e98adb5e24d5ff353eac35fa886b1a588d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ffd3f0021ccfa4fdd4e5985a7e6dabc", "guid": "bfdfe7dc352907fc980b868725387e9877e32b168a5f3a44c7b61ca3fc6ff10f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbed16428f70bc52b82c1945f4a4cb26", "guid": "bfdfe7dc352907fc980b868725387e98e3697a24d284afb360831399c38fdb3d"}], "guid": "bfdfe7dc352907fc980b868725387e980055fbf3a11bc15129d28ebbe5cc1480", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e987ead9e15fc09a570554fa963eed1137d"}], "guid": "bfdfe7dc352907fc980b868725387e98da9cd099e7190758cfc42156c0f4e548", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8defae1b39d33d1c1b6a7ec3e8cf51c", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e987fd8694c1d36b88c99f78feb0d04dd25", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}