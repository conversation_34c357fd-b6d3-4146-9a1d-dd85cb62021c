1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.flutter.flutter.petro"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="31"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:3:3-65
15-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:3:20-62
16    <!-- <PERSON><PERSON><PERSON> quyền call API -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- <PERSON><PERSON><PERSON> quyền ví trí hiện tại -->
17-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:4:3-77
17-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:4:20-74
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:5:3-79
18-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:5:20-76
19    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
19-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:6:3-87
19-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:6:20-84
20    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- Cấp quyền cập nhật vị trí ở khi ở trạng thái nền -->
20-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:7:3-83
20-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:7:20-80
21    <uses-permission
21-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:8:3-78
22        android:name="android.permission.READ_EXTERNAL_STORAGE"
22-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:8:20-75
23        android:maxSdkVersion="32" />
23-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-35
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:9:3-79
24-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:9:20-76
25    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" /> <!-- Cấp quyền truy cập url cho Android > 11. -->
25-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:10:3-84
25-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:10:20-81
26    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Cấp quyền thông báo cho android >= 13 -->
26-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:11:3-75
26-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:11:20-72
27    <uses-permission android:name="android.permission.READ_CONTACTS" />
27-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:12:3-70
27-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:12:20-67
28    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
28-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:13:3-71
28-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:13:20-68
29    <!--
30 Required to query activities that can process text, see:
31       https://developer.android.com/training/package-visibility?hl=en and
32       https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
33
34       In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
35    -->
36    <queries>
36-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:59:3-64:13
37        <intent>
37-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:60:5-63:14
38            <action android:name="android.intent.action.PROCESS_TEXT" />
38-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:61:7-67
38-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:61:15-64
39
40            <data android:mimeType="text/plain" />
40-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:62:7-45
40-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:62:13-42
41        </intent>
42        <intent>
42-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-12:18
43            <action android:name="android.support.customtabs.action.CustomTabsService" />
43-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-90
43-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:21-87
44        </intent> <!-- Needs to be explicitly declared on Android R+ -->
45        <package android:name="com.google.android.apps.maps" />
45-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
45-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
46    </queries> <!-- Provide required visibility configuration for API level 30 and above -->
47    <queries>
47-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:59:3-64:13
48
49        <!-- If your app checks for SMS support -->
50        <intent>
51            <action android:name="android.intent.action.VIEW" />
52
53            <data android:scheme="sms" />
53-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:62:7-45
54        </intent>
55        <!-- If your app checks for call support -->
56        <intent>
57            <action android:name="android.intent.action.VIEW" />
58
59            <data android:scheme="tel" />
59-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:62:7-45
60        </intent>
61        <!-- #enddocregion android-queries -->
62        <!--
63         The "https" scheme is only required for integration tests of this package.
64         It shouldn't be needed in most actual apps, or show up in the README!
65        -->
66        <intent>
67            <action android:name="android.intent.action.VIEW" />
68
69            <data android:scheme="https" />
69-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:62:7-45
70        </intent>
71        <!-- If your application checks for inAppBrowserView launch mode support -->
72        <intent>
72-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-12:18
73            <action android:name="android.support.customtabs.action.CustomTabsService" />
73-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-90
73-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:21-87
74        </intent>
75    </queries>
76
77    <uses-permission android:name="android.permission.WAKE_LOCK" />
77-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-68
77-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:10:22-65
78    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
78-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-79
78-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-76
79    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
79-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-76
79-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:13:22-73
80    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
80-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:14:5-75
80-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:14:22-72
81    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
81-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:15:5-75
81-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:15:22-72
82
83    <uses-feature
83-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
84        android:glEsVersion="0x00020000"
84-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
85        android:required="true" /> <!-- Required by older versions of Google Play services to create IID tokens -->
85-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
86    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
86-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:5-82
86-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:22-79
87
88    <permission
88-->[androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
89        android:name="com.flutter.flutter.petro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
89-->[androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
90        android:protectionLevel="signature" />
90-->[androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
91
92    <uses-permission android:name="com.flutter.flutter.petro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
92-->[androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
92-->[androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
93    <uses-permission android:name="android.permission.CAMERA" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
93-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
93-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:22:22-62
94    <uses-feature
94-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
95        android:name="android.hardware.camera"
95-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
96        android:required="false" />
96-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
97    <uses-feature
97-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
98        android:name="android.hardware.camera.front"
98-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
99        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
99-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
100    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
101    <uses-feature
101-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
102        android:name="android.hardware.camera.autofocus"
102-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
103        android:required="false" />
103-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
104    <uses-feature
104-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
105        android:name="android.hardware.camera.flash"
105-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
106        android:required="false" />
106-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
107    <uses-feature
107-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
108        android:name="android.hardware.screen.landscape"
108-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
109        android:required="false" />
109-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
110    <uses-feature
110-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
111        android:name="android.hardware.wifi"
111-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
112        android:required="false" />
112-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
113
114    <application
115        android:name="android.app.Application"
116        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
116-->[androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
117        android:debuggable="true"
118        android:extractNativeLibs="false"
119        android:icon="@mipmap/ic_launcher"
120        android:label="PGC TMS"
121        android:requestLegacyExternalStorage="true"
122        android:usesCleartextTraffic="true" >
123        <meta-data
124            android:name="com.google.android.gms.version"
125            android:value="@integer/google_play_services_version" />
126        <meta-data
127            android:name="com.google.android.geo.API_KEY"
128            android:value="AIzaSyAO_SXaEFQ4QHOtFjk_zCQvy-Xa02SegyE" />
129
130        <activity
131            android:name="com.flutter.flutter.petro.MainActivity"
132            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
133            android:exported="true"
134            android:hardwareAccelerated="true"
135            android:launchMode="singleTop"
136            android:theme="@style/LaunchTheme"
137            android:windowSoftInputMode="adjustResize" >
138
139            <!--
140           Specifies an Android theme to apply to this Activity as soon as
141           the Android process has started. This theme is visible to the user
142           while the Flutter UI initializes. After that, this theme continues
143           to determine the Window background behind the Flutter UI.
144            -->
145            <meta-data
146                android:name="io.flutter.embedding.android.SplashScreenDrawable"
147                android:resource="@drawable/launch_background" />
148
149            <intent-filter>
150                <action android:name="android.intent.action.MAIN" />
151
152                <category android:name="android.intent.category.LAUNCHER" />
153            </intent-filter>
154        </activity>
155        <!--
156         Don't delete the meta-data below.
157         This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
158        -->
159        <meta-data
160            android:name="flutterEmbedding"
161            android:value="2" />
162
163        <service
163-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-19:72
164            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
164-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-107
165            android:exported="false"
165-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-37
166            android:permission="android.permission.BIND_JOB_SERVICE" />
166-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-69
167        <service
167-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:20:9-26:19
168            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
168-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-97
169            android:exported="false" >
169-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
170            <intent-filter>
170-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-25:29
171                <action android:name="com.google.firebase.MESSAGING_EVENT" />
171-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:17-78
171-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:25-75
172            </intent-filter>
173        </service>
174
175        <receiver
175-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-35:20
176            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
176-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-98
177            android:exported="true"
177-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-36
178            android:permission="com.google.android.c2dm.permission.SEND" >
178-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:13-73
179            <intent-filter>
179-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:32:13-34:29
180                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
180-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:17-81
180-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:25-78
181            </intent-filter>
182        </receiver>
183
184        <service
184-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:9-41:19
185            android:name="com.google.firebase.components.ComponentDiscoveryService"
185-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:18-89
186            android:directBootAware="true"
186-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
187            android:exported="false" >
187-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:56:13-37
188            <meta-data
188-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:13-40:85
189                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
189-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:39:17-128
190                android:value="com.google.firebase.components.ComponentRegistrar" />
190-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:40:17-82
191            <meta-data
191-->[:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-13:85
192                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
192-->[:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:12:17-124
193                android:value="com.google.firebase.components.ComponentRegistrar" />
193-->[:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:13:17-82
194            <meta-data
194-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:57:13-59:85
195                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
195-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:58:17-122
196                android:value="com.google.firebase.components.ComponentRegistrar" />
196-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:59:17-82
197            <meta-data
197-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:60:13-62:85
198                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
198-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:61:17-119
199                android:value="com.google.firebase.components.ComponentRegistrar" />
199-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:62:17-82
200            <meta-data
200-->[com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
201                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
201-->[com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
202                android:value="com.google.firebase.components.ComponentRegistrar" />
202-->[com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
203            <meta-data
203-->[com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
204                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
204-->[com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
205                android:value="com.google.firebase.components.ComponentRegistrar" />
205-->[com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
206            <meta-data
206-->[com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
207                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
207-->[com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
208                android:value="com.google.firebase.components.ComponentRegistrar" />
208-->[com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
209            <meta-data
209-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
210                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
210-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
211                android:value="com.google.firebase.components.ComponentRegistrar" />
211-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
212            <meta-data
212-->[com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
213                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
213-->[com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
214                android:value="com.google.firebase.components.ComponentRegistrar" />
214-->[com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
215        </service>
216
217        <provider
217-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:9-47:38
218            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
218-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-102
219            android:authorities="com.flutter.flutter.petro.flutterfirebasemessaginginitprovider"
219-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-88
220            android:exported="false"
220-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:46:13-37
221            android:initOrder="99" />
221-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:47:13-35
222
223        <service
223-->[:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:72
224            android:name="com.pravera.fl_location.service.LocationServicesStatusIntentService"
224-->[:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-95
225            android:enabled="true"
225-->[:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-35
226            android:exported="false"
226-->[:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-37
227            android:permission="android.permission.BIND_JOB_SERVICE" />
227-->[:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-69
228
229        <activity
229-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-20:47
230            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
230-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-112
231            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
231-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-137
232            android:exported="false"
232-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-37
233            android:theme="@style/AppTheme" />
233-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-44
234        <activity
234-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-24:55
235            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
235-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-120
236            android:exported="false"
236-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-37
237            android:theme="@style/ThemeTransparent" />
237-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-52
238        <activity
238-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:9-28:55
239            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
239-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:26:13-114
240            android:exported="false"
240-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-37
241            android:theme="@style/ThemeTransparent" />
241-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-52
242        <activity
242-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:9-33:55
243            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
243-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-134
244            android:exported="false"
244-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:31:13-37
245            android:launchMode="singleInstance"
245-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:32:13-48
246            android:theme="@style/ThemeTransparent" />
246-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:33:13-52
247        <activity
247-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:34:9-38:55
248            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
248-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:35:13-128
249            android:exported="false"
249-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-37
250            android:launchMode="singleInstance"
250-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:37:13-48
251            android:theme="@style/ThemeTransparent" />
251-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:38:13-52
252
253        <receiver
253-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:40:9-43:40
254            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
254-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:41:13-119
255            android:enabled="true"
255-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-35
256            android:exported="false" />
256-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-37
257
258        <meta-data
258-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:45:9-47:36
259            android:name="io.flutter.embedded_views_preview"
259-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:46:13-61
260            android:value="true" />
260-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:47:13-33
261
262        <provider
262-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-19:20
263            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
263-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-82
264            android:authorities="com.flutter.flutter.petro.flutter.image_provider"
264-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-74
265            android:exported="false"
265-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-37
266            android:grantUriPermissions="true" >
266-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-47
267            <meta-data
267-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-18:75
268                android:name="android.support.FILE_PROVIDER_PATHS"
268-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:17-67
269                android:resource="@xml/flutter_image_picker_file_paths" />
269-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:17-72
270        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
271        <service
271-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-33:19
272            android:name="com.google.android.gms.metadata.ModuleDependencies"
272-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-78
273            android:enabled="false"
273-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-36
274            android:exported="false" >
274-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-37
275            <intent-filter>
275-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:26:13-28:29
276                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
276-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:27:17-94
276-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:27:25-91
277            </intent-filter>
278
279            <meta-data
279-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:36
280                android:name="photopicker_activity:0:required"
280-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-63
281                android:value="" />
281-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:32:17-33
282        </service>
283        <service
283-->[:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-14:56
284            android:name="com.baseflow.geolocator.GeolocatorLocationService"
284-->[:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-77
285            android:enabled="true"
285-->[:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-35
286            android:exported="false"
286-->[:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-37
287            android:foregroundServiceType="location" />
287-->[:geolocator_android] D:\geneat\beta-moible-flutter\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-53
288
289        <provider
289-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-27:20
290            android:name="com.crazecoder.openfile.FileProvider"
290-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-64
291            android:authorities="com.flutter.flutter.petro.fileProvider.com.crazecoder.openfile"
291-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-88
292            android:exported="false"
292-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-37
293            android:grantUriPermissions="true" >
293-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-47
294            <meta-data
294-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-18:75
295                android:name="android.support.FILE_PROVIDER_PATHS"
295-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:17-67
296                android:resource="@xml/filepaths" />
296-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:17-72
297        </provider>
298
299        <activity
299-->[:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-13:74
300            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
300-->[:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
301            android:exported="false"
301-->[:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
302            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Needs to be explicitly declared on P+ -->
302-->[:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-71
303        <uses-library
303-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
304            android:name="org.apache.http.legacy"
304-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
305            android:required="false" />
305-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
306
307        <receiver
307-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:29:9-40:20
308            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
308-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:30:13-78
309            android:exported="true"
309-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:31:13-36
310            android:permission="com.google.android.c2dm.permission.SEND" >
310-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:32:13-73
311            <intent-filter>
311-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:32:13-34:29
312                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
312-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:17-81
312-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:25-78
313            </intent-filter>
314
315            <meta-data
315-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:37:13-39:40
316                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
316-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:38:17-92
317                android:value="true" />
317-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:39:17-37
318        </receiver>
319        <!--
320             FirebaseMessagingService performs security checks at runtime,
321             but set to not exported to explicitly avoid allowing another app to call it.
322        -->
323        <service
323-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:46:9-53:19
324            android:name="com.google.firebase.messaging.FirebaseMessagingService"
324-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:47:13-82
325            android:directBootAware="true"
325-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:48:13-43
326            android:exported="false" >
326-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:49:13-37
327            <intent-filter android:priority="-500" >
327-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-25:29
328                <action android:name="com.google.firebase.MESSAGING_EVENT" />
328-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:17-78
328-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:25-75
329            </intent-filter>
330        </service>
331
332        <activity
332-->[com.google.android.gms:play-services-base:18.3.0] D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
333            android:name="com.google.android.gms.common.api.GoogleApiActivity"
333-->[com.google.android.gms:play-services-base:18.3.0] D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
334            android:exported="false"
334-->[com.google.android.gms:play-services-base:18.3.0] D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
335            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
335-->[com.google.android.gms:play-services-base:18.3.0] D:\android studio\android-gradle\caches\transforms-3\b65ab1a894c6b8dbf614ad54010261ad\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
336
337        <provider
337-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
338            android:name="com.google.firebase.provider.FirebaseInitProvider"
338-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
339            android:authorities="com.flutter.flutter.petro.firebaseinitprovider"
339-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
340            android:directBootAware="true"
340-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
341            android:exported="false"
341-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
342            android:initOrder="100" />
342-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
343        <provider
343-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
344            android:name="androidx.startup.InitializationProvider"
344-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
345            android:authorities="com.flutter.flutter.petro.androidx-startup"
345-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
346            android:exported="false" >
346-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
347            <meta-data
347-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
348                android:name="androidx.emoji2.text.EmojiCompatInitializer"
348-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
349                android:value="androidx.startup" />
349-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
350            <meta-data
350-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
351                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
351-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
352                android:value="androidx.startup" />
352-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
353            <meta-data
353-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
354                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
354-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
355                android:value="androidx.startup" />
355-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
356        </provider>
357
358        <uses-library
358-->[androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
359            android:name="androidx.window.extensions"
359-->[androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
360            android:required="false" />
360-->[androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
361        <uses-library
361-->[androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
362            android:name="androidx.window.sidecar"
362-->[androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
363            android:required="false" />
363-->[androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
364
365        <receiver
365-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
366            android:name="androidx.profileinstaller.ProfileInstallReceiver"
366-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
367            android:directBootAware="false"
367-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
368            android:enabled="true"
368-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
369            android:exported="true"
369-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
370            android:permission="android.permission.DUMP" >
370-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
371            <intent-filter>
371-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
372                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
372-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
372-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
373            </intent-filter>
374            <intent-filter>
374-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
375                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
375-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
375-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
376            </intent-filter>
377            <intent-filter>
377-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
378                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
378-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
378-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
379            </intent-filter>
380            <intent-filter>
380-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
381                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
381-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
381-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
382            </intent-filter>
383        </receiver>
384
385        <service
385-->[com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
386            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
386-->[com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
387            android:exported="false" >
387-->[com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
388            <meta-data
388-->[com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
389                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
389-->[com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
390                android:value="cct" />
390-->[com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
391        </service>
392        <service
392-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
393            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
393-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
394            android:exported="false"
394-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
395            android:permission="android.permission.BIND_JOB_SERVICE" >
395-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
396        </service>
397
398        <receiver
398-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
399            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
399-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
400            android:exported="false" />
400-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
401
402        <activity
402-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
403            android:name="com.journeyapps.barcodescanner.CaptureActivity"
403-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
404            android:clearTaskOnLaunch="true"
404-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
405            android:screenOrientation="sensorLandscape"
405-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
406            android:stateNotNeeded="true"
406-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
407            android:theme="@style/zxing_CaptureTheme"
407-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
408            android:windowSoftInputMode="stateAlwaysHidden" />
408-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
409    </application>
410
411</manifest>
