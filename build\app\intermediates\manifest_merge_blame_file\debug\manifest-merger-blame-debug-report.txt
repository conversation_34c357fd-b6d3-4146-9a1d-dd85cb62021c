1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.flutter.flutter.petro"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:3:3-65
15-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:3:20-62
16    <!-- <PERSON><PERSON><PERSON> quyền call API -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- <PERSON><PERSON><PERSON> quyền ví trí hiện tại -->
17-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:4:3-77
17-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:4:20-74
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:5:3-79
18-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:5:20-76
19    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
19-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:6:3-87
19-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:6:20-84
20    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- Cấp quyền cập nhật vị trí ở khi ở trạng thái nền -->
20-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:7:3-83
20-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:7:20-80
21    <uses-permission
21-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:8:3-78
22        android:name="android.permission.READ_EXTERNAL_STORAGE"
22-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:8:20-75
23        android:maxSdkVersion="32" />
23-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-35
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:9:3-79
24-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:9:20-76
25    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" /> <!-- Cấp quyền truy cập url cho Android > 11. -->
25-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:10:3-84
25-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:10:20-81
26    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Cấp quyền thông báo cho android >= 13 -->
26-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:11:3-75
26-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:11:20-72
27    <uses-permission android:name="android.permission.READ_CONTACTS" />
27-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:12:3-70
27-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:12:20-67
28    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
28-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:13:3-71
28-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:13:20-68
29    <!--
30 Required to query activities that can process text, see:
31       https://developer.android.com/training/package-visibility?hl=en and
32       https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
33
34       In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
35    -->
36    <queries>
36-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:59:3-64:13
37        <intent>
37-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:60:5-63:14
38            <action android:name="android.intent.action.PROCESS_TEXT" />
38-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:61:7-67
38-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:61:15-64
39
40            <data android:mimeType="text/plain" />
40-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:62:7-45
40-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:62:13-42
41        </intent>
42        <intent>
42-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-12:18
43            <action android:name="android.support.customtabs.action.CustomTabsService" />
43-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-90
43-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:21-87
44        </intent> <!-- Needs to be explicitly declared on Android R+ -->
45        <package android:name="com.google.android.apps.maps" />
45-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
45-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
46    </queries> <!-- Provide required visibility configuration for API level 30 and above -->
47    <queries>
47-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:59:3-64:13
48
49        <!-- If your app checks for SMS support -->
50        <intent>
51            <action android:name="android.intent.action.VIEW" />
52
53            <data android:scheme="sms" />
53-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:62:7-45
54        </intent>
55        <!-- If your app checks for call support -->
56        <intent>
57            <action android:name="android.intent.action.VIEW" />
58
59            <data android:scheme="tel" />
59-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:62:7-45
60        </intent>
61        <!-- #enddocregion android-queries -->
62        <!--
63         The "https" scheme is only required for integration tests of this package.
64         It shouldn't be needed in most actual apps, or show up in the README!
65        -->
66        <intent>
67            <action android:name="android.intent.action.VIEW" />
68
69            <data android:scheme="https" />
69-->D:\geneat\beta-moible-flutter\android\app\src\main\AndroidManifest.xml:62:7-45
70        </intent>
71        <!-- If your application checks for inAppBrowserView launch mode support -->
72        <intent>
72-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-12:18
73            <action android:name="android.support.customtabs.action.CustomTabsService" />
73-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-90
73-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:21-87
74        </intent>
75    </queries>
76
77    <uses-permission android:name="android.permission.WAKE_LOCK" />
77-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-68
77-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:10:22-65
78    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
78-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-79
78-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-76
79    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
79-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-76
79-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:13:22-73
80    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
80-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:14:5-75
80-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:14:22-72
81    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
81-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:15:5-75
81-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:15:22-72
82
83    <uses-feature
83-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
84        android:glEsVersion="0x00020000"
84-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
85        android:required="true" /> <!-- Required by older versions of Google Play services to create IID tokens -->
85-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
86    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
86-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:5-82
86-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:22-79
87
88    <permission
88-->[androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
89        android:name="com.flutter.flutter.petro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
89-->[androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
90        android:protectionLevel="signature" />
90-->[androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
91
92    <uses-permission android:name="com.flutter.flutter.petro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
92-->[androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
92-->[androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
93    <uses-permission android:name="android.permission.CAMERA" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
93-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
93-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:22:22-62
94    <uses-feature
94-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
95        android:name="android.hardware.camera"
95-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
96        android:required="false" />
96-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
97    <uses-feature
97-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
98        android:name="android.hardware.camera.front"
98-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
99        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
99-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
100    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
101    <uses-feature
101-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
102        android:name="android.hardware.camera.autofocus"
102-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
103        android:required="false" />
103-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
104    <uses-feature
104-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
105        android:name="android.hardware.camera.flash"
105-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
106        android:required="false" />
106-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
107    <uses-feature
107-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
108        android:name="android.hardware.screen.landscape"
108-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
109        android:required="false" />
109-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
110    <uses-feature
110-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
111        android:name="android.hardware.wifi"
111-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
112        android:required="false" />
112-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
113
114    <application
115        android:name="android.app.Application"
116        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
116-->[androidx.core:core:1.13.1] D:\android studio\android-gradle\caches\transforms-3\66324e78beec671b05773d52d84e9d9a\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
117        android:debuggable="true"
118        android:icon="@mipmap/ic_launcher"
119        android:label="PGC TMS"
120        android:requestLegacyExternalStorage="true"
121        android:usesCleartextTraffic="true" >
122        <meta-data
123            android:name="com.google.android.gms.version"
124            android:value="@integer/google_play_services_version" />
125        <meta-data
126            android:name="com.google.android.geo.API_KEY"
127            android:value="AIzaSyAO_SXaEFQ4QHOtFjk_zCQvy-Xa02SegyE" />
128
129        <activity
130            android:name="com.flutter.flutter.petro.MainActivity"
131            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
132            android:exported="true"
133            android:hardwareAccelerated="true"
134            android:launchMode="singleTop"
135            android:theme="@style/LaunchTheme"
136            android:windowSoftInputMode="adjustResize" >
137
138            <!--
139           Specifies an Android theme to apply to this Activity as soon as
140           the Android process has started. This theme is visible to the user
141           while the Flutter UI initializes. After that, this theme continues
142           to determine the Window background behind the Flutter UI.
143            -->
144            <meta-data
145                android:name="io.flutter.embedding.android.SplashScreenDrawable"
146                android:resource="@drawable/launch_background" />
147
148            <intent-filter>
149                <action android:name="android.intent.action.MAIN" />
150
151                <category android:name="android.intent.category.LAUNCHER" />
152            </intent-filter>
153        </activity>
154        <!--
155         Don't delete the meta-data below.
156         This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
157        -->
158        <meta-data
159            android:name="flutterEmbedding"
160            android:value="2" />
161
162        <service
162-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-19:72
163            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
163-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-107
164            android:exported="false"
164-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-37
165            android:permission="android.permission.BIND_JOB_SERVICE" />
165-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-69
166        <service
166-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:20:9-26:19
167            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
167-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-97
168            android:exported="false" >
168-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
169            <intent-filter>
169-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-25:29
170                <action android:name="com.google.firebase.MESSAGING_EVENT" />
170-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:17-78
170-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:25-75
171            </intent-filter>
172        </service>
173
174        <receiver
174-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-35:20
175            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
175-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-98
176            android:exported="true"
176-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-36
177            android:permission="com.google.android.c2dm.permission.SEND" >
177-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:13-73
178            <intent-filter>
178-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:32:13-34:29
179                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
179-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:17-81
179-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:25-78
180            </intent-filter>
181        </receiver>
182
183        <service
183-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:9-41:19
184            android:name="com.google.firebase.components.ComponentDiscoveryService"
184-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:18-89
185            android:directBootAware="true"
185-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
186            android:exported="false" >
186-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:56:13-37
187            <meta-data
187-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:13-40:85
188                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
188-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:39:17-128
189                android:value="com.google.firebase.components.ComponentRegistrar" />
189-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:40:17-82
190            <meta-data
190-->[:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-13:85
191                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
191-->[:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:12:17-124
192                android:value="com.google.firebase.components.ComponentRegistrar" />
192-->[:firebase_core] D:\geneat\beta-moible-flutter\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:13:17-82
193            <meta-data
193-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:57:13-59:85
194                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
194-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:58:17-122
195                android:value="com.google.firebase.components.ComponentRegistrar" />
195-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:59:17-82
196            <meta-data
196-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:60:13-62:85
197                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
197-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:61:17-119
198                android:value="com.google.firebase.components.ComponentRegistrar" />
198-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:62:17-82
199            <meta-data
199-->[com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
200                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
200-->[com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
201                android:value="com.google.firebase.components.ComponentRegistrar" />
201-->[com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
202            <meta-data
202-->[com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
203                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
203-->[com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
204                android:value="com.google.firebase.components.ComponentRegistrar" />
204-->[com.google.firebase:firebase-installations:18.0.0] D:\android studio\android-gradle\caches\transforms-3\a86f03cf783a914bc1522b279f32f3d0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
205            <meta-data
205-->[com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
206                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
206-->[com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[com.google.firebase:firebase-common-ktx:21.0.0] D:\android studio\android-gradle\caches\transforms-3\4fa2f2f3892e6ddc25b36f518430ec1a\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
208            <meta-data
208-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
209                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
209-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
210                android:value="com.google.firebase.components.ComponentRegistrar" />
210-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
211            <meta-data
211-->[com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
212                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
212-->[com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
213                android:value="com.google.firebase.components.ComponentRegistrar" />
213-->[com.google.firebase:firebase-datatransport:18.2.0] D:\android studio\android-gradle\caches\transforms-3\32fbdfc55c8c033f9a7602e5e9836742\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
214        </service>
215
216        <provider
216-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:9-47:38
217            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
217-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-102
218            android:authorities="com.flutter.flutter.petro.flutterfirebasemessaginginitprovider"
218-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-88
219            android:exported="false"
219-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:46:13-37
220            android:initOrder="99" />
220-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:47:13-35
221
222        <service
222-->[:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:72
223            android:name="com.pravera.fl_location.service.LocationServicesStatusIntentService"
223-->[:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-95
224            android:enabled="true"
224-->[:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-35
225            android:exported="false"
225-->[:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-37
226            android:permission="android.permission.BIND_JOB_SERVICE" />
226-->[:fl_location] D:\geneat\beta-moible-flutter\build\fl_location\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-69
227
228        <activity
228-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-20:47
229            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
229-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-112
230            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
230-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-137
231            android:exported="false"
231-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-37
232            android:theme="@style/AppTheme" />
232-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-44
233        <activity
233-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-24:55
234            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
234-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-120
235            android:exported="false"
235-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-37
236            android:theme="@style/ThemeTransparent" />
236-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-52
237        <activity
237-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:9-28:55
238            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
238-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:26:13-114
239            android:exported="false"
239-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-37
240            android:theme="@style/ThemeTransparent" />
240-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-52
241        <activity
241-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:9-33:55
242            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
242-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-134
243            android:exported="false"
243-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:31:13-37
244            android:launchMode="singleInstance"
244-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:32:13-48
245            android:theme="@style/ThemeTransparent" />
245-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:33:13-52
246        <activity
246-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:34:9-38:55
247            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
247-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:35:13-128
248            android:exported="false"
248-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-37
249            android:launchMode="singleInstance"
249-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:37:13-48
250            android:theme="@style/ThemeTransparent" />
250-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:38:13-52
251
252        <receiver
252-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:40:9-43:40
253            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
253-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:41:13-119
254            android:enabled="true"
254-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-35
255            android:exported="false" />
255-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-37
256
257        <meta-data
257-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:45:9-47:36
258            android:name="io.flutter.embedded_views_preview"
258-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:46:13-61
259            android:value="true" />
259-->[:flutter_inappwebview_android] D:\geneat\beta-moible-flutter\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\AndroidManifest.xml:47:13-33
260
261        <provider
261-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-19:20
262            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
262-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-82
263            android:authorities="com.flutter.flutter.petro.flutter.image_provider"
263-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-74
264            android:exported="false"
264-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-37
265            android:grantUriPermissions="true" >
265-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-47
266            <meta-data
266-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-18:75
267                android:name="android.support.FILE_PROVIDER_PATHS"
267-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:17-67
268                android:resource="@xml/flutter_image_picker_file_paths" />
268-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:17-72
269        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
270        <service
270-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-33:19
271            android:name="com.google.android.gms.metadata.ModuleDependencies"
271-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-78
272            android:enabled="false"
272-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-36
273            android:exported="false" >
273-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-37
274            <intent-filter>
274-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:26:13-28:29
275                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
275-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:27:17-94
275-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:27:25-91
276            </intent-filter>
277
278            <meta-data
278-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:36
279                android:name="photopicker_activity:0:required"
279-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-63
280                android:value="" />
280-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:32:17-33
281        </service>
282
283        <provider
283-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-27:20
284            android:name="com.crazecoder.openfile.FileProvider"
284-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-64
285            android:authorities="com.flutter.flutter.petro.fileProvider.com.crazecoder.openfile"
285-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-88
286            android:exported="false"
286-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-37
287            android:grantUriPermissions="true" >
287-->[:open_filex] D:\geneat\beta-moible-flutter\build\open_filex\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-47
288            <meta-data
288-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-18:75
289                android:name="android.support.FILE_PROVIDER_PATHS"
289-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:17-67
290                android:resource="@xml/filepaths" />
290-->[:image_picker_android] D:\geneat\beta-moible-flutter\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:17-72
291        </provider>
292
293        <activity
293-->[:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-13:74
294            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
294-->[:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
295            android:exported="false"
295-->[:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
296            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Needs to be explicitly declared on P+ -->
296-->[:url_launcher_android] D:\geneat\beta-moible-flutter\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-71
297        <uses-library
297-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
298            android:name="org.apache.http.legacy"
298-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
299            android:required="false" />
299-->[com.google.android.gms:play-services-maps:18.2.0] D:\android studio\android-gradle\caches\transforms-3\1cc0be700c7357f503838b32dba7b93f\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
300
301        <receiver
301-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:29:9-40:20
302            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
302-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:30:13-78
303            android:exported="true"
303-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:31:13-36
304            android:permission="com.google.android.c2dm.permission.SEND" >
304-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:32:13-73
305            <intent-filter>
305-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:32:13-34:29
306                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
306-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:17-81
306-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:33:25-78
307            </intent-filter>
308
309            <meta-data
309-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:37:13-39:40
310                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
310-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:38:17-92
311                android:value="true" />
311-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:39:17-37
312        </receiver>
313        <!--
314             FirebaseMessagingService performs security checks at runtime,
315             but set to not exported to explicitly avoid allowing another app to call it.
316        -->
317        <service
317-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:46:9-53:19
318            android:name="com.google.firebase.messaging.FirebaseMessagingService"
318-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:47:13-82
319            android:directBootAware="true"
319-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:48:13-43
320            android:exported="false" >
320-->[com.google.firebase:firebase-messaging:24.0.0] D:\android studio\android-gradle\caches\transforms-3\081664dd3597f3416efdf45ca7a75130\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:49:13-37
321            <intent-filter android:priority="-500" >
321-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-25:29
322                <action android:name="com.google.firebase.MESSAGING_EVENT" />
322-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:17-78
322-->[:firebase_messaging] D:\geneat\beta-moible-flutter\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:24:25-75
323            </intent-filter>
324        </service>
325
326        <activity
326-->[com.google.android.gms:play-services-base:18.1.0] D:\android studio\android-gradle\caches\transforms-3\ab8e5e5c5ed5e6645e0199cf714a4e7a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
327            android:name="com.google.android.gms.common.api.GoogleApiActivity"
327-->[com.google.android.gms:play-services-base:18.1.0] D:\android studio\android-gradle\caches\transforms-3\ab8e5e5c5ed5e6645e0199cf714a4e7a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
328            android:exported="false"
328-->[com.google.android.gms:play-services-base:18.1.0] D:\android studio\android-gradle\caches\transforms-3\ab8e5e5c5ed5e6645e0199cf714a4e7a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
329            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
329-->[com.google.android.gms:play-services-base:18.1.0] D:\android studio\android-gradle\caches\transforms-3\ab8e5e5c5ed5e6645e0199cf714a4e7a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
330
331        <provider
331-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
332            android:name="com.google.firebase.provider.FirebaseInitProvider"
332-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
333            android:authorities="com.flutter.flutter.petro.firebaseinitprovider"
333-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
334            android:directBootAware="true"
334-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
335            android:exported="false"
335-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
336            android:initOrder="100" />
336-->[com.google.firebase:firebase-common:21.0.0] D:\android studio\android-gradle\caches\transforms-3\3ab65d20153660539f7d9a36b32a6dd3\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
337        <provider
337-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
338            android:name="androidx.startup.InitializationProvider"
338-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
339            android:authorities="com.flutter.flutter.petro.androidx-startup"
339-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
340            android:exported="false" >
340-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
341            <meta-data
341-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
342                android:name="androidx.emoji2.text.EmojiCompatInitializer"
342-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
343                android:value="androidx.startup" />
343-->[androidx.emoji2:emoji2:1.2.0] D:\android studio\android-gradle\caches\transforms-3\3bbb7ad1309b13c1108b46e944291d3d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
344            <meta-data
344-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
345                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
345-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
346                android:value="androidx.startup" />
346-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\android studio\android-gradle\caches\transforms-3\2be212779591a28d2d3fb3e116a1732b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
347            <meta-data
347-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
348                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
348-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
349                android:value="androidx.startup" />
349-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
350        </provider>
351
352        <uses-library
352-->[androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
353            android:name="androidx.window.extensions"
353-->[androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
354            android:required="false" />
354-->[androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
355        <uses-library
355-->[androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
356            android:name="androidx.window.sidecar"
356-->[androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
357            android:required="false" />
357-->[androidx.window:window:1.2.0] D:\android studio\android-gradle\caches\transforms-3\dba738d2b33ccc893d17ddca3d0700d1\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
358
359        <receiver
359-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
360            android:name="androidx.profileinstaller.ProfileInstallReceiver"
360-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
361            android:directBootAware="false"
361-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
362            android:enabled="true"
362-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
363            android:exported="true"
363-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
364            android:permission="android.permission.DUMP" >
364-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
365            <intent-filter>
365-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
366                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
366-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
366-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
367            </intent-filter>
368            <intent-filter>
368-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
369                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
369-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
369-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
370            </intent-filter>
371            <intent-filter>
371-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
372                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
372-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
372-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
373            </intent-filter>
374            <intent-filter>
374-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
375                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
375-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
375-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\android studio\android-gradle\caches\transforms-3\c1e119b4540840142c30cb4b47d3dd43\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
376            </intent-filter>
377        </receiver>
378
379        <service
379-->[com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
380            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
380-->[com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
381            android:exported="false" >
381-->[com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
382            <meta-data
382-->[com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
383                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
383-->[com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
384                android:value="cct" />
384-->[com.google.android.datatransport:transport-backend-cct:3.1.9] D:\android studio\android-gradle\caches\transforms-3\13dffd4f6e4f180d30cca359cb8e9f23\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
385        </service>
386        <service
386-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
387            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
387-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
388            android:exported="false"
388-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
389            android:permission="android.permission.BIND_JOB_SERVICE" >
389-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
390        </service>
391
392        <receiver
392-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
393            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
393-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
394            android:exported="false" />
394-->[com.google.android.datatransport:transport-runtime:3.1.9] D:\android studio\android-gradle\caches\transforms-3\7fefa432a16d074bf98bf3127d5b4d87\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
395
396        <activity
396-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
397            android:name="com.journeyapps.barcodescanner.CaptureActivity"
397-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
398            android:clearTaskOnLaunch="true"
398-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
399            android:screenOrientation="sensorLandscape"
399-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
400            android:stateNotNeeded="true"
400-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
401            android:theme="@style/zxing_CaptureTheme"
401-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
402            android:windowSoftInputMode="stateAlwaysHidden" />
402-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\android studio\android-gradle\caches\transforms-3\fd8f94385d16413adb0e8f5b1a36f410\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
403    </application>
404
405</manifest>
