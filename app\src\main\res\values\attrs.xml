<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="CurveView">
        <attr name="cvColor" format="color"/>
        <attr name="cvCurveSmooth" format="float"/>
        <attr name="cvReverse" format="boolean"/>
        <attr name="cvSmooth" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="BetaEditText">
        <attr name="btedIcon" format="reference"/>
        <attr name="btedHint" format="string"/>
        <attr name="btedText" format="string"/>
        <attr name="android:inputType"/>
        <attr name="android:nextFocusDown"/>
        <attr name="android:imeOptions"/>
    </declare-styleable>

    <declare-styleable name="BetaSelectionView">
        <attr name="btsvIcon" format="reference"/>
        <attr name="btsvText" format="string"/>
        <attr name="btsvHint" format="string"/>
    </declare-styleable>

    <declare-styleable name="BetaMenuItem">
        <attr name="btMenuIcon" format="reference"/>
        <attr name="btMenuIconSelected" format="reference"/>
        <attr name="btMenuTitle" format="string"/>
        <attr name="btMenuIsSelected" format="boolean"/>
        <attr name="btMenuTag" format="string"/>
        <attr name="btMenuIsOverlay" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="ItemTimeBooking">
        <attr name="bookTime" format="string"/>
        <attr name="bookSeat" format="string"/>
    </declare-styleable>

    <declare-styleable name="SeatTable">

        <attr name="seat_checked" format="reference" />
        <attr name="seat_sold" format="reference" />
        <attr name="seat_available" format="reference" />

        <!--Normal-->
        <attr name="seat_normal" format="reference" />
        <attr name="seat_normal_selected" format="reference" />
        <attr name="seat_normal_sold" format="reference" />
        <attr name="seat_normal_processing" format="reference" />
        <attr name="seat_normal_reverse" format="reference" />

        <!--VIP-->
        <attr name="seat_VIP" format="reference" />
        <attr name="seat_VIP_selected" format="reference" />
        <attr name="seat_VIP_sold" format="reference" />
        <attr name="seat_VIP_processing" format="reference" />
        <attr name="seat_VIP_reverse" format="reference" />

        <!--Double-->
        <attr name="seat_double" format="reference" />
        <attr name="seat_double_selected" format="reference" />
        <attr name="seat_double_sold" format="reference" />
        <attr name="seat_double_processing" format="reference" />
        <attr name="seat_double_reverse" format="reference" />

        <attr name="overview_checked" format="color" />
        <attr name="overview_sold" format="color" />
        <attr name="txt_color" format="color"/>
    </declare-styleable>
</resources>