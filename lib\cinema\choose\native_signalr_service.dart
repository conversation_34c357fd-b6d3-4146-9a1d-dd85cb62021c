import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../constants/index.dart';

/// A wrapper for the native SignalR service specifically for seat selection
class NativeSeatSignalRService {
  // Singleton pattern
  static NativeSeatSignalRService? _instance;
  static NativeSeatSignalRService get instance {
    _instance ??= NativeSeatSignalRService._internal();
    return _instance!;
  }

  NativeSeatSignalRService._internal();

  // Method channel for communicating with the native side
  static const MethodChannel _methodChannel =
      MethodChannel('com.betacinemas.signalr/methods');

  // Event channel for receiving events from the native side
  static const EventChannel _eventChannel =
      EventChannel('com.betacinemas.signalr/events');

  // Hub name
  final String _hubName = 'chooseSeatHub';

  // Events
  final String _broadcastMessageEvent = 'broadcastMessage';
  final String _sendMessageEvent = 'sendMessage';  // Match with the server method name
  final String _joinGroupEvent = 'joinGroup';      // Match with the server method name
  final String _leaveGroupEvent = 'leaveGroup';    // Match with the server method name

  // Listeners
  final List<Function(bool)> _connectionListeners = [];
  final List<Function(SeatSignalrResponse)> _dataListeners = [];

  // Connection status
  bool _isConnected = false;
  String? _connectionId;
  StreamSubscription<dynamic>? _eventSubscription;

  /// Start the SignalR connection
  Future<void> startSignalR({String? customUrl}) async {
    if (Platform.isIOS) {
      _logWarning('Native SignalR for iOS is handled by a different implementation');
      return;
    }

    if (!Platform.isAndroid) {
      _logWarning('Native SignalR is only supported on Android');
      return;
    }

    _logInfo('Starting SignalR connection...');

    try {
      // Set up event listener
      _eventSubscription = _eventChannel.receiveBroadcastStream().listen(_handleEvent);

      // Get the base URL
      final baseUrl = customUrl ?? 'https://www.betacinemas.vn';

      // Get the auth token
      final token = await _getAccessToken();

      // Connect to the hub
      final result = await _methodChannel.invokeMethod<Map<dynamic, dynamic>>(
        'connect',
        {
          'url': baseUrl,
          'hubName': _hubName,
          'token': token,
        },
      );

      if (result != null) {
        _connectionId = result['connectionId'] as String?;
        _isConnected = true;
        _notifyConnectionListeners(true);
        _logInfo('SignalR connected with ID: $_connectionId');

        // Register for the broadcastMessage method
        await _methodChannel.invokeMethod<void>(
          'on',
          {'method': _broadcastMessageEvent},
        );
      } else {
        throw PlatformException(
          code: 'CONNECT_FAILED',
          message: 'Failed to connect to SignalR hub',
        );
      }
    } catch (e) {
      _logError('Error starting SignalR: $e');
      _isConnected = false;
      _connectionId = null;
      _notifyConnectionListeners(false);
      rethrow;
    }
  }

  /// Join a group
  Future<void> joinGroup(String showId) async {
    if (!Platform.isAndroid || !_isConnected) return;

    try {
      _logInfo('Joining group: $showId');

      final args = [
        _connectionId ?? '',
        showId
      ];

      await _methodChannel.invokeMethod<void>(
        'invoke',
        {
          'method': _joinGroupEvent,
          'arguments': args,
        },
      );

      _logInfo('Joined group successfully');
    } catch (e) {
      _logError('Error joining group: $e');
      rethrow;
    }
  }

  /// Leave a group
  Future<void> leaveGroup(String showId) async {
    if (!Platform.isAndroid || !_isConnected) return;

    try {
      _logInfo('Leaving group: $showId');

      final args = [
        _connectionId ?? '',
        showId
      ];

      await _methodChannel.invokeMethod<void>(
        'invoke',
        {
          'method': _leaveGroupEvent,
          'arguments': args,
        },
      );

      _logInfo('Left group successfully');
    } catch (e) {
      _logError('Error leaving group: $e');
      rethrow;
    }
  }

  /// Send a seat update
  Future<void> sendSeat(String showId, int seatIndex, int status) async {
    if (!Platform.isAndroid || !_isConnected) return;

    try {
      _logInfo('Sending seat status: showId=$showId, seatIndex=$seatIndex, status=$status');

      final args = [
        _connectionId ?? '',
        showId,
        seatIndex.toString(),
        status.toString()
      ];

      await _methodChannel.invokeMethod<void>(
        'invoke',
        {
          'method': _sendMessageEvent,
          'arguments': args,
        },
      );

      _logInfo('Sent seat status successfully');
    } catch (e) {
      _logError('Error sending seat status: $e');
      rethrow;
    }
  }

  /// Stop the SignalR connection
  Future<void> stop() async {
    if (!Platform.isAndroid) return;

    try {
      _eventSubscription?.cancel();
      await _methodChannel.invokeMethod<void>('disconnect');
      _isConnected = false;
      _connectionId = null;
      _notifyConnectionListeners(false);
    } catch (e) {
      _logWarning('Error stopping SignalR: $e');
    }
  }

  /// Handle events from the native side
  void _handleEvent(dynamic event) {
    if (event is! Map) {
      return;
    }

    final eventName = event['event'] as String?;

    if (eventName == 'connectionState') {
      final connected = event['connected'] as bool? ?? false;
      final connectionId = event['connectionId'] as String?;

      _isConnected = connected;
      _connectionId = connectionId;

      _notifyConnectionListeners(connected);
    } else if (eventName == 'hubMethod') {
      final method = event['method'] as String?;
      final arguments = event['arguments'] as List<dynamic>?;

      if (method == _broadcastMessageEvent && arguments != null && arguments.length >= 4) {
        try {
          final data = SeatSignalrResponse(
            arguments[0].toString(),
            arguments[1].toString(),
            int.parse(arguments[2].toString()),
            int.parse(arguments[3].toString()),
          );

          for (final listener in _dataListeners) {
            listener(data);
          }
        } catch (e) {
          _logError('Error parsing seat data: $e');
        }
      }
    }
  }

  /// Get the connection ID
  String? get connectionId => _connectionId;

  /// Check if connected
  bool get isConnected => _isConnected;

  /// Add a connection listener
  void addConnectionListener(Function(bool) listener) {
    _connectionListeners.add(listener);
    listener(_isConnected);
  }

  /// Remove a connection listener
  void removeConnectionListener(Function(bool) listener) {
    _connectionListeners.remove(listener);
  }

  /// Add a data listener
  void addDataListener(Function(SeatSignalrResponse) listener) {
    _dataListeners.add(listener);
  }

  /// Remove a data listener
  void removeDataListener(Function(SeatSignalrResponse) listener) {
    _dataListeners.remove(listener);
  }

  /// Notify all connection listeners
  void _notifyConnectionListeners(bool isConnected) {
    for (var listener in _connectionListeners) {
      listener(isConnected);
    }
  }

  /// Get the access token
  Future<String> _getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(CPref.token) ?? '';
  }

  /// Log an info message
  void _logInfo(String message) {
    debugPrint('NativeSignalR INFO: $message');
  }

  /// Log a warning message
  void _logWarning(String message) {
    debugPrint('NativeSignalR WARNING: $message');
  }

  /// Log an error message
  void _logError(String message) {
    debugPrint('NativeSignalR ERROR: $message');
  }

  /// Dispose of resources
  void dispose() {
    stop();
    _connectionListeners.clear();
    _dataListeners.clear();
  }
}

/// Model for SignalR seat responses
class SeatSignalrResponse {
  final String connectionId;
  final String showId;
  final int seatIndex;
  final int seatStatus;

  SeatSignalrResponse(
    this.connectionId,
    this.showId,
    this.seatIndex,
    this.seatStatus,
  );
}
