import 'package:http/http.dart';

import '/models/index.dart';
import 'base_http.dart';

class SAuth {
  late String endpoint;
  late Map<String, String> headers;
  late Future<MApi?> Function({required Response result}) checkAuth;

  SAuth(this.endpoint, this.headers, this.checkAuth);

  Future<MApi?> login({required body}) async => checkAuth(
          result: await BaseHttp.post(
        // url: '$endpoint/authentication/jwt/login',
        url: '$endpoint/api/v1/erp/accounts/login',
        body: body,
        headers: headers,
      ));

  Future<MApi?> zalo() async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/parameters/zalo',
        queryParameters: {},
        headers: headers,
      ));

  Future<MApi?> register({required body}) async => checkAuth(
          result: await BaseHttp.post(
        url: '$endpoint/authentication/register',
        body: body,
        headers: headers,
      ));

  Future<MApi?> forgotPassword({required String email}) async => checkAuth(
      result: await BaseHttp.put(
          url: '$endpoint/me/forgot-password/$email',
          body: {},
          headers: headers));

  Future<MApi?> verifyForgotPassword(
          {required String resetPasswordToken}) async =>
      checkAuth(
          result: await BaseHttp.put(
              url: '$endpoint/me/verify-forgot-password',
              body: {'resetPasswordToken': resetPasswordToken},
              headers: headers));

  Future<MApi?> resetPassword(
          {required String resetPasswordToken,
          required String password}) async =>
      checkAuth(
          result: await BaseHttp.put(
              url: '$endpoint/me/verify-forgot-password',
              body: {
                'resetPasswordToken': resetPasswordToken,
                'password': password
              },
              headers: headers));

  Future<MApi?> info({required String token, required String id}) async {
    headers['Authorization'] = 'Bearer $token';
    headers['X-User'] = id;
    return checkAuth(
        result: await BaseHttp.get(
      url: '$endpoint/api/v1/erp/accounts/%7B$id%7D',
      queryParameters: {},
      headers: headers,
    ));
  }

  Future<MApi?> updateProfile({required body}) async => checkAuth(
          result: await BaseHttp.put(
        url: '$endpoint/idm/users/info',
        body: body,
        headers: headers,
      ));
  Future<MApi?> updateAvatar({required body}) async => checkAuth(
          result: await BaseHttp.put(
        url: '$endpoint/me/info/avatar',
        body: body,
        headers: headers,
      ));

  Future<MApi?> updatePassword({required body}) async => checkAuth(
          result: await BaseHttp.put(
        url: '$endpoint/me/password',
        body: body,
        headers: headers,
      ));

  Future<MApi?> delete() async => checkAuth(
          result: await BaseHttp.delete(
        url: '$endpoint/me',
        headers: headers,
      ));

  // Get user profile by ID
  Future<MApi?> getProfileById(String userId) async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/accounts/$userId',
        headers: headers,
        queryParameters: {},
      ));

  // Get card class information
  Future<MApi?> getCardClass() async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/card-class',
        headers: headers,
        queryParameters: {},
      ));

  // Get user's card list
  Future<MApi?> getListCard(String userId) async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/accounts/$userId/cards',
        headers: headers,
        queryParameters: {},
      ));

  // Get user's point information
  Future<MApi?> getPoints(String userId) async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/accounts/$userId/points',
        headers: headers,
        queryParameters: {},
      ));

  // Get point history
  Future<MApi?> getPointHistory() async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/points/history',
        headers: headers,
        queryParameters: {},
      ));

  // Get available point redemptions
  Future<MApi?> getPointRedemptions() async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/points/redemptions',
        headers: headers,
        queryParameters: {},
      ));

  // Redeem points
  Future<MApi?> redeemPoints({required Map<String, dynamic> body}) async => checkAuth(
          result: await BaseHttp.post(
        url: '$endpoint/api/v1/erp/points/redeem',
        headers: headers,
        body: body,
      ));

  // Get transaction history
  Future<MApi?> getTransactionHistory() async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/orders/history',
        headers: headers,
        queryParameters: {},
      ));

  // Get transaction detail
  Future<MApi?> getTransactionDetail(String transactionId) async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/orders/$transactionId',
        headers: headers,
        queryParameters: {},
      ));

  // Register FCM token for push notifications - tương ứng với iOS registerFCMToken
  Future<MApi?> registerFCMToken({
    required String deviceId,
    required String accountId,
    required String deviceToken,
    required String deviceType,
  }) async => checkAuth(
          result: await BaseHttp.post(
        url: '$endpoint/api/v1/erp/notifications/register-device-token',
        headers: headers,
        body: {
          'DeviceId': deviceId,
          'AccountId': accountId,
          'DeviceToken': deviceToken,
          'DeviceType': deviceType,
        },
      ));

  // Unregister FCM token for push notifications - tương ứng với iOS unregisterFCMToken
  Future<MApi?> unregisterFCMToken({
    required String deviceId,
    required String accountId,
    required String deviceToken,
    required String deviceType,
  }) async => checkAuth(
          result: await BaseHttp.put(
        url: '$endpoint/api/v1/erp/notifications/unregister-device-token',
        headers: headers,
        body: {
          'DeviceId': deviceId,
          'AccountId': accountId,
          'DeviceToken': deviceToken,
          'DeviceType': deviceType,
        },
      ));

  // Legacy method - deprecated, use unregisterFCMToken instead
  Future<MApi?> unregisterDeviceToken(String deviceId, String accountId, String token) async => checkAuth(
          result: await BaseHttp.delete(
        url: '$endpoint/api/v1/erp/accounts/device-token?deviceId=$deviceId&accountId=$accountId&token=$token',
        headers: headers,
      ));
}
