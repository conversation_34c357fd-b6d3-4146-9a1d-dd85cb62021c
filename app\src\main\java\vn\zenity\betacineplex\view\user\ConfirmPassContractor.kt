package vn.zenity.betacineplex.view.user

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView

/**
 * Created by Zenity.
 */

interface ConfirmPassContractor {
    interface View : IBaseView {
        fun confirmSuccess()
        fun confirmError(message: String)
    }

    interface Presenter : IBasePresenter<View> {
        fun confirmPassword(password: String)
    }
}
