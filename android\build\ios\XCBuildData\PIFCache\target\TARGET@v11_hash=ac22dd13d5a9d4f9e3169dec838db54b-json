{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838402d31f902c4695916b350e1da79ff", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ab53826ced373658487b7a7504267f3f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989d777f0ba5487392c39e2e1a4ff04b31", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad899ae22d8d078dff5fd5d6c0f40166", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989d777f0ba5487392c39e2e1a4ff04b31", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982e2bd2830aef7eb70c414bf47062b81d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987eae91cf8bcd23089c877465146a6409", "guid": "bfdfe7dc352907fc980b868725387e98c5065eee2c505d502a839dfb6f981997", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98760cb230a3cf57a7082714639c43ce03", "guid": "bfdfe7dc352907fc980b868725387e98585c9f80c4baa473be538a9dd32ad49c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea07a5bbd0b31e8dd998ae55d3252f32", "guid": "bfdfe7dc352907fc980b868725387e988d9120bd20d1e86b08c090f356392f2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849b6247f1c050a844d7ffe060fddcbcf", "guid": "bfdfe7dc352907fc980b868725387e981227307c6d228107eef9c665141db890", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebaa6d19a05df9c2e7f86227676cbc6f", "guid": "bfdfe7dc352907fc980b868725387e9800358acdf6efe68d9c6f87990f03cccd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dee171a683e4cac5145cce25aa651849", "guid": "bfdfe7dc352907fc980b868725387e98e03f7af4e11a6a33a91d6bc1f25ae659", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbd2e8c6dfe89fea43157dce2f80c055", "guid": "bfdfe7dc352907fc980b868725387e989c4b439e9b399322306b7282a25fd601", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c84bccc34dad8a5b67ca0f8bbe1e948", "guid": "bfdfe7dc352907fc980b868725387e984ff8741394b865bafc43fe61ffafac91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819ad8d7637233e6d7381477f3ab6f1cf", "guid": "bfdfe7dc352907fc980b868725387e9807f26b1027813954ad75f71453c8b646", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faa28aecc1ac56fe56c2f1bf826ff649", "guid": "bfdfe7dc352907fc980b868725387e98f43fb4ce6e59c6298f3a2d8a071da41e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989453ae1133511b72ffeaeecb54ee387b", "guid": "bfdfe7dc352907fc980b868725387e983be461acc0e2a29aa50884aeacf1ff9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f75160d602fb8d0816819395aca0787", "guid": "bfdfe7dc352907fc980b868725387e985eea4a6bfab2f7cf80e0340e13c9b2c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b91221b054676074a6c30d6b28c55024", "guid": "bfdfe7dc352907fc980b868725387e983238693f4eb72f5526277e08c33e05a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a274b15d1965a41f102f6f978ffdaae0", "guid": "bfdfe7dc352907fc980b868725387e98db25c9099a54610666743bfe88f1354d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0f968be3d20a21e19a1735bfd35d6de", "guid": "bfdfe7dc352907fc980b868725387e9803a7449593c9d45bd13f69fca1f7cd1e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efdc2413f12fd7ceffb06d644fbef6f0", "guid": "bfdfe7dc352907fc980b868725387e9885c5397bd6c8a7b5b8c29026bb0a0054", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac158d492af72365c335d59bf7f32dbb", "guid": "bfdfe7dc352907fc980b868725387e983c9863f234ef2ce456a529eab158538a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f0b4dbfe1278a1309f5a26ed91be425", "guid": "bfdfe7dc352907fc980b868725387e9868c9d4ee5fee6e92004281c8cbc25fc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98129378f477d92927996ffbac36b7e576", "guid": "bfdfe7dc352907fc980b868725387e9845218e60bea5753b395f3ea8a6092380", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da07f5699f471e6ae9c391860cb16384", "guid": "bfdfe7dc352907fc980b868725387e989aee14c9036ccc7f0563e6b3e02d410c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874d1ef6411a9a44ba1f9f5fe9f9a2c38", "guid": "bfdfe7dc352907fc980b868725387e98ec69a6378ae52d1c02e4e0e92301e812", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983be2a2683f8307e67493735de6f7a744", "guid": "bfdfe7dc352907fc980b868725387e98b822375db78ffe1daf2e655b113b1b21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b7c0dffcf17b552e664169f9cd7a88d", "guid": "bfdfe7dc352907fc980b868725387e98c5c6fcb3ba3e3c85f3b987a5f72cb617", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985320588decfd72ce2f45c3893b738217", "guid": "bfdfe7dc352907fc980b868725387e98ba7c5c54554fd475511a75ba95b14251"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98313c5334a8093487d329c0d5518f6f3b", "guid": "bfdfe7dc352907fc980b868725387e98a602b4fd2a372e048534e96fc9bec38f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98553c50b5f8d3e0097422b51cf20ea8f6", "guid": "bfdfe7dc352907fc980b868725387e983e294da4966dd67c96aca4c7ec1b9934", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c20d42f4c3ed47324dd8b9778068778c", "guid": "bfdfe7dc352907fc980b868725387e981585980436b26a139cad61ffd5b6ce35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98745b6956af406557803e48ecedea8e13", "guid": "bfdfe7dc352907fc980b868725387e98ffd66246850da2ea309c3d5629b1fc84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b94e83c53af6fe802c168b81ad2b013d", "guid": "bfdfe7dc352907fc980b868725387e9851fda62e3b81293cc07780b4ebaa04a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98800d9ee126ed28f24745af7d5d42c4a3", "guid": "bfdfe7dc352907fc980b868725387e983fa66a0003ac24e9e2b831247a8a8d56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983459a2d8acac5e8a9d0a4d44bee2f8d1", "guid": "bfdfe7dc352907fc980b868725387e98951e7f042cb0f0735fe791e36c423e53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdc3845294c94233e0a16989df8bc5c8", "guid": "bfdfe7dc352907fc980b868725387e98eb2af365852577dd52db441a3c15bf44", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f91d12200149b623eb4acf70e5881946", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9807708e1e877b4debfbd737d55dd0e9ab", "guid": "bfdfe7dc352907fc980b868725387e98216c9588015b788a13b11295035c0be6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f0011b5ea6d0541d41f6dddbc7f4224", "guid": "bfdfe7dc352907fc980b868725387e98b81b25e8856f37630ea01d97f66fc584"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824dd4ebe1065262d6f09fed515c33aad", "guid": "bfdfe7dc352907fc980b868725387e981e97c98f69a84d96fc624765a2797a4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b251eb6aea0f8a7ec670542115c89a3", "guid": "bfdfe7dc352907fc980b868725387e98fb3b8853089fca2038804a511a526ccc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987160831d1fb8d6a24f3fbedfdfcb085c", "guid": "bfdfe7dc352907fc980b868725387e98dff55fde41aa51a5a753c813499c5f8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843cf6fc669b912641fd0cf9fa17359d6", "guid": "bfdfe7dc352907fc980b868725387e989649b9c4bca53bd2e210ce1159699dba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865adcb956c9fd7f669493eeb9c7c1556", "guid": "bfdfe7dc352907fc980b868725387e986ecfc97b448d3ead01e726d0c0f50adb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f39aadb9469f8a2667afdedfb68553e", "guid": "bfdfe7dc352907fc980b868725387e986ec9c002ae0f17bd58fe5b33a79a1b68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981595700ae078e263c9148e93f09abb4f", "guid": "bfdfe7dc352907fc980b868725387e9879179081a0d6e259354990952f7bc166"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fea3fdfb4fc4fcce77216db85b7df37", "guid": "bfdfe7dc352907fc980b868725387e9859dd27eaeb904902bd53075020d79527"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826287e09b2156acb425d9970993535f9", "guid": "bfdfe7dc352907fc980b868725387e98aea98db78d2d743113a2a286c701a141"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889dec225fd715eef87c7fa9e05e08dca", "guid": "bfdfe7dc352907fc980b868725387e9864ceb9ab92d7befd102a5c1ebb1f3c63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98420d7485553b2f64ce4628f5ff222496", "guid": "bfdfe7dc352907fc980b868725387e98607a57d1a56ea7ed983ed128627b105d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844d44ba10f7ed9ab4a42c7ca5baacbee", "guid": "bfdfe7dc352907fc980b868725387e9812b68be06a3a06278d40ec7e80fe1a88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f84398a429a22db13816f6cea3f5502", "guid": "bfdfe7dc352907fc980b868725387e989e81ecd9d1fa925cabbd487d210ada97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98475eeaff1868cb57c60ee2aa72452292", "guid": "bfdfe7dc352907fc980b868725387e984dd862db97637b623c7b7f231a55ee5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985761c9abe1bf1b578c48095bce5870c1", "guid": "bfdfe7dc352907fc980b868725387e98ae0d1a695711997e0e73cc28b3d91410"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826bc559549551dcb67d8e4da709bf674", "guid": "bfdfe7dc352907fc980b868725387e987b0454981b464b0560f36c5b3bd08309"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f8cc68cec3f51960f705e08293547a9", "guid": "bfdfe7dc352907fc980b868725387e98750304b0b8f160ad0a8430579c61ff35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899a2ac890362342c1f9b71bb4fb33036", "guid": "bfdfe7dc352907fc980b868725387e982b42b80d46770ba303db7047c780173a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bec20e5c978990d3543676ef2e9ea3be", "guid": "bfdfe7dc352907fc980b868725387e986fb1cadcebfad136cabf0363760b4d35"}], "guid": "bfdfe7dc352907fc980b868725387e98dd370e0e03666d24210f9904ef780c60", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9848de1bd9ed5cdf7b68e4d7ebbc2a7950"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "guid": "bfdfe7dc352907fc980b868725387e98e6960d06b867503063abc39098559ee6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f5979f0786f4a5ad9848f0349b1159", "guid": "bfdfe7dc352907fc980b868725387e98beb38a310e8d5976c44d482273c5729b"}], "guid": "bfdfe7dc352907fc980b868725387e988e34abc1e111c9af7607dbcfe4daa5bc", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987550f604c78112e8a4f75dba9294b80c", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9803810be05adb601d9ab9485699328c78", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}