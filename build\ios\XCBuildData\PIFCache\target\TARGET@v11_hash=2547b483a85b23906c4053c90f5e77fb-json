{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9884204cd6aa104364c2e477527188fa12", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bea8aea1c9963c48ea2d6f529bc89040", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983d114f1c3173e4f319f892fbdc03255f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9836a88502f9cbf89f6f8c11157d743490", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983d114f1c3173e4f319f892fbdc03255f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982a7695078fba560c4061236c62b0b04a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aee657079b9bceac467f60506a4b06ba", "guid": "bfdfe7dc352907fc980b868725387e980612119daee158212328983a0abfa07d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835dd4e8633d16c25350b7fba2a878a90", "guid": "bfdfe7dc352907fc980b868725387e98054d494365103d955bb1593b57d0cf62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db00b889a14c3649d55fff7fc5cc5719", "guid": "bfdfe7dc352907fc980b868725387e9873342b81cb202a3117be2848b376b8c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9ef7d732671eb3d0a34b31e90d54b13", "guid": "bfdfe7dc352907fc980b868725387e980129b944417a78b7a32ad1ec3715c65f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9c714999a911c940a6767f4a9f01f9e", "guid": "bfdfe7dc352907fc980b868725387e98a030ca97ac60b96c54659289b66a8074"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c726ba2b0a33bbeef90c9bcab9f747c", "guid": "bfdfe7dc352907fc980b868725387e989af83d892dd0c2c5bb704e46387f07f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bb8c5d18a7f6ebd62e357d30bcaedaf", "guid": "bfdfe7dc352907fc980b868725387e982e682a9d79e98264ea18b76303b2c129"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d248a97d2ca3ac2a2b98af15aa553be", "guid": "bfdfe7dc352907fc980b868725387e98794325f126f10b15178fb7376a5f194a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894f18e2c3928e1bdee1b7f1d357885f3", "guid": "bfdfe7dc352907fc980b868725387e984150d95035f6da849e0cf66f5cca7dca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1f0e56ea9b2a3043f4a075cce8e53e7", "guid": "bfdfe7dc352907fc980b868725387e98819647c8cb81eff07c1a5511bbe9e4ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bb679de99f4c17dc128e93c9a60069e", "guid": "bfdfe7dc352907fc980b868725387e9828b758024f47a241a8267691e88554da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836ae3cd56223e76c39f7db001823af9e", "guid": "bfdfe7dc352907fc980b868725387e98a1909687bde2cd3c2ecb7401510d5ee3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98128b921ca201e34f1a8316eb175ec7f4", "guid": "bfdfe7dc352907fc980b868725387e981e9bf370d5056c1c5598afe3709946b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844846d37a562e6f3d46419acd32733af", "guid": "bfdfe7dc352907fc980b868725387e98231460bd0c4cb1cc515b1058a2c18f20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b368e8857b1c594bdedc4a75634c0f30", "guid": "bfdfe7dc352907fc980b868725387e98e76e5356b518de79bc141e3954622d75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814a20795da1a2f66b9dbdda33a10e318", "guid": "bfdfe7dc352907fc980b868725387e987c2b366f344c4f24f19ba7af520f616f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2af3cb912a4f5c18ca76e008a4fe2d8", "guid": "bfdfe7dc352907fc980b868725387e98b4e28d06b8f81f7f1983cc40966f4d29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d1b7a1267e1b6a9243a0d1cdae9418a", "guid": "bfdfe7dc352907fc980b868725387e98b245a75838fc820919834dd1895ec876", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982272fac73988deeda62d4aa0117d81d9", "guid": "bfdfe7dc352907fc980b868725387e98c303f31228d0aa033e6749ecd6c4b27a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfac3f6143c16c923fcf762e4d889641", "guid": "bfdfe7dc352907fc980b868725387e981497935f662aec4e50a93397f253df0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8e7a62f0f3d0399eb19f091faaf8983", "guid": "bfdfe7dc352907fc980b868725387e9889011d4d94e269e17121c0701f9b8910"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb6bdacbc78b130e3ad2da4421b55719", "guid": "bfdfe7dc352907fc980b868725387e980e77985687adaa7fcb732b1072ec509a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cb7dd89f89abaa5e756a95ab76eed06", "guid": "bfdfe7dc352907fc980b868725387e98247388c9cbbe86ac70e7e891222ee763"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6c6818ae9bf86600aea43aebeaa22a4", "guid": "bfdfe7dc352907fc980b868725387e984fe977e63f7ab93ec7fc099c1f9f004d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863f53515517c888b886b000a3bcae2bd", "guid": "bfdfe7dc352907fc980b868725387e9841388b34dc65aa47e1464064f506293e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894c776a8e79bc377e5405bc05baecee9", "guid": "bfdfe7dc352907fc980b868725387e98b74cb559ac46ff0537b4fe4b046ecc7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2a7e7e50862657c9f1387176d22d980", "guid": "bfdfe7dc352907fc980b868725387e987dca5033e9063314da236afea6a75f3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f26bbe103c75633b13829fb6367a59d2", "guid": "bfdfe7dc352907fc980b868725387e9870d3699f0905a05b50334ab92e8c6acc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d0e5f617d43f4168fe2f94701f5d5e3", "guid": "bfdfe7dc352907fc980b868725387e98ec46c10d9f1947d5a5f797c3894f5292"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f16172d5edea2c71825852a35044f7c6", "guid": "bfdfe7dc352907fc980b868725387e98880470d6bf522baf93234a51d0e4ac48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888acb5c7d65934dc9423c4ab88eb2e24", "guid": "bfdfe7dc352907fc980b868725387e98261917c2cfe83199dbfb1c2f9849992d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb992d78e3d0f447f1c963f9ec7ef391", "guid": "bfdfe7dc352907fc980b868725387e988bbdb1e1f9feba14085099a119df72ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866d9ead331046b98281450a57699866e", "guid": "bfdfe7dc352907fc980b868725387e9888b67b3cd688dc9b84e27e1c53d449b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837ee63e64c240c818973bdd8f9427348", "guid": "bfdfe7dc352907fc980b868725387e98ea554a0cc56b82bf0d102d77c13c34c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983632731d6fa79363fedd60fda85da383", "guid": "bfdfe7dc352907fc980b868725387e9888beffafae3e934474e543927537f5fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853fb32366c2fb9db0b1721389a137c82", "guid": "bfdfe7dc352907fc980b868725387e989c6157f25a3e8e3e1c7b88fe8f8e7c25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecfd3522a004ffcaedde52d6a606f04e", "guid": "bfdfe7dc352907fc980b868725387e98d18af184d30e686d11e8d37eb14f8166"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dde3aabc2d70d8ceb1dde2f5fc4c76a1", "guid": "bfdfe7dc352907fc980b868725387e981b9441f8d23d54955bf7d301fe0aaae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98336009dfa7491e4791b809b0a018bc32", "guid": "bfdfe7dc352907fc980b868725387e984201d3d5c7febc13a7c866de3763aba4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984af926387ddccfa3e354dab1afe8b993", "guid": "bfdfe7dc352907fc980b868725387e9876576f8bb8db77654dbd427b9f147cfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98699ea07487a767da3848e5be732d2ba4", "guid": "bfdfe7dc352907fc980b868725387e98ab3285a9ed45aa274b2d15b752a1744d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1ecb51629cbf0be3b30aebb9e581286", "guid": "bfdfe7dc352907fc980b868725387e9819044949f1f8764f36622150acbffd4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983abeadb255baffcdd2a972606b39dfc3", "guid": "bfdfe7dc352907fc980b868725387e986944f8b53fb2b2d6d6383e32d9a0341d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849d2dbbf3fa97b3274b7f460668c2145", "guid": "bfdfe7dc352907fc980b868725387e98e45e24caef2ae5c20b535af57fc9f330"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f35b5aa8dab6444606ffb9f6be55817", "guid": "bfdfe7dc352907fc980b868725387e98c46789f02a7e6e7173f720e4384a2162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa247aeae3e5d955c323ad380700b27d", "guid": "bfdfe7dc352907fc980b868725387e983349e117c60ec42db26c4c6623143298"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecda56ca119921bd654c250ce8486636", "guid": "bfdfe7dc352907fc980b868725387e9854397dd805667605d768752029808aae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98613b12c9cd1de12835f4c45fb0078d6b", "guid": "bfdfe7dc352907fc980b868725387e9829ae40cbdfbebaefd55461c7686f3e45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5a1a51802337789a1d8bf37de04f9b5", "guid": "bfdfe7dc352907fc980b868725387e9858df57b8949c445e91c55423e906858f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98014e8a083fcd721f18ce445727d0ca1e", "guid": "bfdfe7dc352907fc980b868725387e98cadde985fb43aabb0816ea3ed2ad9dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98284c35f7557e22f396422b5fb81a7c1c", "guid": "bfdfe7dc352907fc980b868725387e98a00ffb874c78496f2c7baecfc8b292a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e52c891a08b02febe3fccbe03b6c250", "guid": "bfdfe7dc352907fc980b868725387e98992ef88af1db9b5f69c0237f88ed5cca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de97ce3ad92e21aa63635bbb0be35245", "guid": "bfdfe7dc352907fc980b868725387e98d774010404e43e23fed66035a9eb48ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aca2c3b427075b75eb9b1388674f13df", "guid": "bfdfe7dc352907fc980b868725387e98648e3c8addc35fd5ff7c7c25f21958fa"}], "guid": "bfdfe7dc352907fc980b868725387e98c8e1e753ff3a589d894f6c60e9047a0c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987ecd8851cc722c1d7958b4d1f82425f9", "guid": "bfdfe7dc352907fc980b868725387e98d6756bf20af130f143aaf7f5b895c2b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98582c0e3f1b14b78d8f53c731d58db2ba", "guid": "bfdfe7dc352907fc980b868725387e986b4385d29e1c926ad471893e1d445869"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984be04e5b53d670c1a8aea07e3086df09", "guid": "bfdfe7dc352907fc980b868725387e98662b907e081cdd65bf9237b3bedc61e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ff2103e5fa3c01db4afd3e0f8237b89", "guid": "bfdfe7dc352907fc980b868725387e98e72f73888188d0716d55ddcae84fbef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98590676e15a489fffb2247cbfb52aeec2", "guid": "bfdfe7dc352907fc980b868725387e9885d964a7a793c3fa21c8157ab7ba7f63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e1a005df9a88c8bdc4c5dab499e1064", "guid": "bfdfe7dc352907fc980b868725387e98fecf8e05273e7a25e818993f2b993f50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986086ccf3ba934b08a7618d3055e1cd55", "guid": "bfdfe7dc352907fc980b868725387e982fc8e35a13f15486c3939379a1f1e7d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b685c25aa8f20286adb91588f61521fd", "guid": "bfdfe7dc352907fc980b868725387e98d62b4d03020e6fdac0023223ec46e421"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882a6db40f4a6a4187d82f5234e261e9d", "guid": "bfdfe7dc352907fc980b868725387e9827f7f8800e2b891611bfa3a443137698"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf56374b0a7333e4302c3ec01caf9c0d", "guid": "bfdfe7dc352907fc980b868725387e98be66abd19517b953a326e5242181a04e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b48af2523f8f2599bef7a195594682f", "guid": "bfdfe7dc352907fc980b868725387e98b02eda3e2fe7b920e72dd52bba1dbafb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889278faec2c5d5d516bda0f1b793bcef", "guid": "bfdfe7dc352907fc980b868725387e984ec866e42a4b3d2a4c6bb7f89aeee999"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e343b1541f624132606f9f5e47851b0", "guid": "bfdfe7dc352907fc980b868725387e98f2128c6f5b3cab07cc1197e1ef13c5fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9f365af79f5e2168ce5977917e874d6", "guid": "bfdfe7dc352907fc980b868725387e983ce97f3e156e1abdabcbbaa08022fb06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c2131de9d48d23c4491a2daf2c308de", "guid": "bfdfe7dc352907fc980b868725387e9837421fe04a415e0c43d9deeb9f468f9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e528ae8f1baee0b2936950263cd92836", "guid": "bfdfe7dc352907fc980b868725387e98d8d17b7c4a952ef1e5bde2399ce9ad42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0fed263cc17fa2d727888f9da048b3e", "guid": "bfdfe7dc352907fc980b868725387e98fcc41c0349aa8c23494415196136faa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f624808e5b3653400ab8398bf9efdd03", "guid": "bfdfe7dc352907fc980b868725387e98be0d4f486630914cfcf3ad5ce3cec0df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca602f743c640a7935b38d911f998c49", "guid": "bfdfe7dc352907fc980b868725387e98e6843ce2f9698954e2cb94aa76f13bbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898623810f99bc7657e6b8a1d2d40d1ef", "guid": "bfdfe7dc352907fc980b868725387e98bee1fcecb75d91c1d3b9050cf41bd4d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc14f7ec62a7043d7af6d65523c1f710", "guid": "bfdfe7dc352907fc980b868725387e98371e75bd7fc2123f40115079f20baa85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa6e367d64e287332acb3889fcfff151", "guid": "bfdfe7dc352907fc980b868725387e9836b1b93fa9b3bcb6c061c6e5eaf2a290"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98863c3aa833e2c86190b657672e0dd697", "guid": "bfdfe7dc352907fc980b868725387e9849bb9df31c1c09c8eb8ea6bdce64b96b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896d1d264a5456d2469934c46e490a5bc", "guid": "bfdfe7dc352907fc980b868725387e986cc8cc446b41962c1f4a25234dc8b535"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811e67b6e88b8581bd1097d85145477d4", "guid": "bfdfe7dc352907fc980b868725387e986b884d4e01944bcd7d87ac0ec127ef0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850da8a550e78334ae4cc4ceefae66fc3", "guid": "bfdfe7dc352907fc980b868725387e985c8bec98454c6c4efbdb345216d325df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f773c27232db520a8c703f5c6673985", "guid": "bfdfe7dc352907fc980b868725387e981ce12836a26c079795def21019454e26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f76b68c48c454aa5f6fd75201f0bd93", "guid": "bfdfe7dc352907fc980b868725387e98e280038bebf406d45ed119fee700cdb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efad05e57961abe2b87c99691a3ec91e", "guid": "bfdfe7dc352907fc980b868725387e9888cf664b50f62cbdd6593b1c0ee9d4a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98079def78e51c041ed4d0da1b32efc923", "guid": "bfdfe7dc352907fc980b868725387e9831c14d601e8c9e7702f5ca8c7e840c5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b37e4b2f699f2a2fc41eccaa4f48def", "guid": "bfdfe7dc352907fc980b868725387e98c99b32c9e316656678713be7f7ccccdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98862a6c61ff4a50d9a3eebd086e221e8f", "guid": "bfdfe7dc352907fc980b868725387e983e40d2931c727d00cd182895cc32be96"}], "guid": "bfdfe7dc352907fc980b868725387e9842dad44d295d23585f55d261320bf37f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98d327de94ad8f5bf519074cb707d62e9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f5979f0786f4a5ad9848f0349b1159", "guid": "bfdfe7dc352907fc980b868725387e984bea0d6cf3462da0ffa0fcd296209ef7"}], "guid": "bfdfe7dc352907fc980b868725387e98707ad7787f1ee8d638e9c0c8cdde230f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98125f50b1ad3d0311f153df2abfff6c05", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e9882d6ffc4fa09e14d917481216615c6b3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}