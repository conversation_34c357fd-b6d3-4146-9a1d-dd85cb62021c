<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="vn.zenity.betacineplex.helper.support.TrailerPlayActivity"
    android:background="@android:color/transparent"
    android:fitsSystemWindows="true"
    android:clickable="true"
    android:focusable="true">

    <com.google.android.youtube.player.YouTubePlayerView
        android:id="@+id/playerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/btnClose"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:padding="10dp"
        android:layout_marginTop="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/bg_white_oval"
        app:srcCompat="@drawable/ic_close"/>

</androidx.constraintlayout.widget.ConstraintLayout>
