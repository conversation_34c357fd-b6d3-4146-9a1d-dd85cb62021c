<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/margin_small"
    app:cardCornerRadius="@dimen/radius_small"
    app:cardElevation="3dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="3dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvFilmName"
            style="@style/TextTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:textColor="@color/textBlack"
            app:fontFamily="@font/oswald_bold"
            app:layout_constraintBottom_toTopOf="@id/tvTime"
            app:layout_constraintEnd_toStartOf="@+id/view"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Pacific Rim: Trỗi Dậy" />

        <View
            android:id="@+id/view"
            android:layout_width="0.5dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/margin_normal"
            android:background="@color/grayLine"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/totalLl"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTime"
            style="@style/TextContent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="12dp"
            app:layout_constraintBottom_toTopOf="@id/tvCinemaName"
            app:layout_constraintEnd_toEndOf="@+id/tvFilmName"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvFilmName"
            tools:text="27/03/ 2018  |  7:00 PM" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCinemaName"
            style="@style/TextContent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginBottom="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/tvTime"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTime"
            tools:text="Beta Cineplex Mỹ Đình" />

        <LinearLayout
            android:id="@+id/totalLl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingLeft="7dp"
            android:paddingRight="7dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvPrice"
                android:layout_width="100dp"
                android:layout_height="40dp"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:textColor="@color/colorPrimaryDark"
                android:textSize="@dimen/font_extra_large"
                app:autoSizeMaxTextSize="@dimen/font_extra_large"
                app:autoSizeStepGranularity="1sp"
                app:autoSizeTextType="uniform"
                app:fontFamily="@font/oswald_regular"
                tools:text="1.200.000đ" />

            <LinearLayout
                android:id="@+id/llWaitingProcess"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:visibility="gone"
                tools:visibility="gone">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_transaction_waiting_status"
                    android:gravity="center"
                    android:paddingLeft="3dp"
                    android:paddingTop="3dp"
                    android:paddingRight="3dp"
                    android:paddingBottom="3dp"
                    android:text="@string/waiting_process"
                    android:textColor="#fd7c02"
                    app:fontFamily="@font/sanspro_semi_bold"
                    app:textAllCaps="true"
                    tools:text="Chờ xử lý" />
            </LinearLayout>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/textView5"
                style="@style/TextContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/acccumulated_points" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvDiemTichLuy"
                style="@style/TextContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#fd2802"
                android:textSize="@dimen/font_medium"
                app:layout_constraintEnd_toEndOf="@+id/textView5"
                app:layout_constraintStart_toStartOf="@+id/textView5"
                app:layout_constraintTop_toBottomOf="@+id/textView5"
                tools:text="108" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/textView6"
                style="@style/TextContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="@string/date_acccumulated_points" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvDateExpiredPoint"
                style="@style/TextContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_normal"
                android:textColor="@color/textff3377"
                app:layout_constraintEnd_toEndOf="@+id/textView6"
                app:layout_constraintStart_toStartOf="@+id/textView6"
                app:layout_constraintTop_toBottomOf="@+id/textView6"
                android:text="10/10/2023" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>