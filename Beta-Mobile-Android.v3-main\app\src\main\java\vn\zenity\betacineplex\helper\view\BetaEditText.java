package vn.zenity.betacineplex.helper.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Typeface;

import androidx.core.content.res.ResourcesCompat;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.appcompat.widget.AppCompatImageView;

import android.util.AttributeSet;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.RelativeLayout;

import vn.zenity.betacineplex.R;
import vn.zenity.betacineplex.helper.extension.ViewGroup_ExtensionsKt;

/**
 * Created by vinh on 4/7/18.
 */

public class BetaEditText extends RelativeLayout {

    private int icon = 0;
    private String text = "";
    private String hint = "";
    private EditText editText;
    private int inputType = -1;
    private int idNextFocus = -1;
    private int imeOptions = -1;

    public BetaEditText(Context context) {
        this(context, null);
    }

    public BetaEditText(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BetaEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs);
    }

    private void init(AttributeSet attrs) {
        if (attrs != null) {
            TypedArray styledAttributes = getContext().obtainStyledAttributes(attrs, R.styleable.BetaEditText, 0, 0);
            icon = styledAttributes.getResourceId(R.styleable.BetaEditText_btedIcon, 0);
            text = styledAttributes.getString(R.styleable.BetaEditText_btedText);
            hint = styledAttributes.getString(R.styleable.BetaEditText_btedHint);
            inputType = styledAttributes.getInt(R.styleable.BetaEditText_android_inputType, EditorInfo.TYPE_NULL);
            idNextFocus = styledAttributes.getInt(R.styleable.BetaEditText_android_nextFocusDown, -1);
            imeOptions = styledAttributes.getInt(R.styleable.BetaEditText_android_imeOptions, -1);
            styledAttributes.recycle();
        }
        View view = ViewGroup_ExtensionsKt.inflate(this, R.layout.layout_beta_edittext);
//        Drawable drawable = ResourcesCompat.getDrawable(getResources(), icon, null);
//        if (drawable != null) {
//        }
        ((AppCompatImageView)view.findViewById(R.id.ivIcon)).setImageResource(icon);
        editText = view.findViewById(R.id.edtContent);
        editText.setText(text);
        editText.setHint(hint);
        if (inputType > 0)
            editText.setInputType(inputType);
        if (idNextFocus >= 0) {
            editText.setNextFocusDownId(idNextFocus);
        }
        if (imeOptions >= 0) {
            editText.setImeOptions(imeOptions);
        }
        if (isInEditMode()) {
           editText.setTypeface(Typeface.create("sans-serif-condensed", Typeface.NORMAL));
        }
        addView(view);
    }

    public String getText() {
        return editText.getText().toString();
    }

    public void setText(String text) {
        editText.setText(text);
    }

    public void focus() {
        if (editText != null)
            editText.requestFocus();
        else {
            this.requestFocus();
        }
    }
}
