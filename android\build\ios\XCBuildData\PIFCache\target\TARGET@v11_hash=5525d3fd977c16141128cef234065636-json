{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981778913df8cb916e52b8d22bc6b2dc9c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980d3926596ad3cd9c175f9e28b372b54b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987238c73f0264a839debdffd36cf8227c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98288f36904c064e8ffee3307684f0adf4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987238c73f0264a839debdffd36cf8227c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ad21424d04da79e5b6b09fe13bb9d64", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9872d49e86baf12109c741085eb851c37f", "guid": "bfdfe7dc352907fc980b868725387e98c2ba2564738076858fe79d7046297cea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f26b819b8c2ecabbb204fa0268b56b0f", "guid": "bfdfe7dc352907fc980b868725387e98853b0e87dffa71f5b58aa19e5d8456db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833e3320dc0681cfdb827b986d18fd8c1", "guid": "bfdfe7dc352907fc980b868725387e98c0ffabfaa47270a182eb861dcb3c97ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb53959138015be13cec033d36f8066f", "guid": "bfdfe7dc352907fc980b868725387e98e4280c00d9dacc0d029f2a76fae53742", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98611ff547d7cfa702aaa50837ab226e74", "guid": "bfdfe7dc352907fc980b868725387e984ea3eecb0cd4d74cb5250b05948b72c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bd84680700c0977fc6547badc24c20c", "guid": "bfdfe7dc352907fc980b868725387e98b7ca25109c8d0cd0e6ec9cf85e326f07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a94b4c82ff779bc56843d4ec362f7c6c", "guid": "bfdfe7dc352907fc980b868725387e982b00b08c80673b7ac7763b5cca0c05c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98781dc1e0aea564d02a7d2c5e1c7cf12f", "guid": "bfdfe7dc352907fc980b868725387e9824f69690574f55583d0ec1eac6446085", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989acdb258b1b282cf76a2d3c294cffbbc", "guid": "bfdfe7dc352907fc980b868725387e98a3d6ceca4889b1cc6dce1a9db4b8f3b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985017be0569016c8e620bfde2d8091cf4", "guid": "bfdfe7dc352907fc980b868725387e980dcd742a45bc2b581179f4f31e84335f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e409ce32f820e7265f6ef19fea1c96d4", "guid": "bfdfe7dc352907fc980b868725387e988afd726ea2439bce89d32737dd29861e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d7cdd7ebc1c68f18b3f8e492f7d1225", "guid": "bfdfe7dc352907fc980b868725387e9837dc2cb26d47f125c3bfc8a42987c140", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983102938a64493f060fbe99af3259ed21", "guid": "bfdfe7dc352907fc980b868725387e984f13f038c098389b2cc3a7970f1ecf21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc7b0126333dc8a78d2a28a0d6c2ecf9", "guid": "bfdfe7dc352907fc980b868725387e98f57fd05847d719d2fed620540c65bc5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d31dae03c3c0fc97d4a0c5346bbd092e", "guid": "bfdfe7dc352907fc980b868725387e989d5a9b3c658ee5b10e84d06e2bf55ef9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd26d7a4e8384866227806bc07e879d3", "guid": "bfdfe7dc352907fc980b868725387e984df56e010584a7b7a75ec6e43e8e094b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876f97045859ee37709a4fe7c9c299585", "guid": "bfdfe7dc352907fc980b868725387e98a581ec0675bf26a52652879cab6ccb16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd3b93554e71e51225f5e26a4617ad11", "guid": "bfdfe7dc352907fc980b868725387e987b9e081c1f47b9b5c71ff6b7a6741b5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d0c6f0cb4bf191838ed4fd6bbf04d62", "guid": "bfdfe7dc352907fc980b868725387e98001b4d9e2a68f9da26c9df0f8dd22711", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983762507ea667e37a5805a10e18f14187", "guid": "bfdfe7dc352907fc980b868725387e98fd50f07295abcf055b5e4b7b2cb093c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98537e6e8af938311ac018d943f154cf44", "guid": "bfdfe7dc352907fc980b868725387e98ee7c0655d6fdf9bb0b15a2564e3001b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988973a0ddf76a8dd72278087c298e29ab", "guid": "bfdfe7dc352907fc980b868725387e9879f560ef014af1116b607e66871f8a4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98015fcb5b41c9b6f01497fc57494c0392", "guid": "bfdfe7dc352907fc980b868725387e983f72bbb6c37b6e76a32b4a1b51eac00f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844d0ee3bcf0227a44bd1e72187fed8c0", "guid": "bfdfe7dc352907fc980b868725387e987a6b0f9f4cd20dab7e492387fd96c668", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981664dfc7329363764fdacd5d07a0c8e8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987cc17d1e2539a8e1db94580c769f7caa", "guid": "bfdfe7dc352907fc980b868725387e982d80efaa8cac804b67f5522a9e5f081e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884cfea2c42013a4e63832bd694c478af", "guid": "bfdfe7dc352907fc980b868725387e98edb3b316fe792e12b5f399ed436ec453"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6426e5f6d6fc444d031f366de0312f4", "guid": "bfdfe7dc352907fc980b868725387e98205a3f2f01abd99df7d670b1e4bc5b4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825493f4f717caea9fa669d17d62b0f42", "guid": "bfdfe7dc352907fc980b868725387e98762ff973933a5ef20806850da244cc91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ccd4ba6e2160bc3a766f1ecd0656780", "guid": "bfdfe7dc352907fc980b868725387e9870291f334a3b4193c45c3ec4143223a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813a722cb58d104e57f1bf8ceb82baf06", "guid": "bfdfe7dc352907fc980b868725387e98d0fad16d3d7fdee4d59393a157398d1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a69cfa3fed0162b66b07ee3ea331cd9", "guid": "bfdfe7dc352907fc980b868725387e98e0db079f9fbde5fd4c4b8540dc0da824"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864d983f5c5aa9264a72c1d2b3d089b16", "guid": "bfdfe7dc352907fc980b868725387e98c8ce60cef79a7ce69fc2662a318d67f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dc2966ceebd4c61aed99421e7f41fe4", "guid": "bfdfe7dc352907fc980b868725387e9812895a73a0e03eaf508ddd3fb0d0c01f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f2f50e11b5d856f8c038a3fde226918", "guid": "bfdfe7dc352907fc980b868725387e989c97da061777c23cf2387296b895a4e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98168220c49441c65c7678cc911892b73e", "guid": "bfdfe7dc352907fc980b868725387e98f02f600f64d58e0f7d4eecbc25f04c7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861e0c1580874df57c638b40acbd07c62", "guid": "bfdfe7dc352907fc980b868725387e984c6a003f82bd71c2daa58df7da0f74fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824cfe925a1265804fc713e06b77a93aa", "guid": "bfdfe7dc352907fc980b868725387e9820a1666d38ed470527e26d3b96a3fec9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8298df76f2966adfe0905f2611601a8", "guid": "bfdfe7dc352907fc980b868725387e98cd466a315222b40faa33fc6bdc1fae43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98425101112663167cf613d03753ee122d", "guid": "bfdfe7dc352907fc980b868725387e98950a4366924646dd7ea62e7a738fc23f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cea8425531ea8eb9c4b67263702384d", "guid": "bfdfe7dc352907fc980b868725387e9830f1cedde3230afa0f52f0983ff82505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98520d1ca0792c38d2c719fbc287b91f77", "guid": "bfdfe7dc352907fc980b868725387e988b75dd9cae97808bca73ac3a51cbcd92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de78ba9bc7a9b0dd3227cb3cbb93933c", "guid": "bfdfe7dc352907fc980b868725387e98bc69122c6991dc655225143252842d7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98744422921dff312ed2055db32a419429", "guid": "bfdfe7dc352907fc980b868725387e985ad6ea797f8a9f84792fc3790ec5062f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d2a23eea40affa0e023e2fc08b14a57", "guid": "bfdfe7dc352907fc980b868725387e98895c6bd97422a8fbaa2f027ff69d57eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e29bf50fef2a8a315686dd4f440d5720", "guid": "bfdfe7dc352907fc980b868725387e98634b2be6a9361bf9e9f27f008ca68dd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceb70e1404b04bcdfabf5a5633724172", "guid": "bfdfe7dc352907fc980b868725387e9836e395285a4b0a2a22da5efef5807372"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98774dbeffd0de51b4f89b73013ddc1b85", "guid": "bfdfe7dc352907fc980b868725387e9830d50d3b8dada408fbd5ac3ca1c576d1"}], "guid": "bfdfe7dc352907fc980b868725387e98339f3c5c18cf261c37731f1ff9cd3c6b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98f76f428393514513ee220ee3cd45b4b7"}], "guid": "bfdfe7dc352907fc980b868725387e98cf9e1d75d6fd474380329cbf47d24d45", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98911b19d9ae38fc82e6a73bfd4cf29ba7", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98f5ee52bb1ad32275d40a0a37ff2d7e9c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}