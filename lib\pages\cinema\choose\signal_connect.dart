
import 'dart:async';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:signalr_netcore/signalr_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../constants/index.dart';
// typedef ListenerSignalRConnection = void Function(StateSignalR state);
// typedef ListenerData = void Function(SeatSignalrResponse data);
//
// enum StateSignalR { connected, disabled }
//
// class SeatSignalrResponse {
//   final String someString1;
//   final String someString2;
//   final int someInt1;
//   final int someInt2;
//
//   SeatSignalrResponse(this.someString1, this.someString2, this.someInt1, this.someInt2);
// }
//
// class SignalRService {
//   static final SignalRService _instance = SignalRService._internal();
//   factory SignalRService() => _instance;
//   SignalRService._internal();
//
//   final List<ListenerSignalRConnection> _listenerConnections = [];
//   final List<ListenerData> _listenerData = [];
//
//   HubConnection? _hubConnection;
//
//   final String _serverUrl = 'http://dev.betacineplex.vn/signalr';
//   // https://dev.cinemas.betacorp.vn
//   void addListenerConnection(ListenerSignalRConnection listener) {
//     _listenerConnections.add(listener);
//     if (_hubConnection?.state == HubConnectionState.connected) {
//       listener(StateSignalR.connected);
//     }
//   }
//
//   void removeListenerConnection(ListenerSignalRConnection listener) {
//     _listenerConnections.remove(listener);
//   }
//
//   Future<void> startSignalR() async {
//     _hubConnection = HubConnectionBuilder()
//         .withUrl(_serverUrl, HttpConnectionOptions(
//       transport: HttpTransportType.serverSentEvents,
//       logging: (level, message) => print(message),
//     ))
//         .build();
//
//     _hubConnection?.onclose((error) {
//       for (var listener in _listenerConnections) {
//         listener(StateSignalR.disabled);
//       }
//     });
//
//     _hubConnection?.on('broadcastMessage', (args) {
//       if (args != null && args.length >= 4) {
//         try {
//           final data = SeatSignalrResponse(
//             args[0] as String,
//             args[1] as String,
//             args[2] as int,
//             args[3] as int,
//           );
//           for (var listener in _listenerData) {
//             listener(data);
//           }
//         } catch (e) {
//           print('Error parsing message: $e');
//         }
//       }
//     });
//
//     await _hubConnection?.start();
//     _listenerConnections.forEach((listener) => listener(StateSignalR.connected));
//
//     // Gọi lại kết nối sau 5 giây nếu mất
//     Timer.periodic(Duration(seconds: 5), (timer) {
//       if (_hubConnection?.state == HubConnectionState.disconnected) {
//         reconnect();
//       }
//     });
//   }
//
//   void reconnect() async {
//     try {
//       await _hubConnection?.stop();
//       await _hubConnection?.start();
//     } catch (e) {
//       print('Reconnect error: $e');
//     }
//   }
//
//   void stop() async {
//     await _hubConnection?.stop();
//   }
//
//   void invoke(String event, List<Object> args) {
//     if (args.length >= 4) {
//       _hubConnection?.invoke(event, args: args);
//     }
//   }
//
//   void addDataListener(ListenerData listener) {
//     _listenerData.add(listener);
//   }
//
//   void removeDataListener(ListenerData listener) {
//     _listenerData.remove(listener);
//   }
//
//   String connectionId() {
//     return _hubConnection?.connectionId ?? '';
//   }
// }

/// Lớp dịch vụ SignalR cho ứng dụng Flutter
/// Kết hợp ưu điểm từ cả triển khai iOS và Android
class SignalRService {
  // Singleton pattern (từ cả iOS và Android)
  static SignalRService? _instance;
  static SignalRService get instance {
    _instance ??= SignalRService._internal();
    return _instance!;
  }

  SignalRService._internal();

  // Hub connection
  HubConnection? _hubConnection;

  // Listeners (từ Android)
  final List<Function(bool)> _connectionListeners = [];
  final List<Function(SeatSignalrResponse)> _dataListeners = [];

  // Hub name (từ cả iOS và Android)
  final String _hubName = 'chooseSeatHub';

  // Events (từ log.txt và Android)
  final String _broadcastMessageEvent = 'BroadcastMessage';
  final String _sendMessageEvent = 'SendMessage';

  // Cơ chế kiểm tra kết nối (từ Android)
  Timer? _reconnectTimer;
  final Duration _reconnectInterval = const Duration(seconds: 5);

  /// Khởi tạo và cấu hình logging
  void _setupLogging() {
    // Simplified logging approach
    debugPrint('SignalR: Logging initialized');
  }

  /// Log an info message
  void _logInfo(String message) {
    debugPrint('SignalR INFO: $message');
  }

  /// Log a warning message
  void _logWarning(String message) {
    debugPrint('SignalR WARNING: $message');
  }

  /// Log an error message
  void _logError(String message) {
    debugPrint('SignalR ERROR: $message');
  }

  /// Bắt đầu kết nối SignalR
  /// Triển khai dựa trên cách tiếp cận của iOS
  Future<void> startSignalR({String? customUrl}) async {
    _setupLogging();

    // Bypass SSL certificate verification for development
    // HttpOverrides.global = _DevHttpOverrides();

    // Sử dụng URL chính xác từ iOS implementation
    // Trong iOS, URL được định nghĩa trong Config.swift là:
    // static let SignalRURL = "http://dev.betacineplex.vn/signalr/hubs" (dev)
    // Thử cả HTTP và HTTPS
    final urls = [
      "https://www.betacinemas.vn/signalr/hubs/chooseSeatHub",
      // ApiService.signalRUrl,
    ];

    Exception? lastException;

    for (var url in urls) {
      try {
        _logInfo('Thử kết nối với URL: $url');

        // Cấu hình options cho kết nối
        // Trong iOS, không có cấu hình transport cụ thể, nên để mặc định
        final httpOptions = HttpConnectionOptions(
          logMessageContent: true,
          accessTokenFactory: () async => await _getAccessToken(),
        );

        // Tạo kết nối hub
        // Trong iOS, hub name là "chooseSeatHub"
        _hubConnection = HubConnectionBuilder()
            .withUrl(url, /*options: httpOptions,*/transportType: HttpTransportType.WebSockets)

            .withAutomaticReconnect(retryDelays: [2000, 5000, 10000, 20000])
            .build();

        // Cấu hình các event handlers
        _setupEventHandlers();

        // Bắt đầu kết nối
        if (_hubConnection != null) {
          try {
            _logInfo('Đang bắt đầu kết nối SignalR...');
            await _hubConnection!.start();

            _logInfo('Kết nối SignalR đã được thiết lập với URL: $url');

            // Bắt đầu kiểm tra kết nối định kỳ
            _startReconnectTimer();

            // Kết nối thành công, thoát khỏi vòng lặp
            return;
          } catch (error) {
            _logError('Lỗi khi bắt đầu kết nối: $error');
            rethrow;
          }
        }
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());
        _logWarning('Không thể kết nối với URL: $url. Lỗi: $e');

        // Đóng kết nối nếu đã được tạo
        await _hubConnection?.stop().catchError((error) {
          _logWarning('Lỗi khi đóng kết nối: $error');
        });
      }
    }

    // Nếu tất cả các cách kết nối đều thất bại, ném ngoại lệ cuối cùng
    _logError('Tất cả các cách kết nối SignalR đều thất bại');
    throw lastException ?? Exception('Không thể kết nối đến máy chủ SignalR');
  }

  /// Lấy token xác thực nếu cần (từ iOS)
  Future<String> _getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    String token = prefs.getString(CPref.token) ?? '';
    return token;
  }



  /// Thiết lập các event handlers cho kết nối
  /// Dựa trên cách triển khai của iOS
  void _setupEventHandlers() {
    // Xử lý khi kết nối được thiết lập lại
    _hubConnection!.onreconnecting(({error}) {
      _logInfo('Đang kết nối lại...');
      _notifyConnectionListeners(false);
    });

    _hubConnection!.onreconnected(({connectionId}) {
      _logInfo('Đã kết nối lại với ID: $connectionId');
      _notifyConnectionListeners(true);

      // Đăng ký lại sự kiện broadcast
      // Trong iOS, sự kiện là "broadcastMessage"
      _hubConnection!.on(_broadcastMessageEvent, _handleBroadcastMessage);
    });

    // Xử lý khi kết nối bị đóng
    _hubConnection!.onclose(({error}) {
      _logInfo('Kết nối đã đóng: ${error?.toString() ?? "Không có lỗi"}');
      _notifyConnectionListeners(false);

      // Thử kết nối lại sau khi đóng
      if (_reconnectTimer == null || !_reconnectTimer!.isActive) {
        _startReconnectTimer();
      }
    });

    // Đăng ký nhận sự kiện broadcastMessage
    // Trong iOS, sự kiện là "broadcastMessage"
    _hubConnection!.on(_broadcastMessageEvent, _handleBroadcastMessage);
  }

  /// Bắt đầu timer kiểm tra và kết nối lại (từ Android)
  void _startReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer.periodic(_reconnectInterval, (timer) async {
      if (_hubConnection?.state != HubConnectionState.Connected) {
        await reconnect();
      }
    });
  }

  /// Xử lý tin nhắn broadcast từ server
  /// Dựa trên cách triển khai của iOS trong ChooseSeatViewController.swift
  void _handleBroadcastMessage(List<Object?>? parameters) {
    if (parameters != null && parameters.length >= 4) {
      try {
        // Trong iOS:
        // let connectionId = args[safe: 0] as? String
        // let showId = args[safe: 1] as? String
        // let seatIndex = args[safe: 2] as? Int
        // var soldStatus = args[safe: 3] as? Int
        final connectionId = parameters[0].toString();
        final showId = parameters[1].toString();
        final seatIndex = int.parse(parameters[2].toString());
        final seatStatus = int.parse(parameters[3].toString());

        _logInfo('Nhận tin nhắn broadcast: connectionId=$connectionId, showId=$showId, seatIndex=$seatIndex, status=$seatStatus');

        // Kiểm tra nếu tin nhắn đến từ chính client này thì bỏ qua
        // Trong iOS: guard connectionId != self.hubConnection.connectionId, ...
        if (connectionId == this.connectionId()) {
          _logInfo('Bỏ qua tin nhắn từ chính client này');
          return;
        }

        final data = SeatSignalrResponse(
          connectionId,
          showId,
          seatIndex,
          seatStatus,
        );

        // Thông báo cho tất cả listeners
        for (var listener in _dataListeners) {
          listener(data);
        }
      } catch (e) {
        _logWarning('Lỗi khi xử lý tin nhắn broadcast: $e');
      }
    } else {
      _logWarning('Nhận tin nhắn broadcast không hợp lệ: $parameters');
    }
  }

  /// Thông báo cho tất cả connection listeners (từ Android)
  void _notifyConnectionListeners(bool isConnected) {
    for (var listener in _connectionListeners) {
      listener(isConnected);
    }
  }

  /// Đăng ký sự kiện (từ cả hai)
  void subscribe(String event) {
    _hubConnection?.on(event, _handleBroadcastMessage);
  }

  /// Hủy đăng ký sự kiện (từ cả hai)
  void unsubscribe(String event) {
    _hubConnection?.off(event);
  }

  /// Gọi phương thức trên server (kết hợp cả hai)
  Future<dynamic> invoke(String event, List<Object> args) async {
    try {
      // Sử dụng cú pháp của Flutter SignalR
      return await _hubConnection?.invoke(event, args: args);
    } catch (e) {
      _logError('Lỗi khi gọi phương thức $event: $e');
      rethrow;
    }
  }

  /// Gửi tin nhắn chọn/bỏ chọn ghế
  /// Dựa trên cách triển khai của iOS trong ChooseSeatViewController.swift
  Future<void> sendSeat(String showId, int seatIndex, int status) async {
    try {
      _logInfo('Gửi trạng thái ghế: showId=$showId, seatIndex=$seatIndex, status=$status');

      // Cấu trúc tham số giống iOS
      // Trong iOS: hub?.invoke(method: "sendMessage", withArgs: [hubConnection.connectionId ?? "", showTime?.showId ?? "", "\(seat.SeatIndex ?? 0)", "\(status)"])
      final args = [
        connectionId(),
        showId,
        seatIndex.toString(),
        status.toString()
      ];

      // Gọi phương thức SendMessage
      // Trong iOS, method là "sendMessage"
      await invoke(_sendMessageEvent, args);

      _logInfo('Đã gửi trạng thái ghế thành công');
    } catch (e) {
      _logWarning('Lỗi khi gửi trạng thái ghế: $e');
    }
  }

  /// Dừng kết nối (từ cả hai)
  Future<void> stop() async {
    try {
      _reconnectTimer?.cancel();
      _reconnectTimer = null;
      await _hubConnection?.stop();
    } catch (e) {
      _logWarning('Lỗi khi dừng kết nối: $e');
    }
  }

  /// Kết nối lại (từ Android)
  Future<void> reconnect() async {
    try {
      if (_hubConnection?.state == HubConnectionState.Connected) {
        return;
      }

      await stop();
      await Future.delayed(const Duration(seconds: 1));
      await startSignalR();
    } catch (e) {
      _logError('Lỗi khi kết nối lại: $e');
    }
  }

  /// Lấy ID kết nối (từ cả hai)
  String connectionId() {
    return _hubConnection?.connectionId ?? '';
  }

  /// Kiểm tra trạng thái kết nối (từ cả hai)
  bool isConnected() {
    return _hubConnection?.state == HubConnectionState.Connected;
  }

  /// Thêm listener kết nối (từ Android)
  void addConnectionListener(Function(bool) listener) {
    _connectionListeners.add(listener);
    if (isConnected()) {
      listener(true);
    }
  }

  /// Xóa listener kết nối (từ Android)
  void removeConnectionListener(Function(bool) listener) {
    _connectionListeners.remove(listener);
  }

  /// Thêm listener dữ liệu (từ Android)
  void addDataListener(Function(SeatSignalrResponse) listener) {
    _dataListeners.add(listener);
  }

  /// Xóa listener dữ liệu (từ Android)
  void removeDataListener(Function(SeatSignalrResponse) listener) {
    _dataListeners.remove(listener);
  }

  /// Giải phóng tài nguyên (từ cả hai)
  void dispose() {
    stop();
    _connectionListeners.clear();
    _dataListeners.clear();
  }
}

/// Model dữ liệu cho phản hồi từ SignalR (từ Android)
class SeatSignalrResponse {
  final String collectionId;
  final String showId;
  final int seatIndex;
  final int seatStatus;

  SeatSignalrResponse(
      this.collectionId,
      this.showId,
      this.seatIndex,
      this.seatStatus,
      );
}
