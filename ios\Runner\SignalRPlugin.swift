import Flutter
import UIKit
import SignalRSwift
import SwiftSignalRClient

class SignalRPlugin: NSObject, FlutterPlugin {
    private var hubConnection: HubConnection?
    private var hub: HubProxy?
    private var connectionStateEventSink: FlutterEventSink?
    private var dataEventSink: FlutterEventSink?
    private var timer: Timer?
    
    private let hubName = "chooseSeatHub"
    private let defaultUrl = "https://betacinemas.vn/signalr/hubs"
    
    static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "com.betacineplex/signalr", binaryMessenger: registrar.messenger())
        let instance = SignalRPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
        
        // Register event channels
        let connectionStateChannel = FlutterEventChannel(name: "com.betacineplex/signalr/connection_state", binaryMessenger: registrar.messenger())
        connectionStateChannel.setStreamHandler(ConnectionStateStreamHandler(plugin: instance))
        
        let dataChannel = FlutterEventChannel(name: "com.betacineplex/signalr/data", binaryMessenger: registrar.messenger())
        dataChannel.setStreamHandler(DataStreamHandler(plugin: instance))
    }
    
    func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "initialize":
            result(true)
        case "start":
            guard let args = call.arguments as? [String: Any],
                  let url = args["url"] as? String? else {
                startSignalR(url: defaultUrl, result: result)
                return
            }
            startSignalR(url: url ?? defaultUrl, result: result)
        case "stop":
            stopSignalR(result: result)
        case "sendSeat":
            guard let args = call.arguments as? [String: Any],
                  let showId = args["showId"] as? String,
                  let seatIndex = args["seatIndex"] as? Int,
                  let status = args["status"] as? Int else {
                result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
                return
            }
            sendSeat(showId: showId, seatIndex: seatIndex, status: status, result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func startSignalR(url: String, result: @escaping FlutterResult) {
        // Stop existing connection if any
        stopSignalR(result: { _ in })
        
        // Create new connection
        hubConnection = HubConnection(withUrl: url)
        hub = hubConnection?.createHubProxy(hubName: hubName)
        
        // Set up event handlers
        setupEventHandlers()
        
        // Start connection
        hubConnection?.start { error in
            if let error = error {
                print("Error starting SignalR: \(error)")
                result(FlutterError(code: "START_ERROR", message: "Failed to start SignalR connection", details: error.localizedDescription))
            } else {
                print("SignalR connection started")
                self.startKeepAliveTimer()
                result(true)
            }
        }
    }
    
    private func setupEventHandlers() {
        // Handle connection state changes
        hubConnection?.started = { [weak self] in
            guard let self = self else { return }
            DispatchQueue.main.async {
                let data: [String: Any] = [
                    "state": 2, // Connected
                    "connectionId": self.hubConnection?.connectionId ?? ""
                ]
                self.connectionStateEventSink?(data)
            }
            
            // Subscribe to broadcast message
            try? self.hub?.on(method: "broadcastMessage", callback: { (response: Any?, error: Error?) in
                // Handle broadcast message
            })
        }
        
        hubConnection?.reconnecting = { [weak self] in
            guard let self = self else { return }
            DispatchQueue.main.async {
                let data: [String: Any] = [
                    "state": 3, // Reconnecting
                    "connectionId": self.hubConnection?.connectionId ?? ""
                ]
                self.connectionStateEventSink?(data)
            }
        }
        
        hubConnection?.closed = { [weak self] in
            guard let self = self else { return }
            DispatchQueue.main.async {
                let data: [String: Any] = [
                    "state": 0, // Disconnected
                    "connectionId": self.hubConnection?.connectionId ?? ""
                ]
                self.connectionStateEventSink?(data)
            }
        }
        
        // Handle received data
        hubConnection?.received = { [weak self] response in
            guard let self = self else { return }
            
            if let jsonObject = response as? [String: Any],
               let aArray = jsonObject["A"] as? [Any],
               aArray.count >= 4 {
                
                let connectionId = aArray[0] as? String ?? ""
                let showId = aArray[1] as? String ?? ""
                let seatIndex = aArray[2] as? Int ?? 0
                let seatStatus = aArray[3] as? Int ?? 0
                
                let data: [String: Any] = [
                    "connectionId": connectionId,
                    "showId": showId,
                    "seatIndex": seatIndex,
                    "seatStatus": seatStatus
                ]
                
                DispatchQueue.main.async {
                    self.dataEventSink?(data)
                }
            }
        }
    }
    
    private func startKeepAliveTimer() {
        timer?.invalidate()
        timer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            guard let self = self,
                  self.hubConnection != nil else {
                return
            }
            
            // Send a keep-alive message
            try? self.hub?.invoke(method: "ping")
        }
    }
    
    private func stopSignalR(result: @escaping FlutterResult) {
        timer?.invalidate()
        timer = nil
        
        hubConnection?.stop()
        hubConnection = nil
        hub = nil
        
        result(true)
    }
    
    private func sendSeat(showId: String, seatIndex: Int, status: Int, result: @escaping FlutterResult) {
        guard let hub = hub, hubConnection != nil else {
            result(FlutterError(code: "NOT_CONNECTED", message: "SignalR is not connected", details: nil))
            return
        }
        
        let args: [Any] = [
            hubConnection?.connectionId ?? "",
            showId,
            "\(seatIndex)",
            "\(status)"
        ]
        
        try? hub.invoke(method: "sendMessage", withArgs: args) { response, error in
            if let error = error {
                print("Error sending seat: \(error)")
                result(FlutterError(code: "SEND_ERROR", message: "Failed to send seat data", details: error.localizedDescription))
            } else {
                result(true)
            }
        }
    }
    
    func setConnectionStateEventSink(_ eventSink: @escaping FlutterEventSink) {
        connectionStateEventSink = eventSink
    }
    
    func setDataEventSink(_ eventSink: @escaping FlutterEventSink) {
        dataEventSink = eventSink
    }
}

// Stream handler for connection state events
class ConnectionStateStreamHandler: NSObject, FlutterStreamHandler {
    private let plugin: SignalRPlugin
    
    init(plugin: SignalRPlugin) {
        self.plugin = plugin
        super.init()
    }
    
    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        plugin.setConnectionStateEventSink(events)
        return nil
    }
    
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        plugin.setConnectionStateEventSink({ _ in })
        return nil
    }
}

// Stream handler for data events
class DataStreamHandler: NSObject, FlutterStreamHandler {
    private let plugin: SignalRPlugin
    
    init(plugin: SignalRPlugin) {
        self.plugin = plugin
        super.init()
    }
    
    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        plugin.setDataEventSink(events)
        return nil
    }
    
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        plugin.setDataEventSink({ _ in })
        return nil
    }
}
