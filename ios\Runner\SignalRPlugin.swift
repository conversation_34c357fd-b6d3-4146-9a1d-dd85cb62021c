import Foundation
import Flutter
import SignalRSwift
import SwiftSignalRClient

/// A Flutter plugin for SignalR
@objc public class SignalRPlugin: NSObject, FlutterPlugin {
    // MARK: - Properties

    /// The SignalR service (sử dụng SignalRService mới thay vì SignalRClient)
    private let signalRService = SignalRService(enableLogging: true)

    /// The method channel for communicating with Flutter
    private var methodChannel: FlutterMethodChannel?

    /// The event channel for sending events to Flutter
    private var eventChannel: FlutterEventChannel?

    /// The event sink for sending events to Flutter
    private var eventSink: FlutterEventSink?

    /// Enable logging
    private let enableLogging: Bool = true

    // MARK: - Logging

    /// Log a message
    /// - Parameter message: The message to log
    private func log(_ message: String) {
        if enableLogging {
            print("SignalRPlugin: \(message)")
        }
    }

    // MARK: - Plugin Registration

    /// Register the plugin with Flutter
    /// - Parameter registrar: The Flutter plugin registrar
    public static func register(with registrar: FlutterPluginRegistrar) {
        print("SignalRPlugin: Registering plugin")

        // Create the method and event channels
        let channel = FlutterMethodChannel(name: "com.betacinemas.signalr/methods", binaryMessenger: registrar.messenger())
        let eventChannel = FlutterEventChannel(name: "com.betacinemas.signalr/events", binaryMessenger: registrar.messenger())

        // Create the plugin instance
        let instance = SignalRPlugin()
        instance.methodChannel = channel
        instance.eventChannel = eventChannel

        // Register the plugin with Flutter
        registrar.addMethodCallDelegate(instance, channel: channel)
        eventChannel.setStreamHandler(instance)

        print("SignalRPlugin: Plugin registered successfully")
    }

    // MARK: - Method Handling

    /// Handle method calls from Flutter
    /// - Parameters:
    ///   - call: The method call
    ///   - result: The result callback
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        log("Handling method call: \(call.method)")

        switch call.method {
        case "connect":
            handleConnect(call, result: result)
        case "disconnect":
            handleDisconnect(result: result)
        case "invoke":
            handleInvoke(call, result: result)
        case "on":
            handleOn(call, result: result)
        case "off":
            handleOff(call, result: result)
        case "getConnectionId":
            handleGetConnectionId(result: result)
        case "isConnected":
            handleIsConnected(result: result)
        default:
            log("Method not implemented: \(call.method)")
            result(FlutterMethodNotImplemented)
        }
    }

    /// Handle the connect method
    /// - Parameters:
    ///   - call: The method call
    ///   - result: The result callback
    private func handleConnect(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        log("Handling connect method")

        // Validate arguments
        guard let args = call.arguments as? [String: Any],
              let url = args["url"] as? String else {
            log("Invalid arguments: URL is required")
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "URL is required", details: nil))
            return
        }

        log("Connect URL: \(url)")
        let headers = args["headers"] as? [String: String] ?? [:]
        log("Connect headers: \(headers)")

        // Set up the message callback
        signalRService.setMessageCallback { [weak self] connectionId, showId, seatIndex, status in
            guard let self = self else { return }

            self.log("Received message: connectionId=\(connectionId), showId=\(showId), seatIndex=\(seatIndex), status=\(status)")

            self.sendEvent(eventName: "hubMethod", data: [
                "method": "broadcastMessage",
                "arguments": [connectionId, showId, seatIndex, status]
            ])
        }

        // Set up the connection state callback
        signalRService.setConnectionStateCallback { [weak self] isConnected in
            guard let self = self else { return }

            self.log("Connection state changed: isConnected=\(isConnected)")

            self.sendEvent(eventName: "connectionState", data: [
                "connected": isConnected,
                "connectionId": self.signalRService.getConnectionId() ?? ""
            ])
        }

        // Connect to the SignalR hub
        log("Connecting to SignalR hub...")
        signalRService.connect(url: url, headers: headers) { success, error in
            if success {
                self.log("Connection successful, connectionId: \(self.signalRService.getConnectionId() ?? "unknown")")
                result(["success": true, "connectionId": self.signalRService.getConnectionId() ?? ""])
            } else {
                self.log("Connection failed: \(error ?? "Unknown error")")
                result(FlutterError(code: "CONNECTION_ERROR", message: error ?? "Unknown error", details: nil))
            }
        }
    }

    /// Handle the disconnect method
    /// - Parameter result: The result callback
    private func handleDisconnect(result: @escaping FlutterResult) {
        log("Handling disconnect method")
        signalRService.disconnect()
        result(nil)
    }

    /// Handle the invoke method
    /// - Parameters:
    ///   - call: The method call
    ///   - result: The result callback
    private func handleInvoke(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        log("Handling invoke method")

        // Validate arguments
        guard let args = call.arguments as? [String: Any],
              let method = args["method"] as? String else {
            log("Invalid arguments: Method name is required")
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Method name is required", details: nil))
            return
        }

        let arguments = args["arguments"] as? [Any] ?? []
        log("Invoking method: \(method) with arguments: \(arguments)")

        // Kiểm tra nếu đây là phương thức sendMessage với đúng tham số
        if method == "sendMessage" && arguments.count >= 4,
           let connectionId = arguments[0] as? String,
           let showId = arguments[1] as? String,
           let seatIndexStr = arguments[2] as? String,
           let statusStr = arguments[3] as? String,
           let seatIndex = Int(seatIndexStr),
           let status = Int(statusStr) {

            // Sử dụng phương thức sendMessage của SignalRService
            signalRService.sendMessage(connectionId: connectionId, showId: showId, seatIndex: seatIndex, status: status) { error in
                if let error = error {
                    self.log("Invoke error: \(error.localizedDescription)")
                    result(FlutterError(code: "INVOKE_ERROR", message: error.localizedDescription, details: nil))
                    return
                }

                self.log("Invoke successful")
                result(nil)
            }
        } else {
            // Phương thức không được hỗ trợ
            self.log("Method not supported: \(method)")
            result(FlutterError(code: "METHOD_NOT_SUPPORTED", message: "Method not supported: \(method)", details: nil))
        }
    }

    /// Handle the on method
    /// - Parameters:
    ///   - call: The method call
    ///   - result: The result callback
    private func handleOn(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        log("Handling on method")

        // Validate arguments
        guard let args = call.arguments as? [String: Any],
              let method = args["method"] as? String else {
            log("Invalid arguments: Method name is required")
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Method name is required", details: nil))
            return
        }

        log("Registering handler for method: \(method)")

        // Không cần đăng ký handler vì SignalRService đã xử lý sự kiện broadcastMessage
        // Chỉ hỗ trợ phương thức broadcastMessage
        if method == "broadcastMessage" {
            self.log("Handler for broadcastMessage already registered in SignalRService")
            result(nil)
        } else {
            self.log("Method not supported for on: \(method)")
            result(FlutterError(code: "METHOD_NOT_SUPPORTED", message: "Only broadcastMessage is supported", details: nil))
        }
    }

    /// Handle the off method
    /// - Parameters:
    ///   - call: The method call
    ///   - result: The result callback
    private func handleOff(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        log("Handling off method")

        // Validate arguments
        guard let args = call.arguments as? [String: Any],
              let method = args["method"] as? String else {
            log("Invalid arguments: Method name is required")
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Method name is required", details: nil))
            return
        }

        log("Unregistering handler for method: \(method)")

        // Không cần hủy đăng ký handler vì SignalRService đã xử lý sự kiện broadcastMessage
        // Chỉ hỗ trợ phương thức broadcastMessage
        if method == "broadcastMessage" {
            self.log("No need to unregister handler for broadcastMessage")
            result(nil)
        } else {
            self.log("Method not supported for off: \(method)")
            result(FlutterError(code: "METHOD_NOT_SUPPORTED", message: "Only broadcastMessage is supported", details: nil))
        }
    }

    /// Handle the getConnectionId method
    /// - Parameter result: The result callback
    private func handleGetConnectionId(result: @escaping FlutterResult) {
        log("Handling getConnectionId method")
        result(signalRService.getConnectionId())
    }

    /// Handle the isConnected method
    /// - Parameter result: The result callback
    private func handleIsConnected(result: @escaping FlutterResult) {
        log("Handling isConnected method")
        result(signalRService.isConnectedStatus())
    }

    // MARK: - Event Handling

    /// Send an event to Flutter
    /// - Parameters:
    ///   - eventName: The name of the event
    ///   - data: The data to send with the event
    private func sendEvent(eventName: String, data: [String: Any]) {
        log("Sending event: \(eventName)")

        guard let eventSink = eventSink else {
            log("Cannot send event: eventSink is nil")
            return
        }

        var eventData: [String: Any] = ["event": eventName]
        for (key, value) in data {
            eventData[key] = value
        }

        log("Event data: \(eventData)")

        // Make sure we're on the main thread when sending events to Flutter
        DispatchQueue.main.async {
            self.eventSink?(eventData)
        }
    }
}

// MARK: - FlutterStreamHandler
extension SignalRPlugin: FlutterStreamHandler {
    /// Called when Flutter starts listening to the event channel
    /// - Parameters:
    ///   - arguments: Arguments from Flutter
    ///   - events: The event sink to send events to
    /// - Returns: An error if the operation fails
    public func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        log("Event channel onListen called")
        eventSink = events
        return nil
    }

    /// Called when Flutter stops listening to the event channel
    /// - Parameter arguments: Arguments from Flutter
    /// - Returns: An error if the operation fails
    public func onCancel(withArguments arguments: Any?) -> FlutterError? {
        log("Event channel onCancel called")
        eventSink = nil
        return nil
    }
}
