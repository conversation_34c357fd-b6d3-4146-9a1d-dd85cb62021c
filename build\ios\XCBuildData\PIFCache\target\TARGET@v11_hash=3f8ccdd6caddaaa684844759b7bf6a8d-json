{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a183d4a6a4a6186483f8998e5696615c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986552a01a408b6a4e689901595e49705c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a79650e48a1f898150d54b4be19a334", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a613bf16661a546161719dbeb1b3c3b1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a79650e48a1f898150d54b4be19a334", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f5c4ea07889e934fd702982c65bcc31d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac7987110289bde1cb915a13c203caf2", "guid": "bfdfe7dc352907fc980b868725387e98bd239942e99fb540ed7c9c401a02e854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ced8f6ebae5293451c834e17258306c", "guid": "bfdfe7dc352907fc980b868725387e98e29b43a1963aa11ac1ccfe60d881bad5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ef8c12edc0194f1f2aef29d34538069", "guid": "bfdfe7dc352907fc980b868725387e98c149783b2cd5d5b43dd57b256a8f048e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cdfae6f4c0290ec8126a7583ff75752", "guid": "bfdfe7dc352907fc980b868725387e985b313cf5c1df2db6a350f340c12159bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980697d39d8da4e5368fc676fdec088d66", "guid": "bfdfe7dc352907fc980b868725387e98a922cf91e876b060d202d484ae263d90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889a9f324863cff520fb9b4db7768391c", "guid": "bfdfe7dc352907fc980b868725387e98a0cc4f045e5361e9f7ab1d688024e89b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c625a403ef04b03d9f97c0ce0a63d30", "guid": "bfdfe7dc352907fc980b868725387e98a948b02e13557ea90849422ec620434c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f08193fcac620a849d48e1166bab92f5", "guid": "bfdfe7dc352907fc980b868725387e98f82c6dd183b4fd84f4729991eaa70640"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13b2c7678a8ad3a7e3b218fef4d70e5", "guid": "bfdfe7dc352907fc980b868725387e989a51826b0d1c3ba809c285f30e355a1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809ceb4af6a574546902b800e26065723", "guid": "bfdfe7dc352907fc980b868725387e98ddd89a02c213b4f9b62db3c9b5710bbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98836d7727d03ad24eabd3672301d3f6ed", "guid": "bfdfe7dc352907fc980b868725387e9862b35b03bcf068b48cc5f7d79ccd696c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98149b4ccda2204efd762ead1fc12f8c26", "guid": "bfdfe7dc352907fc980b868725387e98ca1423bb28e28840983b1b20b2f409b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ead9a58c79f524d3197499fc071f7d8", "guid": "bfdfe7dc352907fc980b868725387e98333ec2233225a6c8cec618c6a6ec95af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986104752770c62a074bd26d0ee0c27c8f", "guid": "bfdfe7dc352907fc980b868725387e9854a3ad90f713764df65aeaadd61c5b12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a87c23fee5a02adc80570c3fbdd0517e", "guid": "bfdfe7dc352907fc980b868725387e9806467d6fadf4a3d6916ca5b1041ca8a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a91a672e39141a01e83fd87ab928f6c2", "guid": "bfdfe7dc352907fc980b868725387e98e02001ee2c6d4e9d1292b745cb124b8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13cee51f1ce1d3f8c6801afc9deefab", "guid": "bfdfe7dc352907fc980b868725387e9855f5fc85c7459da8ee1438275ff3acca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1a90aa36b8ec49069645a2113163e28", "guid": "bfdfe7dc352907fc980b868725387e9800c5491b80515d678e18ac8556f33dff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98475b91b482c50523ab95c8c44b046a1d", "guid": "bfdfe7dc352907fc980b868725387e985a68d0b3d36b52d63b2ec5556a91666e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98056d69dd462d37a398e9b5b6d12198a0", "guid": "bfdfe7dc352907fc980b868725387e984b36a8ec8c3456035df5329053c1febd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880d26a6894f78e4922da5c35d37c5b36", "guid": "bfdfe7dc352907fc980b868725387e98b5fe547f580b68b8452476a4a883ba0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b665c354b001880c853966ac95a7c263", "guid": "bfdfe7dc352907fc980b868725387e98e742ab7958def7712ea7ec4f4de653d1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cca911fe8c4d86e5ccca2c57fc142d3a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b156318955c8ca4fa72211f0efa2019a", "guid": "bfdfe7dc352907fc980b868725387e985facad78681e27c6f57d0d8bcb62cd4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881485fbd4990bb43c5c2664246ce4d0a", "guid": "bfdfe7dc352907fc980b868725387e98a0894cf2d6c3ccfd8d8cc66fa1ed1ed2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810f2416b592363278904460f663076ef", "guid": "bfdfe7dc352907fc980b868725387e98299ce254f5748940591df54c81afe010"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f78394f596db0f4f2bfbf9aa5314c03d", "guid": "bfdfe7dc352907fc980b868725387e98f9d2af8dff33633fce615ce97c401cb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817fccf4c70181867edc21af964c6fae2", "guid": "bfdfe7dc352907fc980b868725387e98795935466d4f47899191d5bbc493b7ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac3a92ab2c6f6dce447ef98fed5d0f4", "guid": "bfdfe7dc352907fc980b868725387e98d16046fd28834c4e7384a67640f72899"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d04b27b445fba70700de9ded890d4c58", "guid": "bfdfe7dc352907fc980b868725387e9887f4ac2c23363a705c5a808fa26e5e84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f13af427095a500fc5651b2524148c4e", "guid": "bfdfe7dc352907fc980b868725387e989fa37cd6a60fafe2e01b7df6e2d28776"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d63ce54dea0bc1badfddd19cc34e9dd", "guid": "bfdfe7dc352907fc980b868725387e98841e44d2c6941d13c8525ef7820d5042"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986191f2fce5b521e736c850cb7a1a2195", "guid": "bfdfe7dc352907fc980b868725387e98c02ca332d09a9948ff8f6939a0213652"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f993c504419d82e07d6beb71772e51f", "guid": "bfdfe7dc352907fc980b868725387e98bf096d6ae58a0cf9e8bcb4b7140d7b7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3809dc1a75eb58b4eb13ed4cc5fbd2b", "guid": "bfdfe7dc352907fc980b868725387e9823d3880edd0817106366e57dd2229cbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff6f3a6045ca98cf8d43d3e4278f329e", "guid": "bfdfe7dc352907fc980b868725387e98be254cd366bb18781ee648cb847591f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887853f203f6e677eb7bf46091cb9c85d", "guid": "bfdfe7dc352907fc980b868725387e9841116aa594e1ca7990893a7d802df091"}], "guid": "bfdfe7dc352907fc980b868725387e98b4eaebe77c6d9c9ad84babe449b345f1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e985423dd616c87f2e2111812626a5f9fce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d391d713992f1d4e99af11b30a3d8fcd", "guid": "bfdfe7dc352907fc980b868725387e98b2db58a8c45a8283e522cd8baaf2a2a5"}], "guid": "bfdfe7dc352907fc980b868725387e982efb448d26721a72aa23c17ab0769a8a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9891f1c6f47ea47590951ef2dff51745c3", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98baf2fc1d6f0f79b8a55c251b39a985d3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}