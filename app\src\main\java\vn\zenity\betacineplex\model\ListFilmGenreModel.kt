package vn.zenity.betacineplex.model

import vn.zenity.betacineplex.app.App

/**
 * Created by tinhvv on 4/11/18.
 */
class ListFilmGenreModel {
    var GenreId : String? = null
    var Code : String? = null
    var Name : String? = null
        get() {
            if (!App.shared().isLangVi() && Name_F != null) {
                return Name_F
            }
            return field
        }
    var Name_F : String? = null
    var Order : Int? = null
}