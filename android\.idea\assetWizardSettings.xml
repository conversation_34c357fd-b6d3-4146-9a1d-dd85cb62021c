<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="WizardSettings">
    <option name="children">
      <map>
        <entry key="imageWizard">
          <value>
            <PersistentState>
              <option name="children">
                <map>
                  <entry key="imageAssetPanel">
                    <value>
                      <PersistentState>
                        <option name="children">
                          <map>
                            <entry key="actionbar">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="clipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="C:\Users\<USER>\AppData\Local\Temp\ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="text">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="textAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="launcher">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="foregroundClipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="imagePath" value="C:\Users\<USER>\AppData\Local\Temp\ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundImage">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="D:\geneat\beta-moible-flutter\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" />
                                                <entry key="scalingPercent" value="99" />
                                                <entry key="trimmed" value="true" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundText">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundTextAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                  <option name="values">
                                    <map>
                                      <entry key="backgroundAssetType" value="COLOR" />
                                      <entry key="backgroundColor" value="015197" />
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="launcherLegacy">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="clipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="C:\Users\<USER>\AppData\Local\Temp\ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="text">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="textAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="notification">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="clipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="C:\Users\<USER>\AppData\Local\Temp\ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="text">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="textAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="tvBanner">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="foregroundText">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="tvChannel">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="foregroundClipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="imagePath" value="C:\Users\<USER>\AppData\Local\Temp\ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundImage">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundText">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundTextAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                          </map>
                        </option>
                      </PersistentState>
                    </value>
                  </entry>
                </map>
              </option>
            </PersistentState>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>