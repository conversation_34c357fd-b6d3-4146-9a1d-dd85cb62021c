import 'package:flutter/material.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

import '../../../constants/index.dart';

class FAQDetailScreen extends StatefulWidget {
  final String topicId;
  final String topicTitle;

  const FAQDetailScreen({
    super.key,
    required this.topicId,
    required this.topicTitle,
  });

  @override
  State<FAQDetailScreen> createState() => _FAQDetailScreenState();
}

class _FAQDetailScreenState extends State<FAQDetailScreen> {
  bool _isLoading = true;
  List<FAQDetail> _faqDetails = [];
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchFAQDetails();
  }

  Future<void> _fetchFAQDetails() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await RepositoryProvider.of<Api>(context).other.getFAQs(
        id: widget.topicId,
      );

      if (result != null) {
        final List<dynamic> detailsData = result.data['content'] ?? [];
        setState(() {
          _faqDetails = detailsData.map((item) => FAQDetail.fromJson(item)).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
          _error = 'Failed to load FAQ details';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade200,
      appBar: appBar(
        title: 'FAQ - ${widget.topicTitle}',
        titleStyle: const TextStyle(color: Colors.white, fontSize: CFontSize.xl),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(_error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchFAQDetails,
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    if (_faqDetails.isEmpty) {
      return const Center(child: Text('Không có câu hỏi thường gặp'));
    }

    return ListView.builder(
      itemCount: _faqDetails.length,
      itemBuilder: (context, index) {
        final faq = _faqDetails[index];
        return _buildFAQItem(faq, index);
      },
    );
  }

  Widget _buildFAQItem(FAQDetail faq, int index) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ExpansionTile(
        title: Text(
          faq.title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontFamily: 'SourceSansPro',
            fontSize: 16,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: faq.answerContent != null && faq.answerContent!.isNotEmpty
                ? Html(
                    data: faq.answerContent!,
                    style: {
                      "body": Style(
                        fontSize: FontSize(16),
                        fontFamily: 'SourceSansPro',
                        textAlign: TextAlign.justify,
                      ),
                    },
                  )
                : const Text('Không có nội dung chi tiết'),
          ),
        ],
      ),
    );
  }
}

class FAQDetail {
  final String id;
  final String title;
  final String? answerContent;

  FAQDetail({
    required this.id,
    required this.title,
    this.answerContent,
  });

  factory FAQDetail.fromJson(Map<String, dynamic> json) {
    return FAQDetail(
      id: json['Id'] ?? '',
      title: json['Title'] ?? '',
      answerContent: json['AnswerContent'],
    );
  }
}
