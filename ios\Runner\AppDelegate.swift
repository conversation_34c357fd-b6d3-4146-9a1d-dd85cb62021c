import UIKit
import Flutter
import Firebase
import GoogleMaps
import Foundation
import UserNotifications
// This is needed to access our SignalR classes

@main
@objc class AppDelegate: FlutterAppDelegate, MessagingDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    FirebaseApp.configure()
    Messaging.messaging().delegate = self
    // Flutter handles notification permissions and registration
    // Minimal setup - Flutter NotificationService handles the rest
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self
    }
    application.registerForRemoteNotifications()

    GMSServices.provideAPIKey("AIzaSyAO_SXaEFQ4QHOtFjk_zCQvy-Xa02SegyE")

    GeneratedPluginRegistrant.register(with: self)

    // Register SignalR plugin
    if let registrar = self.registrar(forPlugin: "SignalRPlugin") {
        SignalRPlugin.register(with: registrar)
    }

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // MARK: - Firebase Messaging Delegate

  func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
    print("🔔 FCM registration token: \(fcmToken ?? "nil")")

    let dataDict: [String: String] = ["token": fcmToken ?? ""]
    NotificationCenter.default.post(
      name: Notification.Name("FCMToken"),
      object: nil,
      userInfo: dataDict
    )
  }

  // MARK: - UNUserNotificationCenterDelegate
  // Note: FlutterAppDelegate already implements UNUserNotificationCenterDelegate
  // We just need to set the delegate in didFinishLaunchingWithOptions
}
