package vn.zenity.betacineplex.view.home

import android.annotation.SuppressLint
import android.os.Bundle
import android.os.Parcelable
import com.google.android.material.tabs.TabLayout
import androidx.vectordrawable.graphics.drawable.VectorDrawableCompat
import androidx.core.content.res.ResourcesCompat
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_home.*
import kotlinx.android.synthetic.main.item_event_home_pager.view.*
import kotlinx.android.synthetic.main.item_header_home.view.*
import kotlinx.android.synthetic.main.item_movie_home_list.view.*
import load
import loadRounded
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.model.*
import vn.zenity.betacineplex.view.HomeActivity
import vn.zenity.betacineplex.view.auth.LoginFragment
import vn.zenity.betacineplex.view.cenima.CenimaDetailFragment
import vn.zenity.betacineplex.view.cenima.CenimaFragment
import vn.zenity.betacineplex.view.event.EventDetailFragment
import vn.zenity.betacineplex.view.event.EventFragment
import vn.zenity.betacineplex.view.film.BookByFilmFragment
import vn.zenity.betacineplex.view.film.FilmDetailFragment
import vn.zenity.betacineplex.view.user.MemberFragment
import vn.zenity.betacineplex.view.user.VoucherFragment
import vn.zenity.betacineplex.view.user.point.BetaPointFragment
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class HomeFragment : BaseFragment(), HomeContractor.View {

    private lateinit var adapter: Adapter
    private lateinit var rvMovies: RecyclerView
    private val presenter = HomePresenter()

    private var listFilms = listOf<FilmModel>()
    private var listEvents = listOf<NewsModel>()
    private var listBanners = listOf<BannerModel>()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    private var userChangeListener: (UserModel?) -> Unit = { user ->
        activity?.runOnUiThread {
            updateTitle(user)
            if (listFilms.isEmpty()) {
                presenter.fetchListMovie(tabSelected)
            }
            if (listBanners.isEmpty()) {
                presenter.getBanner()
            }
        }
    }

    private var tabSelected = 1
    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_home
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        rvMovies = view.findViewById(R.id.recyclerView)
        adapter = Adapter()
        val gridLayoutManager = GridLayoutManager(this.context, 3)
        gridLayoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return if (position == 0 && listBanners.isNotEmpty()) 3 else 1
            }
        }
        rvMovies.layoutManager = gridLayoutManager
        rvMovies.adapter = adapter
        btnLogin.setOnClickListener {
            openFragment(LoginFragment())
        }
        ivAvatar.setOnClickListener {
            openFragment(MemberFragment())
        }
        btnMenu.setOnClickListener {
            (activity as? HomeActivity)?.openMenu()
        }
        tab2.isSelected = true

        listOf<View>(tab1, tab2, tab3).click {
            listOf<View>(tab1, tab2, tab3).forEach { tab ->
                tab.isSelected = false
            }
            it.isSelected = true
            val position = when(it.id) {
                R.id.tab1 -> {
                    0
                }
                R.id.tab3 -> {
                    2
                }
                else -> 1
            }
            presenter.fetchListMovie(position)
            tabSelected = position
        }

        presenter.fetchListMovie(tabSelected)
        presenter.getBanner()
        presenter.getAppParams()
        Global.share().listenerUserChange.remove(WeakReference(userChangeListener))
        Global.share().listenerUserChange.add(WeakReference(userChangeListener))
        updateTitle(Global.share().user)
        if(Global.share().isLogin) {
            Global.share().user?.AccountId?.let {
                App.shared().updateUserInfo(it)
            }
        }
    }

    override fun showAppParams(params: List<AppParamsModel>) {
        (activity as? HomeActivity)?.appParams = params
    }

    @SuppressLint("SetTextI18n")
    private fun updateTitle(userModel: UserModel?) {
        if (userModel == null) {
            tvUsername?.gone()
            ivAvatar?.gone()
            btnLogin?.visible()
            llUserInfo?.gone()
            ivAvatar?.setImageResource(R.drawable.ic_account_white)
        } else {
            btnLogin?.gone()
            llUserInfo?.visible()
            tvUsername?.visible()
            ivAvatar?.visible()
            tvUserType?.text = userModel.ClassCode
            tvNumberVoucher?.text = userModel.QuantityOfVoucher.toString()
            tvNumberStar?.text = userModel.AvailablePoint?.toVNDCurrency("")
            val fullString = "${R.string.hi.getString()} ${userModel.FullName ?: ""}"
            val special = userModel.FullName ?: ""
            if (TextUtils.isEmpty(special)) {
                tvUsername?.text = fullString
            } else {
                tvUsername?.setTextWithSpecialText(fullString, special) {
                    it.isUnderlineText = false
                    it.typeface = <EMAIL>?.let { it1 -> ResourcesCompat.getFont(it1, R.font.sanspro_bold) }
                    it.color = R.color.textBlack.getColor()
                    it.linkColor = R.color.textBlack.getColor()
                }
            }
            listOf<View>(tvUsername, ivUserType, tvUserType).click {
                openFragment(MemberFragment())
            }
            ivAvatar?.setImageResource(R.drawable.ic_account_white)
            userModel.Picture?.toImageUrl(true)?.let {
                ivAvatar?.load(it, isCirular = true, error = VectorDrawableCompat.create(resources, R.drawable.ic_account_white, null),
                        placeHolder = VectorDrawableCompat.create(resources, R.drawable.ic_account_white, null))
            }
            listOf<View>(ivNumberStar, tvNumberStar).click {
                openFragment(BetaPointFragment())
            }
            listOf<View>(ivNumberVoucher, tvNumberVoucher).click {
                openFragment(VoucherFragment())
            }
        }
    }

    override fun openFilm(film: FilmModel) {
        Tracking.share().selectMovie(context,film?.FilmId,film?.Name)
        openFragment(BookByFilmFragment.getInstance(film))
    }

    override fun openEvent(event: NewsModel) {
        openFragment(EventDetailFragment.getInstance(event))
    }

    override fun openCinema(cinema: Cinema) {
        openFragment(CenimaDetailFragment())
    }

    override fun openAllEvents() {
        openFragment(EventFragment())
    }

    override fun openAllCinema() {
        openFragment(CenimaFragment())
    }

    override fun showListFilm(listFilms: List<FilmModel>) {
        this.listFilms = listFilms
        adapter.notifyDataSetChanged()
    }

    override fun showListEvent(listEvents: List<NewsModel>) {
        this.listEvents = listEvents
        adapter.notifyDataSetChanged()
    }

    override fun showBanner(banners: List<BannerModel>) {
        this.listBanners = banners
        adapter.notifyDataSetChanged()
    }

    override fun showNearCinema(cinema: Cinema) {

    }

    override fun onDestroyView() {
        super.onDestroyView()
        Global.share().listenerUserChange.remove(WeakReference(userChangeListener))
    }

    private inner class Adapter : RecyclerView.Adapter<Holder>() {

        private val TYPE_EVENT = 1
        private val TYPE_HEADER = 0

        override fun getItemCount(): Int {
            return (if (listBanners.isNotEmpty()) 1 else 0) + listFilms.size
        }

        override fun getItemViewType(position: Int): Int {
            if (position == 0 && listBanners.isNotEmpty()) return TYPE_HEADER
            return TYPE_EVENT
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            if (getItemViewType(position) == TYPE_HEADER) {
                holder.itemView.viewPager.clipToPadding = false
                if (holder.itemView.viewPager?.adapter == null)
                    holder.itemView.viewPager?.adapter = EventAdapter()
                holder.itemView.viewPager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                    override fun onPageScrollStateChanged(state: Int) {
                    }

                    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                    }

                    override fun onPageSelected(position: Int) {

                    }
                })
                holder.itemView.apply {
                    indicator.setViewPager(viewPager)
                }
            } else {
                val film = if (itemCount > listFilms.size) {
                    listFilms[position - 1]
                } else {
                    listFilms[position]
                }
                val increasePosition = if (itemCount > listFilms.size) {
                    1
                } else {
                    0
                }
                holder.itemView.apply {
                    val lp = layoutParams as RecyclerView.LayoutParams
                    if (position >= increasePosition && position < increasePosition + 3) {
                        lp.topMargin = 10.px
                    } else {
                        lp.topMargin = 5.px
                    }
                    layoutParams = lp
                    tvContent.visible(tabSelected != 0)
                    tvContent.text = "${film.Duration} ${R.string.minute.getString()}"
                    tvDateStart.visible(tabSelected == 0)
                    tvDateStart.text = film.OpeningDate?.dateConvertFormat(showFormat = Constant.DateFormat.dateVi)
                            ?: R.string.coming_soon.getString()
                    tvTitle.text = film.Name
                    ivFilmBanner.load(film.filmPosterUrl?.toImageUrl())
                    if (film.FilmRestrictAgeName?.isNotEmpty() == true) {
                        ivAgeWarning.visible()
                        ivAgeWarning.setImageResource(when (film.FilmRestrictAgeName?.toLowerCase()
                                ?: "p") {
                            "c18" -> R.drawable.ic_age_c_18
                            "c16" -> R.drawable.c_16
                            "c13" -> R.drawable.ic_age_c_13
                            else -> R.drawable.ic_age_p
                        })
                    } else {
                        ivAgeWarning.gone()
                    }
                    ivIsHot.visible(film.IsHot)
                    ivIsHot2.visible(film.IsHot)
                    click {

                        if (film.HasShow == true) {
                            openFilm(film)
                        } else {
                            openFragment(FilmDetailFragment.getInstance(film))
                        }
                    }
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            return if (viewType == TYPE_HEADER) {
                val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_header_home, parent, false)
                Holder(itemView)
            } else {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_movie_home_list, parent, false)
                Holder(item)
            }
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)

    private inner class EventAdapter() : PagerAdapter() {
        override fun isViewFromObject(view: View, `object`: Any): Boolean {
            return view == `object`
        }

        @SuppressLint("SetTextI18n")
        override fun instantiateItem(parent: ViewGroup, position: Int): Any {
            val itemView = parent.inflate(R.layout.item_event_home_pager)
            itemView.apply {
                val banner = listBanners[position]
                ivBanner.loadRounded(banner.ImageUrl?.toImageUrl())
                if (banner.ScreenCode != null) {
                    click {
                        provideAppLink(banner.ScreenCode!! to (banner.RefId ?: ""))
                    }
                } else {
                    setOnClickListener(null)
                }
            }
            parent.addView(itemView)
            return itemView
        }

        override fun getCount(): Int = listBanners.size

        override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
            if (`object` is ViewGroup) {
                container.removeView(`object`)
            }
        }

        override fun saveState(): Parcelable? {
            return null
        }

        override fun restoreState(state: Parcelable?, loader: ClassLoader?) {

        }
    }
}
