import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class CPref {
  static String token = 'token';
  static String user = 'user';
  static PageStorageBucket bucket = PageStorageBucket();

  // Notification preferences - tương ứng với iOS notification constants
  static String fcmToken = 'fcm_token';
  static String apnsToken = 'apns_token';
  static String isTokenRegistered = 'is_token_registered';
  static String accountId = 'account_id';
  static String deviceId = 'device_id';
  static String notificationEnabled = 'notification_enabled';
  static String lastNotificationCheck = 'last_notification_check';

  static String statusTitle(String status) {
    switch (status) {
      case 'ALL':
        return 'CPref.ALL'.tr();
      case 'ADMIN':
        return 'CPref.ADMIN'.tr();
      case 'REJECTED':
        return 'CPref.REJECTED'.tr();
      case 'WFA':
        return 'CPref.WFA'.tr();
      case 'APPROVED':
        return 'CPref.APPROVED'.tr();
      case 'COMPLETED':
        return 'CPref.COMPLETED'.tr();
      case 'WAIT_TRANSFER':
        return 'CPref.WAIT_TRANSFER'.tr();
      case 'TRANSFER_CONFIRMED':
        return 'CPref.TRANSFER_CONFIRMED'.tr();
      case 'UN_CONFIRM':
        return 'CPref.UN_CONFIRM'.tr();
      case 'CANCELED':
        return 'CPref.CANCELED'.tr();
      case 'MALE':
        return 'CPref.MALE'.tr();
      case 'FEMALE':
        return 'CPref.FEMALE'.tr();
      case 'DRAFT':
        return 'CPref.DRAFT'.tr();
      case 'WAIT_CONFIRM':
        return 'CPref.WAIT_CONFIRM'.tr();

      case 'FARMER':
        return 'Farmer Side';
    }
    return status;
  }

  static Color mimeTypeColor(String mimeType) {
    switch (mimeType) {
      case 'JPEG':
      case 'JPG':
        return Colors.green;
      case 'PNG':
        return Colors.purple;
      case 'PDF':
        return Colors.grey;
      case 'DOCX':
      case 'DOC':
        return Colors.lightGreen;
      case 'XLSX':
      case 'XLS':
        return Colors.blue;
      case 'CSV':
        return Colors.teal;
      case 'HTML':
        return Colors.orange;
    }
    return Colors.blue;
  }
  static Color hexToColor(String hexColor) {
    hexColor = hexColor.replaceAll("#", ""); // Remove the # if it exists
    return Color(int.parse("0xFF$hexColor")); // Prefix with 0xFF for full opacity
  }
  static Color lighterColor(Color color, double amount) {
    return Color.alphaBlend(color.withOpacity(amount), Colors.white);
  }
}
