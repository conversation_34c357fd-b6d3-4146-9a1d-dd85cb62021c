{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988614a089f241f968c1365c107e7c82ad", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b34e8263f501b06d28377aad4914513", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895df1cc65b58be0e72840bcaac5a0cfd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9835c2ad5a96414911d91463be9200860d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895df1cc65b58be0e72840bcaac5a0cfd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98460e9747d23ccd78f457325428acf3b8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982957179fe9912e4f0a35c953cdd2fba0", "guid": "bfdfe7dc352907fc980b868725387e987345cd39fcabb7fc2ea2d8efce40a78e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a88a55408d4416607f4bd1562356a9e", "guid": "bfdfe7dc352907fc980b868725387e984b6e7d1def77b6b7d50e9eea254e2128"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984acb69ef6254f66505edad2de97bc6ee", "guid": "bfdfe7dc352907fc980b868725387e9876864e625a8fd2a75bb0e22c7fa3af2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ab78a7813089f6cee54d772d6978a8c", "guid": "bfdfe7dc352907fc980b868725387e9869c28f71322c1518a21743d22b021ae4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eafe2f83fff7367461654fd0ddcdf34c", "guid": "bfdfe7dc352907fc980b868725387e98d2ba6f458545fc28a93f4b1099ef7d4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab7de24fa4ca641345842617f22430bf", "guid": "bfdfe7dc352907fc980b868725387e98c3da85fff6415b2145197ca0c188040c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f2cdc4b846dd5eb17d70333dd80a328", "guid": "bfdfe7dc352907fc980b868725387e984d0009db6e354bd65a7792d9629a3301"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98282bc16d363342c62baed9dbe73bbc84", "guid": "bfdfe7dc352907fc980b868725387e98347131cca9daa661429728259cc4c8d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801e27964128b6de6d6cff8f6624ae47c", "guid": "bfdfe7dc352907fc980b868725387e987253fe87e878f12ca087d269337f905b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fef9efae0a084bc0d54dc5abe21762d", "guid": "bfdfe7dc352907fc980b868725387e985cd1dff7740378e7231bcac2dd53dee2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecfe8fd589b59116eb19bfa81c27b75b", "guid": "bfdfe7dc352907fc980b868725387e98ebcf2bd72fe1afbe3bc1db82b1e014be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0ccbdf88ea90238f20035ebda538c76", "guid": "bfdfe7dc352907fc980b868725387e98befa7072a6e79f6affaede7da85a9e84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddff3857521d6a2071893c2ac20418ee", "guid": "bfdfe7dc352907fc980b868725387e98060f5b95cd7d9e705877fa95f56c66ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880f04d07c362b4d67ba2d2604b3c647a", "guid": "bfdfe7dc352907fc980b868725387e986901f6a77655fa5daa00b756fa249423", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efdaabb811566fa888d830c8fda61fbb", "guid": "bfdfe7dc352907fc980b868725387e982fc2159f1cd7b02d746691c4b90afebc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980373e3d5c145e725b12f1448113c6824", "guid": "bfdfe7dc352907fc980b868725387e98e4143e310c8c8e5e25af0dc10244968b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866c3cc8c378e0a07ad913465dd57c215", "guid": "bfdfe7dc352907fc980b868725387e98f9919de24b101db71720b43a9ab3a4ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f93d952d8223d57f003e35fdb01eaf6", "guid": "bfdfe7dc352907fc980b868725387e980702d9ceb70dc3a97f47a67be6fed9a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ddd6ae9885673afb43c1023c1d2e72c", "guid": "bfdfe7dc352907fc980b868725387e9830c2bf31efe33a509806ffe215a2f3bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e372aca6a9dffdef5088b5a4f32dad15", "guid": "bfdfe7dc352907fc980b868725387e98d8e5037af1c1e1ca8d55ca191822789f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad63abaa245b8a40eb006ce073be3be1", "guid": "bfdfe7dc352907fc980b868725387e98a9b8ef5d16074f2ad62f168de735ddca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5e229cd5c67260118cdea4c1a8cc715", "guid": "bfdfe7dc352907fc980b868725387e98d11bbcac5a4ee4c2df5e074c1c5ce1c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887dc2bbb64bcf86af0a8f1eac6156bcc", "guid": "bfdfe7dc352907fc980b868725387e983b67fb118c35e14e0498975769be205c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb2ff511443dafc800cabed0b367a863", "guid": "bfdfe7dc352907fc980b868725387e98c7b538397c3592adb7416d4c8b0e14d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e7578cf38d37449e157df103a0961fd", "guid": "bfdfe7dc352907fc980b868725387e98db786d4d3b486f3a46285579fd04fc82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867f738db06359e707edd306438786ba7", "guid": "bfdfe7dc352907fc980b868725387e986f207735c0510dd6ad3e2749caae3140"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1ea6fedefd3f234e448b6e830b2014e", "guid": "bfdfe7dc352907fc980b868725387e987beba6c007b5f554f06258b9170984a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bdd0e233a3f22310f07c302616982e5", "guid": "bfdfe7dc352907fc980b868725387e982636b607ef40530bdb33756700563fc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fdf0f572f053ee03f7c32472965d06b", "guid": "bfdfe7dc352907fc980b868725387e980b7339c14b7f874f1aabe2c5e4b35098"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b00ad3a6de5c77b81a1675b3a9fa5875", "guid": "bfdfe7dc352907fc980b868725387e98c8dad519a6ef77d3b089cec0852ac1fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a68dcb86753ecdf6e80c994c0b7a37a5", "guid": "bfdfe7dc352907fc980b868725387e982c449c2a70acbe2d72f2c52d24027e0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98014424f7dca284afd86b50284f8c1bfc", "guid": "bfdfe7dc352907fc980b868725387e9838e569ed1279136b950f4c2fda11b4e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adb1b47d8d7eee3ecde68fe743802416", "guid": "bfdfe7dc352907fc980b868725387e981bbb6bb85359f1d8c7d2ad915e3e69de"}], "guid": "bfdfe7dc352907fc980b868725387e981a759aa45d077dcbccbd6f9cb86b7d82", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98940107dc8f0e37b4a48046b4d4f0a649", "guid": "bfdfe7dc352907fc980b868725387e98f0a7c66ed987d715597759dd1e96f3d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860d83ae08273f9f499537140fa35acf4", "guid": "bfdfe7dc352907fc980b868725387e9835a430ce2e34821732b313066ef841d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98454241828fb938c59399f127defac32d", "guid": "bfdfe7dc352907fc980b868725387e982c03ebb7e8b25ffc2b9bb9a7f53ed228"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb3050fe4be68cff184bd0e45fea8a78", "guid": "bfdfe7dc352907fc980b868725387e98a0f447d25e49a3de45c3ce62973fc8b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98723c00362c7d0fd6d6660178c4f222e3", "guid": "bfdfe7dc352907fc980b868725387e989c1cf49f0f31794f11cc8519bdc0072a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cc88b7962e81f52d3358808c64f0dac", "guid": "bfdfe7dc352907fc980b868725387e982afb78cd8ecb9eb911b40af0b223c8ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987664e1da40f979b5a70b2187cf4d7f2f", "guid": "bfdfe7dc352907fc980b868725387e98215cd3f35f21396443b1a86c9962b33c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e45e5e84a1eed690edb6742254170ba6", "guid": "bfdfe7dc352907fc980b868725387e98151bb4f529475ee38245e19dbecbffa1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895ac1ef7a0bfa61d99737852b2c1aa20", "guid": "bfdfe7dc352907fc980b868725387e98e1098194e438c539b953ba9bb12dd453"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9574c9d0b53c7797737c846a92020e8", "guid": "bfdfe7dc352907fc980b868725387e98b8331b09bb366b787cd543a695e5d45c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843ecd7468e19e3ce247d436348c8a986", "guid": "bfdfe7dc352907fc980b868725387e985acd0e21fdae771b010c49f37322499f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d22eedb1d4f885a3ce36d270acf5294e", "guid": "bfdfe7dc352907fc980b868725387e98da5902a837f3b464ef7e191dd012818b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdbc8400ac21874c30e6df9237a57b5e", "guid": "bfdfe7dc352907fc980b868725387e986bebe99bc213d8b9f065287389af16d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98103019daa307d90a92ca9a4db08c5f97", "guid": "bfdfe7dc352907fc980b868725387e9800e4711c672ffb261840c551f11df48c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989274a1a02e04dc22e8b8a942959e9b5d", "guid": "bfdfe7dc352907fc980b868725387e988fbd46087423c8bcf314a586ebb64c1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890c9c414f73496165ace06b63c79e2a7", "guid": "bfdfe7dc352907fc980b868725387e98ab52b13c838507358fd4d572a515be93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e3c15ec83ffd7f6035f73bbe447a391", "guid": "bfdfe7dc352907fc980b868725387e9881cbf463ee3223a1b2cb88aef5233816"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dffec9ece1d1134f98abf40775fd81d", "guid": "bfdfe7dc352907fc980b868725387e987faf91e5fb62894cf7704d3a20d93493"}], "guid": "bfdfe7dc352907fc980b868725387e989bafdc4ae8b3552730e0b523fa41a61f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98d5130fb91f8079c0752ed4aaa054d4c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "guid": "bfdfe7dc352907fc980b868725387e98b9bad04e85797668ac12317ad4005037"}], "guid": "bfdfe7dc352907fc980b868725387e98136c8c8735ff592826f82729f325ae5a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b762d7efb01f88ed79cc8ce3f8c20b42", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e985642d11abedd04f1748bc1ac14e90d60", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}