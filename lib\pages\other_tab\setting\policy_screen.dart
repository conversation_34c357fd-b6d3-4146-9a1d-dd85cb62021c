import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum PolicyType {
  terms,
  payment,
  security,
  companyInfo,
}

class PolicyScreen extends StatefulWidget {
  final PolicyType policyType;

  const PolicyScreen({super.key, required this.policyType});

  @override
  State<PolicyScreen> createState() => PolicyScreenState();
}

class PolicyScreenState extends State<PolicyScreen> {
  bool _isLoading = true;
  String _content = '';
  String _title = '';
  String? _error;

  @override
  void initState() {
    super.initState();
    _setTitle();
    _fetchPolicyContent();
  }

  void _setTitle() {
    switch (widget.policyType) {
      case PolicyType.terms:
        _title = 'Terms of Use';
        break;
      case PolicyType.payment:
        _title = 'Payment Policy';
        break;
      case PolicyType.security:
        _title = 'Security Policy';
        break;
      case PolicyType.companyInfo:
        _title = 'Company Information';
        break;
    }
  }

  Future<void> _fetchPolicyContent() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Get policy ID based on the policy type and current language
      late String policyCode;
      // Get language preference from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final isEnglish = prefs.getBool('isEnglish') ?? false;

      switch (widget.policyType) {
        case PolicyType.terms:
          policyCode = isEnglish ? 'mobile:app:dieukhoan:en' : 'mobile:app:dieukhoan:vi';
          break;
        case PolicyType.payment:
          policyCode = isEnglish ? 'mobile:app:dieukhoan-thanhtoan:en' : 'mobile:app:dieukhoan-thanhtoan:vi';
          break;
        case PolicyType.security:
          policyCode = isEnglish ? 'mobile:app:dieukhoan-baomat:en' : 'mobile:app:dieukhoan-baomat:vi';
          break;
        case PolicyType.companyInfo:
          policyCode = isEnglish ? 'mobile:app:thongtin-congty:en' : 'mobile:app:thongtin-congty:vi';
          break;
      }

      final policyIdResult = await RepositoryProvider.of<Api>(context).other.getPolicyId(
        policyType: policyCode,
      );

      if (policyIdResult != null ) {
        final policyId = policyIdResult.data['ParameterValue'];

        if (policyId != null) {
          // Get policy content
          final contentResult = await RepositoryProvider.of<Api>(context).other.getPolicyContent(
            id: policyId,
          );

          if (contentResult != null ) {
            final contentData = contentResult.data;
            if (contentData != null && contentData['Noi_dung_chi_tiet'] != null) {
              final paragraphData = contentData['Noi_dung_chi_tiet'][0]['ParagraphData'];
              if (paragraphData != null && paragraphData['ParagraphContent'] != null) {
                setState(() {
                  _content = paragraphData['ParagraphContent'];
                  _isLoading = false;
                });
                return;
              }
            }
          }
        }

        setState(() {
          _isLoading = false;
          _error = 'Failed to load content';
        });
      } else {
        setState(() {
          _isLoading = false;
          _error = 'Failed to load policy ID';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_title),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(_error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchPolicyContent,
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: LayoutBuilder(
          builder: (context,constraints) {
          return Html(
            data: _content,
            style: {
              "body": Style(
                fontSize: FontSize(16.0),
                lineHeight: LineHeight(1.5),
                width: Width(constraints.maxWidth),
              ),
              "p": Style(
                margin: Margins(bottom: Margin(8)),
              ),
              "img": Style(
                // width: Width(double.infinity),
                alignment: Alignment.center,
              ),
            },
          );
        }
      ),
    );
  }
}
