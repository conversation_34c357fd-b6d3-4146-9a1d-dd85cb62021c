{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c58d33c27db1ec8b6e7ba4abe93db46", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SwiftSignalRClient", "PRODUCT_NAME": "SwiftSignalRClient", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98131aa6fd4aae40a40a79876b6f5af911", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9803ae6d0524f8fb6bb3c7e8aaa881920a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient.modulemap", "PRODUCT_MODULE_NAME": "SwiftSignalRClient", "PRODUCT_NAME": "SwiftSignalRClient", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e0503c58bbdea224a60def444a6b17e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9803ae6d0524f8fb6bb3c7e8aaa881920a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftSignalRClient/SwiftSignalRClient.modulemap", "PRODUCT_MODULE_NAME": "SwiftSignalRClient", "PRODUCT_NAME": "SwiftSignalRClient", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9895eea8c6038850234501f5353f77c065", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cf0f7d6c4aa7ff1f5298ce4c85ef1857", "guid": "bfdfe7dc352907fc980b868725387e981e522d76f0d7c0b6529bb1ae37426315", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9842ea3ae8f60e955e633ea09d8401a108", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98964f43a60787cca1a3f4e0000a6a95a3", "guid": "bfdfe7dc352907fc980b868725387e98dbb615369c65ebe26b860a9055f1f3ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98248c062c7ac34fafc0e2885b4c6e14c5", "guid": "bfdfe7dc352907fc980b868725387e9861a0d2e162a8461b80b98e0f6bb85e1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3042cc18f682c232775e1922f8d14ca", "guid": "bfdfe7dc352907fc980b868725387e98487d27b8ae16dae68b1d0ee014cd20f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc54f6bab9b8a5b209ebde51389b7c50", "guid": "bfdfe7dc352907fc980b868725387e984cf624cd2f534acae29609e3c2c785ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aebec2265e9753d45dc221a01f2aa4c5", "guid": "bfdfe7dc352907fc980b868725387e98f065971cfa0bf1eea0c5112c0f4cb381"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e56ea55860636c3d657a0f01d9b11eef", "guid": "bfdfe7dc352907fc980b868725387e98212dd800466f89181f42017ebeaf4b51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98820e666b781369c2b062b072dd5a0f59", "guid": "bfdfe7dc352907fc980b868725387e981fbd83e2f98be1ee99d04ad75ef3e249"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b7833317c6accc56c970a7e34589bdb", "guid": "bfdfe7dc352907fc980b868725387e98d83d4be79ad465952036e04eee368592"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ea9c8104cda7b706359b4b7444af9fa", "guid": "bfdfe7dc352907fc980b868725387e987b18f67da676332fb9453f626a9918f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1afbf70d0783824d8144a7b60d383e4", "guid": "bfdfe7dc352907fc980b868725387e9805c21959ad87ca0d3a6443d4d296662d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d6769d83525b6b21e6db3fc03359e42", "guid": "bfdfe7dc352907fc980b868725387e981e3d4b1f2a4c65ab986e3a65ba6837a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd540bc94174e3435081b09a81f14ec4", "guid": "bfdfe7dc352907fc980b868725387e987ea31f1ea3e66ae42b2ca61772f108cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800fb6376696b521ef11b4eea156ee00a", "guid": "bfdfe7dc352907fc980b868725387e982982c46609282aa6186c37db3c1f308b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872b9593dbf02ee092dbcde403df97c40", "guid": "bfdfe7dc352907fc980b868725387e98e26499e334dae3011c578186478d8f68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e435d6dce250f10a262bbb6ba05f267", "guid": "bfdfe7dc352907fc980b868725387e98aa301573c7b629857f9e1be9f7a68e44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984851d421e3b949dbfcf58d28be4b888f", "guid": "bfdfe7dc352907fc980b868725387e98e9eab2a4d3ac6c9f127e84157a621aca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984de02505df779253d090547991998d24", "guid": "bfdfe7dc352907fc980b868725387e9836cc662173b47dad84c9cdbb29e39e5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897887c29e780cc82955308c543200be5", "guid": "bfdfe7dc352907fc980b868725387e98cc7ab1a423c547ca193306a08e884453"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844c2020915345cc2600513f7ddf4bafe", "guid": "bfdfe7dc352907fc980b868725387e98e95645f47613efccebfac81d8f0355aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df414cc26789c1c0b0d9e8bda7ffabe8", "guid": "bfdfe7dc352907fc980b868725387e982fb515bd18f6678f200032f55b4d52f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b788242f2edc16ffa076d5fca48b88a3", "guid": "bfdfe7dc352907fc980b868725387e987e9c3cfe414a46e3e1670ac4b8cf188a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a064a972232d84815c10a51076035fe3", "guid": "bfdfe7dc352907fc980b868725387e98b2f552981c6ee31beefcfeb5c2e7bc78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd8bb0731acfc02843482d7fe81cfaac", "guid": "bfdfe7dc352907fc980b868725387e9824ae9bf564a316481b697c36c9bb1ad1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffefe86c1fff3e0f2c92a91e5576583f", "guid": "bfdfe7dc352907fc980b868725387e9881b19f41ccfcd36746e18c581eb36237"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce718137464e60162d698fd4fd834796", "guid": "bfdfe7dc352907fc980b868725387e989972a6d79e2dce63640e7a33331926fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886de7303a6f8a315bcd7a3e9cbc46d90", "guid": "bfdfe7dc352907fc980b868725387e9887f5a9f251807b07c262c8d4017ec105"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882f04c96b2f865ad94f5daad5ea8e326", "guid": "bfdfe7dc352907fc980b868725387e988b104019a229a0ae6acebd9575cd3b42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc56db4f114dec6cd9c3ece4a91ceeda", "guid": "bfdfe7dc352907fc980b868725387e981675178f97b0159dc926b965c092ff4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98003952d9af1a0a5702804536cefd7300", "guid": "bfdfe7dc352907fc980b868725387e98a1c90a4a1af15d58d2345be109397528"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98876309e9a61b22cab9f1163b3122d47d", "guid": "bfdfe7dc352907fc980b868725387e9866d479061b9d5c396881a5cfae10482f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c733aad4039e33a0388aeb9eaffe776", "guid": "bfdfe7dc352907fc980b868725387e989707ffa184dc499ee38aa56a0070fbff"}], "guid": "bfdfe7dc352907fc980b868725387e9896aa49f170f8ad3a9c031358c73c3e27", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98f07eb8b01901cac0d9fb6d27712d617d"}], "guid": "bfdfe7dc352907fc980b868725387e9898f08f700a47307b642072b8f18e6256", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9893a13ea77dae041dcd7f3706baf98dd8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e984091fa6cc3ef8bab223377c5869a38e7", "name": "SwiftSignalRClient", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98802cf383e0cf2c1ddeda423c8c89e6af", "name": "SwiftSignalRClient.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}