package vn.zenity.betacineplex.view.setting

import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class SettingPresenter : SettingContractor.Presenter {
    private var view: WeakReference<SettingContractor.View?>? = null
    override fun attachView(view: SettingContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.view?.clear()
        this.view = null
    }
}
