import 'package:flutter/material.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/service/location_service.dart';
import 'package:flutter_app/widgets/index.dart';
import 'package:fl_location/fl_location.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../constants/index.dart';
import '../../Movie_schedule/widget/news_and_deals_card.dart';
import '../../cinema/model/cinema_model.dart';

String localize(String key) {
  // Implement localization logic here
  Map<String, String> translations = {
    "CinemaDetail.Title": "Chi tiết rạp",
    "ticket_price": "Giá vé",
    "show": "Lịch chiếu",
    "call_now": "Gọi ngay",
    "Address": "Địa chỉ",
    "OpenInGoogleMap": "Mở bằng Google Maps",
    "OpenInAppleMap": "Mở bằng Bản đồ <PERSON>",
    "RouteInMap.Title": "Chỉ đường",
    "Bt.Cancel": "Huỷ",
    // Thêm các key khác
  };
  return translations[key] ?? key;
}
// --- Hết phần giả định hàm dịch ---

class CinemaDetailScreen extends StatefulWidget {
  final Cinema? initialCinema;

  const CinemaDetailScreen({Key? key, this.initialCinema}) : super(key: key);

  @override
  _CinemaDetailScreenState createState() => _CinemaDetailScreenState();
}

class _CinemaDetailScreenState extends State<CinemaDetailScreen> {
  Cinema? _cinema;
  List<NewsModel> _promotions = [];
  bool _isLoading = true;
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  final LocationService _locationService = LocationService();
  Location? _userPosition;
  bool _isLoadingLocation = false;

  // Giả định các service gọi API
  // final CinemaProvider _cinemaProvider = CinemaProvider();
  // final EcmProvider _ecmProvider = EcmProvider();

  @override
  void initState() {
    super.initState();
    _cinema = widget.initialCinema;
    _fetchData();
    _getUserLocation();
  }

  Future<void> _getUserLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      _userPosition = await _locationService.determinePosition(
        showError: false,
        context: context,
      );

      // Calculate distance if coordinates are available
      if (_userPosition != null && _cinema != null && _cinema!.hasValidCoordinates) {
        _cinema!.distance = _locationService.calculateDistance(
          _userPosition!.latitude,
          _userPosition!.longitude,
          _cinema!.latitudeAsDouble!,
          _cinema!.longitudeAsDouble!,
        );
        setState(() {}); // Update UI with distance
      }
    } catch (e) {
      print('Error getting location: $e');
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  Future<void> _fetchData() async {
    setState(() {
      _isLoading = true;
      // FlutterEasyLoading.show(status: 'Loading...'); // Hiển thị loading
    });

    try {
      // Nếu cinema ban đầu không đủ thông tin chi tiết
      if (_cinema?.newsId == null && _cinema?.cinemaId != null) {
        // Giả định hàm getCinemaDetail trả về Future<CinemaModel>
        // final detailedCinema = await _cinemaProvider.getCinemaDetail(_cinema!.cinemaId!);
        // setState(() {
        //   _cinema = detailedCinema;
        // });
      }

      // Giả định hàm getPromotions trả về Future<List<NewsModel>>
      // final promotions = await _ecmProvider.getPromotionsForCinema(_cinema?.cinemaId); // Cần API tương ứng
      // setState(() {
      //   _promotions = promotions;
      // });

      _updateUI(); // Cập nhật UI sau khi có dữ liệu
    } catch (e) {
      // Xử lý lỗi
      print("Error fetching data: $e");
      // FlutterEasyLoading.showError('Failed to load data');
    } finally {
      setState(() {
        _isLoading = false;
        // FlutterEasyLoading.dismiss(); // Ẩn loading
      });
    }
  }

  void _updateUI() {
    if (_cinema == null) return;

    // --- Cập nhật Map ---
    final lat = double.tryParse(_cinema?.latitude ?? '');
    final lng = double.tryParse(_cinema?.longitude ?? '');

    if (lat != null && lng != null) {
      final position = LatLng(lat, lng);
      _markers = {
        Marker(
          markerId: MarkerId(_cinema?.cinemaId ?? 'cinema_marker'),
          position: position,
          infoWindow: InfoWindow(
            title: _cinema?.name ?? '',
            snippet: _cinema?.address ?? '',
          ),
        )
      };
      _mapController?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(target: position, zoom: 17.0),
        ),
      );
    }
    setState(() {}); // Cập nhật lại UI với dữ liệu mới
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    _updateUI(); // Cập nhật marker khi map sẵn sàng
  }

  // --- Xử lý sự kiện nhấn nút ---
  void _onTicketPricePressed() {
    // Navigator.push(context, MaterialPageRoute(builder: (_) => CinemaPriceScreen(cinemaId: _cinema?.newsId)));
    print("Navigate to Cinema Price Screen");
    // showModalBottomSheet(...) // Hoặc hiển thị popup như showPopup
  }

  void _onSessionTimePressed() {
    // Navigator.push(context, MaterialPageRoute(builder: (_) => ChooseCinemasScreen(cinema: _cinema)));
    print("Navigate to Choose Cinemas Screen");
  }

  void _onCallPressed() async {
    final phoneNumber = _cinema?.phoneNumber?.replaceAll(' ', '');
    if (phoneNumber != null) {
      final url = 'tel:$phoneNumber';
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse((url)));
      } else {
        // Xử lý lỗi không thể gọi
        print('Could not launch $url');
      }
    }
  }

  void _onGuidePressed() async {
    final lat = double.tryParse(_cinema?.latitude ?? '');
    final lng = double.tryParse(_cinema?.longitude ?? '');

    if (lat == null || lng == null) return;

    final appleUrl = 'maps://?daddr=$lat,$lng&dirflg=d';
    final googleUrl = 'comgooglemaps://?daddr=$lat,$lng&directionsmode=driving';
    final googleWebUrl = 'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng&travelmode=driving';

    bool canGoogle = await canLaunchUrl(Uri.parse(googleUrl));
    bool canApple = await canLaunchUrl(Uri.parse(appleUrl));

    if (canGoogle && canApple) {
      showModalBottomSheet(
        context: context,
        builder: (context) => SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(Icons.map), // Thay icon phù hợp
                title: Text(localize('OpenInGoogleMap')),
                onTap: () async {
                  await launchUrl(Uri.parse(googleUrl));
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.map_outlined), // Thay icon phù hợp
                title: Text(localize('OpenInAppleMap')),
                onTap: () async {
                  await launchUrl(Uri.parse(appleUrl));
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: Text(localize('Bt.Cancel')),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        ),
      );
    } else if (canApple) {
      await launchUrl(Uri.parse(appleUrl));
    } else if (canGoogle) {
      await launchUrl(Uri.parse(googleUrl));
    } else {
      // Mở Google Maps trên web nếu không có app nào
      if (await canLaunchUrl(Uri.parse(googleWebUrl))) {
        await launchUrl(Uri.parse(googleWebUrl));
      } else {
        print('Could not launch any map application');
      }
    }
  }

  // --- Xử lý delegate từ PromotionSectionWidget ---
  void _onPromotionSelected(NewsModel news) {
    // Navigator.push(context, MaterialPageRoute(builder: (_) => NewsDetailScreen(news: news)));
    print("Navigate to News Detail Screen");
  }

  void _onShowAllPromotions() {
    // Navigator.push(context, MaterialPageRoute(builder: (_) => NewsAndDealsScreen()));
    print("Navigate to News and Deals Screen");
  }

  Widget _buildActionButton(IconData icon, String label, VoidCallback onPressed) {
    return InkWell(
      onTap: onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.blueAccent),
          const SizedBox(height: 4),
          Text(label, style: const TextStyle(fontSize: 13)),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading && _cinema == null
          ? const Center(child: CircularProgressIndicator())
          : CustomScrollView(
              slivers: [
                SliverAppBar(
                  expandedHeight: 250,
                  pinned: true,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  leading: IconButton(
                    icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                    padding: EdgeInsets.zero,
                  ),
                  centerTitle: false,
                  title: const Text(
                    "Rạp phim Beta",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: CFontSize.xl2,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  flexibleSpace: FlexibleSpaceBar(
                    background: Stack(
                      fit: StackFit.expand,
                      children: [
                        _cinema?.picture != null
                            ? imageNetwork(
                                url: '${ApiService.baseUrlImage}/${_cinema?.picture ?? ''}',
                                fit: BoxFit.cover,
                              )
                            : Container(color: Colors.grey[300]),
                        // Lớp phủ làm tối ảnh
                        Container(
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Colors.black54, Colors.transparent],
                              end: Alignment.bottomCenter,
                              begin: Alignment.topCenter,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SliverList(
                  delegate: SliverChildListDelegate([
                    // const SizedBox(height: 16),
                    Container(
                      color: Colors.black45,
                      padding: const EdgeInsets.symmetric(horizontal: CSpace.sm, vertical: CSpace.xl),
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              _cinema?.name ?? "",
                              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.white),
                            ),
                            if (_cinema?.formattedDistance != null)
                              Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.location_on,
                                      size: 16,
                                      color: Colors.red,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'Cách bạn ${_cinema!.formattedDistance!}',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.white,
                                      ),
                                    ),
                                    if (_isLoadingLocation)
                                      Padding(
                                        padding: const EdgeInsets.only(left: 8.0),
                                        child: SizedBox(
                                          width: 12,
                                          height: 12,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: Colors.white.withAlpha(150),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildActionButton(
                              Icons.confirmation_number, localize('ticket_price'), _onTicketPricePressed),
                          _buildActionButton(Icons.schedule, localize('show'), _onSessionTimePressed),
                          _buildActionButton(Icons.phone, localize('call_now'), _onCallPressed),
                        ],
                      ),
                    ),

                    const Divider(height: 32),

                    // --- Bản đồ ---
                    SizedBox(
                      height: 200,
                      child: GoogleMap(
                        onMapCreated: _onMapCreated,
                        initialCameraPosition: CameraPosition(
                          target: LatLng(
                            double.tryParse(_cinema?.latitude ?? '0') ?? 0,
                            double.tryParse(_cinema?.longitude ?? '0') ?? 0,
                          ),
                          zoom: 17.0,
                        ),
                        markers: _markers,
                        myLocationButtonEnabled: false,
                        zoomControlsEnabled: false,
                      ),
                    ),

                    // --- Địa chỉ ---
                    Padding(
                      padding: const EdgeInsets.all(CSpace.base),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(localize("Address"), style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                          const SizedBox(height: 4),
                          Text(_cinema?.address ?? '...', style: const TextStyle(fontSize: 15)),
                          TextButton.icon(
                            onPressed: _onGuidePressed,
                            icon: const Icon(Icons.directions),
                            label: Text(localize('RouteInMap.Title')),
                          ),
                        ],
                      ),
                    ),

                    const Divider(),

                    // --- Khuyến mãi ---
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text("KHUYẾN MÃI MỚI", style: TextStyle(fontWeight: FontWeight.bold)),
                          TextButton(
                            onPressed: _onShowAllPromotions,
                            child: const Text("Tất cả"),
                          ),
                        ],
                      ),
                    ),
                    // PromotionSectionWidget(
                    //   promotions: _promotions,
                    //   onItemSelected: _onPromotionSelected,
                    //   onShowAll: _onShowAllPromotions,
                    // ),

                    const SizedBox(height: 30),
                  ]),
                ),
              ],
            ),
    );
  }
}
