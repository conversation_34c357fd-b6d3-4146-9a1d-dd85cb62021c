import 'package:flutter/material.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
import 'package:flutter_app/pages/cinema/choose/seat.dart';
import 'package:flutter_app/pages/cinema/model/cinema_model.dart';
import 'package:flutter_app/pages/cinema/widget/calendar_header.dart';
import 'package:flutter_app/pages/cinema/widget/film_cell.dart';
import 'package:flutter_app/pages/cinema/widgets/vip_zoom_confirmation_dialog.dart';
import 'package:flutter_app/pages/youtube_play.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../cubit/index.dart';



class ChooseCinemasView extends StatefulWidget {
  final Cinema cinema;

  const ChooseCinemasView({super.key, required this.cinema});

  @override
  State<ChooseCinemasView> createState() => _ChooseCinemasViewState();
}

class _ChooseCinemasViewState extends State<ChooseCinemasView> {
  List<DateTime> showDates = [];
  // List<ShowFilmInCinema> films = [];
  List<ShowFilmModel> films = [];
  ShowModel? selectedShow;
  ShowFilmModel? selectedFilm;
  bool isLoading = false;
  DateTime? _selectedDate;


  @override
  void initState() {
    super.initState();
    _getListDate();
  }

  void _getListDate() async {
    setState(() => isLoading = true);
    try {
      final response = await RepositoryProvider
          .of<Api>(context)
          .cinema
          .getShowDate(id: widget.cinema.cinemaId ?? "");
      if (response?.data != null) {
        final data = response?.data;
        final dates = (data['content'] as List).map((d) => DateTime.tryParse(d)).whereType<DateTime>().toList();

        dates.sort();

        setState(() {
          showDates = dates;
        });

        if (dates.isNotEmpty) {
          _selectedDate = dates.first;
          _getShowAtDate(_selectedDate!);
        }
      } else {
        _showAlert(response?.message ?? "");
      }
    } catch (e) {
      _showAlert("Error fetching data.");
    } finally {
      setState(() => isLoading = false);
    }
  }

  void _onDateSelected(DateTime date) {
    setState(() {
      _selectedDate = date;
    });
    _getShowAtDate(date);
  }


  void _getShowAtDate(DateTime date) async {
    final dateString = DateFormat('yyyy-MM-dd').format(date);
    setState(() => isLoading = true);
    try {
      final response = await RepositoryProvider
          .of<Api>(context)
          .cinema
          .getFilmShow(
        id: widget.cinema.cinemaId ?? "",
        dateShow: dateString,
      );

      if (response != null && response.data != null) {
        final List<dynamic> filmData = response.data['content'] ?? [];
        final List<ShowFilmModel> filmList = filmData
            .map((item) => ShowFilmModel.fromJson(item))
            .toList();

        // Group films by FilmId
        // final List<ShowFilmInCinema> groupedFilms = _groupFilmsByFilmId(filmList);

        setState(() {
          films = filmList;
        });
      } else {
        setState(() {
          films = [];
        });
        _showAlert(response?.message ?? "No showtimes available for this date");
      }
    } catch (e) {
      setState(() {
        films = [];
      });
      _showAlert("Error loading showtimes: $e");
    } finally {
      setState(() => isLoading = false);
    }
  }


  void _selectShow(ShowModel show, ShowFilmModel film) {
    // Check if showtime requires VIP/Zoom confirmation
    if (show.requiresConfirmation) {
      _showVipZoomConfirmation(show, film);
    } else {
      // Navigate directly to seat selection
      _navigateToSeatSelection(show, film);
    }

    // In a real implementation, also check login status:
    // final bool isLoggedIn = checkLoginStatus();
    // if (!isLoggedIn) {
    //   _showLoginDialog(show, film);
    // } else {
    //   // Continue with VIP/Zoom check or direct navigation
    // }
  }

  // Login dialog removed as it's not currently used
  // If login functionality is needed in the future, it can be reimplemented

  /// Converts a ShowFilmModel to a FilmModel for compatibility with ChooseSeatScreen
  FilmModel _convertToFilmModel(ShowFilmModel showFilm) {
    return FilmModel(
      FilmId: showFilm.filmId,
      Name: showFilm.name,
      Duration: showFilm.duration,
      FilmGenreName: showFilm.filmGenreName,
      FilmGenreName_F: showFilm.filmGenreNameF,
      MainPosterUrl: showFilm.mainPosterUrl,
      TrailerURL: showFilm.trailerURL,
      FilmRestrictAgeName: showFilm.filmRestrictAgeName,
      IsHot: showFilm.isHot,
    );
  }

  void _navigateToSeatSelection(ShowModel show, ShowFilmModel film) {
    // Convert ShowFilmModel to FilmModel for compatibility with ChooseSeatScreen
    final filmModel = _convertToFilmModel(film);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (context) => BlocC<FilmModel>(),
          child: ChooseSeatScreen(
            cinemaId: widget.cinema.cinemaId,
            cinemaName: widget.cinema.name,
            showTime: show,
            film: filmModel,
          ),
        ),
      ),
    );
  }

  /// Show VIP/Zoom confirmation dialog
  Future<void> _showVipZoomConfirmation(ShowModel show, ShowFilmModel film) async {
    final confirmed = await VipZoomConfirmationDialog.show(
      context: context,
      showTime: show,
      cinemaName: widget.cinema.name ?? 'Unknown Cinema',
      filmName: film.name ?? 'Unknown Film',
    );

    if (confirmed == true) {
      // User confirmed, proceed to seat selection
      _navigateToSeatSelection(show, film);
    }
    // If confirmed is false or null, do nothing (user cancelled)
  }

  void _showAlert(String message) {
    showDialog(
      context: context,
      builder: (_) =>
          AlertDialog(
            content: Text(message),
            actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text("OK"))],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
        title:
          'Đặt vé theo rạp',
        titleColor: Colors.white,
      ),
      body: Column(
        children: [
          // Cinema name and calendar header
          Container(
            color: Colors.white,
            child: Column(
              children: [
                // Cinema name header
                Padding(
                  padding: const EdgeInsets.only(top: 16, bottom: 8),
                  child: Text(
                    widget.cinema.name ?? 'Beta Cinemas',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // Divider
                Divider(color: Colors.grey.shade300, height: 1),

                // Calendar date selection
                if (showDates.isNotEmpty)
                  CalendarHeaderView(
                    dates: showDates,
                    onDateSelected: _onDateSelected,
                    selectedDate: _selectedDate,
                  )
                else if (isLoading)
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Center(child: CircularProgressIndicator()),
                  )
                else
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Center(child: Text('Không có ngày chiếu')),
                  ),
              ],
            ),
          ),

          // Film list with showtimes
          Expanded(
            child: Container(
              color: Colors.grey.shade100,
              child: isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : films.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.movie_outlined, size: 64, color: Colors.grey.shade300),
                              const SizedBox(height: 16),
                              Text(
                                "Không có suất chiếu cho ngày này",
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          itemCount: films.length,
                          itemBuilder: (context, index) {
                            final film = films[index];
                            return FilmTimeWidget(
                              cinema: widget.cinema,
                              film: film,
                              isTop1: index == 0, // First film is top 1
                              onShowSelected: (show) {
                                // Check if show time is in the future
                                if (show.startTime != null &&
                                    show.startTime!.isAfter(DateTime.now())) {
                                  _selectShow(show,film );
                                } else {
                                  _showAlert("Suất chiếu đã hết hạn.");
                                }
                              },
                              onPlayTrailer: () {
                                if (film.trailerURL != null &&
                                    film.trailerURL!.isNotEmpty) {
                                  Uri uri = Uri.parse('${film.trailerURL}');
                                  final idsUrl = uri.queryParameters['v'] ?? '';
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => YoutubePlay(url: idsUrl)
                                    )
                                  );
                                } else {
                                  _showAlert("Không có trailer cho phim này.");
                                }
                              },
                            );
                          },
                        ),
            ),
          ),
          // Expanded(child: WList<ShowFilmInCinema>(item: (film, index) =>
          //     FilmTimeWidget(
          //       cinema: widget.cinema,
          //       film: film,
          //       onShowSelected: (show) {
          //         if (show.getTimeLockDate()?.isAfter(DateTime.now()) ?? false) {
          //           _selectShow(show, film.film);
          //         } else {
          //           _showAlert("Suất chiếu đã hết hạn.");
          //         }
          //       },
          //       onPlayTrailer: () {
          //         if (film.film?.TrailerURL != null && film.film!.TrailerURL!.isNotEmpty) {
          //           // Show trailer URL in alert for now
          //           _showAlert("Trailer URL: ${film.film?.TrailerURL}");
          //
          //           // Implementation would use a YouTube player package
          //         } else {
          //           _showAlert("Không có trailer cho phim này.");
          //         }
          //       },
          //     ),
          // format: ShowFilmInCinema.fromJson,
          //  api: (filet, page, size, sort) => RepositoryProvider.of<Api>(context).film,))
        ],
      ),
    );
  }
}
