package vn.zenity.betacineplex.view.film

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import kotlinx.android.synthetic.main.fragment_selectchair.*
import load
import vn.zenity.betacineplex.Manager.signalr.ListenerData
import vn.zenity.betacineplex.Manager.signalr.ListenerSignalRConnection
import vn.zenity.betacineplex.Manager.signalr.SignalRService
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Config
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.helper.view.SeatTable
import vn.zenity.betacineplex.model.*
import vn.zenity.betacineplex.model.RequestModel.CreateBookingModel
import vn.zenity.betacineplex.model.RequestModel.SeatBookingModel
import vn.zenity.betacineplex.view.HomeActivity
import vn.zenity.betacineplex.view.auth.LoginFragment
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * Created by Zenity.
 */

class SelectChairFragment : BaseFragment(), SelectChairContractor.View {

    companion object {
        fun getInstance(time: ShowModel?, film: FilmModel?): SelectChairFragment {
            val frag = SelectChairFragment()
            frag.time = time
            frag.film = film
            return frag
        }
    }

    private var time: ShowModel? = null
    private var film: FilmModel? = null
    private var listSeatSelected: ArrayList<SeatScreenModel> = arrayListOf()

    private val presenter = SelectChairPresenter()
    private val hubConnect = SignalRService.share()
    private val eventSubscribe = "broadcastMessage"
    private val eventSendMessage = "sendMessage"

    private var priceDouble = 0
    private var priceVIP = 0
    private var priceNormal = 0

    private var needResetDataWhenDestroyView = true

    private var expiredTime: Date? = null

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_selectchair
    }

    private var isDownInTable = true
    private var showTime: ShowTimeModel? = null

    @SuppressLint("SetTextI18n", "ClickableViewAccessibility")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        scrollView?.setOnTouchListener(View.OnTouchListener { v, event ->
            if (event.action == MotionEvent.ACTION_UP) {
                isDownInTable = true
            }
            val viewC1 = (v as FrameLayout).getChildAt(0)
            if (viewC1 is LinearLayout) {
                if (viewC1.childCount > 1) {
                    val viewC2 = viewC1.getChildAt(1)
                    if (viewC2 is ConstraintLayout) {
                        for (i in 0 until viewC2.childCount) {
                            val viewC3 = viewC2.getChildAt(i)
                            if (viewC3 is SeatTable) {
//                                if(event)
                                if (event.y - v.top > viewC3.top) {
//                                    val ev = event
//                                    ev.setLocation(event.x, event.y)
                                    if (event.action == MotionEvent.ACTION_DOWN) {
                                        isDownInTable = true
                                    }
                                    if (isDownInTable || event.action == MotionEvent.ACTION_UP) {
                                        viewC3.onTouchEvent(event)
                                    }
                                    return@OnTouchListener false
                                }
                                if (event.action == MotionEvent.ACTION_DOWN) {
                                    isDownInTable = false
                                }
                            }
                        }
                    }
                }
            }
            false
        })
        ivBanner?.load(film?.filmPosterUrl?.toImageUrl())
        tvFilmTitle.text = film?.Name

        tvFilmType.text = ""
        seatTable?.setScreenName(getString(R.string.screen).toUpperCase())
        seatTable?.setMaxSelected(Config.MAX_SELECT_CHAIR)
        tvSelectedSeat?.setOnClickListener {
            if (listSeatSelected.size > 0) {
                context?.let {
                    showListSeatSelected(it, listSeatSelected)
                }
            }
        }
        presenter.getListSeat(time?.ShowId ?: return)
        btnNext.setOnClickListener {
            val startTime = time?.getStartDate()?.time ?: return@setOnClickListener
            if (startTime > (System.currentTimeMillis() + ((time?.TimeToLock ?: 0) * 60 * 1000))) {
            } else {
                showNotice(getString(R.string.time_booking_is_expried))
                return@setOnClickListener
            }
            if (listSeatSelected.size <= 0) {
                showNotice(getString(R.string.seat_not_chosen))
            } else {
                if (!validateSelectedSeat()) return@setOnClickListener
                val age = film?.restrictAge ?: 0
                if (age >= 13) {
                    showNoticeAge(age) {
                        openPayment()
                    }
                } else {
                    openPayment()
                }
            }
        }
        hubConnect.addListenerConnection(signalRListenerConnection)
    }

    private fun validateSelectedSeat(): Boolean {
        for (seat in listSeatSelected) {
            if (seat.SeatType?.Value == Constant.SeatType.DOUBLE) continue
            val rowIndex = seat.rowIndex ?: -1
            val columnIndex = seat.columnIndex ?: -1
            val isValidSeat = seatTable?.seatChecker?.isValidSeat(rowIndex, columnIndex)
            if (isValidSeat != true) {
                showNotice(getString(R.string.seat_is_not_use, seat.SeatName))
                return false
            }
            val prevFirstSeat =
                showTime?.mapSeat?.get(seat.rowIndex to ((seat.columnIndex ?: -2) - 1))
            val prevSecondSeat =
                showTime?.mapSeat?.get(seat.rowIndex to ((seat.columnIndex ?: -2) - 2))
            val nextFirstSeat =
                showTime?.mapSeat?.get(seat.rowIndex to ((seat.columnIndex ?: -2) + 1))
            val nextSecondSeat =
                showTime?.mapSeat?.get(seat.rowIndex to ((seat.columnIndex ?: -2) + 2))

            if (prevFirstSeat != null && !Constant.SeatStatus.isSeatNotAvailable(
                    prevFirstSeat.Status?.Value
                        ?: Constant.SeatStatus.NOT_USED
                )
            ) {

                val prevFirstInUsed = prevFirstSeat.SoldStatus != Constant.SeatSoldStatus.EMPTY
                //prevFirstInUsed=false
                val prevFirstInSelect =
                    listSeatSelected.find { e -> e.SeatIndex == prevFirstSeat.SeatIndex } != null

                var isBlock = !prevFirstInUsed && prevFirstInSelect
                if (prevSecondSeat != null && !Constant.SeatStatus.isSeatNotAvailable(
                        prevSecondSeat.Status?.Value
                            ?: Constant.SeatStatus.NOT_USED
                    )
                ) {
                    val prevSecondInUsed =
                        prevSecondSeat.SoldStatus != Constant.SeatSoldStatus.EMPTY
                    val prevSecondInSelect =
                        listSeatSelected.find { e -> e.SeatIndex == prevSecondSeat.SeatIndex } != null
                    if (!prevSecondInUsed && !prevSecondInSelect) {
                        isBlock = false
                    }
                }
                if (isBlock) {
                    showNotice(getString(R.string.seat_can_not_be_empty, prevFirstSeat.SeatName))
                    return false
                }

            }

            if (nextFirstSeat != null && !Constant.SeatStatus.isSeatNotAvailable(
                    nextFirstSeat.Status?.Value
                        ?: Constant.SeatStatus.NOT_USED
                )
            ) {
                val nextFirstInUsed = nextFirstSeat.SoldStatus != Constant.SeatSoldStatus.EMPTY
                val nextFirstInSelect =
                    listSeatSelected.find { e -> e.SeatIndex == nextFirstSeat.SeatIndex } != null
                var isBlock = !nextFirstInUsed && !nextFirstInSelect
                if (nextSecondSeat != null && !Constant.SeatStatus.isSeatNotAvailable(
                        nextSecondSeat.Status?.Value
                            ?: Constant.SeatStatus.NOT_USED
                    )
                ) {
                    val nextSecondInUsed =
                        nextSecondSeat.SoldStatus != Constant.SeatSoldStatus.EMPTY
                    val nextSecondInSelect =
                        listSeatSelected.find { e -> e.SeatIndex == nextSecondSeat.SeatIndex } != null
                    if (!nextSecondInUsed && !nextSecondInSelect) {
                        isBlock = false
                    }
                }
                if (isBlock) {
                    showNotice(getString(R.string.seat_can_not_be_empty, nextFirstSeat.SeatName))
                    return false
                }
            }
        }
        return true
    }

    private val signalRListenerConnection: ListenerSignalRConnection = {
        if (it == SignalRService.StateSignalR.connected) {
            if (showTime != null) {
                hideLoading()
            }
        } else if (it == SignalRService.StateSignalR.disabled) {
//            if (showTime != null) {
//                showLoading()
//            }
        }
    }

    private fun openPayment() {
        if (Global.share().isLogin) {
            val bookingModel = CreateBookingModel(time?.ShowId
                ?: return,
                expiredTime?.toStringFormat(Constant.DateFormat.timeServer),
                listSeatSelected.map { SeatBookingModel.createFromScreen(it) })
            val normalSeats =
                listSeatSelected.filter { v -> v.SeatType?.Value == Constant.SeatType.NORMAL }.size
            val vipSeats =
                listSeatSelected.filter { v -> v.SeatType?.Value == Constant.SeatType.VIP }.size
            val doubleSeats =
                listSeatSelected.filter { v -> v.SeatType?.Value == Constant.SeatType.DOUBLE }.size
            val totalSeats = normalSeats + vipSeats + doubleSeats
            val movieId = film?.FilmId
            val movieName = film?.Name
            val date = showTime?.GioChieu?.toDate("yyyy-MM-dd'T'HH:mm:ss")
            val time = showTime?.GioChieu?.toDate("yyyy-MM-dd'T'HH:mm:ss")
            val screen = showTime?.Screen?.Code
            Tracking.share().selectSeatComplete(
                context,
                film?.CinemaId,
                film?.CinemaName,
                movieId,
                movieName,
                date,
                time,
                screen,
                normalSeats,
                vipSeats,
                doubleSeats,
                totalSeats,
                totalPrice
            )
            openFragment(BookingPaymentFragment.getInstance(
                bookingModel, showTime
                    ?: return, film, totalPrice, filmCombo
            ) {
                needResetDataWhenDestroyView = false
            })
        } else {
            openFragment(LoginFragment())
        }
    }

    private fun connect() {
        try {
            hubConnect.startBGSignalR()
        } catch (ex: Exception) {
            activity?.runOnUiThread {
                logD(ex.message ?: "Exception connect hub")
            }
        }
    }

    @SuppressLint("SetTextI18n")
    override fun showShowTime(showTime: ShowTimeModel) {
        (activity as? HomeActivity)?.startCountdown()
        expiredTime = Date(System.currentTimeMillis() + Constant.orderTime)
        presenter.countDownTime(expiredTime!!)
        activity?.runOnUiThread {
            var times = showTime.GioChieu.dateConvertFormat(
                Constant.DateFormat.default,
                Constant.DateFormat.dateTimeShowTime
            )
            if (times.isNotEmpty()) {
                times = " | $times"
            }
            tvFilmType.text = (showTime.FilmFormatName ?: "") + times +
                    " | " + ((film?.Duration ?: 0).toString() +
                    " " + R.string.minute.getString())
            connect()
            this.showTime = showTime
            this.showTime?.TicketTypes?.let {
                priceDouble = (it.firstOrNull { it.SeatTypeId == Constant.SeatType.DOUBLE }?.Price
                    ?: 0) * 2
                priceVIP = it.firstOrNull { it.SeatTypeId == Constant.SeatType.VIP }?.Price ?: 0
                priceNormal = it.firstOrNull { it.SeatTypeId == Constant.SeatType.NORMAL }?.Price
                    ?: 0
            }
            tvPriceChairDouble.text = priceDouble.toLong().toVNDCurrency()
            tvPriceChairVIP.text = priceVIP.toLong().toVNDCurrency()
            tvPriceChairNormal.text = priceNormal.toLong().toVNDCurrency()
            seatTable?.postDelayed({
                seatTable?.seatChecker = object : SeatTable.SeatChecker {
                    override fun isMoveVertical() = false

                    override fun isMoveHorizontal() = true

                    override fun isEnableZoom() = true

                    //Kiểm tra ghế có được sử dụng không.
                    override fun isValidSeat(row: Int, column: Int): Boolean {

                        val seat = showTime.mapSeat[Pair(
                            row,
                            column
                        )]
                        if (Constant.SeatType.DOUBLE == seat?.SeatType?.Value && !seat.isShowDouble) {
                            return false
                        }
                        return seat != null &&
                                !Constant.SeatStatus.isSeatNotAvailable(
                                    seat.Status?.Value
                                        ?: Constant.SeatStatus.NOT_USED
                                )
                    }

                    override fun checked(row: Int, column: Int) {
                        val seat = showTime.mapSeat[Pair(row, column)] ?: return
                        if (Constant.SeatStatus.IN_PROCESSING == seat.Status?.Value) return
                        if (!listSeatSelected.contains(seat)) {
                            listSeatSelected.add(seat)
                            invokeSignalRMessage(
                                eventSendMessage,
                                time?.ShowId,
                                seat.SeatIndex,
                                Constant.SeatSoldStatus.SELECTING
                            )
                            showTime.mapSeat[showTime.mapSeatIndex[seat.SeatIndex]
                                ?: (-1 to -1)]?.SoldStatus = Constant.SeatSoldStatus.USER_SELECTED
                            if (seat.SeatType?.Value == Constant.SeatType.DOUBLE) {
                                val seatRight = showTime.mapSeat[Pair(row, column + 1)]
                                seatRight?.let {
                                    if (!listSeatSelected.contains(it)) {
                                        listSeatSelected.add(it)
                                        invokeSignalRMessage(
                                            eventSendMessage,
                                            time?.ShowId,
                                            it.SeatIndex,
                                            Constant.SeatSoldStatus.SELECTING
                                        )
                                        showTime.mapSeat[showTime.mapSeatIndex[it.SeatIndex]
                                            ?: (-1 to -1)]?.SoldStatus =
                                            Constant.SeatSoldStatus.USER_SELECTED
                                    }
                                }
                            }
                        }
                        onSelectSeatChanged()
                    }

                    override fun unCheck(row: Int, column: Int) {
                        val seat = showTime.mapSeat[Pair(row, column)] ?: return
                        if (Constant.SeatStatus.IN_PROCESSING == seat.Status?.Value) return
                        listSeatSelected.remove(seat)
                        invokeSignalRMessage(
                            eventSendMessage,
                            time?.ShowId,
                            seat.SeatIndex,
                            Constant.SeatSoldStatus.EMPTY
                        )
                        showTime.mapSeat[showTime.mapSeatIndex[seat.SeatIndex]
                            ?: (-1 to -1)]?.SoldStatus = Constant.SeatSoldStatus.EMPTY
                        if (seat.SeatType?.Value == Constant.SeatType.DOUBLE) {
                            val seatRight = showTime.mapSeat[Pair(row, column + 1)]
                            seatRight?.let {
                                if (listSeatSelected.contains(it)) {
                                    listSeatSelected.remove(it)
                                    invokeSignalRMessage(
                                        eventSendMessage,
                                        time?.ShowId,
                                        it.SeatIndex,
                                        Constant.SeatSoldStatus.EMPTY
                                    )
                                    showTime.mapSeat[showTime.mapSeatIndex[it.SeatIndex]
                                        ?: (-1 to -1)]?.SoldStatus = Constant.SeatSoldStatus.EMPTY
                                }
                            }
                        }
                        onSelectSeatChanged()
                    }

                    override fun checkedSeatTxt(row: Int, column: Int): Array<String>? {
                        return null
                    }

                    override fun getColumnOfRow(row: Int): Int {
                        if (row < 0 || row >= showTime.Screen?.SeatPosition?.size ?: 0) {
                            return 0
                        }
                        return showTime.Screen?.SeatPosition?.get(row)?.size ?: 0
                    }

                    override fun getMarginLeftOfRow(row: Int): Int {
                        return showTime.mapNumberNotUsed[row] ?: 0
                    }

                    override fun getMarginTopOfRow(row: Int): Int {
                        return 0
                    }

                    override fun isSold(row: Int, column: Int): Boolean {
                        val status = showTime.mapSeat[Pair(row, column)]?.SoldStatus ?: return false
                        return when (status) {
                            Constant.SeatSoldStatus.SOLD,
                            Constant.SeatSoldStatus.SOLDED -> true

                            else -> false
                        }
                    }

                    override fun isReverse(row: Int, column: Int): Boolean {
                        val status = showTime.mapSeat[Pair(row, column)]?.SoldStatus ?: return false
                        return when (status) {
                            Constant.SeatSoldStatus.BOOKED -> true
                            else -> false
                        }
                    }

                    override fun isLaneExit(row: Int, column: Int): Boolean {
                        val status =
                            showTime.mapSeat[Pair(row, column)]?.Status?.Value ?: return true
                        return when (status) {
                            Constant.SeatStatus.IN_PROCESSING -> true
                            else -> false
                        }
                    }

                    override fun isDoubleSeat(row: Int, column: Int): Boolean {
                        return Constant.SeatType.DOUBLE == showTime.mapSeat[Pair(
                            row,
                            column
                        )]?.SeatType?.Value && showTime.mapSeat[Pair(
                            row,
                            column
                        )]?.isShowDouble == true && Constant.SeatStatus.USED == showTime.mapSeat[Pair(
                            row,
                            column
                        )]?.Status?.Value
                    }

                    override fun isSelecting(row: Int, column: Int): Boolean {
                        val status = showTime.mapSeat[Pair(row, column)]?.SoldStatus ?: return false
                        return when (status) {
                            Constant.SeatSoldStatus.SELECTING -> true
                            else -> false
                        }
                    }

                    override fun isSelected(row: Int, column: Int): Boolean {
                        val status = showTime.mapSeat[Pair(row, column)]?.SoldStatus ?: return false
                        return when (status) {
                            Constant.SeatSoldStatus.SELECTED -> true
                            else -> false
                        }
                    }

                    override fun isUserSelected(row: Int, column: Int): Boolean {
                        val status = showTime.mapSeat[Pair(row, column)]?.SoldStatus ?: return false
                        return when (status) {
                            Constant.SeatSoldStatus.USER_SELECTED -> true
                            else -> false
                        }
                    }

                    override fun isVIP(row: Int, column: Int): Boolean {
                        return Constant.SeatType.VIP == showTime.mapSeat[Pair(
                            row,
                            column
                        )]?.SeatType?.Value
                    }

                    override fun notifiMaxSelect() {
                        showNotice(getString(R.string.notice_max_seat_select))
                    }

                    override fun getSeatName(row: Int, column: Int): String {
                        return showTime.mapSeat[Pair(row, column)]?.SeatName ?: ""
                    }

                    override fun getSoldStatus(row: Int, column: Int): Int {
                        return showTime.mapSeat[Pair(row, column)]?.SoldStatus ?: return -100
                    }
                }
                seatTable?.setData(
                    this.showTime?.Screen?.NumberRow
                        ?: 0, this.showTime?.Screen?.NumberCol ?: 0
                )
                seatTable?.requestLayout()
            }, 200)
            hubConnect.listenerData.add(seatStatusListener)
        }
    }

    private var totalPrice = 0
    private var filmCombo = ""
    private fun onSelectSeatChanged() {
        activity?.runOnUiThread {
            totalPrice = 0
            filmCombo = ""

            var isHasStandand = false
            var isHasVIP = false
            var isHasDouble = false

            var selectedSeat = ""
            for ((index, seat) in listSeatSelected.withIndex()) {
                selectedSeat += (seat.SeatName)
                if (index < listSeatSelected.size - 1) {
                    selectedSeat += (", ")
                }
                if (Constant.SeatType.DOUBLE == seat.SeatType?.Value && !seat.isShowDouble) {

                } else {
                    totalPrice += when (seat.SeatType?.Value) {
                        Constant.SeatType.DOUBLE -> {
                            isHasDouble = true
                            priceDouble
                        }

                        Constant.SeatType.VIP -> {
                            isHasVIP = true
                            priceVIP
                        }

                        Constant.SeatType.NORMAL -> {
                            isHasStandand = true
                            priceNormal
                        }

                        else -> 0
                    }
                }
            }
            filmCombo =
                "${if (isHasDouble) ",DOUBLE" else ""}${if (isHasVIP) ",VIP" else ""}${if (isHasStandand) ",STANDARD" else ""}"
            if (filmCombo.isNotEmpty()) {
                filmCombo = filmCombo.substring(1)
            }
            tvPrice.text = totalPrice.toLong().toVNDCurrency()
            tvSelectedSeat.text = selectedSeat
            if (selectedSeat.isEmpty()) {
                actionCL.gone()
            } else {
                actionCL.visible()
            }
        }
    }

    private val seatStatusListener: ListenerData = ls@{ data ->
        if (data.showId == time?.ShowId) {
//            if (data.collectionId != hubConnect.connectionId()) {
            showTime?.mapSeat?.get(
                showTime?.mapSeatIndex?.get(data.seatIndex)
                    ?: return@ls
            )?.SoldStatus = data.seatStatus
            activity?.runOnUiThread {
                seatTable?.invalidate()
            }
//            }
        }
    }

    override fun isConnectedSignalR(): Boolean {
        // ToDo HaiBH check bug 18/02/2024: connectionId null => remove check DEBUG
        return hubConnect.connectionId().isNotEmpty();
    }

    override fun showCountDownTime(remainingTime: Long) {
        tvCountDown.text = String.format(
            "%02d:%02d",
            TimeUnit.MILLISECONDS.toMinutes(remainingTime),
            TimeUnit.MILLISECONDS.toSeconds(remainingTime) -
                    TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(remainingTime))
        )
    }

    fun invokeSignalRMessage(event: String, vararg arg: Any?) {
        try {
            val data = arrayListOf<Any>()
            arg.forEach {
                data.add(it ?: "")
            }
            data.add(0, hubConnect.connectionId())
            hubConnect.invoke(event, data)
        } catch (_: Exception) {

        }
    }

    override fun onDestroyView() {
        (activity as? HomeActivity)?.stopCountdown()
        if (needResetDataWhenDestroyView) resetData()
        hubConnect.removeListenerConnection(signalRListenerConnection)
        hubConnect.listenerData.remove(seatStatusListener)
        hubConnect.unSubscribe(eventSubscribe)
        hubConnect.stop()
        super.onDestroyView()
    }

    private fun checkCountDown() {
        if (expiredTime != null && System.currentTimeMillis() - expiredTime!!.time >= 0) { //10 * 60 * 1000
            tvCountDown.text = String.format("%02d:%02d", 0, 0)
        }
    }

    override fun onResume() {
        checkCountDown()
        super.onResume()
    }

    var resetData: () -> Unit = {
        listSeatSelected.forEach { seat ->
            val showStatus = showTime?.mapSeat?.get(
                showTime?.mapSeatIndex?.get(seat.SeatIndex)
                    ?: (-1 to -1)
            )?.SoldStatus ?: -1
            if (showStatus == Constant.SeatSoldStatus.USER_SELECTED) {
                invokeSignalRMessage(
                    eventSendMessage,
                    time?.ShowId,
                    seat.SeatIndex,
                    Constant.SeatSoldStatus.EMPTY
                )
                showTime?.mapSeat?.get(
                    showTime?.mapSeatIndex?.get(seat.SeatIndex)
                        ?: (-1 to -1)
                )?.SoldStatus = Constant.SeatSoldStatus.EMPTY
            }

        }
        seatTable?.resetSelected()
        listSeatSelected = arrayListOf()
        activity?.runOnUiThread {
            seatTable?.invalidate()
        }
        onSelectSeatChanged()
    }
}
