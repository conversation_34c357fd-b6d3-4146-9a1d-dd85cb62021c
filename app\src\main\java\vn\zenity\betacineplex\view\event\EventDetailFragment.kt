package vn.zenity.betacineplex.view.event

import android.os.Build
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.Html
import android.text.Layout
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_eventdetail.*
import kotlinx.android.synthetic.main.item_header_event_detail.view.*
import load
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.model.NewNotification
import vn.zenity.betacineplex.model.NewsModel
import vn.zenity.betacineplex.model.NotificationDetail
import vn.zenity.betacineplex.view.cenima.CenimaDetailFragment
import vn.zenity.betacineplex.view.film.BookByCinemaFragment
import vn.zenity.betacineplex.view.film.BookByFilmFragment
import vn.zenity.betacineplex.view.film.FilmDetailFragment

/**
 * Created by Zenity.
 */

class EventDetailFragment : BaseFragment(), EventDetailContractor.View {
    override fun showEventDetail(event: NewsModel) {
        this.event = event
        activity?.runOnUiThread {
            adapter.notifyItemChanged(0)
        }
    }

    override fun showNotification(noti: NotificationDetail) {
        activity?.runOnUiThread {
            this.notificationDetail = noti
            adapter.notifyItemChanged(0)
        }
//        presenter.readNotification(notification?.Id!!)
    }

    private val presenter = EventDetailPresenter()
    private var event: NewsModel? = null
    private var eventId: String? = null
    private var notification: NewNotification? = null
    private var notificationDetail: NotificationDetail? = null
    private var titleToolbar: String? = null
    private lateinit var adapter: Adapter

    companion object {
        fun getInstance(event: NewsModel?, notification: NewNotification? = null, title: String? = null, eventId: String? = null): EventDetailFragment {
            val frag = EventDetailFragment()
            frag.titleToolbar = title
            frag.event = event
            frag.eventId = eventId
            frag.notification = notification
            return frag
        }
    }

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_eventdetail
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        adapter = Adapter()
        recyclerView.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this.context)
        recyclerView.adapter = adapter
        btnBack.setOnClickListener {
            back()
        }
        (event?.StorylineID ?: eventId)?.let {
            presenter.getEventDetails(it)
        }

        notification?.NotificationCampaignId?.let {
            presenter.getNotificationDetails(it)
        }

        titleToolbar?.let {
            tvTitle?.text = it
        }

        btnShare.setOnClickListener {
            var uri = event?.NewsURI ?: return@setOnClickListener
            uri = if (!uri.startsWith("/")) {
                "${BuildConfig.SHARE_DOMAIN}/$uri"
            } else {
                "${BuildConfig.SHARE_DOMAIN}$uri"
            }
            uri.share(R.string.share.getString(), activity ?: return@setOnClickListener)
        }

        if (notification != null) {
            btnShare.gone()
        }
    }

    private inner class Adapter : RecyclerView.Adapter<Holder>() {

        private val TYPE_EVENT = 1
        private val TYPE_HEADER = 0

        override fun getItemCount(): Int {
            return 1
        }

        override fun getItemViewType(position: Int): Int {
            if (position == 0) return TYPE_HEADER
            return TYPE_EVENT
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            if (getItemViewType(position) == TYPE_HEADER) {
                if (event != null) {
                    holder.itemView.ivBanner.load(event?.Duong_dan_anh_dai_dien?.toImageUrl())
                    holder.itemView.tvEventTitle.text = event?.Tieu_de
                    val content = event?.getFullContent() ?: event?.Tom_tat_noi_dung
                    content?.let {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            holder.itemView.tvDescription.text = Html.fromHtml(it, Html.FROM_HTML_MODE_COMPACT)
                        } else {
                            holder.itemView.tvDescription.text = Html.fromHtml(it)
                        }
                        holder.itemView.webView.loadBetaHtml(it)
                    }
                } else {
                    holder.itemView.ivBanner.load(notificationDetail?.ImageThumb?.toImageUrl())
                    holder.itemView.tvEventTitle.text = notificationDetail?.Title
                    val content = notificationDetail?.Content
                    content?.let {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            holder.itemView.tvDescription.text = Html.fromHtml(it, Html.FROM_HTML_MODE_COMPACT)
                        } else {
                            holder.itemView.tvDescription.text = Html.fromHtml(it)
                        }
                        holder.itemView.webView.loadBetaHtml(it)
                    }
                }
                holder.itemView.webView.setLinkListener { data ->
                    provideAppLink(data)
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_header_event_detail, parent, false)
            return Holder(itemView)
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)
}
