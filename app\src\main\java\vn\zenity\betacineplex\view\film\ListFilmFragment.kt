package vn.zenity.betacineplex.view.film

import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_listfilm.*
import kotlinx.android.synthetic.main.item_film_in_list.view.*
import load
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.helper.extension.gone
import vn.zenity.betacineplex.helper.extension.toImageUrl
import vn.zenity.betacineplex.helper.extension.visible
import vn.zenity.betacineplex.helper.support.TrailerPlayActivity
import vn.zenity.betacineplex.model.Film
import vn.zenity.betacineplex.model.FilmModel

/**
 * Created by Zenity.
 */

class ListFilmFragment : BaseFragment(), ListFilmContractor.View {

    companion object {
        fun getInstance(type: Int): ListFilmFragment{
            val frag = ListFilmFragment()
            frag.type = type
            return frag
        }
    }

    private val presenter = ListFilmPresenter()
    private lateinit var adapter: Adapter
    private var type = 0

    override fun showListFilm(films: List<FilmModel>) {
        adapter.films = films
        activity?.runOnUiThread {
            adapter.notifyDataSetChanged()
        }
    }

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override val isPaddingBottomBar = false

    override fun getLayoutRes(): Int {
        return R.layout.fragment_listfilm
    }

    override fun onRefresh() {
        super.onRefresh()
        activity?.runOnUiThread {
            refreshView?.finishRefreshing()
        }
        presenter.getListFilm(type)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.layoutManager = LinearLayoutManager(context)
        adapter = Adapter(listOf())
        recyclerView.adapter = adapter
        presenter.getListFilm(type)
    }

    private inner class Adapter(var films: List<FilmModel>) : RecyclerView.Adapter<Holder>() {

        override fun getItemCount(): Int {
            return films.size
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            val film = films[position]
            holder.itemView.ivBanner.load(film.MainPosterUrl?.toImageUrl())
            holder.itemView.tvFilmTitle.text = film.Name
            holder.itemView.setOnClickListener {
                Tracking.share().selectMovie(context,film?.FilmId,film?.Name)
                openFragment(BookByFilmFragment.getInstance(film))
            }
            holder.itemView.btnPlayTrailer?.setOnClickListener {
                val intent = Intent(activity, TrailerPlayActivity::class.java)
                intent.putExtra(Constant.Key.trailerId, film.TrailerURL)
                activity?.startActivity(intent)
            }
            if (type == 1 && position == 0) {
                holder.itemView.fillSTT.visible()
            } else {
                holder.itemView.fillSTT.gone()
            }
            holder.itemView.tvFilm2d.text = "(${film.FilmFormatName})"
            holder.itemView.tvFilmType.text = film.filmGenner
            holder.itemView.tvFilmDuration.text = "${film.Duration} ${R.string.minute.getString()}"
            if(film.FilmRestrictAgeName?.isNotEmpty() == true) {
                holder.itemView.ivTypeAge.visible()
                holder.itemView.ivTypeAge.setImageResource(when(film.FilmRestrictAgeName?.toLowerCase() ?: "p") {
                    "c18" -> R.drawable.ic_age_c_18
                    "c16" -> R.drawable.c_16
                    "c13" -> R.drawable.ic_age_c_13
                    else -> R.drawable.ic_age_p
                })
            } else {
                holder.itemView.ivTypeAge.gone()
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            val item = LayoutInflater.from(parent.context).inflate(R.layout.item_film_in_list, parent, false)
            return Holder(item)
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)
}
