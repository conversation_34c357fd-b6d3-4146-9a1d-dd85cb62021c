package vn.zenity.betacineplex.helper.extension

import android.location.Location

fun Location.getDistance(lat: String?, lng: String?): Float{
    if (lat == null || lng == null) return 10000f
    try {
        val distance: FloatArray = floatArrayOf(1000f)
        Location.distanceBetween(latitude, longitude, lat.toDouble(), lng.toDouble(), distance)
        return distance[0] / 1000f
    } catch (_ : Exception) {

    }
    return 10000f
}