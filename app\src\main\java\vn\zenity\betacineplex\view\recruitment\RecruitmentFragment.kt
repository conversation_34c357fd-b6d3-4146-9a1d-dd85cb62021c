package vn.zenity.betacineplex.view.recruitment

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import android.view.View
import android.view.ViewGroup
import com.thoughtbot.expandablerecyclerview.ExpandableRecyclerViewAdapter
import com.thoughtbot.expandablerecyclerview.models.ExpandableGroup
import com.thoughtbot.expandablerecyclerview.viewholders.ChildViewHolder
import com.thoughtbot.expandablerecyclerview.viewholders.GroupViewHolder
import kotlinx.android.synthetic.main.fragment_notification.*
import kotlinx.android.synthetic.main.item_content_notifi_in_list.view.*
import kotlinx.android.synthetic.main.item_title_notifi_in_list.view.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.helper.extension.gone
import vn.zenity.betacineplex.helper.extension.inflate
import vn.zenity.betacineplex.model.Notification
import vn.zenity.betacineplex.view.event.EventDetailFragment

/**
 * Created by Zenity.
 */

class RecruitmentFragment : BaseFragment(), RecruitmentContractor.View {
    override fun showListNotifications(notifes: List<Notification>) {
        activity?.runOnUiThread {
            adapter = NotifiAdapter(notifes)
            recyclerView.adapter = adapter
        }
    }

    private val presenter = RecruitmentPresenter()
    private var adapter: NotifiAdapter? = null

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_notification
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        tvTitle?.text = R.string.recruitment.getString()
        recyclerView.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this.context)
        adapter = NotifiAdapter(listOf())
        presenter.getListNotification(1)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        adapter?.onSaveInstanceState(outState)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        adapter?.onRestoreInstanceState(savedInstanceState)
    }

    inner class NotifiAdapter(notifes: List<Notification>?) : ExpandableRecyclerViewAdapter<TitleHolder, ContentHolder>(notifes) {

        override fun onCreateGroupViewHolder(parent: ViewGroup, viewType: Int): TitleHolder {
            val view = parent.inflate(R.layout.item_title_notifi_in_list)
            return TitleHolder(view)
        }

        override fun onCreateChildViewHolder(parent: ViewGroup, viewType: Int): ContentHolder {
            val view = parent.inflate(R.layout.item_content_notifi_in_list)
            return ContentHolder(view)
        }

        override fun onBindChildViewHolder(holder: ContentHolder, flatPosition: Int, group: ExpandableGroup<*>, childIndex: Int) {
            holder.itemView.tvContent.text = (group as Notification).content
        }

        override fun onBindGroupViewHolder(holder: TitleHolder, flatPosition: Int, group: ExpandableGroup<*>) {
            holder.itemView.tvTitle.text = (group as Notification).title
            holder.itemView.tvDate.gone()
            holder.itemView.ivDropdown?.rotation = -90f
            holder.itemView.setOnClickListener {
                openFragment(EventDetailFragment.getInstance(group.newsModel ?: return@setOnClickListener, null,R.string.recruitment.getString()))
            }
        }

    }

    inner class TitleHolder(itemView: View): GroupViewHolder(itemView) {

        override fun expand() {
            super.expand()
            itemView.ivDropdown?.setImageResource(R.drawable.ic_zipup)
        }

        override fun collapse() {
            super.collapse()
            itemView.ivDropdown?.setImageResource(R.drawable.ic_dropdown)
        }

    }
    inner class ContentHolder(itemView: View): ChildViewHolder(itemView)
}
