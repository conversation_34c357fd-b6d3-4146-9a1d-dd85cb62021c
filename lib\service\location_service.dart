import 'dart:math';
import 'package:flutter/material.dart';
import 'package:fl_location/fl_location.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:permission_handler/permission_handler.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();

  factory LocationService() {
    return _instance;
  }

  LocationService._internal();

  Location? _currentLocation;

  Location? get currentLocation => _currentLocation;

  /// Determine the current position of the device.
  /// When the location services are not enabled or permissions
  /// are denied the `Future` will return an error.
  Future<Location?> determinePosition({bool showError = true, BuildContext? context}) async {
    bool serviceEnabled;
    LocationPermission permission;

    // Test if location services are enabled.
    serviceEnabled = await FlLocation.isLocationServicesEnabled;
    if (!serviceEnabled) {
      // Location services are not enabled, show dialog to enable
      if (showError && context != null) {
        _showLocationServicesDisabledDialog(context);
      }
      return Future.error('Location services are disabled.');
    }

    permission = await FlLocation.checkLocationPermission();
    if (permission == LocationPermission.denied) {
      permission = await FlLocation.requestLocationPermission();
      if (permission == LocationPermission.denied) {
        // Permissions are denied, show dialog to request permission
        if (showError && context != null) {
          _showPermissionDeniedDialog(context);
        }
        return Future.error('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      // Permissions are denied forever, handle appropriately.
      if (showError && context != null) {
        _showPermissionPermanentlyDeniedDialog(context);
      }
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    // When we reach here, permissions are granted and we can
    // continue accessing the position of the device.
    try {
      _currentLocation = await FlLocation.getLocation(
        timeLimit: const Duration(seconds: 5),
        accuracy: LocationAccuracy.high,
      );
      return _currentLocation;
    } catch (e) {
      // If getting high accuracy position fails, try with lower accuracy
      try {
        _currentLocation = await FlLocation.getLocation(
          timeLimit: const Duration(seconds: 5),
          accuracy: LocationAccuracy.low,
        );
        return _currentLocation;
      } catch (e) {
        if (showError && context != null) {
          _showLocationErrorDialog(context, e.toString());
        }
        return null;
      }
    }
  }

  /// Calculate distance between two coordinates in kilometers
  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Radius of the earth in km

    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) * cos(_degreesToRadians(lat2)) *
        sin(dLon / 2) * sin(dLon / 2);

    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    double distance = earthRadius * c; // Distance in km

    return distance;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  /// Format distance to a readable string
  String formatDistance(double distanceInKm) {
    if (distanceInKm < 1) {
      // Convert to meters if less than 1 km
      int meters = (distanceInKm * 1000).round();
      return '$meters m';
    } else if (distanceInKm < 10) {
      // Show one decimal place if less than 10 km
      return '${distanceInKm.toStringAsFixed(1)} km';
    } else {
      // Round to nearest km if 10 km or more
      return '${distanceInKm.round()} km';
    }
  }

  void _showLocationServicesDisabledDialog(BuildContext context) {
    UDialog().showConfirm(
      title: 'Dịch vụ vị trí bị tắt',
      text: 'Vui lòng bật dịch vụ vị trí để xem khoảng cách đến rạp chiếu phim.',
      btnOkText: 'Mở cài đặt',
      btnCancelText: 'Hủy',
      btnOkOnPress: () {
        Navigator.of(context).pop();
        openAppSettings();
      },
      btnCancelOnPress: () {
        Navigator.of(context).pop();
      },
    );
  }

  void _showPermissionDeniedDialog(BuildContext context) {
    UDialog().showConfirm(
      title: 'Quyền truy cập vị trí bị từ chối',
      text: 'Vui lòng cấp quyền truy cập vị trí để xem khoảng cách đến rạp chiếu phim.',
      btnOkText: 'Cấp quyền',
      btnCancelText: 'Hủy',
      btnOkOnPress: () async {
        Navigator.of(context).pop();
        await FlLocation.requestLocationPermission();
      },
      btnCancelOnPress: () {
        Navigator.of(context).pop();
      },
    );
  }

  void _showPermissionPermanentlyDeniedDialog(BuildContext context) {
    UDialog().showConfirm(
      title: 'Quyền truy cập vị trí bị từ chối vĩnh viễn',
      text: 'Vui lòng cấp quyền truy cập vị trí trong cài đặt ứng dụng để xem khoảng cách đến rạp chiếu phim.',
      btnOkText: 'Cấp quyền',
      btnCancelText: 'Hủy',
      btnOkOnPress: () async {
        Navigator.of(context).pop();
        openAppSettings();
      },
      btnCancelOnPress: () {
        Navigator.of(context).pop();
      },
    );
  }

  void _showLocationErrorDialog(BuildContext context, String error) {
    UDialog().showError(
      title: 'Lỗi lấy vị trí',
      text: 'Không thể lấy vị trí hiện tại: $error',
      onTap: () {
        Navigator.of(context).pop();
      },
    );
  }
}
